{"name": "tyxuan-mes", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "scripts": {"lint": "eslint \"src/**/*.{js,vue}\"", "serve": "cross-env NODE_ENV=development vite", "build": "cross-env NODE_ENV=production vite build", "preview": "cross-env vite preview", "build:preview": "vite build --mode production && vite preview", "lint:eslint": "eslint --cache --max-warnings 0  \"{src}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:style": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "lint:pretty": "pretty-quick --staged"}, "dependencies": {"-": "^0.0.1", "@arco-design/web-vue": "^2.47.0", "@element-plus/icons-vue": "^2.0.10", "@fortawesome/fontawesome-free": "^6.5.1", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@icon-park/vue-next": "^1.4.2", "@soerenmartius/vue3-clipboard": "^0.1.2", "@tweenjs/tween.js": "^19.0.0", "animate.css": "^4.1.1", "axios": "^1.3.2", "d3": "^7.8.5", "d3-org-chart": "^2.7.0", "docx": "^8.5.0", "docxtemplater": "^3.44.0", "echarts": "^5.4.1", "element-plus": "^2.2.30", "exceljs": "^4.4.0", "html-docx-js": "^0.3.1", "js-cookie": "^3.0.1", "jszip": "^2.6.1", "langdetect": "^0.2.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "mammoth": "^1.6.0", "moment": "^2.29.4", "node-fetch": "^3.3.2", "nprogress": "^0.2.0", "pizzip": "^3.1.6", "pnpm": "^9.7.1", "pptxgenjs": "^3.12.0", "qs": "^6.11.0", "screenfull": "^6.0.2", "sharp": "^0.33.2", "simple-keyboard": "^3.6.16", "spreadsheet-viewer": "^1.0.1", "swiper": "^9.3.1", "three": "^0.151.3", "uuid": "^9.0.0", "vue": "^3.2.47", "vue-cropper": "^0.6.4", "vue-i18n": "^9.2.2", "vue-json-excel3": "^1.0.19", "vue-pdf-embed": "^1.1.6", "vue-router": "^4.1.6", "vue3-cropper": "^0.4.0", "vue3-datepicker": "^0.4.0", "vue3-google-map": "^0.15.0", "vue3-pdf-app": "^1.0.3", "vuex": "^4.1.0", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "^0.8.2", "@types/node": "^18.13.0", "@vitejs/plugin-legacy": "^4.0.1", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-prettier": "^7.0.0", "autoprefixer": "^10.4.13", "babel-eslint": "^10.1.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^7.20.0", "lint-staged": "^11.2.6", "postcss": "^8.4.21", "prettier": "^2.8.4", "sass": "^1.58.0", "stylelint": "^13.13.1", "stylelint-config-prettier": "^8.0.2", "stylelint-config-recess-order": "^2.6.0", "stylelint-config-standard": "^22.0.0", "svg-sprite-loader": "^6.0.11", "terser": "^5.16.6", "unplugin-auto-import": "^0.14.2", "unplugin-element-plus": "^0.6.0", "unplugin-icons": "^0.15.2", "unplugin-vue-components": "^0.23.0", "vite": "^4.1.1", "vite-plugin-babel-import": "^2.0.5", "vite-plugin-style-import": "^2.0.0"}, "engines": {"node": "^14.18 || >=16"}, "vite": {"optimizeDeps": {"include": ["@element-plus/icons-vue", "@icon-park/vue-next", "@icon-park/vue-next/es/all", "@soerenmartius/vue3-clipboard", "axios", "echarts/charts", "echarts/components", "echarts/core", "echarts/renderers", "element-plus", "js-cookie", "lodash/debounce", "nprogress", "qs", "screenfull", "vue", "vue-i18n", "vue-router", "vuex"]}}}