/*!
 * Spreadsheet Viewer
 * 
 * Version: 1.0.1
 * Code version: 06577ad
 * Build date: Thu, October 21, 2021, 12:29 PM GMT+2
 */
(window.webpackJsonp=window.webpackJsonp||[]).push([[3],{311:function(e,t,r){"use strict";r.r(t),r.d(t,"getChartRenderer",(function(){return q}));var n=r(64),a=r(1),o=r.n(a),i=r(203),l=r.n(i),u=r(192);function c(e){return function(e){if(Array.isArray(e))return p(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||f(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var r=[],n=!0,a=!1,o=void 0;try{for(var i,l=e[Symbol.iterator]();!(n=(i=l.next()).done)&&(r.push(i.value),!t||r.length!==t);n=!0);}catch(e){a=!0,o=e}finally{try{n||null==l.return||l.return()}finally{if(a)throw o}}return r}(e,t)||f(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e,t){if(e){if("string"==typeof e)return p(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?p(e,t):void 0}}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var d,v={appear:{animation:"fadeIn",easing:"easeQuadIn",delay:0,duration:0}},m=function(e,t){var r,n=null===(r=null==e?void 0:e.label)||void 0===r?void 0:r.value;return n||"Series".concat(t+1)},y=function(e){var t,r={},n=function(e){var t=new Set,r=JSON.parse(JSON.stringify(e));return r.forEach((function(e,r){var n=m(e,r);t.has(n)&&(n=m(void 0,r)),t.add(n),e.label&&(e.label.value=n)})),r}(e),a={values:(null===(t=n[0].values.values)||void 0===t?void 0:t.slice())||[]};n.forEach((function(e,t){var n,o=m(e,t);e.categories||(e.categories=a),null===(n=e.categories.values)||void 0===n||n.forEach((function(t,n){r[t]=r[t]||{},r[t][o]=function(){var t,r=null===(t=e.values.values)||void 0===t?void 0:t[n];if(null==r)return 0;var a=Number(r);return Number.isNaN(a)?0:a}()}))}));var o=[];return{data:Object.entries(r).map((function(e){var t=s(e,2),r=t[0],n=t[1];return o=[].concat(c(o),c(Object.keys(n))),Object.assign(Object.assign({},n),{key:r})})),series:c(new Set(o))}},h={1:{back:{shape:"square"},front:{shape:"hollowSquare"}},2:{back:{shape:"diamond"},front:{shape:"hollowDiamond"}},3:{back:{shape:"triangle"},front:{shape:"hollowTriangle"}},4:{front:{shape:"cross"},back:{shape:"square"}},5:{front:{shape:"hexagon"},back:{shape:"square"}},6:{front:{shape:"hyphen"},back:{shape:"hyphen"}},7:{front:{shape:"hyphen"},back:{shape:"hyphen"}},8:{back:{shape:"circle"},front:{shape:"hollowCircle"}},9:{front:{shape:"plus"},back:{shape:"square"}}},b=function(e,t){var r;return null===(r=e.find((function(e){var r;return(null===(r=e.label)||void 0===r?void 0:r.value)===t})))||void 0===r?void 0:r.markerFormat};!function(e){e.MARKER_COMPONENT_FRONT="fNotShowBrd",e.MARKER_COMPONENT_BACK="fNotShowInt"}(d||(d={}));var g,E;!function(e){e.FIELD_FRONT="front",e.FIELD_BACK="back"}(g||(g={})),function(e){e.FIELD_FRONT="rgbFore",e.FIELD_BACK="rgbBack"}(E||(E={}));var k,O=function(e){var t=e.chartSeries,r=e.componentFlag,n=e.adjust,a=e.imkComponent,i=e.colorField;return function(e,t){try{return e.find((function(e){try{return!(e.markerFormat.imk<=0)&&!e.markerFormat[t]}catch(e){return!1}}))}catch(e){return!1}}(t,r)?o.a.createElement(u.Geom,{type:"point",position:"key*value",adjust:n,color:["type",function(e){var n=b(t,e);return"number"==typeof(null==n?void 0:n.imk)&&n.imk<=0||(null==n?void 0:n[r])?"transparent":(null==n?void 0:n[i])||"transparent"}],shape:["type",function(e){var r,n=b(t,e);return void 0===n?"":(r=n.imk)>0&&r<10?h[n.imk][a].shape:""}],size:["type",function(e){var r=b(t,e);return(null==r?void 0:r.size)||0}],style:{fill:"transparent"},animate:v}):null},w=function(e){var t=e.chartSeries,r=e.chartOptions,n=e.name,a=e.width,i=e.height,c=y(t),s=(new l.a.View).source(c.data);s.transform({type:"fold",fields:c.series,key:"type",value:"value"}),r.flags.f100&&s.transform({type:"percent",dimension:"type",field:"value",groupBy:["key"],as:"value"});var f=r.flags.fStacked?"stack":"",p=t.map((function(e){var t,r;return null===(r=null===(t=e.format)||void 0===t?void 0:t.line)||void 0===r?void 0:r.rgb})).filter((function(e){return"string"==typeof e})),m=p.length>0?["type",p]:["type",void 0];return o.a.createElement(u.Chart,{height:i,width:a,data:s,padding:"auto",animate:!1},o.a.createElement("span",{className:"sv-chart-title"},n),o.a.createElement(u.Tooltip,{crosshairs:!0,"g2-tooltip":{display:"inline-flex"}}),o.a.createElement(u.Axis,null),o.a.createElement(u.Legend,null),o.a.createElement(u.Geom,{type:"line",adjust:f,position:"key*value",color:m,shape:"smooth",size:2,animate:v}),o.a.createElement(O,{chartSeries:t,componentFlag:d.MARKER_COMPONENT_FRONT,imkComponent:g.FIELD_FRONT,adjust:f,colorField:E.FIELD_FRONT}),o.a.createElement(O,{chartSeries:t,componentFlag:d.MARKER_COMPONENT_BACK,imkComponent:g.FIELD_BACK,adjust:f,colorField:E.FIELD_BACK}))},S=function(e){var t=e.chartSeries,r=e.chartOptions,n=e.name,a=e.width,i=e.height,c=y(t),s=(new l.a.View).source(c.data);s.transform({type:"fold",fields:c.series,key:"type",value:"value"}),r.flags.f100&&s.transform({type:"percent",dimension:"type",field:"value",groupBy:["key"],as:"value"});var f=r.flags.fStacked?"stack":"",p=t.map((function(e){var t,r;return null===(r=null===(t=e.format)||void 0===t?void 0:t.area)||void 0===r?void 0:r.rgbFore})).filter((function(e){return"string"==typeof e})),d=p.length>0?["type",p]:["type",void 0];return o.a.createElement(u.Chart,{height:i,width:a,data:s,padding:"auto"},o.a.createElement("span",{className:"sv-chart-title"},n),o.a.createElement(u.Tooltip,{crosshairs:!0}),o.a.createElement(u.Axis,null),o.a.createElement(u.Legend,null),o.a.createElement(u.Geom,{type:"area",adjust:f,position:"key*value",color:d,shape:"smooth",size:2,animate:v}))},A=function(e){var t=e.chartSeries,r=e.chartOptions,n=e.name,a=e.width,i=e.height,c=y(t),s=(new l.a.View).source(c.data);s.transform({type:"fold",fields:c.series,key:"type",value:"value"}),r.flags.f100&&s.transform({type:"percent",dimension:"type",field:"value",groupBy:["key"],as:"value"});var f=r.flags.fStacked?"intervalStack":"interval",p=r.flags.fTranspose?o.a.createElement(u.Coord,{transpose:!0,scale:[1,-1]}):o.a.createElement(u.Coord,{scale:[1,1]}),d=t.map((function(e){var t,r;return null===(r=null===(t=e.format)||void 0===t?void 0:t.area)||void 0===r?void 0:r.rgbFore})).filter((function(e){return"string"==typeof e})),m=d.length>0?["type",d]:["type",void 0];return o.a.createElement(u.Chart,{height:i,width:a,data:s,padding:"auto"},o.a.createElement("span",{className:"sv-chart-title"},n),o.a.createElement(u.Tooltip,{crosshairs:!0}),p,o.a.createElement(u.Axis,null),o.a.createElement(u.Legend,null),o.a.createElement(u.Geom,{type:f,position:"key*value",color:m,shape:"smooth",adjust:[{type:"dodge",marginRatio:4/32}],animate:v}))},j=function(e){var t=e.chartSeries,r=e.name,n=e.width,a=e.height,i=y(t),c=(new l.a.View).source(i.data);c.transform({type:"fold",fields:i.series,key:"type",value:"value"}),c.transform({type:"percent",dimension:"type",field:"value",groupBy:["key"],as:"value"});var s="type",f=Object.keys(i.data[0])[0];return c.transform({type:"filter",callback:function(e){return e.type===f}}),s="key",o.a.createElement(u.Chart,{height:a,width:n,data:c,padding:"auto"},o.a.createElement("span",{className:"sv-chart-title"},r),o.a.createElement(u.Tooltip,{crosshairs:!0}),o.a.createElement(u.Axis,null),o.a.createElement(u.Legend,null),o.a.createElement(u.Coord,{type:"theta",radius:.65}),o.a.createElement(u.Geom,{type:"intervalStack",position:"value",color:s,shape:"smooth",tooltip:["key*value*type",function(e,t,r){return{name:"".concat(r,":").concat(e),value:"".concat((1e4*t|0)/100,"%")}}],animate:v}))},N={Line:w,Bar:A,Bar3D:A,Line3D:w,Area:S,Area3D:S,Stock:void 0,Surface3D:void 0,Radar:void 0,Pie:j,OfPie:void 0,Pie3D:j,Doughnut:void 0,Scatter:void 0,Bubble:void 0,treemap:void 0,sunburst:void 0,regionMap:void 0,clusteredColumn:void 0,boxWhisker:void 0,waterfall:void 0,funnel:void 0};!function(e){e.RANGE="PtgArea3d",e.POINT="PtgRef3d"}(k||(k={}));var C=r(24),F=r(255),R=r(221);function T(e){return(T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function I(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function D(e,t){return(D=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function P(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=B(e);if(t){var a=B(this).constructor;r=Reflect.construct(n,arguments,a)}else r=n.apply(this,arguments);return M(this,r)}}function M(e,t){return!t||"object"!==T(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function B(e){return(B=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function L(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var r=[],n=!0,a=!1,o=void 0;try{for(var i,l=e[Symbol.iterator]();!(n=(i=l.next()).done)&&(r.push(i.value),!t||r.length!==t);n=!0);}catch(e){a=!0,o=e}finally{try{n||null==l.return||l.return()}finally{if(a)throw o}}return r}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return x(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return x(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function x(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var K=function(e,t){var r=Object(C.c)(e.c),n=e.r+1,a=t["".concat(r).concat(n)];return a?a.v:null},G=function(e,t){return e&&Array.isArray(e)?e.map((function(e){if(e[0]===k.POINT){var r=e[1][2];return K(r,t)}if(e[0]===k.RANGE)return function(e,t){for(var r=[],n=Math.min(e.s.r,e.e.r),a=Math.max(e.s.r,e.e.r),o=Math.min(e.s.c,e.e.c),i=Math.min(e.s.c,e.e.c),l=n;l<=a;l++)for(var u=o;u<=i;u++){var c=K({r:l,c:u},t);r.push(c)}return r}(e[1][2],t)})).reduce((function(e,t){return e.concat(t)}),[]):[]},J=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&D(e,t)}(o,e);var t,r,n,a=P(o);function o(){return _(this,o),a.apply(this,arguments)}return t=o,(r=[{key:"componentDidMount",value:function(){setTimeout((function(){Object(R.a)()}),0)}},{key:"render",value:function(){return this.props.children}}])&&I(t.prototype,r),n&&I(t,n),o}(a.Component),q=function(e,t){var r=e.chartSeries,o=e.chartOptions,i=e.chartType,l=e.chartTitle;l||(l="");var u=function(e,t){return e&&Array.isArray(e)?e.map((function(e){var r,n,a,o;e=JSON.parse(JSON.stringify(e));try{!e.values.values&&e.values.formula&&(e.values.values=G(e.values.formula,t),delete e.values.formula)}catch(e){}try{if(!(null===(r=e.label)||void 0===r?void 0:r.value)&&(null===(n=e.label)||void 0===n?void 0:n.formula)){var i=L(G(e.label.formula,t),1)[0];e.label.value=i,delete e.label.formula}}catch(e){}try{!(null===(a=e.categories)||void 0===a?void 0:a.values)&&(null===(o=e.categories)||void 0===o?void 0:o.formula)&&(e.categories.values=G(e.categories.formula,t),delete e.categories.formula)}catch(e){}return e})):e}(r,t);return function(e,t,r){e.classList.add("sv-handsontable-chart");var c=N[i]||F.a;"function"==typeof c?Object(n.render)(a.createElement(J,null,a.createElement(c,{width:t,height:r,chartSeries:u,chartOptions:o,name:l||""})),e):Object(R.a)()}}}}]);