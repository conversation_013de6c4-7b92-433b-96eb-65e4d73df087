/*!
 * Spreadsheet Viewer
 * 
 * Version: 1.0.1
 * Code version: 06577ad
 * Build date: Thu, October 21, 2021, 12:29 PM GMT+2
 */
/**
 * Fix for bootstrap styles
 */
.handsontable .table th,
.handsontable .table td {
  border-top: none;
}
.handsontable tr {
  background: #fff;
}
.handsontable td {
  background-color: inherit;
}
.handsontable .table caption + thead tr:first-child th,
.handsontable .table caption + thead tr:first-child td,
.handsontable .table colgroup + thead tr:first-child th,
.handsontable .table colgroup + thead tr:first-child td,
.handsontable .table thead:first-child tr:first-child th,
.handsontable .table thead:first-child tr:first-child td {
  border-top: 1px solid #CCCCCC;
}
/* table-bordered */
.handsontable .table-bordered {
  border: 0;
  border-collapse: separate;
}
.handsontable .table-bordered th,
.handsontable .table-bordered td {
  border-left: none;
}
.handsontable .table-bordered th:first-child,
.handsontable .table-bordered td:first-child {
  border-left: 1px solid #CCCCCC;
}
.handsontable .table > tbody > tr > td,
.handsontable .table > tbody > tr > th,
.handsontable .table > tfoot > tr > td,
.handsontable .table > tfoot > tr > th,
.handsontable .table > thead > tr > td,
.handsontable .table > thead > tr > th {
  line-height: 21px;
  padding: 0 4px;
}
.col-lg-1.handsontable,
.col-lg-10.handsontable,
.col-lg-11.handsontable,
.col-lg-12.handsontable,
.col-lg-2.handsontable,
.col-lg-3.handsontable,
.col-lg-4.handsontable,
.col-lg-5.handsontable,
.col-lg-6.handsontable,
.col-lg-7.handsontable,
.col-lg-8.handsontable,
.col-lg-9.handsontable,
.col-md-1.handsontable,
.col-md-10.handsontable,
.col-md-11.handsontable,
.col-md-12.handsontable,
.col-md-2.handsontable,
.col-md-3.handsontable,
.col-md-4.handsontable,
.col-md-5.handsontable,
.col-md-6.handsontable,
.col-md-7.handsontable,
.col-md-8.handsontable,
.col-md-9.handsontable .col-sm-1.handsontable,
.col-sm-10.handsontable,
.col-sm-11.handsontable,
.col-sm-12.handsontable,
.col-sm-2.handsontable,
.col-sm-3.handsontable,
.col-sm-4.handsontable,
.col-sm-5.handsontable,
.col-sm-6.handsontable,
.col-sm-7.handsontable,
.col-sm-8.handsontable,
.col-sm-9.handsontable .col-xs-1.handsontable,
.col-xs-10.handsontable,
.col-xs-11.handsontable,
.col-xs-12.handsontable,
.col-xs-2.handsontable,
.col-xs-3.handsontable,
.col-xs-4.handsontable,
.col-xs-5.handsontable,
.col-xs-6.handsontable,
.col-xs-7.handsontable,
.col-xs-8.handsontable,
.col-xs-9.handsontable {
  padding-left: 0;
  padding-right: 0;
}
.handsontable .table-striped > tbody > tr:nth-of-type(even) {
  background-color: #FFF;
}

.handsontable {
  position: relative;
}
.handsontable .hide {
  display: none;
}
.handsontable .relative {
  position: relative;
}
.handsontable .wtHider {
  width: 0;
}
.handsontable .wtSpreader {
  position: relative;
  width: 0;
  /*must be 0, otherwise blank space appears in scroll demo after scrolling max to the right */
  height: auto;
}
.handsontable table,
.handsontable tbody,
.handsontable thead,
.handsontable td,
.handsontable th,
.handsontable input,
.handsontable textarea,
.handsontable div {
  box-sizing: content-box;
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
}
.handsontable input,
.handsontable textarea {
  min-height: initial;
}
.handsontable table.htCore {
  border-collapse: separate;
  /* it must be separate, otherwise there are offset miscalculations in WebKit: http://stackoverflow.com/questions/2655987/border-collapse-differences-in-ff-and-webkit */
  /* this actually only changes appearance of user selection - does not make text unselectable */
  /* -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
    -ms-user-select: none;
    user-select: none; */
  /* no browser supports unprefixed version */
  border-spacing: 0;
  margin: 0;
  border-width: 0;
  table-layout: fixed;
  width: 0;
  outline-width: 0;
  cursor: default;
  /* reset bootstrap table style. for more info see: https://github.com/handsontable/handsontable/issues/224 */
  max-width: none;
  max-height: none;
}
.handsontable th,
.handsontable td {
  border-style: solid;
  border-color: #ccc;
  border-top-width: 0;
  border-left-width: 0;
  border-right-width: 1px;
  border-bottom-width: 1px;
  height: 22px;
  empty-cells: show;
  line-height: 21px;
  padding: 0 4px 0 4px;
  /* top, bottom padding different than 0 is handled poorly by FF with HTML5 doctype */
  background-color: #fff;
  vertical-align: top;
  overflow: hidden;
  outline-width: 0;
  white-space: pre-line;
  /* preserve new line character in cell */
}
.handsontable th:last-child {
  /*Foundation framework fix*/
  border-right-width: 1px;
  border-bottom-width: 1px;
}
.handsontable th:first-child,
.handsontable td:first-child {
  border-left-width: 1px;
}
.handsontable thead tr:first-child th,
.handsontable thead tr:first-child td,
.handsontable tbody.afterEmptyThead tr:first-child th,
.handsontable tbody.afterEmptyThead tr:first-child td {
  border-top-width: 1px;
}
.handsontable th {
  background-color: #f0f0f0;
  color: #222;
  text-align: center;
  font-weight: normal;
  white-space: nowrap;
}
.handsontable thead th {
  padding: 0;
}
.handsontable th.active {
  background-color: #ccc;
}
.handsontable thead th .relative {
  padding: 2px 4px;
}
.handsontable span.colHeader {
  display: inline-block;
  line-height: 1.082;
  /* was 1.1 before, but that resulted in different line-height in FF and Ch on Windows */
}
/* Selection */
.handsontable .wtBorder {
  position: absolute;
  font-size: 0;
}
/* A layer order of the selection types */
.handsontable .wtBorder.current.corner {
  z-index: 10;
}
.handsontable .wtBorder.area.corner {
  z-index: 8;
}
.handsontable .wtBorder.corner {
  font-size: 0;
  cursor: crosshair;
}
.ht_clone_master {
  z-index: 100;
}
.ht_clone_right {
  z-index: 110;
}
.ht_clone_left {
  z-index: 120;
}
.ht_clone_bottom {
  z-index: 130;
}
.ht_clone_bottom_right_corner {
  z-index: 140;
}
.ht_clone_bottom_left_corner {
  z-index: 150;
}
.ht_clone_top {
  z-index: 160;
}
.ht_clone_top_right_corner {
  z-index: 170;
}
.ht_clone_top_left_corner {
  z-index: 180;
}
/*
  Cell borders
  */
.handsontable tbody tr th:nth-last-child(2) {
  border-right-width: 1px;
}
.ht_clone_top_left_corner thead tr th:nth-last-child(2) {
  border-right-width: 1px;
}
.handsontable col.hidden {
  width: 0 !important;
}
.handsontable tr.hidden,
.handsontable tr.hidden td,
.handsontable tr.hidden th {
  display: none;
}
.ht_master,
.ht_clone_left,
.ht_clone_top,
.ht_clone_bottom {
  overflow: hidden;
}
.ht_master .wtHolder {
  overflow: auto;
}
.handsontable .ht_master thead,
.handsontable .ht_master tr th,
.handsontable .ht_clone_left thead {
  visibility: hidden;
}
.ht_clone_top .wtHolder,
.ht_clone_left .wtHolder,
.ht_clone_bottom .wtHolder {
  overflow: hidden;
}
.handsontable svg.wtBorders:not(:root) {
  overflow: hidden;
  /* fix for IE, where SVG content is not cropped to the viewbox. Otherwise IE shows artifacts in SVG borders when scrolling with fixedColumnsLeft */
}
@media print {
  .handsontable svg.wtBorders:not(:root) {
    display: none;
  }
}
/**
 * Frozen line, implemented as a special color of the gridline
 */
.ht_clone_top_left_corner thead th:last-child:not(.wtTableCornerCell),
.ht_clone_top_left_corner td:last-child,
.ht_clone_bottom_left_corner td:last-child,
.ht_clone_left td:last-child {
  border-right: 1px solid #5d6365;
}
.ht_clone_top tbody tr:last-child td,
.ht_clone_top_left_corner tbody tr:last-child th,
.ht_clone_top_left_corner tbody tr:last-child td {
  border-bottom: 1px solid #5d6365;
}
.ht_clone_bottom_left_corner tbody.afterEmptyThead tr:first-child th,
.ht_clone_bottom_left_corner tbody.afterEmptyThead tr:first-child td,
.ht_clone_bottom tbody.afterEmptyThead tr:first-child td {
  border-top: 1px solid #5d6365;
}

.handsontable.htAutoSize {
  visibility: hidden;
  left: -99000px;
  position: absolute;
  top: -99000px;
}
.handsontable td.htInvalid {
  background-color: #ff4c42 !important;
  /*gives priority over td.area selection background*/
}
.handsontable td.htNoWrap {
  white-space: nowrap;
}
#hot-display-license-info {
  font-size: 10px;
  color: #323232;
  padding: 5px 0 3px 0;
  font-family: Helvetica, Arial, sans-serif;
  text-align: left;
}
#hot-display-license-info a {
  font-size: 10px;
}
/* plugins */
/* row + column resizer*/
.handsontable .manualColumnResizer {
  position: absolute;
  top: 0;
  cursor: col-resize;
  z-index: 210;
  width: 5px;
  height: 25px;
}
.handsontable .manualRowResizer {
  position: absolute;
  left: 0;
  cursor: row-resize;
  z-index: 210;
  height: 5px;
  width: 50px;
}
.handsontable .manualColumnResizer:hover,
.handsontable .manualColumnResizer.active,
.handsontable .manualRowResizer:hover,
.handsontable .manualRowResizer.active {
  background-color: #34a9db;
}
.handsontable .manualColumnResizerGuide {
  position: absolute;
  right: 0;
  top: 0;
  background-color: #34a9db;
  display: none;
  width: 0;
  border-right: 1px dashed #777;
  margin-left: 5px;
}
.handsontable .manualRowResizerGuide {
  position: absolute;
  left: 0;
  bottom: 0;
  background-color: #34a9db;
  display: none;
  height: 0;
  border-bottom: 1px dashed #777;
  margin-top: 5px;
}
.handsontable .manualColumnResizerGuide.active,
.handsontable .manualRowResizerGuide.active {
  display: block;
  z-index: 209;
}
.handsontable .columnSorting {
  position: relative;
}
.handsontable .columnSorting.sortAction:hover {
  text-decoration: underline;
  cursor: pointer;
}
/* Arrow position */
.handsontable span.colHeader.columnSorting::before {
  /* Centering start */
  top: 50%;
  margin-top: -6px;
  /* One extra pixel for purpose of proper positioning of sorting arrow, when `font-size` set to default */
  /* Centering end */
  padding-left: 8px;
  /* For purpose of continuous mouse over experience, when moving between the `span` and the `::before` elements */
  position: absolute;
  right: -9px;
  content: '';
  height: 10px;
  width: 5px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position-x: right;
}
.handsontable span.colHeader.columnSorting.ascending::before {
  /* arrow up; 20 x 40 px, scaled to 5 x 10 px; base64 size: 0.3kB */
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAoCAMAAADJ7yrpAAAAKlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKE86IAAAADXRSTlMABBEmRGprlJW72e77tTkTKwAAAFNJREFUeAHtzjkSgCAUBNHPgsoy97+ulGXRqJE5L+xkxoYt2UdsLb5bqFINz+aLuuLn5rIu2RkO3fZpWENimNgiw6iBYRTPMLJjGFxQZ1hxxb/xBI1qC8k39CdKAAAAAElFTkSuQmCC");
}
.handsontable span.colHeader.columnSorting.descending::before {
  /* arrow down; 20 x 40 px, scaled to 5 x 10 px; base64 size: 0.3kB */
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAoCAMAAADJ7yrpAAAAKlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKE86IAAAADXRSTlMABBEmRGprlJW72e77tTkTKwAAAFJJREFUeAHtzjkSgCAQRNFmQYUZ7n9dKUvru0TmvPAn3br0QfgdZ5xx6x+rQn23GqTYnq1FDcnuzZIO2WmedVqIRVxgGKEyjNgYRjKGkZ1hFIZ3I70LyM0VtU8AAAAASUVORK5CYII=");
}
.htGhostTable .htCore span.colHeader.columnSorting:not(.indicatorDisabled)::after {
  content: '*';
  display: inline-block;
  position: relative;
  /* The multi-line header and header with longer text need more padding to not hide arrow,
  we make header wider in `GhostTable` to make some space for arrow which is positioned absolutely in the main table */
  padding-right: 20px;
}
.handsontable td.area,
.handsontable td.area-1,
.handsontable td.area-2,
.handsontable td.area-3,
.handsontable td.area-4,
.handsontable td.area-5,
.handsontable td.area-6,
.handsontable td.area-7 {
  position: relative;
}
.handsontable td.area:before,
.handsontable td.area-1:before,
.handsontable td.area-2:before,
.handsontable td.area-3:before,
.handsontable td.area-4:before,
.handsontable td.area-5:before,
.handsontable td.area-6:before,
.handsontable td.area-7:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  bottom: -100% \9;
  /* Fix for IE9 to spread the ":before" pseudo element to 100% height of the parent element */
  background: #005eff;
}
/* Fix for IE10 and IE11 to spread the ":before" pseudo element to 100% height of the parent element */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  .handsontable td.area:before,
  .handsontable td.area-1:before,
  .handsontable td.area-2:before,
  .handsontable td.area-3:before,
  .handsontable td.area-4:before,
  .handsontable td.area-5:before,
  .handsontable td.area-6:before,
  .handsontable td.area-7:before {
    bottom: -100%;
  }
}
.handsontable td.area:before {
  opacity: 0.1;
}
.handsontable td.area-1:before {
  opacity: 0.2;
}
.handsontable td.area-2:before {
  opacity: 0.27;
}
.handsontable td.area-3:before {
  opacity: 0.35;
}
.handsontable td.area-4:before {
  opacity: 0.41;
}
.handsontable td.area-5:before {
  opacity: 0.47;
}
.handsontable td.area-6:before {
  opacity: 0.54;
}
.handsontable td.area-7:before {
  opacity: 0.58;
}
.handsontable tbody th.ht__highlight,
.handsontable thead th.ht__highlight {
  background-color: #dcdcdc;
}
.handsontable tbody th.ht__active_highlight,
.handsontable thead th.ht__active_highlight {
  background-color: #8eb0e7;
  color: #000;
}
.handsontableInput {
  border: none;
  outline-width: 0;
  margin: 0;
  padding: 1px 5px 0 5px;
  font-family: inherit;
  line-height: 21px;
  font-size: inherit;
  box-shadow: 0 0 0 2px #5292F7 inset;
  resize: none;
  /*below are needed to overwrite stuff added by jQuery UI Bootstrap theme*/
  display: block;
  color: #000;
  border-radius: 0;
  background-color: #FFF;
  /*overwrite styles potentionally made by a framework*/
}
.handsontableInputHolder {
  position: absolute;
  top: 0;
  left: 0;
}
.htSelectEditor {
  -webkit-appearance: menulist-button !important;
  position: absolute;
  width: auto;
}
/*
TextRenderer readOnly cell
*/
.handsontable .htDimmed {
  color: #777;
}
.handsontable .htSubmenu {
  position: relative;
}
.handsontable .htSubmenu :after {
  content: '\25B6';
  color: #777;
  position: absolute;
  right: 5px;
  font-size: 9px;
}
/*
TextRenderer horizontal alignment
*/
.handsontable .htLeft {
  text-align: left;
}
.handsontable .htCenter {
  text-align: center;
}
.handsontable .htRight {
  text-align: right;
}
.handsontable .htJustify {
  text-align: justify;
}
/*
TextRenderer vertical alignment
*/
.handsontable .htTop {
  vertical-align: top;
}
.handsontable .htMiddle {
  vertical-align: middle;
}
.handsontable .htBottom {
  vertical-align: bottom;
}
/*
TextRenderer placeholder value
*/
.handsontable .htPlaceholder {
  color: #999;
}
/*
AutocompleteRenderer down arrow
*/
.handsontable .htAutocompleteArrow {
  float: right;
  font-size: 10px;
  color: #EEE;
  cursor: default;
  width: 16px;
  text-align: center;
}
.handsontable td .htAutocompleteArrow:hover {
  color: #777;
}
.handsontable td.area .htAutocompleteArrow {
  color: #d3d3d3;
}
/*
CheckboxRenderer
*/
.handsontable .htCheckboxRendererInput {
  display: inline-block;
}
.handsontable .htCheckboxRendererInput.noValue {
  opacity: 0.5;
}
.handsontable .htCheckboxRendererLabel {
  font-size: inherit;
  vertical-align: middle;
  cursor: pointer;
  display: inline-block;
  width: 100%;
}
/**
 * Handsontable listbox theme
 */
.handsontable.listbox {
  margin: 0;
}
.handsontable.listbox .ht_master table {
  border: 1px solid #ccc;
  border-collapse: separate;
  background: white;
}
.handsontable.listbox th,
.handsontable.listbox tr:first-child th,
.handsontable.listbox tr:last-child th,
.handsontable.listbox tr:first-child td,
.handsontable.listbox td {
  border-color: transparent;
}
.handsontable.listbox th,
.handsontable.listbox td {
  white-space: nowrap;
  text-overflow: ellipsis;
}
.handsontable.listbox td.htDimmed {
  cursor: default;
  color: inherit;
  font-style: inherit;
}
.handsontable.listbox tr td.current,
.handsontable.listbox tr:hover td {
  background: #eee;
}
.ht_editor_hidden {
  z-index: -1;
}
.ht_editor_visible {
  z-index: 200;
}
.handsontable td.htSearchResult {
  background: #fcedd9;
  color: #583707;
}
.collapsibleIndicator {
  position: absolute;
  top: 50%;
  transform: translate(0%, -50%);
  right: 5px;
  border: 1px solid #A6A6A6;
  line-height: 10px;
  color: #222;
  border-radius: 10px;
  font-size: 10px;
  width: 10px;
  height: 10px;
  cursor: pointer;
  -webkit-box-shadow: 0 0 0 6px #eeeeee;
  -moz-box-shadow: 0 0 0 6px #eeeeee;
  box-shadow: 0 0 0 6px #eeeeee;
  background: #eee;
}

/*

 Handsontable Mobile Text Editor stylesheet

 */
.handsontable.mobile,
.handsontable.mobile .wtHolder {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-overflow-scrolling: touch;
}
.htMobileEditorContainer {
  display: none;
  position: absolute;
  top: 0;
  width: 70%;
  height: 54pt;
  background: #f8f8f8;
  border-radius: 20px;
  border: 1px solid #ebebeb;
  z-index: 999;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -webkit-text-size-adjust: none;
}
.topLeftSelectionHandle:not(.ht_master .topLeftSelectionHandle),
.topLeftSelectionHandle-HitArea:not(.ht_master .topLeftSelectionHandle-HitArea) {
  z-index: 9999;
}
/* Initial left/top coordinates - overwritten when actual position is set */
.topLeftSelectionHandle,
.topLeftSelectionHandle-HitArea,
.bottomRightSelectionHandle,
.bottomRightSelectionHandle-HitArea {
  left: -10000px;
  top: -10000px;
}
.htMobileEditorContainer.active {
  display: block;
}
.htMobileEditorContainer .inputs {
  position: absolute;
  right: 210pt;
  bottom: 10pt;
  top: 10pt;
  left: 14px;
  height: 34pt;
}
.htMobileEditorContainer .inputs textarea {
  font-size: 13pt;
  border: 1px solid #a1a1a1;
  -webkit-appearance: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  position: absolute;
  left: 14px;
  right: 14px;
  top: 0;
  bottom: 0;
  padding: 7pt;
}
.htMobileEditorContainer .cellPointer {
  position: absolute;
  top: -13pt;
  height: 0;
  width: 0;
  left: 30px;
  border-left: 13pt solid transparent;
  border-right: 13pt solid transparent;
  border-bottom: 13pt solid #ebebeb;
}
.htMobileEditorContainer .cellPointer.hidden {
  display: none;
}
.htMobileEditorContainer .cellPointer:before {
  content: '';
  display: block;
  position: absolute;
  top: 2px;
  height: 0;
  width: 0;
  left: -13pt;
  border-left: 13pt solid transparent;
  border-right: 13pt solid transparent;
  border-bottom: 13pt solid #f8f8f8;
}
.htMobileEditorContainer .moveHandle {
  position: absolute;
  top: 10pt;
  left: 5px;
  width: 30px;
  bottom: 0px;
  cursor: move;
  z-index: 9999;
}
.htMobileEditorContainer .moveHandle:after {
  content: "..\a..\a..\a..";
  white-space: pre;
  line-height: 10px;
  font-size: 20pt;
  display: inline-block;
  margin-top: -8px;
  color: #ebebeb;
}
.htMobileEditorContainer .positionControls {
  width: 205pt;
  position: absolute;
  right: 5pt;
  top: 0;
  bottom: 0;
}
.htMobileEditorContainer .positionControls > div {
  width: 50pt;
  height: 100%;
  float: left;
}
.htMobileEditorContainer .positionControls > div:after {
  content: " ";
  display: block;
  width: 15pt;
  height: 15pt;
  text-align: center;
  line-height: 50pt;
}
.htMobileEditorContainer .leftButton:after,
.htMobileEditorContainer .rightButton:after,
.htMobileEditorContainer .upButton:after,
.htMobileEditorContainer .downButton:after {
  transform-origin: 5pt 5pt;
  -webkit-transform-origin: 5pt 5pt;
  margin: 21pt 0 0 21pt;
}
.htMobileEditorContainer .leftButton:after {
  border-top: 2px solid #288ffe;
  border-left: 2px solid #288ffe;
  -webkit-transform: rotate(-45deg);
  /*margin-top: 17pt;*/
  /*margin-left: 20pt;*/
}
.htMobileEditorContainer .leftButton:active:after {
  border-color: #cfcfcf;
}
.htMobileEditorContainer .rightButton:after {
  border-top: 2px solid #288ffe;
  border-left: 2px solid #288ffe;
  -webkit-transform: rotate(135deg);
  /*margin-top: 17pt;*/
  /*margin-left: 10pt;*/
}
.htMobileEditorContainer .rightButton:active:after {
  border-color: #cfcfcf;
}
.htMobileEditorContainer .upButton:after {
  /*border-top: 2px solid #cfcfcf;*/
  border-top: 2px solid #288ffe;
  border-left: 2px solid #288ffe;
  -webkit-transform: rotate(45deg);
  /*margin-top: 22pt;*/
  /*margin-left: 15pt;*/
}
.htMobileEditorContainer .upButton:active:after {
  border-color: #cfcfcf;
}
.htMobileEditorContainer .downButton:after {
  border-top: 2px solid #288ffe;
  border-left: 2px solid #288ffe;
  -webkit-transform: rotate(225deg);
  /*margin-top: 15pt;*/
  /*margin-left: 15pt;*/
}
.htMobileEditorContainer .downButton:active:after {
  border-color: #cfcfcf;
}
.handsontable.hide-tween {
  -webkit-animation: opacity-hide 0.3s;
  animation: opacity-hide 0.3s;
  animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
}
.handsontable.show-tween {
  -webkit-animation: opacity-show 0.3s;
  animation: opacity-show 0.3s;
  animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
}

textarea.HandsontableCopyPaste {
  position: fixed !important;
  top: 0 !important;
  right: 100% !important;
  overflow: hidden;
  opacity: 0;
  outline: 0 none !important;
}

.handsontable tbody td[rowspan][class*="area"][class*="highlight"]:not([class*="fullySelectedMergedCell"]):before {
  opacity: 0;
}
.handsontable tbody td[rowspan][class*="area"][class*="highlight"][class*="fullySelectedMergedCell-multiple"]:before {
  opacity: 0.1;
}
.handsontable tbody td[rowspan][class*="area"][class*="highlight"][class*="fullySelectedMergedCell-0"]:before {
  opacity: 0.1;
}
.handsontable tbody td[rowspan][class*="area"][class*="highlight"][class*="fullySelectedMergedCell-1"]:before {
  opacity: 0.2;
}
.handsontable tbody td[rowspan][class*="area"][class*="highlight"][class*="fullySelectedMergedCell-2"]:before {
  opacity: 0.27;
}
.handsontable tbody td[rowspan][class*="area"][class*="highlight"][class*="fullySelectedMergedCell-3"]:before {
  opacity: 0.35;
}
.handsontable tbody td[rowspan][class*="area"][class*="highlight"][class*="fullySelectedMergedCell-4"]:before {
  opacity: 0.41;
}
.handsontable tbody td[rowspan][class*="area"][class*="highlight"][class*="fullySelectedMergedCell-5"]:before {
  opacity: 0.47;
}
.handsontable tbody td[rowspan][class*="area"][class*="highlight"][class*="fullySelectedMergedCell-6"]:before {
  opacity: 0.54;
}
.handsontable tbody td[rowspan][class*="area"][class*="highlight"][class*="fullySelectedMergedCell-7"]:before {
  opacity: 0.58;
}

.sv-handsontable-floating-box {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9;
  /* more than selection corner */
  box-sizing: border-box;
}
.sv-handsontable-chart {
  background: white;
  border: 1px solid #CCC;
}

.sv-chart-image-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
.sv-chart-image-placeholder {
  width: 80%;
  height: 80%;
}

.sv-handsontable-horizontal-overlay {
  position: absolute;
  top: 0;
  z-index: 1;
  pointer-events: none;
}
.ht_master.handsontable .sv-handsontable-hide-right-gridline {
  border-right-color: white;
}
#sv-handsontable-horizontal-overlay-measuring {
  position: fixed;
  top: 0;
  left: 0;
  visibility: hidden;
}
#sv-handsontable-horizontal-overlay-measuring * {
  position: fixed;
  top: 0;
  left: 0;
}

@font-face {
  font-family: 'Caladea';
  font-style: normal;
  font-weight: 400;
  src: local('Caladea Regular'), local('Caladea-Regular'), url(Caladea-Regular-subset.b8fa2f022c8198120a25f1a00250eedd.woff2) format('woff2'), url(Caladea-Regular-subset.2ea03cf32f394650b3a4b22b0f512c73.woff) format('woff');
}
@font-face {
  font-family: 'Caladea';
  font-style: italic;
  font-weight: 400;
  src: local('Caladea Italic'), local('Caladea-Italic'), url(Caladea-Italic-subset.2a25055dd651e03a9e1020daf9169c49.woff2) format('woff2'), url(Caladea-Italic-subset.7335dc2321af242ce39ab2e99b5e9892.woff) format('woff');
}
@font-face {
  font-family: 'Caladea';
  font-style: normal;
  font-weight: 700;
  src: local('Caladea Bold'), local('Caladea-Bold'), url(Caladea-Bold-subset.84961aa373fbd4871a3a0c972432275b.woff2) format('woff2'), url(Caladea-Bold-subset.adab7ec95d679b4e779368693a83dd02.woff) format('woff');
}
@font-face {
  font-family: 'Caladea';
  font-style: italic;
  font-weight: 700;
  src: local('Caladea Bold Italic'), local('Caladea-BoldItalic'), url(Caladea-BoldItalic-subset.f2e44e6c99685d054ad755220360b2a6.woff2) format('woff2'), url(Caladea-BoldItalic-subset.7ea380a78087dd49b7bd7eee1d4073bd.woff) format('woff');
}
@font-face {
  font-family: 'Carlito';
  font-style: normal;
  font-weight: 400;
  src: local('Carlito Regular'), local('Carlito-Regular'), url(Carlito-Regular-subset.ef50f2f80012bf1492e47e3ce758df45.woff2) format('woff2'), url(Carlito-Regular-subset.f06467bf3939b5a424f3019b55d45411.woff) format('woff');
}
@font-face {
  font-family: 'Carlito';
  font-style: italic;
  font-weight: 400;
  src: local('Carlito Italic'), local('Carlito-Italic'), url(Carlito-Italic-subset.7154f55b2c79b2e5ed26e28fb872fe8a.woff2) format('woff2'), url(Carlito-Italic-subset.cd4bf11a438280a757a01b7948af8afc.woff) format('woff');
}
@font-face {
  font-family: 'Carlito';
  font-style: normal;
  font-weight: 700;
  src: local('Carlito Bold'), local('Carlito-Bold'), url(Carlito-Bold-subset.572368f827a424e6c8d7f43e81bb1361.woff2) format('woff2'), url(Carlito-Bold-subset.048b275289e26bd989ac312fede43b86.woff) format('woff');
}
@font-face {
  font-family: 'Carlito';
  font-style: italic;
  font-weight: 700;
  src: local('Carlito Bold Italic'), local('Carlito-BoldItalic'), url(Carlito-BoldItalic-subset.7c2db429730f20c93e4fd15b31288246.woff2) format('woff2'), url(Carlito-BoldItalic-subset.f144b145c3613788cbf55e27944c75c0.woff) format('woff');
}
@font-face {
  font-family: 'Liberation Mono';
  font-style: normal;
  font-weight: 400;
  src: local('Liberation Mono Regular'), local('Liberation-Mono-Regular'), url(LiberationMono-Regular-subset.67afa57926e61e15acf10dcad129b46a.woff2) format('woff2'), url(LiberationMono-Regular-subset.e8de1bb58653fd42e733118707174b01.woff) format('woff');
}
@font-face {
  font-family: 'Liberation Mono';
  font-style: italic;
  font-weight: 400;
  src: local('Liberation Mono Italic'), local('Liberation-Mono-Italic'), url(LiberationMono-Italic-subset.4463d0fb0f9aa13f5fb41c776c3a7d96.woff2) format('woff2'), url(LiberationMono-Italic-subset.ea983aff360cda68333c4045f412526b.woff) format('woff');
}
@font-face {
  font-family: 'Liberation Mono';
  font-style: normal;
  font-weight: 700;
  src: local('Liberation Mono Bold'), local('Liberation-Mono-Bold'), url(LiberationMono-Bold-subset.e7c8101025b920835e93807198cff2df.woff2) format('woff2'), url(LiberationMono-Bold-subset.94990a04995c220b94252fb59cbbab69.woff) format('woff');
}
@font-face {
  font-family: 'Liberation Mono';
  font-style: italic;
  font-weight: 700;
  src: local('Liberation Mono Bold Italic'), local('Liberation-Mono-BoldItalic'), url(LiberationMono-BoldItalic-subset.ca99c5b8e809f3ef6235b6565421b095.woff2) format('woff2'), url(LiberationMono-BoldItalic-subset.70ac43319a05dfe2dcc8f24909719503.woff) format('woff');
}
@font-face {
  font-family: 'Liberation Sans';
  font-style: normal;
  font-weight: 400;
  src: local('Liberation Sans Regular'), local('Liberation-Sans-Regular'), url(LiberationSans-Regular-subset.b50f937c9612ac62dd2a1151106b5fd5.woff2) format('woff2'), url(LiberationSans-Regular-subset.2b97cff8063809d9fa0c0b7d2f053aa7.woff) format('woff');
}
@font-face {
  font-family: 'Liberation Sans';
  font-style: italic;
  font-weight: 400;
  src: local('Liberation Sans Italic'), local('Liberation-Sans-Italic'), url(LiberationSans-Italic-subset.4920e1ceee70bc1b7810c1e9f4847e1b.woff2) format('woff2'), url(LiberationSans-Italic-subset.bc0c34cd6f512b13f1fbed09229a8ecd.woff) format('woff');
}
@font-face {
  font-family: 'Liberation Sans';
  font-style: normal;
  font-weight: 700;
  src: local('Liberation Sans Bold'), local('Liberation-Sans-Bold'), url(LiberationSans-Bold-subset.6015dc726392088d2b14ee28785694ac.woff2) format('woff2'), url(LiberationSans-Bold-subset.38011fe183ab34156fa7d2143b681c16.woff) format('woff');
}
@font-face {
  font-family: 'Liberation Sans';
  font-style: italic;
  font-weight: 700;
  src: local('Liberation Sans Bold Italic'), local('Liberation-Sans-BoldItalic'), url(LiberationSans-BoldItalic-subset.8b790b2c8abb7f486f9c2687870b43e6.woff2) format('woff2'), url(LiberationSans-BoldItalic-subset.de355cd7c9f285f95d85aaac4460b6cd.woff) format('woff');
}
@font-face {
  font-family: 'Liberation Serif';
  font-style: normal;
  font-weight: 400;
  src: local('Liberation Serif Regular'), local('Liberation-Serif-Regular'), url(LiberationSerif-Regular-subset.0a94e526ebb3942676fc64e1583045a6.woff2) format('woff2'), url(LiberationSerif-Regular-subset.bd05e65dbbadf2e7c1cc93456c8eb16f.woff) format('woff');
}
@font-face {
  font-family: 'Liberation Serif';
  font-style: italic;
  font-weight: 400;
  src: local('Liberation Serif Italic'), local('Liberation-Serif-Italic'), url(LiberationSerif-Italic-subset.80de4ff1d8783ac8b45ff8f639ebb8f5.woff2) format('woff2'), url(LiberationSerif-Italic-subset.65e33b5ae00884a42336f47456bd0624.woff) format('woff');
}
@font-face {
  font-family: 'Liberation Serif';
  font-style: normal;
  font-weight: 700;
  src: local('Liberation Serif Bold'), local('Liberation-Serif-Bold'), url(LiberationSerif-Bold-subset.485f3cb25ddc6ccc73d72de591b80a23.woff2) format('woff2'), url(LiberationSerif-Bold-subset.fb61528c0bf02698f8f41b24a584099d.woff) format('woff');
}
@font-face {
  font-family: 'Liberation Serif';
  font-style: italic;
  font-weight: 700;
  src: local('Liberation Serif Bold Italic'), local('Liberation-Serif-BoldItalic'), url(LiberationSerif-BoldItalic-subset.b31761eee8afa617b040b6b78deadf46.woff2) format('woff2'), url(LiberationSerif-BoldItalic-subset.dee31b024313181cb9d21381b0f266d1.woff) format('woff');
}
/* 
below `height` and `line-height` changes are a workaround for rendering 
row heights that are smaller than 22px
@see https://github.com/handsontable/spreadsheet-viewer-dev/issues/335
@see https://github.com/handsontable/handsontable/issues/4021
*/
.handsontable tbody th,
.handsontable tbody td {
  height: auto;
  line-height: normal;
}
.handsontable tbody th {
  line-height: 1;
  vertical-align: middle;
}
.handsontable tbody th div.relative {
  padding-bottom: 1px;
}
.handsontable td {
  position: relative;
  overflow: visible;
  padding: 0;
}
.ht_clone_top_left_corner .wtHolder {
  overflow: hidden;
}
.handsontable.sv-hide-gridlines .handsontable td {
  border-right-color: transparent;
  border-bottom-color: transparent;
}
.sv-chart-title {
  font-size: 18px;
  color: #777;
  padding: 10px;
  position: absolute;
  width: 100%;
  text-align: center;
}
.handsontable .sv-cell-text-wrapper {
  white-space: pre;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  align-items: flex-end;
}
.handsontable .sv-cell-text-wrapper__currency {
  display: none;
}
.handsontable .sv-cell-text-wrapper--accounting {
  margin: 0 !important;
  justify-content: space-between !important;
}
.handsontable .sv-cell-text-wrapper--accounting .sv-cell-text-wrapper__currency {
  display: block;
  margin-left: 4px;
}
.handsontable .sv-cell-text-wrapper--accounting .sv-cell-text-wrapper__value {
  margin-right: 4px;
}

