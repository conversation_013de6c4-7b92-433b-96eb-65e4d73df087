/*!
 * Spreadsheet Viewer
 * 
 * Version: 1.0.1
 * Code version: 06577ad
 * Build date: Thu, October 21, 2021, 12:29 PM GMT+2
 */
(window.webpackJsonp=window.webpackJsonp||[]).push([[2],Array(32).concat([function(e,t,r){"use strict";r.r(t),r.d(t,"duckSchema",(function(){return n})),r.d(t,"inherit",(function(){return i})),r.d(t,"extend",(function(){return s})),r.d(t,"deepExtend",(function(){return a})),r.d(t,"deepClone",(function(){return l})),r.d(t,"clone",(function(){return c})),r.d(t,"mixin",(function(){return h})),r.d(t,"isObjectEqual",(function(){return d})),r.d(t,"isObject",(function(){return u})),r.d(t,"defineGetter",(function(){return g})),r.d(t,"objectEach",(function(){return f})),r.d(t,"getProperty",(function(){return m})),r.d(t,"setProperty",(function(){return p})),r.d(t,"deepObjectSize",(function(){return v})),r.d(t,"createObjectPropListener",(function(){return w})),r.d(t,"hasOwnProperty",(function(){return b}));var o=r(33);function n(e){var t;return Array.isArray(e)?t=[]:(t={},f(e,(e,r)=>{"__children"!==r&&(e&&"object"==typeof e&&!Array.isArray(e)?t[r]=n(e):Array.isArray(e)?e.length&&"object"==typeof e[0]&&!Array.isArray(e[0])?t[r]=[n(e[0])]:t[r]=[]:t[r]=null)})),t}function i(e,t){return t.prototype.constructor=t,e.prototype=new t,e.prototype.constructor=e,e}function s(e,t,r){var o=Array.isArray(r);return f(t,(t,n)=>{(!1===o||r.includes(n))&&(e[n]=t)}),e}function a(e,t){f(t,(r,o)=>{t[o]&&"object"==typeof t[o]?(e[o]||(Array.isArray(t[o])?e[o]=[]:"[object Date]"===Object.prototype.toString.call(t[o])?e[o]=t[o]:e[o]={}),a(e[o],t[o])):e[o]=t[o]})}function l(e){return"object"==typeof e?JSON.parse(JSON.stringify(e)):e}function c(e){var t={};return f(e,(e,r)=>{t[r]=e}),t}function h(e){e.MIXINS||(e.MIXINS=[]);for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return Object(o.arrayEach)(r,t=>{e.MIXINS.push(t.MIXIN_NAME),f(t,(t,r)=>{if(void 0!==e.prototype[r])throw new Error("Mixin conflict. Property '".concat(r,"' already exist and cannot be overwritten."));if("function"==typeof t)e.prototype[r]=t;else{Object.defineProperty(e.prototype,r,{get:(o=r,n=t,i="_".concat(o),function(){var e;return void 0===this[i]&&(this[i]=(e=n,(Array.isArray(e)||u(e))&&(e=l(e)),e)),this[i]}),set:function(e){var t="_".concat(e);return function(e){this[t]=e}}(r),configurable:!0})}var o,n,i})}),e}function d(e,t){return JSON.stringify(e)===JSON.stringify(t)}function u(e){return"[object Object]"===Object.prototype.toString.call(e)}function g(e,t,r,o){o.value=r,o.writable=!1!==o.writable,o.enumerable=!1!==o.enumerable,o.configurable=!1!==o.configurable,Object.defineProperty(e,t,o)}function f(e,t){for(var r in e)if((!e.hasOwnProperty||e.hasOwnProperty&&Object.prototype.hasOwnProperty.call(e,r))&&!1===t(e[r],r,e))break;return e}function m(e,t){var r=t.split("."),o=e;return f(r,e=>{if(void 0===(o=o[e]))return o=void 0,!1}),o}function p(e,t,r){var o=t.split("."),n=e;o.forEach((e,t)=>{t!==o.length-1?(b(n,e)||(n[e]={}),n=n[e]):n[e]=r})}function v(e){if(!u(e))return 0;return function e(t){var r=0;return u(t)?f(t,(t,o)=>{"__children"!==o&&(r+=e(t))}):r+=1,r}(e)}function w(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"value",r="_".concat(t),o={_touched:!1,[r]:e,isTouched(){return this._touched}};return Object.defineProperty(o,t,{get(){return this[r]},set(e){this._touched=!0,this[r]=e},enumerable:!0,configurable:!0}),o}function b(e,t){return Object.prototype.hasOwnProperty.call(e,t)}},function(e,t,r){"use strict";function o(e){for(var t=e.length,r=0;r<t;)e[r]=[e[r]],r+=1}function n(e,t){for(var r=t.length,o=0;o<r;)e.push(t[o]),o+=1}function i(e){var t=[];if(!e||0===e.length||!e[0]||0===e[0].length)return t;for(var r=e.length,o=e[0].length,n=0;n<r;n++)for(var i=0;i<o;i++)t[i]||(t[i]=[]),t[i][n]=e[n][i];return t}function s(e,t,r,o){var n=-1,i=e,s=r;Array.isArray(e)||(i=Array.from(e));var a=i.length;for(o&&a&&(s=i[n+=1]),n+=1;n<a;)s=t(s,i[n],n,i),n+=1;return s}function a(e,t){var r=0,o=e;Array.isArray(e)||(o=Array.from(e));for(var n=o.length,i=[],s=-1;r<n;){var a=o[r];t(a,r,o)&&(i[s+=1]=a),r+=1}return i}function l(e,t){var r=0,o=e;Array.isArray(e)||(o=Array.from(e));for(var n=o.length,i=[],s=-1;r<n;){var a=o[r];i[s+=1]=t(a,r,o),r+=1}return i}function c(e,t){var r=0,o=e;Array.isArray(e)||(o=Array.from(e));for(var n=o.length;r<n&&!1!==t(o[r],r,o);)r+=1;return e}function h(e){return s(e,(e,t)=>e+t,0)}function d(e){return s(e,(e,t)=>e>t?e:t,Array.isArray(e)?e[0]:void 0)}function u(e){return s(e,(e,t)=>e<t?e:t,Array.isArray(e)?e[0]:void 0)}function g(e){return e.length?h(e)/e.length:0}function f(e){return s(e,(e,t)=>e.concat(Array.isArray(t)?f(t):t),[])}function m(e){var t=[];return c(e,e=>{-1===t.indexOf(e)&&t.push(e)}),t}function p(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var[o,...n]=[...t],i=o;return c(n,e=>{i=i.filter(t=>!e.includes(t))}),i}function v(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var[o,...n]=[...t],i=o;return c(n,e=>{i=i.filter(t=>e.includes(t))}),i}function w(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var[o,...n]=[...t],i=new Set(o);return c(n,e=>{c(e,e=>{i.has(e)||i.add(e)})}),Array.from(i)}function b(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ";return e.split(t)}r.r(t),r.d(t,"to2dArray",(function(){return o})),r.d(t,"extendArray",(function(){return n})),r.d(t,"pivot",(function(){return i})),r.d(t,"arrayReduce",(function(){return s})),r.d(t,"arrayFilter",(function(){return a})),r.d(t,"arrayMap",(function(){return l})),r.d(t,"arrayEach",(function(){return c})),r.d(t,"arraySum",(function(){return h})),r.d(t,"arrayMax",(function(){return d})),r.d(t,"arrayMin",(function(){return u})),r.d(t,"arrayAvg",(function(){return g})),r.d(t,"arrayFlatten",(function(){return f})),r.d(t,"arrayUnique",(function(){return m})),r.d(t,"getDifferenceOfArrays",(function(){return p})),r.d(t,"getIntersectionOfArrays",(function(){return v})),r.d(t,"getUnionOfArrays",(function(){return w})),r.d(t,"stringToArray",(function(){return b}))},function(e,t,r){"use strict";r.r(t),r.d(t,"getParent",(function(){return u})),r.d(t,"getFrameElement",(function(){return g})),r.d(t,"getParentWindow",(function(){return f})),r.d(t,"hasAccessToParentWindow",(function(){return m})),r.d(t,"closest",(function(){return p})),r.d(t,"closestDown",(function(){return v})),r.d(t,"isChildOf",(function(){return w})),r.d(t,"index",(function(){return b})),r.d(t,"overlayContainsElement",(function(){return C})),r.d(t,"hasClass",(function(){return R})),r.d(t,"addClass",(function(){return T})),r.d(t,"removeClass",(function(){return S})),r.d(t,"removeTextNodes",(function(){return M})),r.d(t,"empty",(function(){return N})),r.d(t,"HTML_CHARACTERS",(function(){return H})),r.d(t,"fastInnerHTML",(function(){return L})),r.d(t,"fastInnerText",(function(){return x})),r.d(t,"isVisible",(function(){return A})),r.d(t,"offset",(function(){return I})),r.d(t,"getWindowScrollTop",(function(){return j})),r.d(t,"getWindowScrollLeft",(function(){return D})),r.d(t,"getScrollTop",(function(){return _})),r.d(t,"getScrollLeft",(function(){return k})),r.d(t,"clientWidth",(function(){return P})),r.d(t,"clientHeight",(function(){return B})),r.d(t,"offsetWidth",(function(){return F})),r.d(t,"offsetHeight",(function(){return V})),r.d(t,"scrollWidth",(function(){return W})),r.d(t,"scrollHeight",(function(){return z})),r.d(t,"getScrollableElement",(function(){return U})),r.d(t,"getTrimmingContainer",(function(){return G})),r.d(t,"getStyle",(function(){return X})),r.d(t,"matchesCSSRules",(function(){return K})),r.d(t,"getComputedStyle",(function(){return Y})),r.d(t,"outerWidth",(function(){return q})),r.d(t,"outerHeight",(function(){return Q})),r.d(t,"innerHeight",(function(){return J})),r.d(t,"innerWidth",(function(){return $})),r.d(t,"addEvent",(function(){return ee})),r.d(t,"removeEvent",(function(){return te})),r.d(t,"getCaretPosition",(function(){return re})),r.d(t,"getSelectionEndPosition",(function(){return oe})),r.d(t,"getSelectionText",(function(){return ne})),r.d(t,"clearTextSelection",(function(){return ie})),r.d(t,"setCaretPosition",(function(){return se})),r.d(t,"getScrollbarWidth",(function(){return le})),r.d(t,"hasVerticalScrollbar",(function(){return ce})),r.d(t,"hasHorizontalScrollbar",(function(){return he})),r.d(t,"setOverlayPosition",(function(){return de})),r.d(t,"getCssTransform",(function(){return ue})),r.d(t,"resetCssTransform",(function(){return ge})),r.d(t,"isInput",(function(){return fe})),r.d(t,"isOutsideInput",(function(){return me})),r.d(t,"selectElementIfAllowed",(function(){return pe})),r.d(t,"getBoundingClientRect",(function(){return ve})),r.d(t,"clearMemoizedFunctionsBeforeRender",(function(){return be}));var o,n,i,s=r(55),a=r(46),l=r(141),c=r.n(l),h=e=>{var t=c()(e,{maxSize:1/0});return t.cache.orderByLru=(e,r,o)=>{t.cache.keys[o]=e,t.cache.values[o]=r},t.clear=()=>{t.cache.keys.length=0,t.cache.values.length=0},t},d=e=>(e.clear=()=>{},e);function u(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1,o=null,n=e;null!==n;){if(r===t){o=n;break}n.host&&n.nodeType===Node.DOCUMENT_FRAGMENT_NODE?n=n.host:(r+=1,n=n.parentNode)}return o}function g(e){return Object.getPrototypeOf(e.parent)&&e.frameElement}function f(e){return g(e)&&e.parent}function m(e){return!!Object.getPrototypeOf(e.parent)}function p(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2?arguments[2]:void 0,{ELEMENT_NODE:o,DOCUMENT_FRAGMENT_NODE:n}=Node,i=e;null!=i&&i!==r;){var{nodeType:s,nodeName:a}=i;if(s===o&&(t.includes(a)||t.includes(i)))return i;var{host:l}=i;i=l&&s===n?l:i.parentNode}return null}function v(e,t,r){for(var o=[],n=e;n&&(n=p(n,t,r))&&(!r||r.contains(n));)o.push(n),n=n.host&&n.nodeType===Node.DOCUMENT_FRAGMENT_NODE?n.host:n.parentNode;var i=o.length;return i?o[i-1]:null}function w(e,t){var r=e.parentNode,o=[];for("string"==typeof t?o=e.defaultView?Array.prototype.slice.call(e.querySelectorAll(t),0):Array.prototype.slice.call(e.ownerDocument.querySelectorAll(t),0):o.push(t);null!==r;){if(o.indexOf(r)>-1)return!0;r=r.parentNode}return!1}function b(e){var t=0,r=e;if(r.previousSibling)for(;r=r.previousSibling;)t+=1;return t}function C(e,t,r){var o=r.parentElement.querySelector(".ht_clone_".concat(e));return o?o.contains(t):null}function E(e){return e&&e.length?e.filter(e=>!!e):[]}if(Object(s.isClassListSupported)()){var O=function(e){var t=e.createElement("div");return t.classList.add("test","test2"),t.classList.contains("test2")};o=function(e,t){return void 0!==e.classList&&"string"==typeof t&&""!==t&&e.classList.contains(t)},n=function(e,t){var r=e.ownerDocument,o=t;if("string"==typeof o&&(o=o.split(" ")),(o=E(o)).length>0)if(O(r))e.classList.add(...o);else for(var n=0;o&&o[n];)e.classList.add(o[n]),n+=1},i=function(e,t){var r=t;if("string"==typeof r&&(r=r.split(" ")),(r=E(r)).length>0)if(O)e.classList.remove(...r);else for(var o=0;r&&r[o];)e.classList.remove(r[o]),o+=1}}else{var y=function(e){return new RegExp("(\\s|^)".concat(e,"(\\s|$)"))};o=function(e,t){return void 0!==e.className&&y(t).test(e.className)},n=function(e,t){var r=e.className,o=t;if("string"==typeof o&&(o=o.split(" ")),""===r)r=o.join(" ");else for(var n=0;n<o.length;n++)o[n]&&!y(o[n]).test(r)&&(r+=" ".concat(o[n]));e.className=r},i=function(e,t){var r=0,o=e.className,n=t;for("string"==typeof n&&(n=n.split(" "));n&&n[r];)o=o.replace(y(n[r])," ").trim(),r+=1;e.className!==o&&(e.className=o)}}function R(e,t){return o(e,t)}function T(e,t){n(e,t)}function S(e,t){i(e,t)}function M(e){if(3===e.nodeType)e.parentNode.removeChild(e);else if(["TABLE","THEAD","TBODY","TFOOT","TR"].indexOf(e.nodeName)>-1)for(var t=e.childNodes,r=t.length-1;r>=0;r--)M(t[r])}function N(e){for(var t;t=e.lastChild;)e.removeChild(t)}var H=/(<(.*)>|&(.*);)/;function L(e,t){H.test(t)?e.innerHTML=t:x(e,t)}function x(e,t){var r=e.firstChild;r&&3===r.nodeType&&null===r.nextSibling?s.isTextContentSupported?r.textContent=t:r.data=t:(N(e),e.appendChild(e.ownerDocument.createTextNode(t)))}function A(e){for(var t=e.ownerDocument.documentElement,r=e;r!==t;){if(null===r)return!1;if(r.nodeType===Node.DOCUMENT_FRAGMENT_NODE){if(r.host){if(r.host.impl)return A(r.host.impl);if(r.host)return A(r.host);throw new Error("Lost in Web Components world")}return!1}if(r.style&&"none"===r.style.display)return!1;r=r.parentNode}return!0}function I(e){var t,r,o,n,i=e.ownerDocument,a=i.defaultView,l=i.documentElement,c=e;if(Object(s.hasCaptionProblem)()&&c.firstChild&&"CAPTION"===c.firstChild.nodeName)return{top:(n=ve(c)).top+(a.pageYOffset||l.scrollTop)-(l.clientTop||0),left:n.left+(a.pageXOffset||l.scrollLeft)-(l.clientLeft||0)};for(t=c.offsetLeft,r=c.offsetTop,o=c;(c=c.offsetParent)&&c!==i.body;)t+=c.offsetLeft,r+=c.offsetTop,o=c;return o&&"fixed"===o.style.position&&(t+=a.pageXOffset||l.scrollLeft,r+=a.pageYOffset||l.scrollTop),{left:t,top:r}}function j(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window,t=e.scrollY;return void 0===t&&(t=e.document.documentElement.scrollTop),t}function D(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window,t=e.scrollX;return void 0===t&&(t=e.document.documentElement.scrollLeft),t}var _=h((function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window;return e===t?j(t):e.scrollTop}));function k(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window;return e===t?D(t):e.scrollLeft}var P=h(e=>e.clientWidth),B=d(e=>e.clientHeight),F=d(e=>e.offsetWidth),V=d(e=>e.offsetHeight),W=d(e=>e.scrollWidth),z=d(e=>e.scrollHeight);function U(e){var t=e.ownerDocument,r=t?t.defaultView:void 0;t||(r=(t=e.document?e.document:e).defaultView);for(var o=["auto","scroll"],n=Object(s.isGetComputedStyleSupported)(),i=e.parentNode;i&&i.style&&t.body!==i;){var{overflow:a,overflowX:l,overflowY:c}=i.style;if([a,l,c].includes("scroll"))return i;if(n&&(({overflow:a,overflowX:l,overflowY:c}=Y(i,r)),o.includes(a)||o.includes(l)||o.includes(c)))return i;if(B(i)<=z(i)+1&&(o.includes(c)||o.includes(a)))return i;if(P(i)<=W(i)+1&&(o.includes(l)||o.includes(a)))return i;i=i.parentNode}return r}function G(e){for(var t=e.ownerDocument,r=t.defaultView,o=e.parentNode;o&&o.style&&t.body!==o;){if("visible"!==o.style.overflow&&""!==o.style.overflow)return o;var n=Y(o,r),i=["scroll","hidden","auto"],s=n.getPropertyValue("overflow"),a=n.getPropertyValue("overflow-y"),l=n.getPropertyValue("overflow-x");if(i.includes(s)||i.includes(a)||i.includes(l))return o;o=o.parentNode}return r}function X(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:window;if(e){if(e===r)return"width"===t?"".concat(r.innerWidth,"px"):"height"===t?"".concat(r.innerHeight,"px"):void 0;var o=e.style[t];if(""!==o&&void 0!==o)return o;var n=Y(e,r);return""!==n[t]&&void 0!==n[t]?n[t]:void 0}}function K(e,t){var{selectorText:r}=t,o=!1;return t.type===CSSRule.STYLE_RULE&&r&&(e.msMatchesSelector?o=e.msMatchesSelector(r):e.matches&&(o=e.matches(r))),o}var Y=d((function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window;return e.currentStyle||t.getComputedStyle(e)}));function q(e){return F(e)}function Q(e){return Object(s.hasCaptionProblem)()&&e.firstChild&&"CAPTION"===e.firstChild.nodeName?V(e)+V(e.firstChild):V(e)}var Z,J=h(e=>B(e)||e.innerHeight),$=h(e=>P(e)||e.innerWidth);function ee(e,t,r){e.addEventListener(t,r,!1)}function te(e,t,r){e.removeEventListener(t,r,!1)}function re(e){var t=e.ownerDocument;if(e.selectionStart)return e.selectionStart;if(t.selection){e.focus();var r=t.selection.createRange();if(null===r)return 0;var o=e.createTextRange(),n=o.duplicate();return o.moveToBookmark(r.getBookmark()),n.setEndPoint("EndToStart",o),n.text.length}return 0}function oe(e){var t=e.ownerDocument;if(e.selectionEnd)return e.selectionEnd;if(t.selection){var r=t.selection.createRange();return null===r?0:e.createTextRange().text.indexOf(r.text)+r.text.length}return 0}function ne(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window,t=e.document,r="";return e.getSelection?r=e.getSelection().toString():t.selection&&"Control"!==t.selection.type&&(r=t.selection.createRange().text),r}function ie(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window,t=e.document;e.getSelection?e.getSelection().empty?e.getSelection().empty():e.getSelection().removeAllRanges&&e.getSelection().removeAllRanges():t.selection&&t.selection.empty()}function se(e,t,r){if(void 0===r&&(r=t),e.setSelectionRange){e.focus();try{e.setSelectionRange(t,r)}catch(i){var o=e.parentNode,n=o.style.display;o.style.display="block",e.setSelectionRange(t,r),o.style.display=n}}}function ae(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,t=e.createElement("div");t.style.height="200px",t.style.width="100%";var r=e.createElement("div");r.style.boxSizing="content-box",r.style.height="150px",r.style.left="0px",r.style.overflow="hidden",r.style.position="absolute",r.style.top="0px",r.style.width="200px",r.style.visibility="hidden",r.appendChild(t),(e.body||e.documentElement).appendChild(r);var o=F(t);r.style.overflow="scroll";var n=F(t);return o===n&&(n=P(r)),(e.body||e.documentElement).removeChild(r),o-n}function le(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document;return void 0===Z&&(Z=ae(e)),Z}function ce(e){return F(e)!==P(e)}function he(e){return V(e)!==B(e)}function de(e,t,r){Object(a.isIE9)()?(e.style.top=r,e.style.left=t):Object(a.isSafari)()?(e.style["-webkit-transform"]="translate3d(".concat(t,",").concat(r,",0)"),e.style["-webkit-transform"]="translate3d(".concat(t,",").concat(r,",0)")):e.style.transform="translate3d(".concat(t,",").concat(r,",0)")}function ue(e){var t;return e.style.transform&&""!==(t=e.style.transform)?["transform",t]:e.style["-webkit-transform"]&&""!==(t=e.style["-webkit-transform"])?["-webkit-transform",t]:-1}function ge(e){e.style.transform&&""!==e.style.transform?e.style.transform="":e.style["-webkit-transform"]&&""!==e.style["-webkit-transform"]&&(e.style["-webkit-transform"]="")}function fe(e){return e&&(["INPUT","SELECT","TEXTAREA"].indexOf(e.nodeName)>-1||"true"===e.contentEditable)}function me(e){return fe(e)&&!1===e.hasAttribute("data-hot-input")}function pe(e){me(e.ownerDocument.activeElement)||e.select()}var ve=d(e=>e.getBoundingClientRect()),we=[_,ve,P,B,F,V,W,z,Y,$,J],be=()=>{we.forEach(e=>e.clear())}},,,,function(e,t,r){"use strict";r.d(t,"d",(function(){return i})),r.d(t,"a",(function(){return c})),r.d(t,"b",(function(){return h})),r.d(t,"e",(function(){return He})),r.d(t,"c",(function(){return st}));var o=new WeakMap;class n{static get DEFAULT_WIDTH(){return 50}constructor(){var{viewportSize:e,scrollOffset:t,totalItems:r,itemSizeFn:n,overrideFn:i,calculationType:s,stretchMode:a,stretchingItemWidthFn:l=(e=>e)}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};o.set(this,{viewportWidth:e,scrollOffset:t,totalColumns:r,columnWidthFn:n,overrideFn:i,calculationType:s,stretchingColumnWidthFn:l}),this.count=0,this.startColumn=null,this.endColumn=null,this.startPosition=null,this.stretchAllRatio=0,this.stretchLastWidth=0,this.stretch=a,this.totalTargetWidth=0,this.needVerifyLastColumnWidth=!0,this.stretchAllColumnsWidth=[],this.calculate()}calculate(){for(var e,t=0,r=!0,n=[],i=o.get(this),s=i.calculationType,a=i.overrideFn,l=i.scrollOffset,c=i.totalColumns,h=i.viewportWidth,d=0;d<c;d++){if(e=this._getColumnWidth(d),t<=l&&2!==s&&(this.startColumn=d),t>=l&&t+(2===s?e:0)<=l+(l>0?h+1:h)&&(null!==this.startColumn&&void 0!==this.startColumn||(this.startColumn=d),this.endColumn=d),n.push(t),t+=e,2!==s&&(this.endColumn=d),t>=l+h){r=!1;break}}if(this.endColumn===c-1&&r)for(this.startColumn=this.endColumn;this.startColumn>0;){var u=n[this.endColumn]+e-n[this.startColumn-1];if((u<=h||2!==s)&&(this.startColumn-=1),u>h)break}1===s&&null!==this.startColumn&&a&&a(this),this.startPosition=n[this.startColumn],void 0===this.startPosition&&(this.startPosition=null),c<this.endColumn&&(this.endColumn=c-1),null!==this.startColumn&&(this.count=this.endColumn-this.startColumn+1)}refreshStretching(e){if("none"!==this.stretch){var t=e;this.totalTargetWidth=t;for(var r=o.get(this),n=r.totalColumns,i=0,s=0;s<n;s++){var a=this._getColumnWidth(s),l=r.stretchingColumnWidthFn(void 0,s);"number"==typeof l?t-=l:i+=a}var c=t-i;if("all"===this.stretch&&c>0)this.stretchAllRatio=t/i,this.stretchAllColumnsWidth=[],this.needVerifyLastColumnWidth=!0;else if("last"===this.stretch&&t!==1/0){var h=this._getColumnWidth(n-1),d=c+h;this.stretchLastWidth=d>=0?d:h}}}getStretchedColumnWidth(e,t){var r=null;return"all"===this.stretch&&0!==this.stretchAllRatio?r=this._getStretchedAllColumnWidth(e,t):"last"===this.stretch&&0!==this.stretchLastWidth&&(r=this._getStretchedLastColumnWidth(e)),r}_getStretchedAllColumnWidth(e,t){var r=0,n=o.get(this),i=n.totalColumns;if(!this.stretchAllColumnsWidth[e]){var s=Math.round(t*this.stretchAllRatio),a=n.stretchingColumnWidthFn(s,e);this.stretchAllColumnsWidth[e]=void 0===a?s:isNaN(a)?this._getColumnWidth(e):a}if(this.stretchAllColumnsWidth.length===i&&this.needVerifyLastColumnWidth){this.needVerifyLastColumnWidth=!1;for(var l=0;l<this.stretchAllColumnsWidth.length;l++)r+=this.stretchAllColumnsWidth[l];r!==this.totalTargetWidth&&(this.stretchAllColumnsWidth[this.stretchAllColumnsWidth.length-1]+=this.totalTargetWidth-r)}return this.stretchAllColumnsWidth[e]}_getStretchedLastColumnWidth(e){return e===o.get(this).totalColumns-1?this.stretchLastWidth:null}_getColumnWidth(e){var t=o.get(this).columnWidthFn(e);return isNaN(t)&&(t=n.DEFAULT_WIDTH),t}}var i=n,s=new WeakMap;class a{static get DEFAULT_HEIGHT(){return 23}constructor(){var{viewportSize:e,scrollOffset:t,totalItems:r,itemSizeFn:o,overrideFn:n,calculationType:i,scrollbarHeight:a}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};s.set(this,{viewportHeight:e,scrollOffset:t,totalRows:r,rowHeightFn:o,overrideFn:n,calculationType:i,horizontalScrollbarHeight:a}),this.count=0,this.startRow=null,this.endRow=null,this.startPosition=null,this.calculate()}calculate(){for(var e,t=0,r=!0,o=[],n=s.get(this),i=n.calculationType,l=n.overrideFn,c=n.rowHeightFn,h=n.scrollOffset,d=n.totalRows,u=n.viewportHeight,g=n.horizontalScrollbarHeight||0,f=0;f<d;f++)if(e=c(f),isNaN(e)&&(e=a.DEFAULT_HEIGHT),t<=h&&2!==i&&(this.startRow=f),t>=h&&t+(2===i?e:0)<=h+u-g&&(null===this.startRow&&(this.startRow=f),this.endRow=f),o.push(t),t+=e,2!==i&&(this.endRow=f),t>=h+u-g){r=!1;break}if(this.endRow===d-1&&r)for(this.startRow=this.endRow;this.startRow>0;){var m=o[this.endRow]+e-o[this.startRow-1];if((m<=u-g||2!==i)&&(this.startRow-=1),m>=u-g)break}1===i&&null!==this.startRow&&l&&l(this),this.startPosition=o[this.startRow],void 0===this.startPosition&&(this.startPosition=null),d<this.endRow&&(this.endRow=d-1),null!==this.startRow&&(this.count=this.endRow-this.startRow+1)}}var l=a;var c=class{constructor(e,t){this.row=null,this.col=null,void 0!==e&&void 0!==t&&(this.row=e,this.col=t)}isValid(e){return!(this.row<0||this.col<0)&&!(this.row>=e.getSetting("totalRows")||this.col>=e.getSetting("totalColumns"))}isEqual(e){return e===this||this.row===e.row&&this.col===e.col}isSouthEastOf(e){return this.row>=e.row&&this.col>=e.col}isNorthWestOf(e){return this.row<=e.row&&this.col<=e.col}isSouthWestOf(e){return this.row>=e.row&&this.col<=e.col}isNorthEastOf(e){return this.row<=e.row&&this.col>=e.col}toObject(){return{row:this.row,col:this.col}}};var h=class{constructor(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e;this.highlight=e,this.from=t,this.to=r}setHighlight(e){return this.highlight=e,this}setFrom(e){return this.from=e,this}setTo(e){return this.to=e,this}isValid(e){return this.from.isValid(e)&&this.to.isValid(e)}isSingle(){return this.from.row===this.to.row&&this.from.col===this.to.col}getHeight(){return Math.max(this.from.row,this.to.row)-Math.min(this.from.row,this.to.row)+1}getWidth(){return Math.max(this.from.col,this.to.col)-Math.min(this.from.col,this.to.col)+1}includes(e){var{row:t,col:r}=e,o=this.getTopLeftCorner(),n=this.getBottomRightCorner();return o.row<=t&&n.row>=t&&o.col<=r&&n.col>=r}includesRange(e){return this.includes(e.getTopLeftCorner())&&this.includes(e.getBottomRightCorner())}isEqual(e){return Math.min(this.from.row,this.to.row)===Math.min(e.from.row,e.to.row)&&Math.max(this.from.row,this.to.row)===Math.max(e.from.row,e.to.row)&&Math.min(this.from.col,this.to.col)===Math.min(e.from.col,e.to.col)&&Math.max(this.from.col,this.to.col)===Math.max(e.from.col,e.to.col)}overlaps(e){return e.isSouthEastOf(this.getTopLeftCorner())&&e.isNorthWestOf(this.getBottomRightCorner())}isSouthEastOf(e){return this.getTopLeftCorner().isSouthEastOf(e)||this.getBottomRightCorner().isSouthEastOf(e)}isNorthWestOf(e){return this.getTopLeftCorner().isNorthWestOf(e)||this.getBottomRightCorner().isNorthWestOf(e)}isOverlappingHorizontally(e){return this.getTopRightCorner().col>=e.getTopLeftCorner().col&&this.getTopRightCorner().col<=e.getTopRightCorner().col||this.getTopLeftCorner().col<=e.getTopRightCorner().col&&this.getTopLeftCorner().col>=e.getTopLeftCorner().col}isOverlappingVertically(e){return this.getBottomRightCorner().row>=e.getTopRightCorner().row&&this.getBottomRightCorner().row<=e.getBottomRightCorner().row||this.getTopRightCorner().row<=e.getBottomRightCorner().row&&this.getTopRightCorner().row>=e.getTopRightCorner().row}expand(e){var t=this.getTopLeftCorner(),r=this.getBottomRightCorner();return(e.row<t.row||e.col<t.col||e.row>r.row||e.col>r.col)&&(this.from=new c(Math.min(t.row,e.row),Math.min(t.col,e.col)),this.to=new c(Math.max(r.row,e.row),Math.max(r.col,e.col)),!0)}expandByRange(e){if(this.includesRange(e)||!this.overlaps(e))return!1;var t=this.getTopLeftCorner(),r=this.getBottomRightCorner(),o=this.getDirection(),n=e.getTopLeftCorner(),i=e.getBottomRightCorner(),s=Math.min(t.row,n.row),a=Math.min(t.col,n.col),l=Math.max(r.row,i.row),h=Math.max(r.col,i.col),d=new c(s,a),u=new c(l,h);return this.from=d,this.to=u,this.setDirection(o),this.highlight.row===this.getBottomRightCorner().row&&"N-S"===this.getVerticalDirection()&&this.flipDirectionVertically(),this.highlight.col===this.getTopRightCorner().col&&"W-E"===this.getHorizontalDirection()&&this.flipDirectionHorizontally(),!0}getDirection(){return this.from.isNorthWestOf(this.to)?"NW-SE":this.from.isNorthEastOf(this.to)?"NE-SW":this.from.isSouthEastOf(this.to)?"SE-NW":this.from.isSouthWestOf(this.to)?"SW-NE":void 0}setDirection(e){switch(e){case"NW-SE":[this.from,this.to]=[this.getTopLeftCorner(),this.getBottomRightCorner()];break;case"NE-SW":[this.from,this.to]=[this.getTopRightCorner(),this.getBottomLeftCorner()];break;case"SE-NW":[this.from,this.to]=[this.getBottomRightCorner(),this.getTopLeftCorner()];break;case"SW-NE":[this.from,this.to]=[this.getBottomLeftCorner(),this.getTopRightCorner()]}}getVerticalDirection(){return["NE-SW","NW-SE"].indexOf(this.getDirection())>-1?"N-S":"S-N"}getHorizontalDirection(){return["NW-SE","SW-NE"].indexOf(this.getDirection())>-1?"W-E":"E-W"}flipDirectionVertically(){switch(this.getDirection()){case"NW-SE":this.setDirection("SW-NE");break;case"NE-SW":this.setDirection("SE-NW");break;case"SE-NW":this.setDirection("NE-SW");break;case"SW-NE":this.setDirection("NW-SE")}}flipDirectionHorizontally(){switch(this.getDirection()){case"NW-SE":this.setDirection("NE-SW");break;case"NE-SW":this.setDirection("NW-SE");break;case"SE-NW":this.setDirection("SW-NE");break;case"SW-NE":this.setDirection("SE-NW")}}getCorners(){return[Math.min(this.from.row,this.to.row),Math.min(this.from.col,this.to.col),Math.max(this.from.row,this.to.row),Math.max(this.from.col,this.to.col)]}getTopLeftCorner(){return new c(Math.min(this.from.row,this.to.row),Math.min(this.from.col,this.to.col))}getBottomRightCorner(){return new c(Math.max(this.from.row,this.to.row),Math.max(this.from.col,this.to.col))}getTopRightCorner(){return new c(Math.min(this.from.row,this.to.row),Math.max(this.from.col,this.to.col))}getBottomLeftCorner(){return new c(Math.max(this.from.row,this.to.row),Math.min(this.from.col,this.to.col))}isCorner(e,t){return!!(t&&t.includes(e)&&(this.getTopLeftCorner().isEqual(new c(t.from.row,t.from.col))||this.getTopRightCorner().isEqual(new c(t.from.row,t.to.col))||this.getBottomLeftCorner().isEqual(new c(t.to.row,t.from.col))||this.getBottomRightCorner().isEqual(new c(t.to.row,t.to.col))))||(e.isEqual(this.getTopLeftCorner())||e.isEqual(this.getTopRightCorner())||e.isEqual(this.getBottomLeftCorner())||e.isEqual(this.getBottomRightCorner()))}getOppositeCorner(e,t){if(!(e instanceof c))return!1;if(t&&t.includes(e)){if(this.getTopLeftCorner().isEqual(new c(t.from.row,t.from.col)))return this.getBottomRightCorner();if(this.getTopRightCorner().isEqual(new c(t.from.row,t.to.col)))return this.getBottomLeftCorner();if(this.getBottomLeftCorner().isEqual(new c(t.to.row,t.from.col)))return this.getTopRightCorner();if(this.getBottomRightCorner().isEqual(new c(t.to.row,t.to.col)))return this.getTopLeftCorner()}return e.isEqual(this.getBottomRightCorner())?this.getTopLeftCorner():e.isEqual(this.getTopLeftCorner())?this.getBottomRightCorner():e.isEqual(this.getTopRightCorner())?this.getBottomLeftCorner():e.isEqual(this.getBottomLeftCorner())?this.getTopRightCorner():void 0}getBordersSharedWith(e){if(!this.includesRange(e))return[];var t=Math.min(this.from.row,this.to.row),r=Math.max(this.from.row,this.to.row),o=Math.min(this.from.col,this.to.col),n=Math.max(this.from.col,this.to.col),i=Math.min(e.from.row,e.to.row),s=Math.max(e.from.row,e.to.row),a=Math.min(e.from.col,e.to.col),l=Math.max(e.from.col,e.to.col),c=[];return t===i&&c.push("top"),n===l&&c.push("right"),r===s&&c.push("bottom"),o===a&&c.push("left"),c}getInner(){for(var e=this.getTopLeftCorner(),t=this.getBottomRightCorner(),r=[],o=e.row;o<=t.row;o++)for(var n=e.col;n<=t.col;n++)this.from.row===o&&this.from.col===n||this.to.row===o&&this.to.col===n||r.push(new c(o,n));return r}getAll(){for(var e=this.getTopLeftCorner(),t=this.getBottomRightCorner(),r=[],o=e.row;o<=t.row;o++)for(var n=e.col;n<=t.col;n++)e.row===o&&e.col===n?r.push(e):t.row===o&&t.col===n?r.push(t):r.push(new c(o,n));return r}forAll(e){for(var t=this.getTopLeftCorner(),r=this.getBottomRightCorner(),o=t.row;o<=r.row;o++)for(var n=t.col;n<=r.col;n++){if(!1===e(o,n))return}}toObject(){return{from:this.from.toObject(),to:this.to.toObject()}}};var d=class{constructor(e,t,r){this.offset=e,this.total=t,this.countTH=r}offsetted(e){return e+this.offset}unOffsetted(e){return e-this.offset}renderedToSource(e){return this.offsetted(e)}sourceToRendered(e){return this.unOffsetted(e)}offsettedTH(e){return e-this.countTH}unOffsettedTH(e){return e+this.countTH}visibleRowHeadedColumnToSourceColumn(e){return this.renderedToSource(this.offsettedTH(e))}sourceColumnToVisibleRowHeadedColumn(e){return this.unOffsettedTH(this.sourceToRendered(e))}};var u=class{constructor(e,t,r){this.offset=e,this.total=t,this.countTH=r}offsetted(e){return e+this.offset}unOffsetted(e){return e-this.offset}renderedToSource(e){return this.offsetted(e)}sourceToRendered(e){return this.unOffsetted(e)}offsettedTH(e){return e-this.countTH}unOffsettedTH(e){return e+this.countTH}visibleColHeadedRowToSourceRow(e){return this.renderedToSource(this.offsettedTH(e))}sourceRowToVisibleColHeadedRow(e){return this.unOffsettedTH(this.sourceToRendered(e))}},g=r(34);class f{constructor(){this.currentSize=0,this.nextSize=0,this.currentOffset=0,this.nextOffset=0}setSize(e){this.currentSize=this.nextSize,this.nextSize=e}setOffset(e){this.currentOffset=this.nextOffset,this.nextOffset=e}}class m{constructor(){this.size=new f,this.workingSpace=0,this.sharedSize=null}setSize(e){this.size.setSize(e)}setOffset(e){this.size.setOffset(e)}getViewSize(){return this.size}isShared(){return this.sharedSize instanceof f}isPlaceOn(e){return this.workingSpace===e}append(e){this.workingSpace=1,e.workingSpace=2,this.sharedSize=e.getViewSize()}prepend(e){this.workingSpace=2,e.workingSpace=1,this.sharedSize=e.getViewSize()}}class p{constructor(e,t,r){this.rootNode=e,this.nodesPool=t,this.sizeSet=new m,this.childNodeType=r.toUpperCase(),this.visualIndex=0,this.collectedNodes=[]}setSize(e){return this.sizeSet.setSize(e),this}setOffset(e){return this.sizeSet.setOffset(e),this}isSharedViewSet(){return this.sizeSet.isShared()}getNode(e){return e<this.collectedNodes.length?this.collectedNodes[e]:null}getCurrentNode(){var e=this.collectedNodes.length;return e>0?this.collectedNodes[e-1]:null}getRenderedChildCount(){var{rootNode:e,sizeSet:t}=this,r=0;if(this.isSharedViewSet())for(var o=e.firstElementChild;o;){if(o.tagName===this.childNodeType)r+=1;else if(t.isPlaceOn(1))break;o=o.nextElementSibling}else r=e.childElementCount;return r}start(){this.collectedNodes.length=0,this.visualIndex=0;for(var{rootNode:e,sizeSet:t}=this,r=this.isSharedViewSet(),{nextSize:o}=t.getViewSize(),n=this.getRenderedChildCount();n<o;){var i=this.nodesPool();!r||r&&t.isPlaceOn(2)?e.appendChild(i):e.insertBefore(i,e.firstChild),n+=1}for(var s=r&&t.isPlaceOn(1);n>o;)e.removeChild(s?e.firstChild:e.lastChild),n-=1}render(){var{rootNode:e,sizeSet:t}=this,r=this.visualIndex;this.isSharedViewSet()&&t.isPlaceOn(2)&&(r+=t.sharedSize.nextSize);var o=e.childNodes[r];if(o.tagName!==this.childNodeType){var n=this.nodesPool();e.replaceChild(n,o),o=n}this.collectedNodes.push(o),this.visualIndex+=1}end(){}}class v extends p{prependView(e){return this.sizeSet.prepend(e.sizeSet),e.sizeSet.append(this.sizeSet),this}appendView(e){return this.sizeSet.append(e.sizeSet),e.sizeSet.prepend(this.sizeSet),this}}class w{constructor(e){this.nodeType=e.toUpperCase()}setRootDocument(e){this.rootDocument=e}obtain(){return this.rootDocument.createElement(this.nodeType)}}class b{constructor(e,t){this.nodesPool="string"==typeof e?new w(e):null,this.nodeType=e,this.rootNode=t,this.table=null,this.renderedNodes=0}setTable(e){this.nodesPool&&this.nodesPool.setRootDocument(e.rootDocument),this.table=e}adjust(){}render(){}}class C extends b{constructor(){super("TH"),this.orderViews=new WeakMap,this.sourceRowIndex=0}obtainOrderView(e){var t;return this.orderViews.has(e)?t=this.orderViews.get(e):(t=new v(e,e=>this.nodesPool.obtain(this.sourceRowIndex,e),this.nodeType),this.orderViews.set(e,t)),t}render(){for(var{rowsToRender:e,rowHeaderFunctions:t,rowHeadersCount:r,rows:o,cells:n}=this.table,i=0;i<e;i++){var s=this.table.renderedRowToSource(i),a=o.getRenderedNode(i);this.sourceRowIndex=s;var l=this.obtainOrderView(a),c=n.obtainOrderView(a);l.appendView(c).setSize(r).setOffset(this.table.renderedColumnToSource(0)).start();for(var h=0;h<r;h++){l.render();var d=l.getCurrentNode();d.className="",d.removeAttribute("style"),t[h](s,d,h)}l.end()}}}class E extends b{constructor(e){super(null,e)}adjust(){var{columnHeadersCount:e,rowHeadersCount:t}=this.table,r=this.rootNode.firstChild,{columnsToRender:o}=this.table;if(e||o){for(var n=o+t,i=0,s=e;i<s;i++){for((r=this.rootNode.childNodes[i])||(r=this.table.rootDocument.createElement("tr"),this.rootNode.appendChild(r)),this.renderedNodes=r.childNodes.length;this.renderedNodes<n;)r.appendChild(this.table.rootDocument.createElement("th")),this.renderedNodes+=1;for(;this.renderedNodes>n;)r.removeChild(r.lastChild),this.renderedNodes-=1}var a=this.rootNode.childNodes.length;if(a>e)for(var l=e;l<a;l++)this.rootNode.removeChild(this.rootNode.lastChild)}else r&&Object(g.empty)(r)}render(){for(var{columnHeadersCount:e}=this.table,t=0;t<e;t+=1)for(var{columnHeaderFunctions:r,columnsToRender:o,rowHeadersCount:n}=this.table,i=this.rootNode.childNodes[t],s=-1*n;s<o;s+=1){var a=this.table.renderedColumnToSource(s),l=i.childNodes[s+n];l.className="",l.removeAttribute("style"),r[t](a,l,t),a<0&&Object(g.addClass)(l,"wtTableCornerCell")}}}var O=r(56);class y extends b{constructor(e){super(null,e)}adjust(){for(var{columnsToRender:e,rowHeadersCount:t}=this.table,r=e+t;this.renderedNodes<r;)this.rootNode.appendChild(this.table.rootDocument.createElement("col")),this.renderedNodes+=1;for(;this.renderedNodes>r;)this.rootNode.removeChild(this.rootNode.lastChild),this.renderedNodes-=1}render(){this.adjust();for(var{columnsToRender:e,rowHeadersCount:t}=this.table,r=0;r<t;r++){var o=this.table.renderedColumnToSource(r),n=this.table.columnUtils.getHeaderWidth(o);this.rootNode.childNodes[r].style.width="".concat(n,"px")}for(var i=0;i<e;i++){var s=this.table.renderedColumnToSource(i),a=this.table.columnUtils.getStretchedColumnWidth(s);0===t&&0===i&&(a+=O.a),this.rootNode.childNodes[i+t].style.width="".concat(a,"px")}var l=this.rootNode.firstChild;l&&Object(g.addClass)(l,"rowHeader")}}var R=r(60),T=r(54);function S(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['Performance tip: Handsontable rendered more than 1000 visible rows. Consider limiting the number \n        of rendered rows by specifying the table height and/or turning off the "renderAllRows" option.'],['Performance tip: Handsontable rendered more than 1000 visible rows. Consider limiting the number\\x20\n        of rendered rows by specifying the table height and/or turning off the "renderAllRows" option.']);return S=function(){return e},e}var M=!1;class N extends b{constructor(e){super("TR",e),this.orderView=new p(e,e=>this.nodesPool.obtain(e),this.nodeType)}getRenderedNode(e){return this.orderView.getNode(e)}render(){var{rowsToRender:e}=this.table;!M&&e>1e3&&(M=!0,Object(R.b)(Object(T.a)(S()))),this.orderView.setSize(e).setOffset(this.table.renderedRowToSource(0)).start();for(var t=0;t<e;t++)this.orderView.render();this.orderView.end()}}class H extends b{constructor(){super("TD"),this.orderViews=new WeakMap,this.sourceRowIndex=0}obtainOrderView(e){var t;return this.orderViews.has(e)?t=this.orderViews.get(e):(t=new v(e,e=>this.nodesPool.obtain(this.sourceRowIndex,e),this.nodeType),this.orderViews.set(e,t)),t}render(){for(var{rowsToRender:e,columnsToRender:t,rows:r,rowHeaders:o}=this.table,n=0;n<e;n++){var i=this.table.renderedRowToSource(n),s=r.getRenderedNode(n);this.sourceRowIndex=i;var a=this.obtainOrderView(s),l=o.obtainOrderView(s);a.prependView(l).setSize(t).setOffset(this.table.renderedColumnToSource(0)).start();for(var c=0;c<t;c++){a.render();var h=a.getCurrentNode(),d=this.table.renderedColumnToSource(c);Object(g.hasClass)(h,"hide")||(h.className=""),h.removeAttribute("style"),this.table.cellRenderer(i,d,h)}a.end()}}}class L{constructor(e){var{cellRenderer:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.rootNode=e,this.rootDocument=this.rootNode.ownerDocument,this.rowHeaders=null,this.columnHeaders=null,this.colGroup=null,this.rows=null,this.cells=null,this.rowFilter=null,this.columnFilter=null,this.rowUtils=null,this.columnUtils=null,this.rowsToRender=0,this.columnsToRender=0,this.rowHeaderFunctions=[],this.rowHeadersCount=0,this.columnHeaderFunctions=[],this.columnHeadersCount=0,this.cellRenderer=t}setAxisUtils(e,t){this.rowUtils=e,this.columnUtils=t}setViewportSize(e,t){this.rowsToRender=e,this.columnsToRender=t}setFilters(e,t){this.rowFilter=e,this.columnFilter=t}setHeaderContentRenderers(e,t){this.rowHeaderFunctions=e,this.rowHeadersCount=e.length,this.columnHeaderFunctions=t,this.columnHeadersCount=t.length}setRenderers(){var{rowHeaders:e,columnHeaders:t,colGroup:r,rows:o,cells:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.setTable(this),t.setTable(this),r.setTable(this),o.setTable(this),n.setTable(this),this.rowHeaders=e,this.columnHeaders=t,this.colGroup=r,this.rows=o,this.cells=n}renderedRowToSource(e){return this.rowFilter.renderedToSource(e)}renderedColumnToSource(e){return this.columnFilter.renderedToSource(e)}render(){this.colGroup.adjust(),this.columnHeaders.adjust(),this.rows.adjust(),this.rowHeaders.adjust(),this.columnHeaders.render(),this.rows.render(),this.rowHeaders.render(),this.cells.render(),this.columnUtils.calculateWidths(),this.colGroup.render();var{rowsToRender:e,rows:t,columnHeadersCount:r}=this;0===r?Object(g.addClass)(t.rootNode,"afterEmptyThead"):Object(g.removeClass)(t.rootNode,"afterEmptyThead");for(var o=0;o<e;o++){var n=t.getRenderedNode(o);if(n.firstChild){var i=this.renderedRowToSource(o),s=this.rowUtils.getHeight(i);n.firstChild.style.height=s?"".concat(s-1,"px"):""}}}}class x{constructor(){var{TABLE:e,THEAD:t,COLGROUP:r,TBODY:o,rowUtils:n,columnUtils:i,cellRenderer:s}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.renderer=new L(e,{cellRenderer:s}),this.renderer.setRenderers({rowHeaders:new C,columnHeaders:new E(t),colGroup:new y(r),rows:new N(o),cells:new H}),this.renderer.setAxisUtils(n,i)}setFilters(e,t){return this.renderer.setFilters(e,t),this}setViewportSize(e,t){return this.renderer.setViewportSize(e,t),this}setHeaderContentRenderers(e,t){return this.renderer.setHeaderContentRenderers(e,t),this}render(){this.renderer.render()}}var A,I=r(43),j=r(49),D=/[-]{0,1}[\d]*[.]{0,1}[\d]+/g;var _=new Map;function k(e){var t=_.get(e);if(void 0!==t)return t;var r=function(e){var[t,r,o]=e;return.2126*t+.7152*r+.0722*o}(function(e){A.ownerDocument.body.appendChild(A),A.style.color=e;var t=A.ownerDocument.defaultView,r=Object(g.getComputedStyle)(A,t).color.match(D);return A.ownerDocument.body.removeChild(A),null===r||r.length<2?[0,0,0,1]:[parseInt(r[0],10),parseInt(r[1],10),parseInt(r[2],10),r.length>3?parseFloat(r[3]):1]}(e));return _.set(e,r),r}var P=class{constructor(e){this.guid="wt_".concat(Object(j.randomString)()),this.rootDocument=e.table.ownerDocument,this.rootWindow=this.rootDocument.defaultView,function(e){(A=e.document.createElement("span")).style.display="none"}(this.rootWindow)}getSetting(e,t,r,o,n){return this.wtSettings.getSetting(e,t,r,o,n)}hasSetting(e){return this.wtSettings.has(e)}},B=r(61),F=r(55),V=r(46),W=new WeakMap;var z=class{constructor(e){this.instance=e,this.eventManager=new I.a(e),W.set(this,{selectedCellBeforeTouchEnd:void 0,dblClickTimeout:[null,null],dblClickOrigin:[null,null]}),this.registerEvents()}registerEvents(){this.eventManager.addEventListener(this.instance.wtTable.holder,"contextmenu",e=>this.onContextMenu(e)),this.eventManager.addEventListener(this.instance.wtTable.TABLE,"mouseover",e=>this.onMouseOver(e)),this.eventManager.addEventListener(this.instance.wtTable.TABLE,"mouseout",e=>this.onMouseOut(e));var e=()=>{this.eventManager.addEventListener(this.instance.wtTable.holder,"touchstart",e=>this.onTouchStart(e)),this.eventManager.addEventListener(this.instance.wtTable.holder,"touchend",e=>this.onTouchEnd(e)),this.instance.momentumScrolling||(this.instance.momentumScrolling={}),this.eventManager.addEventListener(this.instance.wtTable.holder,"scroll",()=>{clearTimeout(this.instance.momentumScrolling._timeout),this.instance.momentumScrolling.ongoing||this.instance.getSetting("onBeforeTouchScroll"),this.instance.momentumScrolling.ongoing=!0,this.instance.momentumScrolling._timeout=setTimeout(()=>{this.instance.touchApplied||(this.instance.momentumScrolling.ongoing=!1,this.instance.getSetting("onAfterMomentumScroll"))},200)})};Object(V.isMobileBrowser)()?e():(Object(F.isTouchSupported)()&&e(),(()=>{this.eventManager.addEventListener(this.instance.wtTable.holder,"mouseup",e=>this.onMouseUp(e)),this.eventManager.addEventListener(this.instance.wtTable.holder,"mousedown",e=>this.onMouseDown(e))})())}selectedCellWasTouched(e){var t=W.get(this),r=this.parentCell(e).coords;if(t.selectedCellBeforeTouchEnd&&r){var[o,n]=[r.row,t.selectedCellBeforeTouchEnd.from.row],[i,s]=[r.col,t.selectedCellBeforeTouchEnd.from.col];return o===n&&i===s}return!1}parentCell(e){var t={},r=this.instance.wtTable.TABLE,o=Object(g.closestDown)(e,["TD","TH"],r);return o?(t.coords=this.instance.wtTable.getCoords(o),t.TD=o):Object(g.hasClass)(e,"wtBorder")&&Object(g.hasClass)(e,"current")?(t.coords=this.instance.selections.getCell().cellRange.highlight,t.TD=this.instance.wtTable.getCell(t.coords)):Object(g.hasClass)(e,"wtBorder")&&Object(g.hasClass)(e,"area")&&this.instance.selections.createOrGetArea().cellRange&&(t.coords=this.instance.selections.createOrGetArea().cellRange.to,t.TD=this.instance.wtTable.getCell(t.coords)),t}onMouseDown(e){var t=W.get(this),r=this.instance.rootDocument.activeElement,o=Object(B.partial)(g.getParent,e.target),n=e.target;if(n!==r&&o(0)!==r&&o(1)!==r){var i=this.parentCell(n);Object(g.hasClass)(n,"corner")?this.instance.getSetting("onCellCornerMouseDown",e,n):i.TD&&this.instance.hasSetting("onCellMouseDown")&&this.instance.getSetting("onCellMouseDown",e,i.coords,i.TD,this.instance),(0===e.button||this.instance.touchApplied)&&i.TD&&(t.dblClickOrigin[0]=i.TD,clearTimeout(t.dblClickTimeout[0]),t.dblClickTimeout[0]=setTimeout(()=>{t.dblClickOrigin[0]=null},1e3))}}onContextMenu(e){if(this.instance.hasSetting("onCellContextMenu")){var t=this.parentCell(e.target);t.TD&&this.instance.getSetting("onCellContextMenu",e,t.coords,t.TD,this.instance)}}onMouseOver(e){if(this.instance.hasSetting("onCellMouseOver")){var t=this.instance.wtTable.TABLE,r=Object(g.closestDown)(e.target,["TD","TH"],t),o=this.instance.overlay?this.instance.overlay.master:this.instance;r&&r!==o.lastMouseOver&&Object(g.isChildOf)(r,t)&&(o.lastMouseOver=r,this.instance.getSetting("onCellMouseOver",e,this.instance.wtTable.getCoords(r),r,this.instance))}}onMouseOut(e){if(this.instance.hasSetting("onCellMouseOut")){var t=this.instance.wtTable.TABLE,r=Object(g.closestDown)(e.target,["TD","TH"],t),o=Object(g.closestDown)(e.relatedTarget,["TD","TH"],t);r&&r!==o&&Object(g.isChildOf)(r,t)&&this.instance.getSetting("onCellMouseOut",e,this.instance.wtTable.getCoords(r),r,this.instance)}}onMouseUp(e){var t=W.get(this),r=this.parentCell(e.target);r.TD&&this.instance.hasSetting("onCellMouseUp")&&this.instance.getSetting("onCellMouseUp",e,r.coords,r.TD,this.instance),(0===e.button||this.instance.touchApplied)&&(r.TD===t.dblClickOrigin[0]&&r.TD===t.dblClickOrigin[1]?(Object(g.hasClass)(e.target,"corner")?this.instance.getSetting("onCellCornerDblClick",e,r.coords,r.TD,this.instance):this.instance.getSetting("onCellDblClick",e,r.coords,r.TD,this.instance),t.dblClickOrigin[0]=null,t.dblClickOrigin[1]=null):r.TD===t.dblClickOrigin[0]&&(t.dblClickOrigin[1]=r.TD,clearTimeout(t.dblClickTimeout[1]),t.dblClickTimeout[1]=setTimeout(()=>{t.dblClickOrigin[1]=null},500)))}onTouchStart(e){W.get(this).selectedCellBeforeTouchEnd=this.instance.selections.getCell().cellRange,this.instance.touchApplied=!0,this.onMouseDown(e)}onTouchEnd(e){var t=e.target;!1===this.selectedCellWasTouched(t)&&["A","BUTTON","INPUT"].includes(t.tagName)&&e.preventDefault(),this.onMouseUp(e),this.instance.touchApplied=!1}destroy(){var e=W.get(this);clearTimeout(e.dblClickTimeout[0]),clearTimeout(e.dblClickTimeout[1]),this.eventManager.destroy()}};var U=class{constructor(e){this.wot=e}scrollViewport(e,t,r,o,n){if(e.col<0||e.row<0)return!1;var i=this.scrollViewportHorizontally(e.col,r,n),s=this.scrollViewportVertically(e.row,t,o);return i||s}scrollViewportHorizontally(e,t,r){if(!this.wot.drawn)return!1;var{fixedColumnsLeft:o,leftOverlay:n,totalColumns:i}=this._getVariables(),s=!1;if(e>=0&&e<=Math.max(i-1,0)){var a=this.getFirstVisibleColumn(),l=this.getLastVisibleColumn();e>=o&&a>-1&&(e<a||r)?s=n.scrollTo(e):(-1===l||l>-1&&(e>l||t))&&(s=n.scrollTo(e,!0))}return s}scrollViewportVertically(e,t,r){if(!this.wot.drawn)return!1;var{fixedRowsBottom:o,fixedRowsTop:n,topOverlay:i,totalRows:s}=this._getVariables(),a=!1;if(e>=0&&e<=Math.max(s-1,0)){var l=this.getFirstVisibleRow(),c=this.getLastVisibleRow();e>=n&&l>-1&&(e<l||t)?a=i.scrollTo(e):(-1===c||c>-1&&(e>c&&e<s-o||r))&&(a=i.scrollTo(e,!0))}return a}getFirstVisibleRow(){var{topOverlay:e,wtTable:t,wtViewport:r,totalRows:o,fixedRowsTop:n}=this._getVariables(),i=this.wot.rootWindow,s=t.getFirstVisibleRow();if(e.mainTableScrollableElement===i){var a=Object(g.offset)(t.wtRootElement),l=Object(g.innerHeight)(t.hider),c=Object(g.innerHeight)(i),h=Object(g.getScrollTop)(i,i);if(a.top+l-c<=h){var d=r.getColumnHeaderHeight();d+=e.sumCellSizes(0,n);for(var u=o;u>0;u--)if(d+=e.sumCellSizes(u-1,u),a.top+l-d<=h){s=u;break}}}return s}getLastVisibleRow(){var{topOverlay:e,wtTable:t,wtViewport:r,totalRows:o}=this._getVariables(),n=this.wot.rootWindow,i=t.getLastVisibleRow();if(e.mainTableScrollableElement===n){var s=Object(g.offset)(t.wtRootElement),a=Object(g.getScrollTop)(n,n);if(s.top>a)for(var l=Object(g.innerHeight)(n),c=r.getColumnHeaderHeight(),h=1;h<=o;h++)if(c+=e.sumCellSizes(h-1,h),s.top+c-a>=l){i=h-2;break}}return i}getFirstVisibleColumn(){var{leftOverlay:e,wtTable:t,wtViewport:r,totalColumns:o}=this._getVariables(),n=this.wot.rootWindow,i=t.getFirstVisibleColumn();if(e.mainTableScrollableElement===n){var s=Object(g.offset)(t.wtRootElement),a=Object(g.innerWidth)(t.hider),l=Object(g.innerWidth)(n),c=Object(g.getScrollLeft)(n,n);if(s.left+a-l<=c)for(var h=r.getRowHeaderWidth(),d=o;d>0;d--)if(h+=e.sumCellSizes(d-1,d),s.left+a-h<=c){i=d;break}}return i}getLastVisibleColumn(){var{leftOverlay:e,wtTable:t,wtViewport:r,totalColumns:o}=this._getVariables(),n=this.wot.rootWindow,i=t.getLastVisibleColumn();if(e.mainTableScrollableElement===n){var s=Object(g.offset)(t.wtRootElement),a=Object(g.getScrollLeft)(n,n);if(s.left>a)for(var l=Object(g.innerWidth)(n),c=r.getRowHeaderWidth(),h=1;h<=o;h++)if(c+=e.sumCellSizes(h-1,h),s.left+c-a>=l){i=h-2;break}}return i}_getVariables(){var{wot:e}=this;return{topOverlay:e.wtOverlays.topOverlay,leftOverlay:e.wtOverlays.leftOverlay,wtTable:e.wtTable,wtViewport:e.wtViewport,totalRows:e.getSetting("totalRows"),totalColumns:e.getSetting("totalColumns"),fixedRowsTop:e.getSetting("fixedRowsTop"),fixedRowsBottom:e.getSetting("fixedRowsBottom"),fixedColumnsLeft:e.getSetting("fixedColumnsLeft")}}};var G=class extends P{constructor(e){super(e),this.overlay=e.overlay,this.overlayName=e.overlay.type,this.wtSettings=this.overlay.master.wtSettings,this.wtTable=e.createTableFn(this,e.table),this.wtScroll=new U(this),this.wtViewport=this.overlay.master.wtViewport,this.wtEvent=new z(this),this.selections=this.overlay.master.selections}drawClone(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this.wtTable.draw(e),this}getOverlayName(){return this.overlayName}},X={};class K{static get CLONE_TOP(){return"top"}static get CLONE_BOTTOM(){return"bottom"}static get CLONE_LEFT(){return"left"}static get CLONE_TOP_LEFT_CORNER(){return"top_left_corner"}static get CLONE_BOTTOM_LEFT_CORNER(){return"bottom_left_corner"}static get CLONE_TYPES(){return[K.CLONE_TOP,K.CLONE_BOTTOM,K.CLONE_LEFT,K.CLONE_TOP_LEFT_CORNER,K.CLONE_BOTTOM_LEFT_CORNER]}static registerOverlay(e,t){if(-1===K.CLONE_TYPES.indexOf(e))throw new Error("Unsupported overlay (".concat(e,")."));X[e]=t}static createOverlay(e,t){return new X[e](t)}static hasOverlay(e){return void 0!==X[e]}constructor(e){this.master=e,this.type="",this.mainTableScrollableElement=null,this.areElementSizesAdjusted=!1}updateStateOfRendering(){var e=this.needFullRender;this.needFullRender=this.shouldBeRendered(),e&&!this.needFullRender&&this.resetElementsSize()}shouldBeRendered(){return!0}updateMainScrollableElement(){var{master:e}=this;"hidden"===Object(g.getComputedStyle)(e.wtTable.wtRootElement.parentNode,e.rootWindow).getPropertyValue("overflow")?this.mainTableScrollableElement=e.wtTable.holder:this.mainTableScrollableElement=Object(g.getScrollableElement)(e.wtTable.TABLE)}getRelativeCellPosition(e,t,r){if(!1!==this.clone.wtTable.holder.contains(e)){var o=this.mainTableScrollableElement===this.master.rootWindow,n=r<this.master.getSetting("fixedColumnsLeft"),i=t<this.master.getSetting("fixedRowsTop"),s=t>=this.master.getSetting("totalRows")-this.master.getSetting("fixedRowsBottom"),a={left:this.clone.wtTable.spreader.offsetLeft,top:this.clone.wtTable.spreader.offsetTop},l={left:e.offsetLeft,top:e.offsetTop};return o?this.getRelativeCellPositionWithinWindow(i,n,l,a):this.getRelativeCellPositionWithinHolder(i,s,n,l,a)}Object(R.b)("The provided element is not a child of the ".concat(this.type," overlay"))}getRelativeCellPositionWithinWindow(e,t,r,o){var n=Object(g.getBoundingClientRect)(this.master.wtTable.wtRootElement),i=0,s=0;(i=t?n.left<=0?-1*n.left:0:o.left,e)?s=Object(g.getBoundingClientRect)(this.clone.wtTable.TABLE).top-n.top:s=o.top;return{left:r.left+i,top:r.top+s}}getRelativeCellPositionWithinHolder(e,t,r,o,n){var i=this.clone.overlay.master.wtOverlays.leftOverlay.getScrollPosition(),s=this.clone.overlay.master.wtOverlays.topOverlay.getScrollPosition(),a=0,l=0;if(r||(a=i-n.left),t){var c=Object(g.getBoundingClientRect)(this.master.wtTable.wtRootElement);l=-1*Object(g.getBoundingClientRect)(this.clone.wtTable.TABLE).top+c.top}else e||(l=s-n.top);return{left:o.left-a,top:o.top-l}}makeClone(e){if(-1===K.CLONE_TYPES.indexOf(e))throw new Error('Clone type "'.concat(e,'" is not supported.'));var{master:t}=this,r=t.rootDocument.createElement("DIV"),o=t.rootDocument.createElement("TABLE");r.className="ht_clone_".concat(e," handsontable"),r.style.position="absolute",r.style.top=0,r.style.left=0,r.style.overflow="visible",o.className=t.wtTable.TABLE.className,r.appendChild(o),this.type=e,t.wtTable.wtRootElement.parentNode.appendChild(r);var n=t.getSetting("preventOverflow");return!0===n||"horizontal"===n&&this.type===K.CLONE_TOP||"vertical"===n&&this.type===K.CLONE_LEFT?this.mainTableScrollableElement=t.rootWindow:"hidden"===Object(g.getComputedStyle)(t.wtTable.wtRootElement.parentNode,t.rootWindow).getPropertyValue("overflow")?this.mainTableScrollableElement=t.wtTable.holder:this.mainTableScrollableElement=Object(g.getScrollableElement)(t.wtTable.TABLE),new G({overlay:this,createTableFn:this.createTable,table:o})}redrawClone(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.adjustElementsPosition(),this.needFullRender&&this.clone.drawClone(e)}resetElementsSize(){var{clone:e}=this;e.wtTable.holder.style.width="",e.wtTable.holder.style.height="",e.wtTable.hider.style.width="",e.wtTable.hider.style.height="",e.wtTable.wtRootElement.style.width="",e.wtTable.wtRootElement.style.height=""}destroy(){new I.a(this.clone).destroy()}}var Y,q=K;function Q(e){e.command=""}function Z(e){e.renderedCommand!==e.command&&(e.elem.setAttribute("d",e.command),e.renderedCommand=e.command)}function J(e,t){var r=e.split(" "),o=t.split(" "),n=parseInt(r[0],10),i=parseInt(o[0],10);r.shift(),r.shift(),o.shift(),o.shift();var s,a,l=r.join(" "),c=o.join(" ");return n>i?1:n<i?-1:(s=k(c),a=k(l),s>a?1:s<a?-1:0)}function $(e,t,r){var o=e.get(t);if(!o){var n=r.ownerDocument.createElementNS("http://www.w3.org/2000/svg","path"),i=t.split(" "),s=parseInt(i[0],10);i.shift(),i.shift();var a=i.join(" ");n.setAttribute("stroke",a),n.setAttribute("stroke-width",s),n.setAttribute("shape-rendering","geometricPrecision"),n.setAttribute("data-stroke-style",t),Q(o={elem:n,command:"",renderedCommand:""});for(var l=null,c=r.firstElementChild;c;){if(-1===J(t,c.getAttribute("data-stroke-style"))){l=c;break}c=c.nextSibling}l?r.insertBefore(n,l):r.appendChild(n),e.set(t,o)}return o}function ee(e){for(var t="M ",r=0;r<e.length;r++)t+=r>1&&r%2==0?" L ".concat(e[r]):" ".concat(e[r]);return t}function te(e){return"M ".concat(e.join(" "))}function re(e){for(var t=[],r=new Map,o=0;o<e.length;o++){var n=e[o],i=n[1]===n[3]?"h-".concat(n[1]):"v-".concat(n[0]),s=r.get(i);s?s.push(n):r.set(i,[n])}return r.forEach(e=>{e.length>1?t.push(...function(e){var t=0;do{var r=e[t],o=r[1]===r[3],n=void 0;do{n=!1;for(var i=t+1;i<e.length;i++){var s=e[i];if(!s.deleted&&(l=r,n=oe((c=s)[0],c[1],l)||oe(c[2],c[3],l))){var a=ie(r,s);o?(r[0]=a.low[0],r[2]=a.high[0]):(r[1]=a.low[1],r[3]=a.high[1]),s.deleted=!0;break}}}while(n);t+=1}while(t<e.length);var l,c;return e.filter(e=>!e.deleted)}(e)):t.push(e[0])}),t}function oe(e,t,r){return!!ne(e,r[0],r[2])&&ne(t,r[1],r[3])}function ne(e,t,r){return r>t?e>=t&&e<=r:e>=r&&e<=t}function ie(e,t){var r,o,n=se(e),i=se(t);return o=i,le((r=n).low,o.low,Math.min),le(r.high,o.high,Math.max),r}function se(e){return{low:ae(e,Math.min),high:ae(e,Math.max)}}function ae(e,t){return[t(e[0],e[2]),t(e[1],e[3])]}function le(e,t,r){e[0]=r(e[0],t[0]),e[1]=r(e[1],t[1])}function ce(e){for(var t=new he,r=[],o=0;o<e.length;o++){var n=e[o],i=t.insertValue({pathContext:n,reversed:!1});t.insertValueIdAtPoint(i,de(n));var s=t.insertValue({pathContext:n,reversed:!0});t.insertValueIdAtPoint(s,ue(n))}var a=[];t.forEachPoint((e,o)=>{if(o.length>0){var n=[];if(ge(t,e[0],n,e[0]),n.endless)r.push(n);else{n.reverse();var i=n[0];n.forEach(e=>{e.reversed=!e.reversed});var s=n.pop();ge(t,s,n,i),n.length>1?(n.endless=(l=pe(n[0],!0),c=pe(n[n.length-1]),l[0]===c[0]&&l[1]===c[1]),r.push(n)):a.push(n[0].pathContext)}}var l,c});var l=[];return r.forEach(e=>{l.push(function(e){var t=[];t.push(...fe(e[0].pathContext,!e[0].reversed));for(var r=0;r<e.length;r++)t.push(...fe(e[r].pathContext,e[r].reversed));return t}(e))}),a.forEach(e=>{l.push(e)}),l}function he(){this.reset()}function de(e){return e.slice(0,2)}function ue(e){return e.slice(2,4)}function ge(e,t,r,o){for(;t;){r.push(t);var n=fe(t.pathContext,t.reversed),i=e.getElementAtPoint(n);if(!i||0===i.length)break;var s=me(i.map(t=>e.values[t]),t.pathContext);if(i.splice(0,2),!s)break;if(s.pathContext===o.pathContext){r.length>1&&(r.endless=!0);break}t=s}}function fe(e,t){return t?de(e):ue(e)}function me(e,t){return e[0].pathContext===t?e[1]:e[0]}function pe(e,t){return t===e.reversed?ue(e.pathContext):de(e.pathContext)}function ve(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}he.prototype.reset=function(){this.insertedCount=0,this.graph={},this.index={},this.values=[]},he.prototype.insertValue=function(e){return this.values.push(e),this.values.length-1},he.prototype.insertValueIdAtPoint=function(e,t){var r=t[0],o=t[1];this.graph[r]||(this.graph[r]={});var n,i,s=this.graph[r];o in s?(i=s[o],(n=this.index[i]).push(e)):(i=this.insertedCount,s[o]=i,this.insertedCount+=1,n=[e],this.index[i]=n)},he.prototype.forEachPoint=function(e){for(var t=0;t<this.insertedCount;t++){var r=this.index[t];r&&r.length>0&&e(r.map(e=>this.values[e]),r)}},he.prototype.getIdOfPoint=function(e){var t=this.graph[e[0]];if(t){var r=t[e[1]];if(r>=0)return r}},he.prototype.getElementAtPoint=function(e){var t=this.getIdOfPoint(e);if(t>=0)return this.index[t]};class we{constructor(e){ve(this,"rootDocument",void 0),ve(this,"uniqueDomInnerClipId",void 0),ve(this,"svg",void 0),ve(this,"rect",void 0),this.rootDocument=e,this.uniqueDomInnerClipId="".concat(Object(j.randomString)(),"-inner-clip"),this.createElement()}createSvgGroup(e){var t=this.rootDocument.createElementNS("http://www.w3.org/2000/svg","g");return e&&t.setAttribute("clip-path","url(#".concat(this.uniqueDomInnerClipId)),this.svg.appendChild(t),t}setClipAttributes(e){Object.keys(e).forEach(t=>{this.rect.setAttribute(t,e[t])})}createElement(){if(void 0===this.svg){var e=this.rootDocument.createElementNS("http://www.w3.org/2000/svg","svg");e.style.top="0",e.style.left="0",e.style.width="0",e.style.height="0",e.style.position="absolute",e.style.zIndex="5",e.setAttribute("pointer-events","none"),e.setAttribute("class","wtBorders");var t=this.rootDocument.createElementNS("http://www.w3.org/2000/svg","defs"),r=this.rootDocument.createElementNS("http://www.w3.org/2000/svg","clipPath"),o=this.rootDocument.createElementNS("http://www.w3.org/2000/svg","rect");r.setAttribute("id",this.uniqueDomInnerClipId),r.appendChild(o),t.appendChild(r),e.appendChild(t),this.rect=o,this.svg=e}}}var be=-O.a;class Ce{constructor(e,t,r){var o,n,i;this.overlayName=t,this.getCellFn=r,this.svgElement=new we(e.ownerDocument),this.svgResizer=(o=this.svgElement.svg,(e,t)=>{e=Math.max(e,0),t=Math.max(t,0),e===n&&t===i||(o.setAttribute("viewBox","0 0 ".concat(e," ").concat(t)),e!==n&&(o.style.width="".concat(e,"px"),o.setAttribute("width",e),n=e),t!==i&&(o.style.height="".concat(t,"px"),o.setAttribute("height",t),i=t))}),this.pathGroups=[],this.containerBoundingRect=null,this.maxSvgWidth=0,this.maxSvgHeight=0,this.rootWindow=e.ownerDocument.defaultView,e.appendChild(this.svgElement.svg)}ensurePathGroup(e){var t=this.pathGroups[e];if(!t){this.pathGroups.length<e&&this.ensurePathGroup(e-1);var r=0===e,o={svgPathsRenderer:this.getSvgPathsRendererForGroup(r),stylesAndLines:new Map,styles:[],commands:[]};return this.pathGroups[e]=o,o}return t}render(e,t,r){this.containerBoundingRect=Object(g.getBoundingClientRect)(e),this.pathGroups.forEach(e=>e.stylesAndLines.clear());for(var o=e.querySelector("tbody td"),n=o?Object(g.getBoundingClientRect)(o):null,i=0;i<r.length;i++)this.convertBorderEdgesDescriptorToLines(r[i]);this.pathGroups.forEach(e=>this.convertLinesToCommands(e));var s=Math.min(this.maxSvgWidth,this.containerBoundingRect.width),a=Math.min(this.maxSvgHeight,this.containerBoundingRect.height);if(this.svgResizer(s,a),n){var l=t.left+n.left-this.containerBoundingRect.left+be,c=t.top+n.top-this.containerBoundingRect.top+be;this.svgElement.setClipAttributes({width:Math.max(s-l-t.right,0),height:Math.max(a-c-t.bottom,0),x:l,y:c})}this.pathGroups.forEach(e=>e.svgPathsRenderer(e.styles,e.commands))}sumArrayElementAtIndex(e,t){return e.reduce((e,r)=>Math.max(e,r[t]),0)}setIn2dMap(e,t,r,o){var n=e.get(t);n?n.set(r,o):e.set(t,new Map([[r,o]]))}adjustTipsOfLines(e,t,r,o){if(0!==e.length)for(var n=0;n<e.length;n++){var i,s,a=e[n],l=a[0]===a[2],c=l?r:o,h=l?o:r,d=l?1:0,u=a.length,g=u-2,f=u-1,m=l?f:g,p=null===(i=c.get(a[0]))||void 0===i?void 0:i.get(a[1]),v=null===(s=c.get(a[g]))||void 0===s?void 0:s.get(a[f]);if(t>1)for(var w=0;w<u;w+=2)this.setIn2dMap(h,a[w],a[w+1],t);if(p&&(a[d]-=Math.floor(p/2)),a[m]+=O.a,v){var b=v%2==0?-1:0;a[m]+=Math.floor(v/2)+b}}}convertLinesToCommands(e){var{stylesAndLines:t,commands:r}=e,o=[...t.keys()],n=new Map,i=new Map;r.length=0,e.styles=o.sort(J),e.styles.forEach(e=>{var o=t.get(e),s=parseInt(e,10);this.adjustTipsOfLines(o,s,n,i);var a=function(e,t){for(var r="",o=-1,n=-1,i=-1,s=-1,a=0;a<e.length;a++){var l=e[a];0===a&&(o=l[0],n=l[1]);var c=l.length;i=l[c-2],s=l[c-1],a>0&&(r+=" "),r+=Y(l)}var h=e[0],d=e[e.length-1];if(o!==i||n!==s){if(1===t){var u=d.length;r="M ".concat(h[2]," ").concat(h[3]," ").concat(r.substring(2)," ").concat(d[u-4]," ").concat(d[u-3])}}else r+=" Z";return r}(function(e){return ce(re(e))}(function(e,t){for(var r=new Array(t.length),o=e%2!=0,n=0;n<t.length;n++){var[i,s,a,l]=t[n];if(o)s===l?(s+=.5,l+=.5):(i+=.5,a+=.5);r[n]=[i,s,a,l]}return r}(s,o))),l=Math.ceil(s/2),c=this.sumArrayElementAtIndex(o,2)+l,h=this.sumArrayElementAtIndex(o,3)+l;c>this.maxSvgWidth&&(this.maxSvgWidth=c),h>this.maxSvgHeight&&(this.maxSvgHeight=h),r.push(a)})}getSvgPathsRendererForGroup(e){return function(e){Y||(Y=function(e){var t=e.createElementNS("http://www.w3.org/2000/svg","path");return t.setAttribute("d","M 0 0 10 0"),"M 0 0 10 0"!==t.getAttribute("d")}(e.ownerDocument)?ee:te),e.setAttribute("fill","none");var t=new Map;return(r,o)=>{t.forEach(Q);for(var n=0;n<r.length;n++){$(t,r[n],e).command=o[n]}t.forEach(Z)}}(this.svgElement.createSvgGroup(e))}getLayerNumber(e){switch(e.className){case"current":return 3;case"area":return 2;case"fill":return 1;default:return 0}}convertBorderEdgesDescriptorToLines(e){var{settings:t,selectionStart:r,selectionEnd:o,hasTopEdge:n,hasRightEdge:i,hasBottomEdge:s,hasLeftEdge:a}=e,l=this.getLayerNumber(t),c=this.ensurePathGroup(l).stylesAndLines,h=r.row===o.row&&r.col===o.col,d=0,u=0,f=this.getCellFn(r);if(-1===f&&(r.row+=1,f=this.getCellFn(r),u=-1),-2===f&&(r.row-=1,f=this.getCellFn(r),u=1),-4===f&&(r.col-=1,f=this.getCellFn(r),d=1),"object"==typeof f){var m,p=0,v=0;if(h?(m=f,p=d,v=u):(-1===(m=this.getCellFn(o))&&(o.row+=1,m=this.getCellFn(o),v=-1),-2===m&&(o.row-=1,m=this.getCellFn(o),v=1),-4===m&&(o.col-=1,m=this.getCellFn(o),p=1)),"object"==typeof m){var w=Object(g.getBoundingClientRect)(f),b=f===m?w:Object(g.getBoundingClientRect)(m),C=w.left,E=w.top,O=b.left+b.width,y=b.top+b.height;C+=d*w.width,E+=u*w.height,O+=p*b.width,y+=v*b.height;var R=!("TH"===f.nodeName||f.previousElementSibling),T=!f.parentNode.previousElementSibling&&!f.parentNode.parentNode.previousElementSibling.firstElementChild;if(R||(C+=be),T||(E+=be),C+=-this.containerBoundingRect.left,E+=-this.containerBoundingRect.top,O+=be-this.containerBoundingRect.left,y+=be-this.containerBoundingRect.top,t.border&&t.border.width&&"inside"===t.border.strokeAlignment){var S=Math.floor(t.border.width/2),M=Math.ceil(t.border.width/2)-1;C+=S,E+=S,O-=M,y-=M}if("bottom"!==this.overlayName&&"bottom_left_corner"!==this.overlayName||-1===y&&(y=0),n&&this.hasLineAtEdge(t,"top"))this.getLines(c,t,"top").push([C,E,O,E]);if(i&&this.hasLineAtEdge(t,"right"))this.getLines(c,t,"right").push([O,E,O,y]);if(s&&this.hasLineAtEdge(t,"bottom"))this.getLines(c,t,"bottom").push([C,y,O,y]);if(a&&this.hasLineAtEdge(t,"left"))this.getLines(c,t,"left").push([C,E,C,y])}}}hasLineAtEdge(e,t){return!(e[t]&&e[t].hide)}getLines(e,t,r){var o=1;t[r]&&void 0!==t[r].width?o=t[r].width:t.border&&void 0!==t.border.width&&(o=t.border.width);var n=t[r]&&t[r].color||t.border&&t.border.color||"black",i="".concat(o,"px solid ").concat(n),s=e.get(i);if(s)return s;var a=[];return e.set(i,a),a}}var Ee=r(32),Oe=r(33),ye=r(74);var Re=class{constructor(e){this.wot=e;var{rootDocument:t,rootWindow:r,wtTable:o}=this.wot,n=parseInt(Object(g.getComputedStyle)(t.body,r).lineHeight,10),i=1.2*parseInt(Object(g.getComputedStyle)(t.body,r).fontSize,10);this.eventManager=new I.a(this.wot),this.scrollbarSize=Object(g.getScrollbarWidth)(t),this.wot.update("scrollbarWidth",this.scrollbarSize),this.wot.update("scrollbarHeight",this.scrollbarSize);var s="hidden"===Object(g.getComputedStyle)(o.wtRootElement.parentNode,r).getPropertyValue("overflow");this.scrollableElement=s?o.holder:Object(g.getScrollableElement)(o.TABLE),this.topOverlay=void 0,this.bottomOverlay=void 0,this.leftOverlay=void 0,this.topLeftCornerOverlay=void 0,this.bottomLeftCornerOverlay=void 0,this.prepareOverlays(),this.hasScrollbarBottom=!1,this.hasScrollbarRight=!1,this.destroyed=!1,this.keyPressed=!1,this.spreaderLastSize={width:null,height:null},this.verticalScrolling=!1,this.horizontalScrolling=!1,this.browserLineHeight=n||i,this.registerListeners(),this.lastScrollX=r.scrollX,this.lastScrollY=r.scrollY}prepareOverlays(){this.topOverlay?this.topOverlay.updateStateOfRendering():this.topOverlay=q.createOverlay(q.CLONE_TOP,this.wot),q.hasOverlay(q.CLONE_BOTTOM)||(this.bottomOverlay={needFullRender:!1,updateStateOfRendering:()=>{}}),q.hasOverlay(q.CLONE_BOTTOM_LEFT_CORNER)||(this.bottomLeftCornerOverlay={needFullRender:!1,updateStateOfRendering:()=>{}}),this.bottomOverlay?this.bottomOverlay.updateStateOfRendering():this.bottomOverlay=q.createOverlay(q.CLONE_BOTTOM,this.wot),this.leftOverlay?this.leftOverlay.updateStateOfRendering():this.leftOverlay=q.createOverlay(q.CLONE_LEFT,this.wot),this.topOverlay.needFullRender&&this.leftOverlay.needFullRender&&(this.topLeftCornerOverlay?this.topLeftCornerOverlay.updateStateOfRendering():this.topLeftCornerOverlay=q.createOverlay(q.CLONE_TOP_LEFT_CORNER,this.wot)),this.bottomOverlay.needFullRender&&this.leftOverlay.needFullRender&&(this.bottomLeftCornerOverlay?this.bottomLeftCornerOverlay.updateStateOfRendering():this.bottomLeftCornerOverlay=q.createOverlay(q.CLONE_BOTTOM_LEFT_CORNER,this.wot))}refreshMasterAndClones(){this.wot.drawn&&(this.wot.wtTable.holder.parentNode?(this.wot.draw(!0),this.verticalScrolling&&this.leftOverlay.onScroll(),this.horizontalScrolling&&this.topOverlay.onScroll(),this.verticalScrolling=!1,this.horizontalScrolling=!1):this.destroy())}registerListeners(){var{rootDocument:e,rootWindow:t}=this.wot,{mainTableScrollableElement:r}=this.topOverlay,{mainTableScrollableElement:o}=this.leftOverlay;this.eventManager.addEventListener(e.documentElement,"keydown",e=>this.onKeyDown(e)),this.eventManager.addEventListener(e.documentElement,"keyup",()=>this.onKeyUp()),this.eventManager.addEventListener(e,"visibilitychange",()=>this.onKeyUp()),this.eventManager.addEventListener(r,"scroll",e=>this.onTableScroll(e),{passive:!0}),r!==o&&this.eventManager.addEventListener(o,"scroll",e=>this.onTableScroll(e),{passive:!0});var n,i=t.devicePixelRatio&&t.devicePixelRatio>1,s=this.scrollableElement===t,a=this.wot.wtSettings.getSetting("preventWheel"),l={passive:s};(a||i||!Object(V.isChrome)())&&this.eventManager.addEventListener(this.wot.wtTable.wtRootElement,"wheel",e=>this.onCloneWheel(e,a),l),[this.topOverlay,this.bottomOverlay,this.leftOverlay,this.topLeftCornerOverlay,this.bottomLeftCornerOverlay].forEach(e=>{if(e&&e.needFullRender){var{holder:t}=e.clone.wtTable;this.eventManager.addEventListener(t,"wheel",e=>this.onCloneWheel(e,a),l)}}),this.eventManager.addEventListener(t,"resize",()=>{clearTimeout(n),n=setTimeout(()=>{this.wot.getSetting("onWindowResize")},200)})}deregisterListeners(){this.eventManager.clearEvents(!0)}onTableScroll(e){var t=this.wot.rootWindow,r=this.leftOverlay.mainTableScrollableElement,o=this.topOverlay.mainTableScrollableElement,n=e.target;this.keyPressed&&(o!==t&&n!==t&&!e.target.contains(o)||r!==t&&n!==t&&!e.target.contains(r))||(this.propagateMasterScrollPositionsToClones(),this.refreshMasterAndClones())}onCloneWheel(e,t){var{rootWindow:r}=this.wot,o=this.leftOverlay.mainTableScrollableElement,n=this.topOverlay.mainTableScrollableElement,i=e.target,s=n!==r&&i!==r&&!i.contains(n),a=o!==r&&i!==r&&!i.contains(o);if(!this.keyPressed||!s&&!a){var l=this.translateMouseWheelToScroll(e);(t||this.scrollableElement!==r&&l)&&e.preventDefault()}}onKeyDown(e){this.keyPressed=Object(ye.isKey)(e.keyCode,"ARROW_UP|ARROW_RIGHT|ARROW_DOWN|ARROW_LEFT")}onKeyUp(){this.keyPressed=!1}translateMouseWheelToScroll(e){var t=this.browserLineHeight,r=isNaN(e.deltaY)?-1*e.wheelDeltaY:e.deltaY,o=isNaN(e.deltaX)?-1*e.wheelDeltaX:e.deltaX;1===e.deltaMode&&(o+=o*t,r+=r*t);var n=this.scrollVertically(r),i=this.scrollHorizontally(o);return n||i}scrollVertically(e){var t=this.scrollableElement.scrollTop;return this.scrollableElement.scrollTop+=e,t!==this.scrollableElement.scrollTop}scrollHorizontally(e){var t=this.scrollableElement.scrollLeft;return this.scrollableElement.scrollLeft+=e,t!==this.scrollableElement.scrollLeft}propagateMasterScrollPositionsToClones(){if(!this.destroyed){var{rootWindow:e}=this.wot,t=this.topOverlay.clone.wtTable.holder,r=this.leftOverlay.clone.wtTable.holder,o=this.bottomOverlay.clone.wtTable.holder,[n,i]=[this.scrollableElement.scrollLeft,this.scrollableElement.scrollTop];this.horizontalScrolling=t.scrollLeft!==n||this.lastScrollX!==e.scrollX,this.verticalScrolling=r.scrollTop!==i||this.lastScrollY!==e.scrollY,this.lastScrollX=e.scrollX,this.lastScrollY=e.scrollY,this.horizontalScrolling&&(this.topOverlay.needFullRender&&(t.scrollLeft=n),this.bottomOverlay.needFullRender&&(o.scrollLeft=n)),this.verticalScrolling&&this.leftOverlay.needFullRender&&(r.scrollTop=i)}}updateMainScrollableElements(){this.deregisterListeners(),this.leftOverlay.updateMainScrollableElement(),this.topOverlay.updateMainScrollableElement(),this.bottomOverlay.needFullRender&&this.bottomOverlay.updateMainScrollableElement();var{rootWindow:e,wtTable:t}=this.wot;"hidden"===Object(g.getComputedStyle)(t.wtRootElement.parentNode,e).getPropertyValue("overflow")?this.scrollableElement=t.holder:this.scrollableElement=Object(g.getScrollableElement)(t.TABLE),this.registerListeners()}destroy(){this.eventManager.destroy(),this.topOverlay.destroy(),this.bottomOverlay.clone&&this.bottomOverlay.destroy(),this.leftOverlay.destroy(),this.topLeftCornerOverlay&&this.topLeftCornerOverlay.destroy(),this.bottomLeftCornerOverlay&&this.bottomLeftCornerOverlay.clone&&this.bottomLeftCornerOverlay.destroy(),this.destroyed=!0}refreshClones(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(e||this.prepareOverlays(),this.bottomOverlay.clone&&this.bottomOverlay.redrawClone(e),this.leftOverlay.redrawClone(e),this.topOverlay.redrawClone(e),this.topLeftCornerOverlay&&this.topLeftCornerOverlay.redrawClone(e),this.bottomLeftCornerOverlay&&this.bottomLeftCornerOverlay.clone&&this.bottomLeftCornerOverlay.redrawClone(e),this.topOverlay.areElementSizesAdjusted&&this.leftOverlay.areElementSizesAdjusted){var t=this.wot.wtTable.wtRootElement.parentNode||this.wot.wtTable.wtRootElement,r=Object(g.clientWidth)(t),o=Object(g.clientHeight)(t);r===this.spreaderLastSize.width&&o===this.spreaderLastSize.height||(this.spreaderLastSize.width=r,this.spreaderLastSize.height=o,this.adjustElementsSizes())}else e||this.adjustElementsSizes()}adjustElementsSizes(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],{wtViewport:t,wtTable:r}=this.wot,o=this.wot.getSetting("totalColumns"),n=this.wot.getSetting("totalRows"),i=t.getRowHeaderWidth(),s=t.getColumnHeaderHeight(),a=r.hider.style,l=i||O.a,c=s||O.a;if(a.width="".concat(l+this.leftOverlay.sumCellSizes(0,o),"px"),a.height="".concat(c+this.topOverlay.sumCellSizes(0,n),"px"),this.scrollbarSize>0){var{scrollHeight:h,scrollWidth:d}=r.wtRootElement,{scrollHeight:u,scrollWidth:f}=r.holder;this.hasScrollbarRight=h<u,this.hasScrollbarBottom=d<f,this.hasScrollbarRight&&Object(g.scrollWidth)(r.hider)+this.scrollbarSize>d?this.hasScrollbarBottom=!0:this.hasScrollbarBottom&&Object(g.scrollHeight)(r.hider)+this.scrollbarSize>h&&(this.hasScrollbarRight=!0)}this.topOverlay.adjustElementsSize(e),this.leftOverlay.adjustElementsSize(e),this.bottomOverlay.adjustElementsSize(e),this.topLeftCornerOverlay&&this.topLeftCornerOverlay.adjustElementsSize(e),this.bottomLeftCornerOverlay&&this.bottomLeftCornerOverlay.adjustElementsSize(e)}getParentOverlay(e){if(!e)return null;var t=[this.topOverlay,this.leftOverlay,this.bottomOverlay,this.topLeftCornerOverlay,this.bottomLeftCornerOverlay],r=null;return Object(Oe.arrayEach)(t,t=>{t&&t.clone&&t.clone.wtTable.TABLE.contains(e)&&(r=t.clone)}),r}syncOverlayTableClassNames(){var e=this.wot.wtTable.TABLE,t=[this.topOverlay,this.leftOverlay,this.bottomOverlay,this.topLeftCornerOverlay,this.bottomLeftCornerOverlay];Object(Oe.arrayEach)(t,t=>{t&&(t.clone.wtTable.TABLE.className=e.className)})}};var Te=class{constructor(e,t){this.wot=e,this.defaults={table:void 0,externalRowCalculator:!1,stretchH:"none",currentRowClassName:null,currentColumnClassName:null,preventOverflow:()=>!1,preventWheel:!1,data:void 0,freezeOverlays:!1,fixedColumnsLeft:0,fixedRowsTop:0,fixedRowsBottom:0,minSpareRows:0,rowHeaders:()=>[],columnHeaders:()=>[],totalRows:void 0,totalColumns:void 0,cellRenderer:(e,t,r)=>{var o=this.getSetting("data",e,t);Object(g.fastInnerText)(r,null==o?"":o)},columnWidth(){},rowHeight(){},defaultRowHeight:23,defaultColumnWidth:50,selections:null,hideBorderOnMouseDownOver:!1,viewportRowCalculatorOverride:null,viewportColumnCalculatorOverride:null,onCellMouseDown:null,onCellContextMenu:null,onCellMouseOver:null,onCellMouseOut:null,onCellMouseUp:null,onCellDblClick:null,onCellCornerMouseDown:null,onCellCornerDblClick:null,beforeDraw:null,onDraw:null,onBeforeRemoveCellClassNames:null,onAfterDrawSelection:null,onBeforeDrawBorders:null,onScrollVertically:null,onScrollHorizontally:null,onBeforeTouchScroll:null,onAfterMomentumScroll:null,onBeforeStretchingColumnWidth:e=>e,onModifyRowHeaderWidth:null,onModifyGetCellCoords:null,onWindowResize:null,scrollbarWidth:10,scrollbarHeight:10,renderAllRows:!1,groups:!1,rowHeaderWidth:null,columnHeaderHeight:null,headerClassName:null},this.settings={},Object(Ee.objectEach)(this.defaults,(e,r)=>{if(void 0!==t[r])this.settings[r]=t[r];else{if(void 0===e)throw new Error('A required setting "'.concat(r,'" was not provided'));this.settings[r]=e}})}update(e,t){return void 0===t?Object(Ee.objectEach)(e,(e,t)=>{this.settings[t]=e}):this.settings[e]=t,this.wot}getSetting(e,t,r,o,n){return"function"==typeof this.settings[e]?this.settings[e](t,r,o,n):void 0!==t&&Array.isArray(this.settings[e])?this.settings[e][t]:this.settings[e]}has(e){return!!this.settings[e]}};var Se=class{constructor(e){this.wot=e,this.oversizedRows=[],this.oversizedColumnHeaders=[],this.hasOversizedColumnHeadersMarked=!1,this.clientHeight=this._getWorkspaceHeight(),this.containerWidth=NaN,this.rowHeaderWidth=NaN,this.rowsVisibleCalculator=null,this.columnsVisibleCalculator=null,this.eventManager=new I.a(this.wot),this.eventManager.addEventListener(this.wot.rootWindow,"resize",()=>{this.clientHeight=this._getWorkspaceHeight()})}getWorkspaceHeight(){return this.clientHeight}_getWorkspaceHeight(){var e=this.wot.rootDocument,t=this.wot.wtTable.trimmingContainer,r=0;t===this.wot.rootWindow?r=Object(g.clientHeight)(e.documentElement):r=Object(g.outerHeight)(t)>0&&Object(g.clientHeight)(t)>0?Object(g.clientHeight)(t):1/0;return r}getWorkspaceWidth(){return window.innerWidth}hasVerticalScroll(){return this.getWorkspaceActualHeight()>this.getWorkspaceHeight()}hasHorizontalScroll(){return this.getWorkspaceActualWidth()>this.getWorkspaceWidth()}sumColumnWidths(e,t){for(var r=0,o=e;o<t;)r+=this.wot.columnUtils.getWidth(o),o+=1;return r}getContainerFillWidth(){if(this.containerWidth)return this.containerWidth;var e=this.wot.wtTable.holder,t=this.wot.rootDocument.createElement("div");t.style.width="100%",t.style.height="1px",e.appendChild(t);var r=Object(g.offsetWidth)(t);return this.containerWidth=r,e.removeChild(t),r}getWorkspaceOffset(){return Object(g.offset)(this.wot.wtTable.TABLE)}getWorkspaceActualHeight(){return Object(g.outerHeight)(this.wot.wtTable.TABLE)}getWorkspaceActualWidth(){var{wtTable:e}=this.wot;return Object(g.outerWidth)(e.TABLE)||Object(g.outerWidth)(e.TBODY)||Object(g.outerWidth)(e.THEAD)}getColumnHeaderHeight(){return this.columnHeaderHeight||(this.wot.getSetting("columnHeaders").length?this.columnHeaderHeight||(this.columnHeaderHeight=Object(g.outerHeight)(this.wot.wtTable.THEAD)):this.columnHeaderHeight=0),this.columnHeaderHeight}getViewportHeight(){var e=this.getWorkspaceHeight();if(e===1/0)return e;var t=this.getColumnHeaderHeight();return t>0&&(e-=t),e}getRowHeaderWidth(){return this.rowHeaderWidth=50,this.rowHeaderWidth}getViewportWidth(){var e=this.getWorkspaceWidth();if(e===1/0)return e;var t=this.getRowHeaderWidth();return t>0?e-t:e-O.a}createRowsCalculator(){var e,t,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,{wot:o}=this,{wtSettings:n,wtOverlays:i,wtTable:s,rootDocument:a}=o;this.rowHeaderWidth=NaN,e=n.settings.renderAllRows&&1===r?1/0:this.getViewportHeight();var c=i.topOverlay.getScrollPosition()-i.topOverlay.getTableParentOffset();c<0&&(c=0);var h=o.getSetting("fixedRowsTop"),d=o.getSetting("fixedRowsBottom"),u=o.getSetting("totalRows");return h&&(c+=t=i.topOverlay.sumCellSizes(0,h),e-=t),d&&i.bottomOverlay.clone&&(e-=t=i.bottomOverlay.sumCellSizes(u-d,u)),0,new l({viewportSize:e,scrollOffset:c,totalItems:o.getSetting("totalRows"),itemSizeFn:e=>o.rowUtils.getHeight(e),overrideFn:n.settings.viewportRowCalculatorOverride,calculationType:r,scrollbarHeight:0})}createColumnsCalculator(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,{wot:t}=this,{wtSettings:r,wtOverlays:o,wtTable:n,rootDocument:s}=t,a=this.getViewportWidth(),l=o.leftOverlay.getScrollPosition()-o.leftOverlay.getTableParentOffset();l<0&&(l=0);var c=t.getSetting("fixedColumnsLeft");if(c){var h=o.leftOverlay.sumCellSizes(0,c);l+=h,a-=h}return new i({viewportSize:a,scrollOffset:l,totalItems:t.getSetting("totalColumns"),itemSizeFn:e=>t.columnUtils.getWidth(e),overrideFn:r.settings.viewportColumnCalculatorOverride,calculationType:e,stretchMode:t.getSetting("stretchH"),stretchingItemWidthFn:(e,r)=>t.getSetting("onBeforeStretchingColumnWidth",e,r)})}createRenderCalculators(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(e){var t=this.createRowsCalculator(2),r=this.createColumnsCalculator(2);this.areAllProposedVisibleRowsAlreadyRendered(t)&&this.areAllProposedVisibleColumnsAlreadyRendered(r)||(e=!1)}return e||(this.rowsRenderCalculator=this.createRowsCalculator(1),this.columnsRenderCalculator=this.createColumnsCalculator(1)),this.rowsVisibleCalculator=null,this.columnsVisibleCalculator=null,e}createVisibleCalculators(){this.rowsVisibleCalculator=this.createRowsCalculator(2),this.columnsVisibleCalculator=this.createColumnsCalculator(2)}areAllProposedVisibleRowsAlreadyRendered(e){if(!this.rowsVisibleCalculator)return!1;var{startRow:t,endRow:r}=e,{startRow:o,endRow:n}=this.rowsRenderCalculator;return!(t<o||t===o&&t>0)&&!(r>n||r===n&&r<this.wot.getSetting("totalRows")-1)}areAllProposedVisibleColumnsAlreadyRendered(e){if(!this.columnsVisibleCalculator)return!1;var{startColumn:t,endColumn:r}=e,{startColumn:o,endColumn:n}=this.columnsRenderCalculator;return!(t<o||t===o&&t>0)&&!(r>n||r===n&&r<this.wot.getSetting("totalColumns")-1)}resetHasOversizedColumnHeadersMarked(){this.hasOversizedColumnHeadersMarked=!1}};class Me{constructor(e){this.wot=e,this.headerWidths=new Map}getWidth(e){var t=this.wot.wtSettings.settings.columnWidth;return"function"==typeof t?t=t(e):"object"==typeof t&&(t=t[e]),t||this.wot.wtSettings.settings.defaultColumnWidth}getStretchedColumnWidth(e){var t=this.getWidth(e),r=this.wot.wtViewport.columnsRenderCalculator,o=null==t?this.wot.wtSettings.settings.defaultColumnWidth:t;if(r){var n=r.getStretchedColumnWidth(e,o);n&&(o=n)}return o}getHeaderHeight(e){var t=this.wot.wtSettings.settings.defaultRowHeight,r=this.wot.wtViewport.oversizedColumnHeaders[e];return void 0!==r&&(t=t?Math.max(t,r):r),t}getHeaderWidth(e){return this.headerWidths.get(this.wot.wtTable.columnFilter.sourceToRendered(e))}calculateWidths(){var{wot:e}=this,t=e.getSetting("rowHeaderWidth");null==(t=e.getSetting("onModifyRowHeaderWidth",t))&&(t=50);for(var r=e.getSetting("rowHeaders").length,o=e.getSetting("defaultColumnWidth"),n=0;n<r;n++){var i=Array.isArray(t)?t[n]:t;i=null==i?o:i,this.headerWidths.set(n,i)}}}class Ne{constructor(e){this.wot=e}getHeight(e){var t=this.wot.wtSettings.settings.rowHeight(e),r=this.wot.wtViewport.oversizedRows[e];return void 0!==r&&(t=void 0===t?r:Math.max(t,r)),t}}var He=class extends P{constructor(e){super(e),this.wtSettings=new Te(this,e),this.rowUtils=new Ne(this),this.columnUtils=new Me(this),this.wtTable=new _e(this,e.table),this.wtScroll=new U(this),this.wtViewport=new Se(this),this.wtEvent=new z(this),this.selections=this.getSetting("selections"),this.wtOverlays=new Re(this),this.exportSettingsAsClassNames();var t=[];if(this.wtTable.THEAD.childNodes.length&&this.wtTable.THEAD.childNodes[0].childNodes.length){for(var r=0,o=this.wtTable.THEAD.childNodes[0].childNodes.length;r<o;r++)t.push(this.wtTable.THEAD.childNodes[0].childNodes[r].innerHTML);this.getSetting("columnHeaders").length||this.update("columnHeaders",[function(e,r){Object(g.fastInnerText)(r,t[e])}])}this.drawn=!1,this.drawInterrupted=!1}draw(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return Object(g.clearMemoizedFunctionsBeforeRender)(),this.drawInterrupted=!1,e||this.wtTable.isVisible()?(this.wtTable.draw(e),this.drawn=!0):this.drawInterrupted=!0,this}getCell(e){if(!(arguments.length>1&&void 0!==arguments[1]&&arguments[1]))return this.wtTable.getCell(e);var t=this.wtSettings.getSetting("totalRows"),r=this.wtSettings.getSetting("fixedRowsTop"),o=this.wtSettings.getSetting("fixedRowsBottom"),n=this.wtSettings.getSetting("fixedColumnsLeft");if(e.row<r&&e.col<n)return this.wtOverlays.topLeftCornerOverlay.clone.wtTable.getCell(e);if(e.row<r)return this.wtOverlays.topOverlay.clone.wtTable.getCell(e);if(e.col<n&&e.row>=t-o){if(this.wtOverlays.bottomLeftCornerOverlay&&this.wtOverlays.bottomLeftCornerOverlay.clone)return this.wtOverlays.bottomLeftCornerOverlay.clone.wtTable.getCell(e)}else{if(e.col<n)return this.wtOverlays.leftOverlay.clone.wtTable.getCell(e);if(e.row<t&&e.row>=t-o&&this.wtOverlays.bottomOverlay&&this.wtOverlays.bottomOverlay.clone)return this.wtOverlays.bottomOverlay.clone.wtTable.getCell(e)}return this.wtTable.getCell(e)}update(e,t){return this.wtSettings.update(e,t)}scrollViewport(e,t,r,o,n){return!(e.col<0||e.row<0)&&this.wtScroll.scrollViewport(e,t,r,o,n)}scrollViewportHorizontally(e,t,r){return!(e<0)&&this.wtScroll.scrollViewportHorizontally(e,t,r)}scrollViewportVertically(e,t,r){return!(e<0)&&this.wtScroll.scrollViewportVertically(e,t,r)}getViewport(){return[this.wtTable.getFirstVisibleRow(),this.wtTable.getFirstVisibleColumn(),this.wtTable.getLastVisibleRow(),this.wtTable.getLastVisibleColumn()]}exportSettingsAsClassNames(){var e=[],t=[];Object(Ee.objectEach)({rowHeaders:"htRowHeaders",columnHeaders:"htColumnHeaders"},(r,o)=>{this.getSetting(o).length&&t.push(r),e.push(r)}),Object(g.removeClass)(this.wtTable.wtRootElement.parentNode,e),Object(g.addClass)(this.wtTable.wtRootElement.parentNode,t)}destroy(){this.wtOverlays.destroy(),this.wtEvent.destroy()}};var Le=class{constructor(e,t){this.isMaster=e instanceof He,this.wot=e,this.TABLE=t,this.TBODY=null,this.THEAD=null,this.COLGROUP=null,this.hasTableHeight=!0,this.hasTableWidth=!0,Object(g.removeTextNodes)(this.TABLE),this.spreader=this.createSpreader(this.TABLE),this.hider=this.createHider(this.spreader),this.holder=this.createHolder(this.hider),this.wtRootElement=this.holder.parentNode,this.fixTableDomTree(),this.rowFilter=null,this.columnFilter=null,this.correctHeaderWidth=!1,this.tableRenderer=new x({TABLE:this.TABLE,THEAD:this.THEAD,COLGROUP:this.COLGROUP,TBODY:this.TBODY,rowUtils:this.isMaster?this.wot.rowUtils:this.wot.overlay.master.rowUtils,columnUtils:this.isMaster?this.wot.columnUtils:this.wot.overlay.master.columnUtils,cellRenderer:this.wot.wtSettings.settings.cellRenderer}),this.borderRenderer=new Ce(this.spreader,this.isMaster?"master":this.wot.getOverlayName(),this.getCell.bind(this))}is(e){return!this.isMaster&&this.wot.getOverlayName()===e}createBorderPaddingObject(){var e=0,t=0,r=0,o=0;return(this.is(q.CLONE_LEFT)||this.is(q.CLONE_TOP_LEFT_CORNER)||this.is(q.CLONE_BOTTOM_LEFT_CORNER))&&(o=1,this.wot.getSetting("rowHeaders").length>0&&(t=1)),(this.is(q.CLONE_TOP)||this.is(q.CLONE_TOP_LEFT_CORNER))&&(r=1,this.wot.getSetting("columnHeaders").length>0&&(e=1)),(this.is(q.CLONE_BOTTOM)||this.is(q.CLONE_BOTTOM_LEFT_CORNER))&&(e=1),{top:e,left:t,bottom:r,right:o}}fixTableDomTree(){var e=this.wot.rootDocument;this.TBODY=this.TABLE.querySelector("tbody"),this.TBODY||(this.TBODY=e.createElement("tbody"),this.TABLE.appendChild(this.TBODY)),this.THEAD=this.TABLE.querySelector("thead"),this.THEAD||(this.THEAD=e.createElement("thead"),this.TABLE.insertBefore(this.THEAD,this.TBODY)),this.COLGROUP=this.TABLE.querySelector("colgroup"),this.COLGROUP||(this.COLGROUP=e.createElement("colgroup"),this.TABLE.insertBefore(this.COLGROUP,this.THEAD))}createSpreader(e){var t,r=e.parentNode;return r&&r.nodeType===Node.ELEMENT_NODE&&Object(g.hasClass)(r,"wtHolder")||((t=this.wot.rootDocument.createElement("div")).className="wtSpreader",r&&r.insertBefore(t,e),t.appendChild(e)),t.style.position="relative",t}createHider(e){var t,r=e.parentNode;return r&&r.nodeType===Node.ELEMENT_NODE&&Object(g.hasClass)(r,"wtHolder")||((t=this.wot.rootDocument.createElement("div")).className="wtHider",r&&r.insertBefore(t,e),t.appendChild(e)),t}createHolder(e){var t,r=e.parentNode;return r&&r.nodeType===Node.ELEMENT_NODE&&Object(g.hasClass)(r,"wtHolder")||((t=this.wot.rootDocument.createElement("div")).style.position="relative",t.className="wtHolder",r&&r.insertBefore(t,e),t.appendChild(e)),t}draw(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],{wot:t}=this,r=t.getSetting("totalRows"),o=t.getSetting("totalColumns"),n=t.getSetting("rowHeaders"),i=n.length,s=t.getSetting("columnHeaders"),a=s.length;if(!e){var l=r>0?this.getFirstRenderedRow():0,c=o>0?this.getFirstRenderedColumn():0;this.rowFilter=new u(l,r,a),this.columnFilter=new d(c,o,i),this.is(q.CLONE_BOTTOM)||this.is(q.CLONE_BOTTOM_LEFT_CORNER)?this.tableRenderer.setHeaderContentRenderers(n,[]):this.tableRenderer.setHeaderContentRenderers(n,s),this.is(q.CLONE_BOTTOM)&&this.resetOversizedRows(),this.tableRenderer.setViewportSize(this.getRenderedRowsCount(),this.getRenderedColumnsCount()).setFilters(this.rowFilter,this.columnFilter).render(),this.adjustColumnHeaderHeights(),this.is(q.CLONE_BOTTOM)&&this.markOversizedRows()}this.refreshSelections(e)}markIfOversizedColumnHeader(e){for(var t,r,o,n=this.columnFilter.renderedToSource(e),i=this.wot.getSetting("columnHeaders").length,s=this.wot.wtSettings.settings.defaultRowHeight,a=this.wot.getSetting("columnHeaderHeight")||[];i;)i-=1,t=this.wot.columnUtils.getHeaderHeight(i),(r=this.getColumnHeader(n,i))&&(o=Object(g.innerHeight)(r),(!t&&s<o||t<o)&&(this.wot.wtViewport.oversizedColumnHeaders[i]=o),Array.isArray(a)?null!==a[i]&&void 0!==a[i]&&(this.wot.wtViewport.oversizedColumnHeaders[i]=a[i]):isNaN(a)||(this.wot.wtViewport.oversizedColumnHeaders[i]=a),this.wot.wtViewport.oversizedColumnHeaders[i]<(a[i]||a)&&(this.wot.wtViewport.oversizedColumnHeaders[i]=a[i]||a))}adjustColumnHeaderHeights(){for(var{wot:e}=this,t=this.THEAD.childNodes,r=e.wtViewport.oversizedColumnHeaders,o=0,n=e.getSetting("columnHeaders").length;o<n;o++)if(r[o]){if(!t[o]||0===t[o].childNodes.length)return;t[o].childNodes[0].style.height="".concat(r[o],"px")}}resetOversizedRows(){var{wot:e}=this;if(!e.getSetting("externalRowCalculator"))for(var t=this.getRenderedRowsCount(),r=0;r<t;r++){var o=this.rowFilter.renderedToSource(r);e.wtViewport.oversizedRows&&e.wtViewport.oversizedRows[o]&&(e.wtViewport.oversizedRows[o]=void 0)}}removeClassFromCells(e){for(var t=this.TABLE.querySelectorAll(".".concat(e)),r=0,o=t.length;r<o;r++)Object(g.removeClass)(t[r],e)}refreshSelections(e){var{wot:t}=this;if(t.selections){var r=Array.isArray(t.selections)?t.selections:t.selections.getAll(),o=r.length;if(e){for(var n=new Set,i=t.getSetting("onBeforeRemoveCellClassNames"),s=0;s<o;s++){var{highlightHeaderClassName:a,highlightRowClassName:l,highlightColumnClassName:c}=r[s].settings;r[s].classNames.forEach(n.add,n),a&&n.add(a),l&&n.add(l),c&&n.add(c)}Array.isArray(i)&&i.forEach(n.add,n),n.forEach(this.removeClassFromCells,this)}var h=this.getRenderedRowsCount(),d=this.getRenderedColumnsCount(),u=this.getFirstRenderedRow(),g=this.getFirstRenderedColumn(),f=this.getLastRenderedRow(),m=this.getLastRenderedColumn(),p=[];(this.is(q.CLONE_LEFT)||this.is(q.CLONE_TOP_LEFT_CORNER)||this.is(q.CLONE_BOTTOM_LEFT_CORNER))&&(m+=1),(this.is(q.CLONE_TOP)||this.is(q.CLONE_TOP_LEFT_CORNER))&&(f+=1),(this.is(q.CLONE_BOTTOM)||this.is(q.CLONE_BOTTOM_LEFT_CORNER))&&(u-=1);for(var v=0;v<o;v++){var w=r[v].draw(t,h,d,u,g,f,m);w&&p.push(w)}this.wot.getSetting("columnHeaders"),this.borderRenderer.render(this.TABLE,this.createBorderPaddingObject(),p)}}getCell(e){var t,r=e.row,o=e.col,n=this.wot.getSetting("onModifyGetCellCoords",r,o);if(n&&Array.isArray(n)&&([r,o]=n),this.isRowBeforeRenderedRows(r))return-1;if(this.isRowAfterRenderedRows(r))return-2;if(this.isColumnBeforeRenderedColumns(o))return-3;if(this.isColumnAfterRenderedColumns(o))return-4;if(!(t=r<0?this.THEAD.childNodes[this.rowFilter.sourceRowToVisibleColHeadedRow(r)]:this.TBODY.childNodes[this.rowFilter.sourceToRendered(r)])&&r>=0)throw new Error("TR was expected to be rendered but is not");var i=t.childNodes[this.columnFilter.sourceColumnToVisibleRowHeadedColumn(o)];if(!i&&o>=0)throw new Error("TD or TH was expected to be rendered but is not");return i}getColumnHeader(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=this.THEAD.childNodes[t];if(r)return r.childNodes[this.columnFilter.sourceColumnToVisibleRowHeadedColumn(e)]}getRowHeader(e){if(0===this.columnFilter.sourceColumnToVisibleRowHeadedColumn(0))return null;var t=this.TBODY.childNodes[this.rowFilter.sourceToRendered(e)];return t?t.childNodes[0]:void 0}getCoords(e){var t=e;if("TD"!==t.nodeName&&"TH"!==t.nodeName&&(t=Object(g.closest)(t,["TD","TH"])),null===t)return null;var r=t.parentNode,o=r.parentNode,n=Object(g.index)(r),i=t.cellIndex;if(Object(g.overlayContainsElement)(q.CLONE_TOP_LEFT_CORNER,t,this.wtRootElement)||Object(g.overlayContainsElement)(q.CLONE_TOP,t,this.wtRootElement))"THEAD"===o.nodeName&&(n-=o.childNodes.length);else if(Object(g.overlayContainsElement)(q.CLONE_BOTTOM_LEFT_CORNER,t,this.wtRootElement)||Object(g.overlayContainsElement)(q.CLONE_BOTTOM,t,this.wtRootElement)){n=this.wot.getSetting("totalRows")-o.childNodes.length+n}else n=o===this.THEAD?this.rowFilter.visibleColHeadedRowToSourceRow(n):this.rowFilter.renderedToSource(n);return i=Object(g.overlayContainsElement)(q.CLONE_TOP_LEFT_CORNER,t,this.wtRootElement)||Object(g.overlayContainsElement)(q.CLONE_LEFT,t,this.wtRootElement)||Object(g.overlayContainsElement)(q.CLONE_BOTTOM_LEFT_CORNER,t,this.wtRootElement)?this.columnFilter.offsettedTH(i):this.columnFilter.visibleRowHeadedColumnToSourceColumn(i),new c(n,i)}markOversizedRows(){if(!this.wot.getSetting("externalRowCalculator")){var e,t,r,o,n,i=this.TBODY.childNodes.length,s=i*this.wot.wtSettings.settings.defaultRowHeight,a=Object(g.innerHeight)(this.TBODY)-1,l=this.isMaster?this.wot.rowUtils:this.wot.overlay.master.rowUtils;if(s!==a||this.wot.getSetting("fixedRowsBottom"))for(;i;)i-=1,r=this.rowFilter.renderedToSource(i),e=l.getHeight(r),t=(n=(o=this.getTrForRow(r)).querySelector("th"))?Object(g.innerHeight)(n):Object(g.innerHeight)(o)-1,(!e&&this.wot.wtSettings.settings.defaultRowHeight<t||e<t)&&(t+=1,this.wot.wtViewport.oversizedRows[r]=t)}}getTrForRow(e){return this.TBODY.childNodes[this.rowFilter.sourceToRendered(e)]}isColumnHeaderLevelRendered(e){return e>this.wot.getSetting("columnHeaders").length-1}isRowHeaderLevelRendered(e){return e>this.wot.getSetting("rowHeaders").length-1}isRowBeforeRenderedRows(e){var t=this.getFirstRenderedRow();return e<0&&(e=0),-1===t||e<t}isRowAfterViewport(e){return this.rowFilter&&e>this.getLastVisibleRow()}isRowAfterRenderedRows(e){if(e<0){var t=this.wot.getSetting("columnHeaders").length+e;return this.isColumnHeaderLevelRendered(t)}return e>this.getLastRenderedRow()}isColumnBeforeViewport(e){return this.columnFilter&&this.columnFilter.sourceToRendered(e)<0&&e>=0}isColumnBeforeRenderedColumns(e){var t=this.getFirstRenderedColumn();return e<0&&(e=0),-1===t||e<t}isColumnAfterViewport(e){return this.columnFilter&&e>this.getLastVisibleColumn()}isColumnAfterRenderedColumns(e){if(e<0){var t=this.wot.getSetting("rowHeaders").length+e;return this.isRowHeaderLevelRendered(t)}return this.columnFilter&&e>this.getLastRenderedColumn()}isLastRowFullyVisible(){return this.getLastVisibleRow()===this.getLastRenderedRow()}isLastColumnFullyVisible(){return this.getLastVisibleColumn()===this.getLastRenderedColumn()}allRowsInViewport(){return this.wot.getSetting("totalRows")===this.getVisibleRowsCount()}allColumnsInViewport(){return this.wot.getSetting("totalColumns")===this.getVisibleColumnsCount()}hasDefinedSize(){return this.hasTableHeight&&this.hasTableWidth}isVisible(){return Object(g.isVisible)(this.TABLE)}},xe={getFirstRenderedRow(){var e=this.wot.wtViewport.rowsRenderCalculator.startRow;return null===e?-1:e},getFirstVisibleRow(){var e=this.wot.wtViewport.rowsVisibleCalculator.startRow;return null===e?-1:e},getLastRenderedRow(){var e=this.wot.wtViewport.rowsRenderCalculator.endRow;return null===e?-1:e},getLastVisibleRow(){var e=this.wot.wtViewport.rowsVisibleCalculator.endRow;return null===e?-1:e},getRenderedRowsCount(){return this.wot.wtViewport.rowsRenderCalculator.count},getVisibleRowsCount(){return this.wot.wtViewport.rowsVisibleCalculator.count}};Object(Ee.defineGetter)(xe,"MIXIN_NAME","calculatedRows",{writable:!1,enumerable:!1});var Ae=xe,Ie={getFirstRenderedColumn(){var e=this.wot.wtViewport.columnsRenderCalculator.startColumn;return null===e?-1:e},getFirstVisibleColumn(){var e=this.wot.wtViewport.columnsVisibleCalculator.startColumn;return null===e?-1:e},getLastRenderedColumn(){var e=this.wot.wtViewport.columnsRenderCalculator.endColumn;return null===e?-1:e},getLastVisibleColumn(){var e=this.wot.wtViewport.columnsVisibleCalculator.endColumn;return null===e?-1:e},getRenderedColumnsCount(){return this.wot.wtViewport.columnsRenderCalculator.count},getVisibleColumnsCount(){return this.wot.wtViewport.columnsVisibleCalculator.count}};Object(Ee.defineGetter)(Ie,"MIXIN_NAME","calculatedColumns",{writable:!1,enumerable:!1});var je=Ie;class De extends Le{constructor(e,t){super(e,t),this.holderOffset=0,this.wtRootElement.className+="ht_master handsontable",this.trimmingContainer=null,this.alignOverlaysWithTrimmingContainer()}alignOverlaysWithTrimmingContainer(){this.trimmingContainer=Object(g.getTrimmingContainer)(this.wtRootElement);var e=this.trimmingContainer,{rootWindow:t}=this.wot;if(e===t){this.wot.getSetting("preventOverflow")||(this.holder.style.overflow="visible",this.wtRootElement.style.overflow="visible")}else{e.parentElement;var r=Object(g.getStyle)(e,"height",t),o=(Object(g.getStyle)(e,"overflow",t),this.holder.style),{width:n,height:i}=Object(g.getBoundingClientRect)(e);i=Math.min(i,Object(g.scrollHeight)(e)),o.height="auto"===r?"auto":"".concat(i,"px"),n=Math.min(n,Object(g.scrollWidth)(e)),o.width="".concat(n,"px"),o.overflow="",this.hasTableHeight="auto"===o.height||i>0,this.hasTableWidth=n>0}}markOversizedColumnHeaders(){var{wot:e}=this,t=e.getSetting("columnHeaders").length;if(t&&!e.wtViewport.hasOversizedColumnHeadersMarked){for(var r=e.getSetting("rowHeaders").length,o=this.getRenderedColumnsCount(),n=0;n<t;n++)for(var i=-1*r;i<o;i++)this.markIfOversizedColumnHeader(i);e.wtViewport.hasOversizedColumnHeadersMarked=!0}}draw(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],{wot:t}=this,{wtOverlays:r,wtViewport:o}=t,n=t.getSetting("totalRows"),i=t.getSetting("totalColumns"),s=t.getSetting("rowHeaders"),a=s.length,l=t.getSetting("columnHeaders"),c=l.length,h=e;if(this.holderOffset=Object(g.offset)(this.holder),h=o.createRenderCalculators(h))o.createVisibleCalculators(),r.refreshClones(!0),this.refreshSelections(!0);else{var f=n>0?this.getFirstRenderedRow():0,m=i>0?this.getFirstRenderedColumn():0;this.rowFilter=new u(f,n,c),this.columnFilter=new d(m,i,a),this.alignOverlaysWithTrimmingContainer();var p={};if(this.wot.getSetting("beforeDraw",!0,p),!0!==p.skipRender){this.tableRenderer.setHeaderContentRenderers(s,l),this.resetOversizedRows(),this.tableRenderer.setViewportSize(this.getRenderedRowsCount(),this.getRenderedColumnsCount()).setFilters(this.rowFilter,this.columnFilter).render();o.getWorkspaceWidth();o.containerWidth=null,this.markOversizedColumnHeaders(),this.adjustColumnHeaderHeights(),this.markOversizedRows(),o.createVisibleCalculators(),r.refreshClones(!1),this.refreshSelections(!1),this.wot.getSetting("onDraw",!0)}}}}Object(Ee.mixin)(De,Ae),Object(Ee.mixin)(De,je);var _e=De,ke={getFirstRenderedColumn(){return 0===this.wot.getSetting("totalColumns")?-1:0},getFirstVisibleColumn(){return this.getFirstRenderedColumn()},getLastRenderedColumn(){return this.getRenderedColumnsCount()-1},getLastVisibleColumn(){return this.getLastRenderedColumn()},getRenderedColumnsCount(){var e=this.wot.getSetting("totalColumns");return Math.min(this.wot.getSetting("fixedColumnsLeft"),e)},getVisibleColumnsCount(){return this.getRenderedColumnsCount()}};Object(Ee.defineGetter)(ke,"MIXIN_NAME","stickyColumnsLeft",{writable:!1,enumerable:!1});var Pe=ke;class Be extends Le{}Object(Ee.mixin)(Be,Ae),Object(Ee.mixin)(Be,Pe);var Fe=Be;class Ve extends q{constructor(e){super(e),this.clone=this.makeClone(q.CLONE_LEFT),this.updateStateOfRendering()}createTable(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return new Fe(...t)}shouldBeRendered(){return!(!this.master.getSetting("fixedColumnsLeft")&&!this.master.getSetting("rowHeaders").length)}adjustElementsPosition(){var{master:e}=this,t=e.getSetting("totalColumns");if("number"==typeof e.wtViewport.columnsRenderCalculator.startPosition)e.wtTable.spreader.style.left="".concat(e.wtViewport.columnsRenderCalculator.startPosition,"px");else{if(0!==t)throw new Error("Incorrect value of the columnsRenderCalculator");e.wtTable.spreader.style.left="0"}if(e.wtTable.spreader.style.right="",this.needFullRender&&("number"==typeof e.wtViewport.rowsRenderCalculator.startPosition?this.clone.wtTable.spreader.style.top="".concat(e.wtViewport.rowsRenderCalculator.startPosition,"px"):this.clone.wtTable.spreader.style.top=""),this.needFullRender&&e.wtTable.holder.parentNode){var r=this.clone.wtTable.wtRootElement,o=e.getSetting("preventOverflow");if(e.wtTable.trimmingContainer!==e.rootWindow||o&&"horizontal"===o)Object(g.resetCssTransform)(r);else{var n,i,s=Object(g.getBoundingClientRect)(e.wtTable.hider),a=Math.ceil(s.left),l=Math.ceil(s.right);i=""===(i=e.wtTable.hider.style.top)?0:i,n=a<0&&l-Object(g.offsetWidth)(r)>0?-a:0,n+="px",Object(g.setOverlayPosition)(r,n,i)}var c=e.wtTable.wtRootElement;e.getSetting("totalRows")?Object(g.removeClass)(c,"emptyRows"):Object(g.addClass)(c,"emptyRows")}}setScrollPosition(e){var{rootWindow:t}=this.master,r=!1;return this.mainTableScrollableElement===t&&t.scrollX!==e?(t.scrollTo(e,Object(g.getWindowScrollTop)(t)),r=!0):this.mainTableScrollableElement.scrollLeft!==e&&(this.mainTableScrollableElement.scrollLeft=e,r=!0),r}onScroll(){this.master.getSetting("onScrollVertically")}sumCellSizes(e,t){for(var r=this.master.wtSettings.defaultColumnWidth,o=e,n=0;o<t;)n+=this.master.columnUtils.getStretchedColumnWidth(o)||r,o+=1;return n}adjustElementsSize(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.needFullRender||e){var{clone:t,master:r}=this,o=t.wtTable.wtRootElement.style,n=r.getSetting("preventOverflow");if(r.wtTable.trimmingContainer!==r.rootWindow||"vertical"===n){var i=r.wtViewport.getWorkspaceHeight();r.wtOverlays.hasScrollbarBottom&&(i-=Object(g.getScrollbarWidth)(r.rootDocument)),i=Math.min(i,Object(g.scrollHeight)(r.wtTable.wtRootElement)),o.height="".concat(i,"px")}else o.height="";var s=Object(g.outerWidth)(t.wtTable.TABLE);o.width="".concat(s,"px"),t.wtTable.hider.style.height=r.wtTable.hider.style.height,t.wtTable.holder.style.height=o.height,t.wtTable.holder.style.width=o.width,e||(this.areElementSizesAdjusted=!0)}}scrollTo(e,t){var{master:r}=this,o=this.getTableParentOffset(),n=r.wtTable.holder,i=0;return t&&Object(g.offsetWidth)(n)!==Object(g.clientWidth)(n)&&(i=Object(g.getScrollbarWidth)(r.rootDocument)),t?(o+=this.sumCellSizes(0,e+1),o-=r.wtViewport.getViewportWidth()):o+=this.sumCellSizes(r.getSetting("fixedColumnsLeft"),e),o+=i,this.setScrollPosition(o)}getTableParentOffset(){var e=0;return this.master.getSetting("preventOverflow")||this.master.wtTable.trimmingContainer!==this.master.rootWindow||(e=this.master.wtTable.holderOffset.left),e}getScrollPosition(){return Object(g.getScrollLeft)(this.mainTableScrollableElement,this.master.rootWindow)}}q.registerOverlay(q.CLONE_LEFT,Ve);var We={getFirstRenderedRow(){return 0===this.wot.getSetting("totalRows")?-1:0},getFirstVisibleRow(){return this.getFirstRenderedRow()},getLastRenderedRow(){return this.getRenderedRowsCount()-1},getLastVisibleRow(){return this.getLastRenderedRow()},getRenderedRowsCount(){var e=this.wot.getSetting("totalRows");return Math.min(this.wot.getSetting("fixedRowsTop"),e)},getVisibleRowsCount(){return this.getRenderedRowsCount()}};Object(Ee.defineGetter)(We,"MIXIN_NAME","stickyRowsTop",{writable:!1,enumerable:!1});var ze=We;class Ue extends Le{}Object(Ee.mixin)(Ue,ze),Object(Ee.mixin)(Ue,je);var Ge=Ue;class Xe extends q{constructor(e){super(e),this.clone=this.makeClone(q.CLONE_TOP),this.updateStateOfRendering()}createTable(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return new Ge(...t)}shouldBeRendered(){return!(!this.master.getSetting("fixedRowsTop")&&!this.master.getSetting("columnHeaders").length)}adjustElementsPosition(){var{master:e}=this,t=e.getSetting("totalRows");if("number"==typeof e.wtViewport.rowsRenderCalculator.startPosition)e.wtTable.spreader.style.top="".concat(e.wtViewport.rowsRenderCalculator.startPosition,"px");else{if(0!==t)throw new Error("Incorrect value of the rowsRenderCalculator");e.wtTable.spreader.style.top="0"}if(e.wtTable.spreader.style.bottom="",this.needFullRender&&("number"==typeof e.wtViewport.columnsRenderCalculator.startPosition?this.clone.wtTable.spreader.style.left="".concat(e.wtViewport.columnsRenderCalculator.startPosition,"px"):this.clone.wtTable.spreader.style.left=""),this.needFullRender&&e.wtTable.holder.parentNode){var r=this.clone.wtTable.wtRootElement,o=e.getSetting("preventOverflow");if(e.wtTable.trimmingContainer!==e.rootWindow||o&&"vertical"===o)Object(g.resetCssTransform)(r);else{var n,i,s=Object(g.getBoundingClientRect)(e.wtTable.hider),a=Math.ceil(s.top),l=Math.ceil(s.bottom);n=""===(n=e.wtTable.hider.style.left)?0:n,i=a<0&&l-Object(g.offsetHeight)(r)>0?-a:0,i+="px",Object(g.setOverlayPosition)(r,n,i)}}}setScrollPosition(e){var t=this.master.rootWindow,r=!1;return this.mainTableScrollableElement===t&&t.scrollY!==e?(t.scrollTo(Object(g.getWindowScrollLeft)(t),e),r=!0):this.mainTableScrollableElement.scrollTop!==e&&(this.mainTableScrollableElement.scrollTop=e,r=!0),r}onScroll(){this.master.getSetting("onScrollHorizontally")}sumCellSizes(e,t){for(var r=this.master.wtSettings.settings.defaultRowHeight,o=e,n=0;o<t;){var i=this.master.rowUtils.getHeight(o);n+=void 0===i?r:i,o+=1}return n}adjustElementsSize(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.needFullRender||e){var{clone:t,master:r}=this,o=t.wtTable.wtRootElement.style,n=r.getSetting("preventOverflow");if(r.wtTable.trimmingContainer!==r.rootWindow||"horizontal"===n){var i=r.wtViewport.getWorkspaceWidth();r.wtOverlays.hasScrollbarRight&&(i-=Object(g.getScrollbarWidth)(r.rootDocument)),i=Math.min(i,Object(g.scrollWidth)(r.wtTable.wtRootElement)),o.width="".concat(i,"px")}else o.width="";var s=Object(g.outerHeight)(t.wtTable.TABLE);r.wtTable.hasDefinedSize()||(s=0),o.height="".concat(s,"px"),t.wtTable.hider.style.width=r.wtTable.hider.style.width,t.wtTable.holder.style.width=o.width,t.wtTable.holder.style.height=o.height,e||(this.areElementSizesAdjusted=!0)}}scrollTo(e,t){var{master:r}=this,o=r.wtTable.holder,n=this.getTableParentOffset(),i=0;if(t&&Object(g.offsetHeight)(o)!==Object(g.clientHeight)(o)&&(i=Object(g.getScrollbarWidth)(r.rootDocument)),t){var s=r.getSetting("fixedRowsBottom"),a=r.getSetting("totalRows");n+=this.sumCellSizes(0,e+1),n-=r.wtViewport.getViewportHeight()-this.sumCellSizes(a-s,a),n+=1}else n+=this.sumCellSizes(r.getSetting("fixedRowsTop"),e);return n+=i,this.setScrollPosition(n)}getTableParentOffset(){return this.mainTableScrollableElement===this.master.rootWindow?this.master.wtTable.holderOffset.top:0}getScrollPosition(){return Object(g.getScrollTop)(this.mainTableScrollableElement,this.master.rootWindow)}redrawSelectionBorders(e){if(e&&e.cellRange&&e.hasSelectionHandle()){var t=e.getSelectionHandle(this.master),r=e.getCorners();t.disappear(),t.appear(r)}}redrawAllSelectionsBorders(){var e=this.master.selections;this.redrawSelectionBorders(e.getCell()),Object(Oe.arrayEach)(e.getAreas(),e=>{this.redrawSelectionBorders(e)}),this.redrawSelectionBorders(e.getFill()),this.master.wtOverlays.leftOverlay.redrawClone()}redrawClone(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(super.redrawClone(e),!e&&0===this.master.getSetting("rowHeaders").length){var t=this.clone.wtTable.THEAD.querySelectorAll("th:nth-of-type(2)");if(t)for(var r=0;r<t.length;r++)t[r].style["border-left-width"]=0}}}q.registerOverlay(q.CLONE_TOP,Xe);class Ke extends Le{}Object(Ee.mixin)(Ke,ze),Object(Ee.mixin)(Ke,Pe);var Ye=Ke;class qe extends q{constructor(e){super(e),this.clone=this.makeClone(q.CLONE_TOP_LEFT_CORNER),this.updateStateOfRendering()}createTable(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return new Ye(...t)}shouldBeRendered(){var{master:e}=this;return!(!e.getSetting("fixedRowsTop")&&!e.getSetting("columnHeaders").length||!e.getSetting("fixedColumnsLeft")&&!e.getSetting("rowHeaders").length)}adjustElementsPosition(){var{clone:e,master:t}=this;if(t.wtTable.holder.parentNode){var r=e.wtTable.wtRootElement,o=t.getSetting("preventOverflow");if(t.wtTable.trimmingContainer===t.rootWindow){var n=Object(g.getBoundingClientRect)(t.wtTable.hider),i=Math.ceil(n.top),s=Math.ceil(n.left),a=Math.ceil(n.bottom),l=Math.ceil(n.right),c="0",h="0";o&&"vertical"!==o||s<0&&l-Object(g.offsetWidth)(r)>0&&(c="".concat(-s,"px")),o&&"horizontal"!==o||i<0&&a-Object(g.offsetHeight)(r)>0&&(h="".concat(-i,"px")),Object(g.setOverlayPosition)(r,c,h)}else Object(g.resetCssTransform)(r)}}adjustElementsSize(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.needFullRender||e){var{clone:t,master:r}=this,o=Object(g.outerHeight)(t.wtTable.TABLE),n=Object(g.outerWidth)(t.wtTable.TABLE),i=t.wtTable.wtRootElement;r.wtTable.hasDefinedSize()||(o=0),i.style.height="".concat(o,"px"),i.style.width="".concat(n,"px")}}}q.registerOverlay(q.CLONE_TOP_LEFT_CORNER,qe);var Qe={getFirstRenderedRow(){var e=this.wot.getSetting("totalRows"),t=this.wot.getSetting("fixedRowsBottom"),r=e-t;return 0===e||0===t?-1:r<0?0:r},getFirstVisibleRow(){return this.getFirstRenderedRow()},getLastRenderedRow(){return this.wot.getSetting("totalRows")-1},getLastVisibleRow(){return this.getLastRenderedRow()},getRenderedRowsCount(){var e=this.wot.getSetting("totalRows");return Math.min(this.wot.getSetting("fixedRowsBottom"),e)},getVisibleRowsCount(){return this.getRenderedRowsCount()}};Object(Ee.defineGetter)(Qe,"MIXIN_NAME","stickyRowsBottom",{writable:!1,enumerable:!1});var Ze=Qe;class Je extends Le{}Object(Ee.mixin)(Je,Ze),Object(Ee.mixin)(Je,je);var $e=Je;class et extends q{constructor(e){super(e),this.clone=this.makeClone(q.CLONE_BOTTOM),this.updateStateOfRendering()}createTable(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return new $e(...t)}shouldBeRendered(){return!!this.master.getSetting("fixedRowsBottom")}adjustElementsPosition(){var{master:e}=this,t=e.getSetting("totalRows");if("number"==typeof e.wtViewport.rowsRenderCalculator.startPosition)e.wtTable.spreader.style.top="".concat(e.wtViewport.rowsRenderCalculator.startPosition,"px");else{if(0!==t)throw new Error("Incorrect value of the rowsRenderCalculator");e.wtTable.spreader.style.top="0"}if(e.wtTable.spreader.style.bottom="",this.needFullRender&&("number"==typeof e.wtViewport.columnsRenderCalculator.startPosition?this.clone.wtTable.spreader.style.left="".concat(e.wtViewport.columnsRenderCalculator.startPosition,"px"):this.clone.wtTable.spreader.style.left=""),this.needFullRender&&e.wtTable.holder.parentNode){var r=this.clone.wtTable.wtRootElement,o=e.getSetting("preventOverflow");if(r.style.top="",e.wtTable.trimmingContainer!==e.rootWindow||o&&"vertical"===o){Object(g.resetCssTransform)(r);var n=Object(g.getScrollbarWidth)(e.rootDocument);Object(g.clientHeight)(e.wtTable.holder)===Object(g.offsetHeight)(e.wtTable.holder)&&(n=0),r.style.top="",r.style.bottom="".concat(n,"px")}else{var i,s,a=Object(g.getBoundingClientRect)(e.wtTable.hider),l=Math.ceil(a.bottom),c=Object(g.offsetHeight)(e.rootDocument.body);i=""===(i=e.wtTable.hider.style.left)?0:i,s=l>c?l-c:0,s+="px",r.style.top="",r.style.left=i,r.style.bottom=s}}}setScrollPosition(e){var{master:t}=this,r=!1;return this.mainTableScrollableElement===t.rootWindow?(t.rootWindow.scrollTo(Object(g.getWindowScrollLeft)(t.rootWindow),e),r=!0):this.mainTableScrollableElement.scrollTop!==e&&(this.mainTableScrollableElement.scrollTop=e,r=!0),r}onScroll(){this.master.getSetting("onScrollHorizontally")}sumCellSizes(e,t){for(var{master:r}=this,o=r.wtSettings.settings.defaultRowHeight,n=e,i=0;n<t;){var s=r.rowUtils.getHeight(n);i+=void 0===s?o:s,n+=1}return i}adjustElementsSize(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.needFullRender||e){var{clone:t,master:r}=this,o=t.wtTable.wtRootElement.style,n=r.getSetting("preventOverflow");if(r.wtTable.trimmingContainer!==r.rootWindow||"horizontal"===n){var i=r.wtViewport.getWorkspaceWidth();r.wtOverlays.hasScrollbarRight&&(i-=Object(g.getScrollbarWidth)(r.rootDocument)),i=Math.min(i,Object(g.scrollWidth)(r.wtTable.wtRootElement)),o.width="".concat(i,"px")}else o.width="";var s=Object(g.outerHeight)(t.wtTable.TABLE);r.wtTable.hasDefinedSize()||(s=0),o.height="".concat(s,"px"),t.wtTable.hider.style.width=this.master.wtTable.hider.style.width,t.wtTable.holder.style.width=o.width,t.wtTable.holder.style.height=o.height,e||(this.areElementSizesAdjusted=!0)}}scrollTo(e,t){var{master:r}=this,o=this.getTableParentOffset(),n=r.wtTable.holder,i=0;t&&Object(g.offsetHeight)(n)!==Object(g.clientHeight)(n)&&(i=Object(g.getScrollbarWidth)(r.rootDocument)),t?(o+=this.sumCellSizes(0,e+1),o-=r.wtViewport.getViewportHeight(),o+=1):o+=this.sumCellSizes(r.getSetting("fixedRowsBottom"),e),o+=i,this.setScrollPosition(o)}getTableParentOffset(){return this.mainTableScrollableElement===this.master.rootWindow?this.master.wtTable.holderOffset.top:0}getScrollPosition(){return Object(g.getScrollTop)(this.mainTableScrollableElement,this.master.rootWindow)}redrawClone(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(super.redrawClone(e),!e&&0===this.master.getSetting("rowHeaders").length){var t=this.clone.wtTable.THEAD.querySelector("th:nth-of-type(2)");t&&(t.style["border-left-width"]=0)}}}q.registerOverlay(q.CLONE_BOTTOM,et);class tt extends Le{}Object(Ee.mixin)(tt,Ze),Object(Ee.mixin)(tt,Pe);var rt=tt;class ot extends q{constructor(e){super(e),this.clone=this.makeClone(q.CLONE_BOTTOM_LEFT_CORNER),this.updateStateOfRendering()}createTable(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return new rt(...t)}shouldBeRendered(){var{master:e}=this;return!(!e.getSetting("fixedRowsBottom")||!e.getSetting("fixedColumnsLeft")&&!e.getSetting("rowHeaders").length)}adjustElementsPosition(){var{clone:e,master:t}=this;if(t.wtTable.holder.parentNode){var r=e.wtTable.wtRootElement;if(r.style.top="",t.wtTable.trimmingContainer===t.rootWindow){var o,n,i=Object(g.getBoundingClientRect)(t.wtTable.hider),s=Math.ceil(i.bottom),a=Math.ceil(i.left),l=Object(g.offsetHeight)(t.rootDocument.body);o=a<0?-a:0,n=s>l?s-l:0,n+="px",o+="px",r.style.top="",r.style.left=o,r.style.bottom=n}else{Object(g.resetCssTransform)(r);var c=Object(g.getScrollbarWidth)(t.rootDocument);Object(g.clientHeight)(t.wtTable.holder)===Object(g.offsetHeight)(t.wtTable.holder)&&(c=0),r.style.top="",r.style.bottom="".concat(c,"px")}}}adjustElementsSize(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.needFullRender||e){var{clone:t,master:r}=this,o=Object(g.outerHeight)(t.wtTable.TABLE),n=Object(g.outerWidth)(t.wtTable.TABLE),i=t.wtTable.wtRootElement;r.wtTable.hasDefinedSize()||(o=0),i.style.height="".concat(o,"px"),i.style.width="".concat(n,"px")}}}q.registerOverlay(q.CLONE_BOTTOM_LEFT_CORNER,ot);var nt=r(57);var it=class{constructor(e,t){t&&(this.eventManager=new I.a(e),this.wot=e,this.settings=t,this.mouseDown=!1,this.main=null,this.cornerDefaultStyle={width:"6px",height:"6px",borderWidth:"1px",borderStyle:"solid",borderColor:"#FFF"},this.corner=null,this.cornerStyle=null,this.createElements(t),this.registerListeners())}registerListeners(){var e=this,t=this.wot.rootDocument.body;this.eventManager.addEventListener(t,"mousedown",()=>this.onMouseDown()),this.eventManager.addEventListener(t,"mouseup",()=>this.onMouseUp());for(var r=function(t,r){e.eventManager.addEventListener(e.main.childNodes[t],"mouseenter",r=>e.onMouseEnter(r,e.main.childNodes[t]))},o=0,n=this.main.childNodes.length;o<n;o++)r(o)}onMouseDown(){this.mouseDown=!0}onMouseUp(){this.mouseDown=!1}onMouseEnter(e,t){if(this.mouseDown&&this.wot.getSetting("hideBorderOnMouseDownOver")){e.preventDefault(),Object(nt.stopImmediatePropagation)(e);var r=this,o=this.wot.rootDocument.body,n=Object(g.getBoundingClientRect)(t);t.style.display="none",this.eventManager.addEventListener(o,"mousemove",(function e(i){var s;((s=i).clientY<Math.floor(n.top)||s.clientY>Math.ceil(n.top+n.height)||s.clientX<Math.floor(n.left)||s.clientX>Math.ceil(n.left+n.width))&&(r.eventManager.removeEventListener(o,"mousemove",e),t.style.display="block")}))}}createElements(e){var{rootDocument:t}=this.wot;this.main=t.createElement("div");var r=this.main.style;r.position="absolute",r.top=0,r.left=0;var o=t.createElement("div");o.className="wtBorder ".concat(this.settings.className||""),(r=o.style).height=this.settings.corner&&this.settings.corner.width?"".concat(this.settings.corner.width,"px"):"".concat(e.border.width,"px"),r.width=this.settings.corner&&this.settings.corner.width?"".concat(this.settings.corner.width,"px"):"".concat(e.border.width,"px"),this.main.appendChild(o),this.corner=o,this.corner.className+=" corner",this.cornerStyle=this.corner.style,this.cornerStyle.width=this.cornerDefaultStyle.width,this.cornerStyle.height=this.cornerDefaultStyle.height,this.cornerStyle.border=[this.cornerDefaultStyle.borderWidth,this.cornerDefaultStyle.borderStyle,this.cornerDefaultStyle.borderColor].join(" "),Object(V.isMobileBrowser)()&&this.createMultipleSelectorHandles(),this.disappear();var{wtTable:n}=this.wot,i=n.selectionHandlesHolder;i||((i=t.createElement("div")).className="htBorders",n.selectionHandlesHolder=i,n.spreader.appendChild(i)),i.appendChild(this.main)}createMultipleSelectorHandles(){var{rootDocument:e}=this.wot;this.selectionHandles={topLeft:e.createElement("DIV"),topLeftHitArea:e.createElement("DIV"),bottomRight:e.createElement("DIV"),bottomRightHitArea:e.createElement("DIV")};this.selectionHandles.topLeft.className="topLeftSelectionHandle",this.selectionHandles.topLeftHitArea.className="topLeftSelectionHandle-HitArea",this.selectionHandles.bottomRight.className="bottomRightSelectionHandle",this.selectionHandles.bottomRightHitArea.className="bottomRightSelectionHandle-HitArea",this.selectionHandles.styles={topLeft:this.selectionHandles.topLeft.style,topLeftHitArea:this.selectionHandles.topLeftHitArea.style,bottomRight:this.selectionHandles.bottomRight.style,bottomRightHitArea:this.selectionHandles.bottomRightHitArea.style};var t={position:"absolute",height:"".concat(40,"px"),width:"".concat(40,"px"),"border-radius":"".concat(parseInt(40/1.5,10),"px")};Object(Ee.objectEach)(t,(e,t)=>{this.selectionHandles.styles.bottomRightHitArea[t]=e,this.selectionHandles.styles.topLeftHitArea[t]=e});var r={position:"absolute",height:"".concat(10,"px"),width:"".concat(10,"px"),"border-radius":"".concat(parseInt(10/1.5,10),"px"),background:"#FFFFFF","border-width":"1px","border-style":"solid"};Object(Ee.objectEach)(r,(e,t)=>{this.selectionHandles.styles.bottomRight[t]=e,this.selectionHandles.styles.topLeft[t]=e}),this.main.appendChild(this.selectionHandles.topLeft),this.main.appendChild(this.selectionHandles.bottomRight),this.main.appendChild(this.selectionHandles.topLeftHitArea),this.main.appendChild(this.selectionHandles.bottomRightHitArea)}isPartRange(e,t){var r=this.wot.selections.createOrGetArea();return!(!r.cellRange||e===r.cellRange.to.row&&t===r.cellRange.to.col)}updateMultipleSelectionHandlesPosition(e,t,r,o,n,i){var s=parseInt(this.selectionHandles.styles.topLeft.width,10),a=parseInt(this.selectionHandles.styles.topLeftHitArea.width,10),l=this.settings.corner&&this.settings.corner.color?this.settings.corner.color:this.settings.border.color;this.selectionHandles.styles.topLeft.borderColor=l,this.selectionHandles.styles.bottomRight.borderColor=l,this.selectionHandles.styles.topLeft.top="".concat(parseInt(r-s,10),"px"),this.selectionHandles.styles.topLeft.left="".concat(parseInt(o-s,10),"px"),this.selectionHandles.styles.topLeftHitArea.top="".concat(parseInt(r-a/4*3,10),"px"),this.selectionHandles.styles.topLeftHitArea.left="".concat(parseInt(o-a/4*3,10),"px"),this.selectionHandles.styles.bottomRight.top="".concat(parseInt(r+i,10),"px"),this.selectionHandles.styles.bottomRight.left="".concat(parseInt(o+n,10),"px"),this.selectionHandles.styles.bottomRightHitArea.top="".concat(parseInt(r+i-a/4,10),"px"),this.selectionHandles.styles.bottomRightHitArea.left="".concat(parseInt(o+n-a/4,10),"px"),this.settings.border.cornerVisible&&this.settings.border.cornerVisible()?(this.selectionHandles.styles.topLeft.display="block",this.selectionHandles.styles.topLeftHitArea.display="block",this.isPartRange(e,t)?(this.selectionHandles.styles.bottomRight.display="none",this.selectionHandles.styles.bottomRightHitArea.display="none"):(this.selectionHandles.styles.bottomRight.display="block",this.selectionHandles.styles.bottomRightHitArea.display="block")):(this.selectionHandles.styles.topLeft.display="none",this.selectionHandles.styles.bottomRight.display="none",this.selectionHandles.styles.topLeftHitArea.display="none",this.selectionHandles.styles.bottomRightHitArea.display="none"),e===this.wot.wtSettings.getSetting("fixedRowsTop")||t===this.wot.wtSettings.getSetting("fixedColumnsLeft")?(this.selectionHandles.styles.topLeft.zIndex="9999",this.selectionHandles.styles.topLeftHitArea.zIndex="9999"):(this.selectionHandles.styles.topLeft.zIndex="",this.selectionHandles.styles.topLeftHitArea.zIndex="")}appear(e){if(!this.disabled){for(var t,r,o,n,{wtTable:i,rootDocument:s,rootWindow:a}=this.wot,l=i.getRenderedRowsCount(),h=0;h<l;h+=1){var d=i.rowFilter.renderedToSource(h);if(d>=e[0]&&d<=e[2]){t=d;break}}for(var u=l-1;u>=0;u-=1){var f=i.rowFilter.renderedToSource(u);if(f>=e[0]&&f<=e[2]){r=f;break}}for(var m=i.getRenderedColumnsCount(),p=0;p<m;p+=1){var v=i.columnFilter.renderedToSource(p);if(v>=e[1]&&v<=e[3]){o=v;break}}for(var w=m-1;w>=0;w-=1){var b=i.columnFilter.renderedToSource(w);if(b>=e[1]&&b<=e[3]){n=b;break}}if(void 0!==t&&void 0!==o){var C=i.getCell(new c(t,o)),E=t!==r||o!==n,O=E?i.getCell(new c(r,n)):C,y=Object(g.offset)(C),R=E?Object(g.offset)(O):y,T=Object(g.offset)(i.TABLE),S=y.top,M=y.left,N=M-T.left-1,H=R.left+Object(g.outerWidth)(O)-M;if(this.isEntireColumnSelected(t,r)){var L=this.getDimensionsFromHeader("columns",o,n,T),x=null;L&&([x,N,H]=L),x&&(C=x)}var A=S-T.top-1,I=R.top+Object(g.outerHeight)(O)-S;if(this.isEntireRowSelected(o,n)){var j=this.getDimensionsFromHeader("rows",t,r,T),D=null;j&&([D,A,I]=j),D&&(C=D)}var _=this.settings.border.cornerVisible;_="function"==typeof _?_(this.settings.layerLevel):_;var k=this.wot.getSetting("onModifyGetCellCoords",r,n),[P,B]=[r,n];if(k&&Array.isArray(k)&&([,,P,B]=k),Object(V.isMobileBrowser)()||!_||this.isPartRange(P,B))this.cornerStyle.display="none";else{this.cornerStyle.top="".concat(A+I-4,"px"),this.cornerStyle.left="".concat(N+H-4,"px"),this.cornerStyle.borderRightWidth=this.cornerDefaultStyle.borderWidth,this.cornerStyle.width=this.cornerDefaultStyle.width,this.cornerStyle.backgroundColor=this.settings.corner&&this.settings.corner.color?this.settings.corner.color:this.settings.border.color,this.cornerStyle.display="none";var F=this.wot.overlay?this.wot.overlay.master.wtTable.trimmingContainer:i.trimmingContainer,W=F===a;if(W&&(F=s.documentElement),n===this.wot.getSetting("totalColumns")-1){var z=(W?Object(g.getBoundingClientRect)(O).left:O.offsetLeft)+Object(g.outerWidth)(O)+parseInt(this.cornerDefaultStyle.width,10)/2,U=this.wot.overlay?this.wot.overlay.master.columnUtils:this.wot.columnUtils;z>=Object(g.innerWidth)(F)-U.scrollbarCompensation&&(this.cornerStyle.left="".concat(Math.floor(N+H-3-parseInt(this.cornerDefaultStyle.width,10)/2),"px"),this.cornerStyle.borderRightWidth=0)}if(r===this.wot.getSetting("totalRows")-1)(W?Object(g.getBoundingClientRect)(O).top:O.offsetTop)+Object(g.outerHeight)(O)+parseInt(this.cornerDefaultStyle.height,10)/2>=Object(g.innerHeight)(F)&&(this.cornerStyle.top="".concat(Math.floor(A+I-3-parseInt(this.cornerDefaultStyle.height,10)/2),"px"),this.cornerStyle.borderBottomWidth=0);this.cornerStyle.display="block"}Object(V.isMobileBrowser)()&&this.updateMultipleSelectionHandlesPosition(r,n,A,N,H,I)}else this.disappear()}}isEntireColumnSelected(e,t){return e===this.wot.wtTable.getFirstRenderedRow()&&t===this.wot.wtTable.getLastRenderedRow()}isEntireRowSelected(e,t){return e===this.wot.wtTable.getFirstRenderedColumn()&&t===this.wot.wtTable.getLastRenderedColumn()}getDimensionsFromHeader(e,t,r,o){var{wtTable:n}=this.wot,i=n.wtRootElement.parentNode,s=null,a=null,l=null,c=null,h=null,d=null,u=null,f=null;switch(e){case"rows":s=function(){return n.getRowHeader(...arguments)},a=function(){return Object(g.outerHeight)(...arguments)},l="ht__selection--rows",d="top";break;case"columns":s=function(){return n.getColumnHeader(...arguments)},a=function(){return Object(g.outerWidth)(...arguments)},l="ht__selection--columns",d="left"}if(i.className.includes(l)){var m=this.wot.getSetting("columnHeaders").length;if(u=s(t,m-1),f=s(r,m-1),!u||!f)return!1;var p=Object(g.offset)(u),v=Object(g.offset)(f);return u&&f&&(c=p[d]-o[d]-1,h=v[d]+a(f)-p[d]),[u,c,h]}return!1}disappear(){this.cornerStyle.display="none",Object(V.isMobileBrowser)()&&(this.selectionHandles.styles.topLeft.display="none",this.selectionHandles.styles.bottomRight.display="none")}destroy(){this.eventManager.destroyWithOwnEventsOnly(),this.main.parentNode.removeChild(this.main)}};var st=class{constructor(e,t){this.settings=e,this.cellRange=t||null,this.instanceSelectionHandles=new Map,this.classNames=[this.settings.className],this.classNameGenerator=this.linearClassNameGenerator(this.settings.className,this.settings.layerLevel)}hasSelectionHandle(){return this.settings.border&&"function"==typeof this.settings.border.cornerVisible}getSelectionHandle(e){var t=this.getSelectionHandleIfExists(e);if(t)return t;var r=new it(e,this.settings);return this.instanceSelectionHandles.set(e,r),r}getSelectionHandleIfExists(e){return this.instanceSelectionHandles.get(e)}isEmpty(){return null===this.cellRange}add(e){return this.isEmpty()?this.cellRange=new h(e):this.cellRange.expand(e),this}replace(e,t){if(!this.isEmpty()){if(this.cellRange.from.isEqual(e))return this.cellRange.from=t,!0;if(this.cellRange.to.isEqual(e))return this.cellRange.to=t,!0}return!1}clear(){return this.cellRange=null,this}getCorners(){return this.cellRange.getCorners()}addClassAtCoords(e,t,r,o){var n=arguments.length>4&&void 0!==arguments[4]&&arguments[4],i=e.getCell(new c(t,r));if("object"==typeof i){var s=o;n&&(s=this.classNameGenerator(i),this.classNames.includes(s)||this.classNames.push(s)),Object(g.addClass)(i,s)}return this}linearClassNameGenerator(e,t){return function r(o){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1;if(0===t||0===n)return e;var i=n>=0?n:t,s=e,a=0===(i-=1)?e:"".concat(e,"-").concat(i);if(Object(g.hasClass)(o,a)){var l=i+1;s="".concat(e,"-").concat(l)}else s=r(o,i);return s}}addClassIfElemExists(e,t){e&&Object(g.addClass)(e,t)}draw(e,t,r,o,n,i,s){if(!this.isEmpty()){var a,{wtTable:l}=e,{highlightHeaderClassName:c,highlightRowClassName:h,highlightColumnClassName:d}=this.settings,u=this.getCorners(),[g,f,m,p]=u,v=Math.max(g,o),w=Math.max(f,n),b=Math.min(m,i),C=Math.min(p,s),E={row:v,col:w},O={row:b,col:C},y=v===g,R=C===p,T=b===m,S=w===f;if(r&&(c||d))for(var M=w;M<=C;M+=1)if(this.addClassIfElemExists(l.getColumnHeader(M),[c,d]),d)for(var N=0;N<t;N+=1)if(N<v||N>b){var H=l.rowFilter.renderedToSource(N);this.addClassAtCoords(l,H,M,d)}if(t&&(c||h))for(var L=v;L<=b;L+=1)if(this.addClassIfElemExists(l.getRowHeader(L),[c,h]),h)for(var x=0;x<r;x+=1)if(x<w||x>C){var A=l.columnFilter.renderedToSource(x);this.addClassAtCoords(l,L,A,h)}this.settings.border&&v<=b&&w<=C&&(a={settings:this.settings,selectionStart:E,selectionEnd:O,hasTopEdge:y,hasRightEdge:R,hasBottomEdge:T,hasLeftEdge:S});for(var I=v;I<=b;I+=1)for(var j=w;j<=C;j+=1)if(I>=v&&I<=b&&j>=w&&j<=C&&this.settings.className&&this.addClassAtCoords(l,I,j,this.settings.className,this.settings.markIntersections),this.settings.className){var D=e.getSetting("onAfterDrawSelection",I,j,u,this.settings.layerLevel);"string"==typeof D&&this.addClassAtCoords(l,I,j,D)}return e.getSetting("onBeforeDrawBorders",u,this.settings.className),this.hasSelectionHandle()&&this.getSelectionHandle(e).appear(u),a}if(this.hasSelectionHandle()){var _=this.getSelectionHandleIfExists(e);_&&_.disappear()}}destroy(){this.instanceSelectionHandles.forEach(e=>e.destroy())}}},,function(e,t,r){"use strict";r.r(t),r.d(t,"stringify",(function(){return o})),r.d(t,"isDefined",(function(){return n})),r.d(t,"isUndefined",(function(){return i})),r.d(t,"isEmpty",(function(){return s})),r.d(t,"isRegExp",(function(){return a})),r.d(t,"_injectProductInfo",(function(){return l}));r(54);function o(e){var t;switch(typeof e){case"string":case"number":t="".concat(e);break;case"object":t=null===e?"":e.toString();break;case"undefined":t="";break;default:t=e.toString()}return t}function n(e){return void 0!==e}function i(e){return void 0===e}function s(e){return null===e||""===e||i(e)}function a(e){return"[object RegExp]"===Object.prototype.toString.call(e)}function l(e,t){}},,,function(e,t,r){"use strict";r.d(t,"b",(function(){return s}));var o=r(55),n=r(57),i=0;function s(){return i}t.a=class{constructor(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.context=e||this,this.context.eventListeners||(this.context.eventListeners=[])}addEventListener(e,t,r){var s=arguments.length>3&&void 0!==arguments[3]&&arguments[3];function a(e){r.call(this,function(e){var t=e.stopImmediatePropagation;return e.stopImmediatePropagation=function(){t.apply(this),Object(n.stopImmediatePropagation)(this)},e}(e))}return"boolean"==typeof s||Object(o.isPassiveEventSupported)()||(s=!1),this.context.eventListeners.push({element:e,event:t,callback:r,callbackProxy:a,options:s,eventManager:this}),e.addEventListener(t,a,s),i+=1,()=>{this.removeEventListener(e,t,r)}}removeEventListener(e,t,r){for(var o,n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],s=this.context.eventListeners.length;s;)if(s-=1,(o=this.context.eventListeners[s]).event===t&&o.element===e){if(r&&r!==o.callback)continue;if(n&&o.eventManager!==this)continue;this.context.eventListeners.splice(s,1),o.element.removeEventListener(o.event,o.callbackProxy,o.options),i-=1}}clearEvents(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.context)for(var t=this.context.eventListeners.length;t;){t-=1;var r=this.context.eventListeners[t];r&&this.removeEventListener(r.element,r.event,r.callback,e)}}clear(){this.clearEvents()}destroy(){this.clearEvents(),this.context=null}destroyWithOwnEventsOnly(){this.clearEvents(!0),this.context=null}fireEvent(e,t){var r=e.document,o=e;r||(o=(r=e.ownerDocument?e.ownerDocument:e).defaultView);var n,i={bubbles:!0,cancelable:"mousemove"!==t,view:o,detail:0,screenX:0,screenY:0,clientX:1,clientY:1,ctrlKey:!1,altKey:!1,shiftKey:!1,metaKey:!1,button:0,relatedTarget:void 0};r.createEvent?(n=r.createEvent("MouseEvents")).initMouseEvent(t,i.bubbles,i.cancelable,i.view,i.detail,i.screenX,i.screenY,i.clientX,i.clientY,i.ctrlKey,i.altKey,i.shiftKey,i.metaKey,i.button,i.relatedTarget||r.body.parentNode):n=r.createEventObject(),e.dispatchEvent?e.dispatchEvent(n):e.fireEvent("on".concat(t),n)}}},function(e,t,r){"use strict";function o(e){var t=typeof e;return"number"==t?!isNaN(e)&&isFinite(e):"string"==t?!!e.length&&(1==e.length?/\d/.test(e):/^\s*[+-]?\s*(?:(?:\d+(?:\.\d+)?(?:e[+-]?\d+)?)|(?:0x[a-f\d]+))\s*$/i.test(e)):"object"==t&&!(!e||"number"!=typeof e.valueOf()||e instanceof Date)}function n(e,t,r){var o=-1;for("function"==typeof t?(r=t,t=e):o=e-1;++o<=t&&!1!==r(o););}function i(e,t,r){var o=e+1;for("function"==typeof t&&(r=t,t=0);--o>=t&&!1!==r(o););}function s(e,t){return t=parseInt(t.toString().replace("%",""),10),t=parseInt(e*t/100,10)}r.r(t),r.d(t,"isNumeric",(function(){return o})),r.d(t,"rangeEach",(function(){return n})),r.d(t,"rangeEachReverse",(function(){return i})),r.d(t,"valueAccordingPercent",(function(){return s}))},function(e,t,r){"use strict";r.r(t),r.d(t,"CONTEXT_MENU_ITEMS_NAMESPACE",(function(){return o})),r.d(t,"CONTEXTMENU_ITEMS_NO_ITEMS",(function(){return n})),r.d(t,"CONTEXTMENU_ITEMS_ROW_ABOVE",(function(){return i})),r.d(t,"CONTEXTMENU_ITEMS_ROW_BELOW",(function(){return s})),r.d(t,"CONTEXTMENU_ITEMS_INSERT_LEFT",(function(){return a})),r.d(t,"CONTEXTMENU_ITEMS_INSERT_RIGHT",(function(){return l})),r.d(t,"CONTEXTMENU_ITEMS_REMOVE_ROW",(function(){return c})),r.d(t,"CONTEXTMENU_ITEMS_REMOVE_COLUMN",(function(){return h})),r.d(t,"CONTEXTMENU_ITEMS_UNDO",(function(){return d})),r.d(t,"CONTEXTMENU_ITEMS_REDO",(function(){return u})),r.d(t,"CONTEXTMENU_ITEMS_READ_ONLY",(function(){return g})),r.d(t,"CONTEXTMENU_ITEMS_CLEAR_COLUMN",(function(){return f})),r.d(t,"CONTEXTMENU_ITEMS_COPY",(function(){return m})),r.d(t,"CONTEXTMENU_ITEMS_CUT",(function(){return p})),r.d(t,"CONTEXTMENU_ITEMS_FREEZE_COLUMN",(function(){return v})),r.d(t,"CONTEXTMENU_ITEMS_UNFREEZE_COLUMN",(function(){return w})),r.d(t,"CONTEXTMENU_ITEMS_MERGE_CELLS",(function(){return b})),r.d(t,"CONTEXTMENU_ITEMS_UNMERGE_CELLS",(function(){return C})),r.d(t,"CONTEXTMENU_ITEMS_ADD_COMMENT",(function(){return E})),r.d(t,"CONTEXTMENU_ITEMS_EDIT_COMMENT",(function(){return O})),r.d(t,"CONTEXTMENU_ITEMS_REMOVE_COMMENT",(function(){return y})),r.d(t,"CONTEXTMENU_ITEMS_READ_ONLY_COMMENT",(function(){return R})),r.d(t,"CONTEXTMENU_ITEMS_ALIGNMENT",(function(){return T})),r.d(t,"CONTEXTMENU_ITEMS_ALIGNMENT_LEFT",(function(){return S})),r.d(t,"CONTEXTMENU_ITEMS_ALIGNMENT_CENTER",(function(){return M})),r.d(t,"CONTEXTMENU_ITEMS_ALIGNMENT_RIGHT",(function(){return N})),r.d(t,"CONTEXTMENU_ITEMS_ALIGNMENT_JUSTIFY",(function(){return H})),r.d(t,"CONTEXTMENU_ITEMS_ALIGNMENT_TOP",(function(){return L})),r.d(t,"CONTEXTMENU_ITEMS_ALIGNMENT_MIDDLE",(function(){return x})),r.d(t,"CONTEXTMENU_ITEMS_ALIGNMENT_BOTTOM",(function(){return A})),r.d(t,"CONTEXTMENU_ITEMS_BORDERS",(function(){return I})),r.d(t,"CONTEXTMENU_ITEMS_BORDERS_TOP",(function(){return j})),r.d(t,"CONTEXTMENU_ITEMS_BORDERS_RIGHT",(function(){return D})),r.d(t,"CONTEXTMENU_ITEMS_BORDERS_BOTTOM",(function(){return _})),r.d(t,"CONTEXTMENU_ITEMS_BORDERS_LEFT",(function(){return k})),r.d(t,"CONTEXTMENU_ITEMS_REMOVE_BORDERS",(function(){return P})),r.d(t,"CONTEXTMENU_ITEMS_NESTED_ROWS_INSERT_CHILD",(function(){return B})),r.d(t,"CONTEXTMENU_ITEMS_NESTED_ROWS_DETACH_CHILD",(function(){return F})),r.d(t,"CONTEXTMENU_ITEMS_HIDE_COLUMN",(function(){return V})),r.d(t,"CONTEXTMENU_ITEMS_SHOW_COLUMN",(function(){return W})),r.d(t,"CONTEXTMENU_ITEMS_HIDE_ROW",(function(){return z})),r.d(t,"CONTEXTMENU_ITEMS_SHOW_ROW",(function(){return U})),r.d(t,"FILTERS_NAMESPACE",(function(){return G})),r.d(t,"FILTERS_CONDITIONS_NAMESPACE",(function(){return X})),r.d(t,"FILTERS_CONDITIONS_NONE",(function(){return K})),r.d(t,"FILTERS_CONDITIONS_EMPTY",(function(){return Y})),r.d(t,"FILTERS_CONDITIONS_NOT_EMPTY",(function(){return q})),r.d(t,"FILTERS_CONDITIONS_EQUAL",(function(){return Q})),r.d(t,"FILTERS_CONDITIONS_NOT_EQUAL",(function(){return Z})),r.d(t,"FILTERS_CONDITIONS_BEGINS_WITH",(function(){return J})),r.d(t,"FILTERS_CONDITIONS_ENDS_WITH",(function(){return $})),r.d(t,"FILTERS_CONDITIONS_CONTAINS",(function(){return ee})),r.d(t,"FILTERS_CONDITIONS_NOT_CONTAIN",(function(){return te})),r.d(t,"FILTERS_CONDITIONS_BY_VALUE",(function(){return re})),r.d(t,"FILTERS_CONDITIONS_GREATER_THAN",(function(){return oe})),r.d(t,"FILTERS_CONDITIONS_GREATER_THAN_OR_EQUAL",(function(){return ne})),r.d(t,"FILTERS_CONDITIONS_LESS_THAN",(function(){return ie})),r.d(t,"FILTERS_CONDITIONS_LESS_THAN_OR_EQUAL",(function(){return se})),r.d(t,"FILTERS_CONDITIONS_BETWEEN",(function(){return ae})),r.d(t,"FILTERS_CONDITIONS_NOT_BETWEEN",(function(){return le})),r.d(t,"FILTERS_CONDITIONS_AFTER",(function(){return ce})),r.d(t,"FILTERS_CONDITIONS_BEFORE",(function(){return he})),r.d(t,"FILTERS_CONDITIONS_TODAY",(function(){return de})),r.d(t,"FILTERS_CONDITIONS_TOMORROW",(function(){return ue})),r.d(t,"FILTERS_CONDITIONS_YESTERDAY",(function(){return ge})),r.d(t,"FILTERS_DIVS_FILTER_BY_CONDITION",(function(){return fe})),r.d(t,"FILTERS_DIVS_FILTER_BY_VALUE",(function(){return me})),r.d(t,"FILTERS_LABELS_CONJUNCTION",(function(){return pe})),r.d(t,"FILTERS_LABELS_DISJUNCTION",(function(){return ve})),r.d(t,"FILTERS_VALUES_BLANK_CELLS",(function(){return we})),r.d(t,"FILTERS_BUTTONS_SELECT_ALL",(function(){return be})),r.d(t,"FILTERS_BUTTONS_CLEAR",(function(){return Ce})),r.d(t,"FILTERS_BUTTONS_OK",(function(){return Ee})),r.d(t,"FILTERS_BUTTONS_CANCEL",(function(){return Oe})),r.d(t,"FILTERS_BUTTONS_PLACEHOLDER_SEARCH",(function(){return ye})),r.d(t,"FILTERS_BUTTONS_PLACEHOLDER_VALUE",(function(){return Re})),r.d(t,"FILTERS_BUTTONS_PLACEHOLDER_SECOND_VALUE",(function(){return Te}));var o="ContextMenu:items",n="".concat(o,".noItems"),i="".concat(o,".insertRowAbove"),s="".concat(o,".insertRowBelow"),a="".concat(o,".insertColumnOnTheLeft"),l="".concat(o,".insertColumnOnTheRight"),c="".concat(o,".removeRow"),h="".concat(o,".removeColumn"),d="".concat(o,".undo"),u="".concat(o,".redo"),g="".concat(o,".readOnly"),f="".concat(o,".clearColumn"),m="".concat(o,".copy"),p="".concat(o,".cut"),v="".concat(o,".freezeColumn"),w="".concat(o,".unfreezeColumn"),b="".concat(o,".mergeCells"),C="".concat(o,".unmergeCells"),E="".concat(o,".addComment"),O="".concat(o,".editComment"),y="".concat(o,".removeComment"),R="".concat(o,".readOnlyComment"),T="".concat(o,".align"),S="".concat(o,".align.left"),M="".concat(o,".align.center"),N="".concat(o,".align.right"),H="".concat(o,".align.justify"),L="".concat(o,".align.top"),x="".concat(o,".align.middle"),A="".concat(o,".align.bottom"),I="".concat(o,".borders"),j="".concat(o,".borders.top"),D="".concat(o,".borders.right"),_="".concat(o,".borders.bottom"),k="".concat(o,".borders.left"),P="".concat(o,".borders.remove"),B="".concat(o,".nestedHeaders.insertChildRow"),F="".concat(o,".nestedHeaders.detachFromParent"),V="".concat(o,".hideColumn"),W="".concat(o,".showColumn"),z="".concat(o,".hideRow"),U="".concat(o,".showRow"),G="Filters:",X="".concat(G,"conditions"),K="".concat(X,".none"),Y="".concat(X,".isEmpty"),q="".concat(X,".isNotEmpty"),Q="".concat(X,".isEqualTo"),Z="".concat(X,".isNotEqualTo"),J="".concat(X,".beginsWith"),$="".concat(X,".endsWith"),ee="".concat(X,".contains"),te="".concat(X,".doesNotContain"),re="".concat(X,".byValue"),oe="".concat(X,".greaterThan"),ne="".concat(X,".greaterThanOrEqualTo"),ie="".concat(X,".lessThan"),se="".concat(X,".lessThanOrEqualTo"),ae="".concat(X,".isBetween"),le="".concat(X,".isNotBetween"),ce="".concat(X,".after"),he="".concat(X,".before"),de="".concat(X,".today"),ue="".concat(X,".tomorrow"),ge="".concat(X,".yesterday"),fe="".concat(G,"labels.filterByCondition"),me="".concat(G,"labels.filterByValue"),pe="".concat(G,"labels.conjunction"),ve="".concat(G,"labels.disjunction"),we="".concat(G,"values.blankCells"),be="".concat(G,"buttons.selectAll"),Ce="".concat(G,"buttons.clear"),Ee="".concat(G,"buttons.ok"),Oe="".concat(G,"buttons.cancel"),ye="".concat(G,"buttons.placeholder.search"),Re="".concat(G,"buttons.placeholder.value"),Te="".concat(G,"buttons.placeholder.secondValue")},function(e,t,r){"use strict";r.r(t),r.d(t,"setBrowserMeta",(function(){return a})),r.d(t,"setPlatformMeta",(function(){return l})),r.d(t,"isChrome",(function(){return c})),r.d(t,"isEdge",(function(){return h})),r.d(t,"isIE",(function(){return d})),r.d(t,"isIE9",(function(){return u})),r.d(t,"isMSBrowser",(function(){return g})),r.d(t,"isMobileBrowser",(function(){return f})),r.d(t,"isSafari",(function(){return m})),r.d(t,"isFirefox",(function(){return p})),r.d(t,"isWindowsOS",(function(){return v})),r.d(t,"isMacOS",(function(){return w})),r.d(t,"isLinuxOS",(function(){return b}));var o=r(32),n=e=>{var t={value:!1,test:(r,o)=>{t.value=e(r,o)}};return t},i={chrome:n((e,t)=>/Chrome/.test(e)&&/Google/.test(t)),edge:n(e=>/Edge/.test(e)),firefox:n(e=>/Firefox/.test(e)),ie:n(e=>/Trident/.test(e)),ie9:n(()=>!!document.documentMode),mobile:n(e=>/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(e)),safari:n((e,t)=>/Safari/.test(e)&&/Apple Computer/.test(t))},s={mac:n(e=>/^Mac/.test(e)),win:n(e=>/^Win/.test(e)),linux:n(e=>/^Linux/.test(e))};function a(){var{userAgent:e=navigator.userAgent,vendor:t=navigator.vendor}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Object(o.objectEach)(i,r=>{var{test:o}=r;o(e,t)})}function l(){var{platform:e=navigator.platform}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Object(o.objectEach)(s,t=>{var{test:r}=t;r(e)})}function c(){return i.chrome.value}function h(){return i.edge.value}function d(){return i.ie.value}function u(){return i.ie9.value}function g(){return i.ie.value||i.edge.value}function f(){return i.mobile.value}function m(){return i.safari.value}function p(){return i.firefox.value}function v(){return s.win.value}function w(){return s.mac.value}function b(){return s.linux.value}a(),l()},function(e,t,r){"use strict";r.d(t,"d",(function(){return a})),r.d(t,"a",(function(){return l})),r.d(t,"c",(function(){return c})),r.d(t,"b",(function(){return h}));var o=r(59),n=r(32),i=r(49),s=new WeakMap;function a(e,t){var r=Object(i.toUpperCaseFirst)(e);o.a.getSingleton().add("construct",(function(){s.has(this)||s.set(this,{});var e=s.get(this);e[r]||(e[r]=new t(this))})),o.a.getSingleton().add("afterDestroy",(function(){if(s.has(this)){var e=s.get(this);Object(n.objectEach)(e,e=>e.destroy()),s.delete(this)}}))}function l(e,t){if("string"!=typeof t)throw Error('Only strings can be passed as "plugin" parameter');var r=Object(i.toUpperCaseFirst)(t);if(s.has(e)&&s.get(e)[r])return s.get(e)[r]}function c(e){return s.has(e)?Object.keys(s.get(e)):[]}function h(e,t){var r=null;return s.has(e)&&Object(n.objectEach)(s.get(e),(e,o)=>{e===t&&(r=o)}),r}},,function(e,t,r){"use strict";r.r(t),r.d(t,"toUpperCaseFirst",(function(){return n})),r.d(t,"equalsIgnoreCase",(function(){return i})),r.d(t,"randomString",(function(){return s})),r.d(t,"isPercentValue",(function(){return a})),r.d(t,"substitute",(function(){return l})),r.d(t,"stripTags",(function(){return h}));var o=r(40);function n(e){return e[0].toUpperCase()+e.substr(1)}function i(){for(var e=[],t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];for(var i=r.length;i;){i-=1;var s=Object(o.stringify)(r[i]).toLowerCase();-1===e.indexOf(s)&&e.push(s)}return 1===e.length}function s(){function e(){return Math.floor(65536*(1+Math.random())).toString(16).substring(1)}return e()+e()+e()+e()}function a(e){return/^([0-9][0-9]?%$)|(^100%$)/.test(e)}function l(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return"".concat(e).replace(/(?:\\)?\[([^[\]]+)]/g,(e,r)=>"\\"===e.charAt(0)?e.substr(1,e.length-1):void 0===t[r]?"":t[r])}var c=/<\/?\w+\/?>|<\w+[\s|/][^>]*>/gi;function h(e){return"".concat(e).replace(c,"")}},,,function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));var o=r(1),n=r.n(o),i=e=>{var{description:t}=e;return n.a.createElement("p",{className:"toolbar-mobile-menu-item-text"},t)}},function(e,t,r){"use strict";r.d(t,"a",(function(){return n}));var o=new Map;function n(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"common";o.has(e)||o.set(e,new Map);var t=o.get(e);function r(e,r){t.set(e,r)}function n(e){return t.get(e)}function i(e){return t.has(e)}function s(){return[...t.keys()]}function a(){return[...t.values()]}return{register:r,getItem:n,hasItem:i,getNames:s,getValues:a}}},function(e,t,r){"use strict";r.d(t,"a",(function(){return n}));var o=r(33);function n(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var i=Object(o.arrayReduce)(e,(e,t,o)=>e+t.replace(/\r?\n\s*/g,"")+(r[o]?r[o]:""),"");return i.trim()}},function(e,t,r){"use strict";r.r(t),r.d(t,"requestAnimationFrame",(function(){return u})),r.d(t,"isClassListSupported",(function(){return g})),r.d(t,"isTextContentSupported",(function(){return f})),r.d(t,"isGetComputedStyleSupported",(function(){return m})),r.d(t,"cancelAnimationFrame",(function(){return p})),r.d(t,"isTouchSupported",(function(){return v})),r.d(t,"hasCaptionProblem",(function(){return w})),r.d(t,"getComparisonFunction",(function(){return b})),r.d(t,"isPassiveEventSupported",(function(){return C}));for(var o,n,i,s=r(34),a=0,l=["ms","moz","webkit","o"],c=window.requestAnimationFrame,h=window.cancelAnimationFrame,d=0;d<l.length&&!c;++d)c=window["".concat(l[d],"RequestAnimationFrame")],h=window["".concat(l[d],"CancelAnimationFrame")]||window["".concat(l[d],"CancelRequestAnimationFrame")];function u(e){return c.call(window,e)}function g(){return!!document.documentElement.classList}function f(){return!!document.createTextNode("test").textContent}function m(){return!!window.getComputedStyle}function p(e){h.call(window,e)}function v(){return"ontouchstart"in window}function w(){return void 0===o&&function(){var e=document.createElement("TABLE");e.style.borderSpacing="0",e.style.borderWidth="0",e.style.padding="0";var t=document.createElement("TBODY");e.appendChild(t),t.appendChild(document.createElement("TR")),t.firstChild.appendChild(document.createElement("TD")),t.firstChild.firstChild.innerHTML="<tr><td>t<br>t</td></tr>";var r=document.createElement("CAPTION");r.innerHTML="c<br>c<br>c<br>c",r.style.padding="0",r.style.margin="0",e.insertBefore(r,t),document.body.appendChild(e),o=Object(s.offsetHeight)(e)<2*Object(s.offsetHeight)(e.lastChild),document.body.removeChild(e)}(),o}function b(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n||(n="object"==typeof Intl?new Intl.Collator(e,t).compare:"function"==typeof String.prototype.localeCompare?(e,t)=>"".concat(e).localeCompare(t):(e,t)=>e===t?0:e>t?-1:1)}function C(){if(void 0!==i)return i;try{var e={get passive(){i=!0}};window.addEventListener("test",e,e),window.removeEventListener("test",e,e)}catch(e){i=!1}return i}c||(c=function(e){var t=(new Date).getTime(),r=Math.max(0,16-(t-a)),o=window.setTimeout(()=>{e(t+r)},r);return a=t+r,o}),h||(h=function(e){clearTimeout(e)})},function(e,t,r){"use strict";r.d(t,"a",(function(){return o}));var o=1},function(e,t,r){"use strict";function o(e){e.isImmediatePropagationEnabled=!1,e.cancelBubble=!0}function n(e){return!1===e.isImmediatePropagationEnabled}function i(e){return 2===e.button}function s(e){return 0===e.button}r.r(t),r.d(t,"stopImmediatePropagation",(function(){return o})),r.d(t,"isImmediatePropagationStopped",(function(){return n})),r.d(t,"isRightClick",(function(){return i})),r.d(t,"isLeftClick",(function(){return s}))},,function(e,t,r){"use strict";var o=r(33),n=r(32),i=r(49),s=r(60),a=r(54);function l(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['The plugin hook "[hookName]" was removed in Handsontable [removedInVersion]. \n  Please consult release notes https://github.com/handsontable/handsontable/releases/tag/[removedInVersion] to learn about the migration path.'],['The plugin hook "[hookName]" was removed in Handsontable [removedInVersion].\\x20\n  Please consult release notes https://github.com/handsontable/handsontable/releases/tag/[removedInVersion] to learn about the migration path.']);return l=function(){return e},e}var c=["afterCellMetaReset","afterChange","afterChangesObserved","afterContextMenuDefaultOptions","beforeContextMenuSetItems","afterDropdownMenuDefaultOptions","beforeDropdownMenuSetItems","afterContextMenuHide","beforeContextMenuShow","afterContextMenuShow","afterCopyLimit","beforeCreateCol","afterCreateCol","beforeCreateRow","afterCreateRow","afterDeselect","afterDestroy","afterDocumentKeyDown","afterDrawSelection","beforeRemoveCellClassNames","afterGetCellMeta","afterGetColHeader","afterGetRowHeader","afterInit","afterLoadData","afterMomentumScroll","afterOnCellCornerMouseDown","afterOnCellCornerDblClick","afterOnCellMouseDown","afterOnCellMouseUp","afterOnCellContextMenu","afterOnCellMouseOver","afterOnCellMouseOut","afterRemoveCol","afterRemoveRow","afterRender","beforeRenderer","afterRenderer","afterScrollHorizontally","afterScrollVertically","afterSelection","afterSelectionByProp","afterSelectionEnd","afterSelectionEndByProp","afterSetCellMeta","afterRemoveCellMeta","afterSetDataAtCell","afterSetDataAtRowProp","afterSetSourceDataAtCell","afterUpdateSettings","afterValidate","beforeLanguageChange","afterLanguageChange","beforeAutofill","afterAutofill","beforeCellAlignment","beforeChange","beforeChangeRender","beforeDrawBorders","beforeGetCellMeta","beforeRemoveCellMeta","beforeInit","beforeInitWalkontable","beforeLoadData","beforeKeyDown","beforeOnCellMouseDown","beforeOnCellMouseUp","beforeOnCellContextMenu","beforeOnCellMouseOver","beforeOnCellMouseOut","beforeRemoveCol","beforeRemoveRow","beforeRender","beforeSetCellMeta","beforeSetRangeStartOnly","beforeSetRangeStart","beforeSetRangeEnd","beforeTouchScroll","beforeValidate","beforeValueRender","construct","init","modifyColHeader","modifyColWidth","modifyRowHeader","modifyRowHeight","modifyData","modifySourceData","modifyRowData","modifyGetCellCoords","persistentStateLoad","persistentStateReset","persistentStateSave","beforeColumnSort","afterColumnSort","modifyAutofillRange","modifyCopyableRange","beforeCut","afterCut","beforeCopy","afterCopy","beforePaste","afterPaste","beforeColumnMove","afterColumnMove","beforeRowMove","afterRowMove","beforeColumnResize","afterColumnResize","beforeRowResize","afterRowResize","afterGetColumnHeaderRenderers","afterGetRowHeaderRenderers","beforeStretchingColumnWidth","beforeFilter","afterFilter","modifyColumnHeaderHeight","beforeUndo","afterUndo","beforeRedo","afterRedo","modifyRowHeaderWidth","beforeAutofillInsidePopulate","modifyTransformStart","modifyTransformEnd","afterModifyTransformStart","afterModifyTransformEnd","afterViewportRowCalculatorOverride","afterViewportColumnCalculatorOverride","afterPluginsInitialized","beforeHideRows","afterHideRows","beforeUnhideRows","afterUnhideRows","beforeHideColumns","afterHideColumns","beforeUnhideColumns","afterUnhideColumns","beforeTrimRow","afterTrimRow","beforeUntrimRow","afterUntrimRow","beforeDropdownMenuShow","afterDropdownMenuShow","afterDropdownMenuHide","hiddenRow","beforeAddChild","afterAddChild","beforeDetachChild","afterDetachChild","afterBeginEditing","beforeMergeCells","afterMergeCells","beforeUnmergeCells","afterUnmergeCells","afterListen","afterUnlisten","afterRefreshDimensions","beforeRefreshDimensions","beforeColumnCollapse","afterColumnCollapse","beforeColumnExpand","afterColumnExpand"],h=Object(a.a)(l()),d=new Map([["modifyRow","8.0.0"],["modifyCol","8.0.0"],["unmodifyRow","8.0.0"],["unmodifyCol","8.0.0"],["skipLengthCache","8.0.0"],["hiddenColumn","8.0.0"],["hiddenRow","8.0.0"]]),u=new Map([]);class g{static getSingleton(){return f}constructor(){this.globalBucket=this.createEmptyBucket()}createEmptyBucket(){var e=Object.create(null);return Object(o.arrayEach)(c,t=>e[t]=[]),e}getBucket(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return e?(e.pluginHookBucket||(e.pluginHookBucket=this.createEmptyBucket()),e.pluginHookBucket):this.globalBucket}add(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(Array.isArray(t))Object(o.arrayEach)(t,t=>this.add(e,t,r));else{d.has(e)&&Object(s.b)(Object(i.substitute)(h,{hookName:e,removedInVersion:d.get(e)})),u.has(e)&&Object(s.b)(u.get(e));var n=this.getBucket(r);if(void 0===n[e]&&(this.register(e),n[e]=[]),t.skip=!1,-1===n[e].indexOf(t)){var a=!1;t.initialHook&&Object(o.arrayEach)(n[e],(r,o)=>{if(r.initialHook)return n[e][o]=t,a=!0,!1}),a||n[e].push(t)}}return this}once(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;Array.isArray(t)?Object(o.arrayEach)(t,t=>this.once(e,t,r)):(t.runOnce=!0,this.add(e,t,r))}remove(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=this.getBucket(r);return void 0!==o[e]&&o[e].indexOf(t)>=0&&(t.skip=!0,!0)}has(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=this.getBucket(t);return!(void 0===r[e]||!r[e].length)}run(e,t,r,o,n,i,s,a){var l=this.globalBucket[t],c=l?l.length:0,h=0;if(c)for(;h<c;)if(l[h]&&!l[h].skip){var d=l[h].call(e,r,o,n,i,s,a);void 0!==d&&(r=d),l[h]&&l[h].runOnce&&this.remove(t,l[h]),h+=1}else h+=1;var u=this.getBucket(e)[t],g=u?u.length:0,f=0;if(g)for(;f<g;)if(u[f]&&!u[f].skip){var m=u[f].call(e,r,o,n,i,s,a);void 0!==m&&(r=m),u[f]&&u[f].runOnce&&this.remove(t,u[f],e),f+=1}else f+=1;return r}destroy(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;Object(n.objectEach)(this.getBucket(e),(e,t,r)=>r[t].length=0)}register(e){this.isRegistered(e)||c.push(e)}deregister(e){this.isRegistered(e)&&c.splice(c.indexOf(e),1)}isDeprecated(e){return u.has(e)||d.has(e)}isRegistered(e){return c.indexOf(e)>=0}getRegistered(){return c}}var f=new g;t.a=g},function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"a",(function(){return i}));var o=r(40);function n(){Object(o.isDefined)(console)&&console.warn(...arguments)}function i(){Object(o.isDefined)(console)&&console.error(...arguments)}},function(e,t,r){"use strict";r.r(t),r.d(t,"isFunction",(function(){return n})),r.d(t,"throttle",(function(){return i})),r.d(t,"throttleAfterHits",(function(){return s})),r.d(t,"debounce",(function(){return a})),r.d(t,"pipe",(function(){return l})),r.d(t,"partial",(function(){return c})),r.d(t,"curry",(function(){return h})),r.d(t,"curryRight",(function(){return d}));var o=r(33);function n(e){return"function"==typeof e}function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:200,r=0,o={lastCallThrottled:!0},n=null;function i(){for(var i=arguments.length,s=new Array(i),a=0;a<i;a++)s[a]=arguments[a];var l=Date.now(),c=!1;o.lastCallThrottled=!0,r||(r=l,c=!0);var h=t-(l-r);return c?(o.lastCallThrottled=!1,e.apply(this,s)):(n&&clearTimeout(n),n=setTimeout(()=>{o.lastCallThrottled=!1,e.apply(this,s),r=0,n=void 0},h)),o}return i}function s(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:200,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,o=i(e,t),n=r;function s(){n=r}function a(){for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];return n?(n-=1,e.apply(this,r)):o.apply(this,r)}return a.clearHits=s,a}function a(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:200,o=null;function n(){for(var n=arguments.length,i=new Array(n),s=0;s<n;s++)i[s]=arguments[s];return o&&clearTimeout(o),o=setTimeout(()=>{t=e.apply(this,i)},r),t}return n}function l(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var[n,...i]=t;return function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Object(o.arrayReduce)(i,(e,t)=>t(e),n.apply(this,t))}}function c(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),o=1;o<t;o++)r[o-1]=arguments[o];return function(){for(var t=arguments.length,o=new Array(t),n=0;n<t;n++)o[n]=arguments[n];return e.apply(this,r.concat(o))}}function h(e){var t=e.length;return function r(o){return function(){for(var n=arguments.length,i=new Array(n),s=0;s<n;s++)i[s]=arguments[s];var a=o.concat(i);return a.length>=t?e.apply(this,a):r(a)}}([])}function d(e){var t=e.length;return function r(o){return function(){for(var n=arguments.length,i=new Array(n),s=0;s<n;s++)i[s]=arguments[s];var a=o.concat(i.reverse());return a.length>=t?e.apply(this,a):r(a)}}([])}},function(e,t,r){"use strict";var o=r(33),n=r(32),i={_localHooks:Object.create(null),addLocalHook(e,t){return this._localHooks[e]||(this._localHooks[e]=[]),this._localHooks[e].push(t),this},runLocalHooks(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];this._localHooks[e]&&Object(o.arrayEach)(this._localHooks[e],e=>e.apply(this,r))},clearLocalHooks(){return this._localHooks={},this}};Object(n.defineGetter)(i,"MIXIN_NAME","localHooks",{writable:!1,enumerable:!1}),t.a=i},function(e,t){},,,function(e,t,r){"use strict";r.d(t,"a",(function(){return a}));var o=r(1),n=r.n(o),i=r(67),s=e=>{var{state:t,close:r}=e;return n.a.createElement(n.a.Fragment,null,t[i.b]&&n.a.createElement(i.a,{onClose:()=>r(i.b)}))},a=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[t,r]=Object(o.useState)(()=>e),i=e=>{r(t=>Object.assign(Object.assign({},t),{[e]:!0}))},a=e=>{r(t=>Object.assign(Object.assign({},t),{[e]:!1}))},l=()=>n.a.createElement(s,{state:t,close:a});return{Modals:l,openModal:i,closeModal:a}}},function(e,t,r){"use strict";r.d(t,"b",(function(){return l})),r.d(t,"a",(function(){return c}));var o=r(1),n=r.n(o),i=r(189),s=e=>{var{onClose:t,isMobile:r}=e;return n.a.createElement("svg",Object.assign({viewBox:"0 0 16 16",className:"close-icon-modal",onClick:t,"data-cy":"close-modal"},r?{width:24,height:24}:{width:16,height:16}),n.a.createElement("path",{d:"M12.054 4.95l-.844-.844a.066.066 0 00-.093 0l-3 3a.066.066 0 01-.093 0l-3-3a.066.066 0 00-.093 0l-.838.844a.066.066 0 000 .093l3 3a.066.066 0 010 .093l-3 3a.066.066 0 000 .093l.844.844a.066.066 0 00.093 0l3-3a.066.066 0 01.093 0l3 3a.066.066 0 00.093 0l.844-.844a.066.066 0 000-.093l-3-3a.066.066 0 010-.093l3-3a.066.066 0 00-.006-.093z",fill:"#1d1d1d"}))},a=r(17),l="feedback",c=e=>{var{onClose:t}=e,r=Object(a.b)();return n.a.createElement(i.a,{open:!0,onClose:t,PaperComponent:()=>n.a.createElement("div",{className:"modal modal-feedback","data-cy":"modal-feedback"},n.a.createElement(s,{onClose:t,isMobile:r}),n.a.createElement("p",{className:"modal-title"},"Feedback"),n.a.createElement("p",{className:"modal-description"},"Are you missing a feature?",n.a.createElement("br",null),"Do you think an existing function could be improved?"),n.a.createElement("a",{href:"https://handsoncode.typeform.com/to/LqlEyO8z",target:"_blank",rel:"noopener noreferrer",className:"sv-button ".concat(r?"mobile-feedback-button":"")},"Fill out the feedback form")),classes:{root:"modal-feedback-background"}})}},,function(e,t,r){"use strict";r.d(t,"a",(function(){return n}));var o=r(1),n=e=>{var[t,r]=Object(o.useState)(void 0);return Object(o.useEffect)(()=>{if("string"==typeof e)return 0===e.indexOf("data:")||e.indexOf("blob:"),void r(e);var t=URL.createObjectURL(new Blob([e]));return r(t),()=>URL.revokeObjectURL(t)},[e]),t}},,,function(e,t,r){"use strict";var o;r.d(t,"a",(function(){return n}));var n=()=>{var e;return void 0===o&&(o="download"in document.createElement("a")&&!(null===(e=window.webkit)||void 0===e?void 0:e.messageHandlers)),o}},function(e,t,r){"use strict";(function(e){r(137),r(138),r(139),r(140);var o=r(87),n=r(97),i=r(88),s=r(89),a=r(142),l=r(147),c=r(43),h=r(92),d=r(59),u=r(93),g=r(90),f=r(33),m=r(46),p=r(75),v=r(148),w=r(55),b=r(61),C=r(40),E=r(44),O=r(32),y=r(49),R=r(74),T=r(34),S=r(57),M=r(150),N=r(47),H=r(124),L=r(118),x=r(127),A=r(45),I=r(79);function j(e,t){var r=new a.a(e,t||{},L.d);return r.init(),r}Object(l.a)(j),j.Core=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new a.a(e,t,L.d)},j.DefaultSettings=Object(H.c)(),j.EventManager=c.a,j._getListenersCounter=c.b,j._getRegisteredMapsCounter=h.b,j.packageName="handsontable",j.buildDate=e.env.HOT_BUILD_DATE,j.version=e.env.HOT_VERSION,j.hooks=d.a.getSingleton(),j.__GhostTable=u.a;var D=[f,m,p,v,w,b,C,E,O,y,R,g],_=[T,S];j.helper={},j.dom={},f.arrayEach(D,e=>{f.arrayEach(Object.getOwnPropertyNames(e),t=>{"_"!==t.charAt(0)&&(j.helper[t]=e[t])})}),f.arrayEach(_,e=>{f.arrayEach(Object.getOwnPropertyNames(e),t=>{"_"!==t.charAt(0)&&(j.dom[t]=e[t])})}),j.cellTypes={},f.arrayEach(Object(s.b)(),e=>{j.cellTypes[e]=Object(s.a)(e)}),j.cellTypes.registerCellType=s.c,j.cellTypes.getCellType=s.a,j.editors={},f.arrayEach(Object(o.c)(),e=>{j.editors["".concat(y.toUpperCaseFirst(e),"Editor")]=Object(o.a)(e)}),j.editors.registerEditor=o.d,j.editors.getEditor=o.a,j.renderers={},f.arrayEach(Object(n.a)(),e=>{var t=Object(n.b)(e);"base"===e&&(j.renderers.cellDecorator=t),j.renderers["".concat(y.toUpperCaseFirst(e),"Renderer")]=t}),j.renderers.registerRenderer=n.c,j.renderers.getRenderer=n.b,j.validators={},f.arrayEach(Object(i.a)(),e=>{j.validators["".concat(y.toUpperCaseFirst(e),"Validator")]=Object(i.b)(e)}),j.validators.registerValidator=i.c,j.validators.getValidator=i.b,j.plugins={},f.arrayEach(Object.getOwnPropertyNames(M),e=>{var t=M[e];"Base"===e?j.plugins["".concat(e,"Plugin")]=t:j.plugins[e]=t}),j.plugins.registerPlugin=N.d,j.languages={},j.languages.dictionaryKeys=A,j.languages.getLanguageDictionary=I.b,j.languages.getLanguagesDictionaries=I.c,j.languages.registerLanguageDictionary=I.e,j.languages.getTranslatedPhrase=function(){return Object(x.a)(...arguments)},t.a=j}).call(this,r(115))},function(e,t,r){"use strict";r.r(t),r.d(t,"KEY_CODES",(function(){return n})),r.d(t,"isPrintableChar",(function(){return i})),r.d(t,"isMetaKey",(function(){return s})),r.d(t,"isCtrlKey",(function(){return a})),r.d(t,"isCtrlMetaKey",(function(){return l})),r.d(t,"isKey",(function(){return c}));var o=r(33),n={MOUSE_LEFT:1,MOUSE_RIGHT:3,MOUSE_MIDDLE:2,BACKSPACE:8,COMMA:188,INSERT:45,DELETE:46,END:35,ENTER:13,ESCAPE:27,CONTROL:17,COMMAND_LEFT:91,COMMAND_RIGHT:93,COMMAND_FIREFOX:224,ALT:18,HOME:36,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,SPACE:32,SHIFT:16,CAPS_LOCK:20,TAB:9,ARROW_RIGHT:39,ARROW_LEFT:37,ARROW_UP:38,ARROW_DOWN:40,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,A:65,C:67,D:68,F:70,L:76,O:79,P:80,S:83,V:86,X:88};function i(e){return 32===e||e>=48&&e<=57||e>=96&&e<=111||e>=186&&e<=192||e>=219&&e<=222||e>=226||e>=65&&e<=90}function s(e){return-1!==[n.ARROW_DOWN,n.ARROW_UP,n.ARROW_LEFT,n.ARROW_RIGHT,n.HOME,n.END,n.DELETE,n.BACKSPACE,n.F1,n.F2,n.F3,n.F4,n.F5,n.F6,n.F7,n.F8,n.F9,n.F10,n.F11,n.F12,n.TAB,n.PAGE_DOWN,n.PAGE_UP,n.ENTER,n.ESCAPE,n.SHIFT,n.CAPS_LOCK,n.ALT].indexOf(e)}function a(e){var t=[];return navigator.platform.includes("Mac")?t.push(n.COMMAND_LEFT,n.COMMAND_RIGHT,n.COMMAND_FIREFOX):t.push(n.CONTROL),t.includes(e)}function l(e){return[n.CONTROL,n.COMMAND_LEFT,n.COMMAND_RIGHT,n.COMMAND_FIREFOX].includes(e)}function c(e,t){var r=t.split("|"),i=!1;return Object(o.arrayEach)(r,t=>{if(e===n[t])return i=!0,!1}),i}},function(e,t,r){"use strict";r.r(t),r.d(t,"spreadsheetColumnLabel",(function(){return s})),r.d(t,"spreadsheetColumnIndex",(function(){return a})),r.d(t,"createSpreadsheetData",(function(){return l})),r.d(t,"createSpreadsheetObjectData",(function(){return c})),r.d(t,"createEmptySpreadsheetData",(function(){return h})),r.d(t,"translateRowsToColumns",(function(){return d})),r.d(t,"cellMethodLookupFactory",(function(){return u})),r.d(t,"dataRowToChangesArray",(function(){return g})),r.d(t,"countFirstRowKeys",(function(){return f}));var o=r(89),n=r(32),i="ABCDEFGHIJKLMNOPQRSTUVWXYZ".length;function s(e){for(var t,r=e+1,o="";r>0;)t=(r-1)%i,o=String.fromCharCode(65+t)+o,r=parseInt((r-t)/i,10);return o}function a(e){var t=0;if(e)for(var r=0,o=e.length-1;r<e.length;r+=1,o-=1)t+=i**o*("ABCDEFGHIJKLMNOPQRSTUVWXYZ".indexOf(e[r])+1);return t-=1}function l(){var e,t,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4,n=[];for(e=0;e<r;e++){var i=[];for(t=0;t<o;t++)i.push(s(t)+(e+1));n.push(i)}return n}function c(){var e,t,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4,n=[];for(e=0;e<r;e++){var i={};for(t=0;t<o;t++)i["prop".concat(t)]=s(t)+(e+1);n.push(i)}return n}function h(e,t){for(var r,o=[],n=0;n<e;n++){r=[];for(var i=0;i<t;i++)r.push("");o.push(r)}return o}function d(e){var t,r,o,n,i=[],s=0;for(t=0,r=e.length;t<r;t++)for(o=0,n=e[t].length;o<n;o++)o===s&&(i.push([]),s+=1),i[o].push(e[t][o]);return i}function u(e,t){var r=void 0===t||t;return function(t,i){return function t(i){if(i){if(Object(n.hasOwnProperty)(i,e)&&void 0!==i[e])return i[e];if(Object(n.hasOwnProperty)(i,"type")&&i.type){if("string"!=typeof i.type)throw new Error('Cell "type" must be a string');var s=Object(o.a)(i.type);if(Object(n.hasOwnProperty)(s,e))return s[e];if(r)return}return t(Object.getPrototypeOf(i))}}("number"==typeof t?this.getCellMeta(t,i):t)}}function g(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=e,o=[];return Array.isArray(e)&&Array.isArray(e[0])||(r=[e]),r.forEach((e,r)=>{Array.isArray(e)?e.forEach((e,n)=>{o.push([r+t,n,e])}):Object.keys(e).forEach(n=>{o.push([r+t,n,e[n]])})}),o}function f(e){var t=0;return Array.isArray(e)&&(e[0]&&Array.isArray(e[0])?t=e[0].length:e[0]&&Object(n.isObject)(e[0])&&(t=Object(n.deepObjectSize)(e[0]))),t}},,function(e,t,r){"use strict";r.d(t,"a",(function(){return l}));var o=r(1),n=r.n(o),i=()=>n.a.createElement("svg",{viewBox:"0 0 24 24"},n.a.createElement("path",{fill:"none",d:"M0 0h24v24H0z"}),n.a.createElement("rect",{className:"svg-icon",width:14,height:2,rx:.095,transform:"translate(5 3)"}),n.a.createElement("path",{className:"svg-icon",d:"M8.116 12h2.789a.1.1 0 01.095.1v8.809a.1.1 0 00.095.1h1.81a.1.1 0 00.095-.1V12.1a.1.1 0 01.095-.1h2.789a.1.1 0 00.067-.163l-3.884-3.874a.1.1 0 00-.134 0l-3.884 3.874a.1.1 0 00.067.163z"})),s=r(52),a=function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function s(e){try{l(o.next(e))}catch(e){i(e)}}function a(e){try{l(o.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}l((o=o.apply(e,t||[])).next())}))},l=e=>{var{renderWorkbook:t,mobile:r}=e,o=e=>a(void 0,void 0,void 0,(function*(){if(0!==e.length){var r=e.item(0);if(null!==r){var o=yield(e=>new Promise((t,r)=>{e.name&&console.log("RECEIVED FILE: ".concat(e.name));var o=new FileReader;o.readAsArrayBuffer(e),o.addEventListener("load",e=>{var o;(null===(o=e.target)||void 0===o?void 0:o.result)instanceof ArrayBuffer?t(e.target.result):r()})}))(r);t({type:"arraybuffer",arrayBuffer:o,fileName:r.name})}}})),l=r?"toolbar-upload-input-mobile":"toolbar-upload-input";return n.a.createElement("label",{htmlFor:l},n.a.createElement("input",{type:"file",onChange:e=>{var t;(null===(t=e.target)||void 0===t?void 0:t.files)&&o(e.target.files)},hidden:!0,"data-cy":l,id:l}),n.a.createElement("div",{className:"icon","data-cy":r?"toolbar-upload-button-mobile":"toolbar-upload-button"},n.a.createElement(i,null),r?n.a.createElement(s.a,{description:"Upload"}):n.a.createElement("div",{role:"tooltip",className:"icon-tooltip"},n.a.createElement("span",null,"Upload"))))}},function(e,t,r){"use strict";r.d(t,"a",(function(){return l}));var o=r(1),n=r.n(o),i=()=>n.a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},n.a.createElement("path",{className:"svg-icon",d:"M447.46,404.216l0,3.4a2.286,2.286,0,0,1-2.279,2.279l-9.45-.007a1.089,1.089,0,0,0-.77.319l-2.33,2.1a.1.1,0,0,1-.169-.07V401.209a2.321,2.321,0,0,1,2.279-2.319h5.393a.1.1,0,0,0,.07-.029l1.781-1.8a.1.1,0,0,0-.07-.169h-7.174a4.327,4.327,0,0,0-4.279,4.319V414.89a1,1,0,0,0,.62.92.839.839,0,0,0,.38.08,1.007,1.007,0,0,0,.71-.29l3.919-3.688a.1.1,0,0,1,.07-.029l9.119.007a4.185,4.185,0,0,0,4.18-4.18l0-5.3a.1.1,0,0,0-.169-.07l-1.8,1.8A.1.1,0,0,0,447.46,404.216Z",transform:"translate(-427.46 -393.89)"}),n.a.createElement("path",{className:"svg-icon",d:"M466.282,397.81a3.005,3.005,0,0,0-4.249,0l-6.578,6.578a1,1,0,0,0-.293.707v2.835a1,1,0,0,0,1,1H459a1,1,0,0,0,.707-.293l6.578-6.578A3.005,3.005,0,0,0,466.282,397.81Zm-1.414,1.414.006.007a1,1,0,0,1-.006,1.414l0,0-1.421-1.42,0,0A1.008,1.008,0,0,1,464.868,399.224Zm-6.285,7.706h-1.421v-1.421l4.868-4.868,1.421,1.42Z",transform:"translate(-445.162 -394.93)"})),s=r(52),a=r(67),l=n.a.forwardRef((e,t)=>{var{open:r,mobile:o=!1}=e;return n.a.createElement("button",{type:"button",className:"icon",id:"feedback-icon",onClick:()=>r(a.b),"data-cy":o?"toolbar-feedback-button-mobile":"toolbar-feedback-button",ref:t},n.a.createElement(i,null),o?n.a.createElement(s.a,{description:"Feedback"}):n.a.createElement("div",{role:"tooltip",className:"icon-tooltip"},n.a.createElement("span",null,"Feedback")))})},function(e,t,r){"use strict";r.d(t,"e",(function(){return g})),r.d(t,"b",(function(){return f})),r.d(t,"d",(function(){return m})),r.d(t,"c",(function(){return p})),r.d(t,"a",(function(){return l}));var o=r(32),n=r(119),i=r(53),s=r(45),a={languageCode:"en-US",[s.CONTEXTMENU_ITEMS_NO_ITEMS]:"No available options",[s.CONTEXTMENU_ITEMS_ROW_ABOVE]:"Insert row above",[s.CONTEXTMENU_ITEMS_ROW_BELOW]:"Insert row below",[s.CONTEXTMENU_ITEMS_INSERT_LEFT]:"Insert column left",[s.CONTEXTMENU_ITEMS_INSERT_RIGHT]:"Insert column right",[s.CONTEXTMENU_ITEMS_REMOVE_ROW]:["Remove row","Remove rows"],[s.CONTEXTMENU_ITEMS_REMOVE_COLUMN]:["Remove column","Remove columns"],[s.CONTEXTMENU_ITEMS_UNDO]:"Undo",[s.CONTEXTMENU_ITEMS_REDO]:"Redo",[s.CONTEXTMENU_ITEMS_READ_ONLY]:"Read only",[s.CONTEXTMENU_ITEMS_CLEAR_COLUMN]:"Clear column",[s.CONTEXTMENU_ITEMS_ALIGNMENT]:"Alignment",[s.CONTEXTMENU_ITEMS_ALIGNMENT_LEFT]:"Left",[s.CONTEXTMENU_ITEMS_ALIGNMENT_CENTER]:"Center",[s.CONTEXTMENU_ITEMS_ALIGNMENT_RIGHT]:"Right",[s.CONTEXTMENU_ITEMS_ALIGNMENT_JUSTIFY]:"Justify",[s.CONTEXTMENU_ITEMS_ALIGNMENT_TOP]:"Top",[s.CONTEXTMENU_ITEMS_ALIGNMENT_MIDDLE]:"Middle",[s.CONTEXTMENU_ITEMS_ALIGNMENT_BOTTOM]:"Bottom",[s.CONTEXTMENU_ITEMS_FREEZE_COLUMN]:"Freeze column",[s.CONTEXTMENU_ITEMS_UNFREEZE_COLUMN]:"Unfreeze column",[s.CONTEXTMENU_ITEMS_BORDERS]:"Borders",[s.CONTEXTMENU_ITEMS_BORDERS_TOP]:"Top",[s.CONTEXTMENU_ITEMS_BORDERS_RIGHT]:"Right",[s.CONTEXTMENU_ITEMS_BORDERS_BOTTOM]:"Bottom",[s.CONTEXTMENU_ITEMS_BORDERS_LEFT]:"Left",[s.CONTEXTMENU_ITEMS_REMOVE_BORDERS]:"Remove border(s)",[s.CONTEXTMENU_ITEMS_ADD_COMMENT]:"Add comment",[s.CONTEXTMENU_ITEMS_EDIT_COMMENT]:"Edit comment",[s.CONTEXTMENU_ITEMS_REMOVE_COMMENT]:"Delete comment",[s.CONTEXTMENU_ITEMS_READ_ONLY_COMMENT]:"Read-only comment",[s.CONTEXTMENU_ITEMS_MERGE_CELLS]:"Merge cells",[s.CONTEXTMENU_ITEMS_UNMERGE_CELLS]:"Unmerge cells",[s.CONTEXTMENU_ITEMS_COPY]:"Copy",[s.CONTEXTMENU_ITEMS_CUT]:"Cut",[s.CONTEXTMENU_ITEMS_NESTED_ROWS_INSERT_CHILD]:"Insert child row",[s.CONTEXTMENU_ITEMS_NESTED_ROWS_DETACH_CHILD]:"Detach from parent",[s.CONTEXTMENU_ITEMS_HIDE_COLUMN]:["Hide column","Hide columns"],[s.CONTEXTMENU_ITEMS_SHOW_COLUMN]:["Show column","Show columns"],[s.CONTEXTMENU_ITEMS_HIDE_ROW]:["Hide row","Hide rows"],[s.CONTEXTMENU_ITEMS_SHOW_ROW]:["Show row","Show rows"],[s.FILTERS_CONDITIONS_NONE]:"None",[s.FILTERS_CONDITIONS_EMPTY]:"Is empty",[s.FILTERS_CONDITIONS_NOT_EMPTY]:"Is not empty",[s.FILTERS_CONDITIONS_EQUAL]:"Is equal to",[s.FILTERS_CONDITIONS_NOT_EQUAL]:"Is not equal to",[s.FILTERS_CONDITIONS_BEGINS_WITH]:"Begins with",[s.FILTERS_CONDITIONS_ENDS_WITH]:"Ends with",[s.FILTERS_CONDITIONS_CONTAINS]:"Contains",[s.FILTERS_CONDITIONS_NOT_CONTAIN]:"Does not contain",[s.FILTERS_CONDITIONS_GREATER_THAN]:"Greater than",[s.FILTERS_CONDITIONS_GREATER_THAN_OR_EQUAL]:"Greater than or equal to",[s.FILTERS_CONDITIONS_LESS_THAN]:"Less than",[s.FILTERS_CONDITIONS_LESS_THAN_OR_EQUAL]:"Less than or equal to",[s.FILTERS_CONDITIONS_BETWEEN]:"Is between",[s.FILTERS_CONDITIONS_NOT_BETWEEN]:"Is not between",[s.FILTERS_CONDITIONS_AFTER]:"After",[s.FILTERS_CONDITIONS_BEFORE]:"Before",[s.FILTERS_CONDITIONS_TODAY]:"Today",[s.FILTERS_CONDITIONS_TOMORROW]:"Tomorrow",[s.FILTERS_CONDITIONS_YESTERDAY]:"Yesterday",[s.FILTERS_VALUES_BLANK_CELLS]:"Blank cells",[s.FILTERS_DIVS_FILTER_BY_CONDITION]:"Filter by condition",[s.FILTERS_DIVS_FILTER_BY_VALUE]:"Filter by value",[s.FILTERS_LABELS_CONJUNCTION]:"And",[s.FILTERS_LABELS_DISJUNCTION]:"Or",[s.FILTERS_BUTTONS_SELECT_ALL]:"Select all",[s.FILTERS_BUTTONS_CLEAR]:"Clear",[s.FILTERS_BUTTONS_OK]:"OK",[s.FILTERS_BUTTONS_CANCEL]:"Cancel",[s.FILTERS_BUTTONS_PLACEHOLDER_SEARCH]:"Search",[s.FILTERS_BUTTONS_PLACEHOLDER_VALUE]:"Value",[s.FILTERS_BUTTONS_PLACEHOLDER_SECOND_VALUE]:"Second value"},l=a.languageCode,{register:c,getItem:h,hasItem:d,getValues:u}=Object(i.a)("languagesDictionaries");function g(e,t){var r=e,i=t;return Object(o.isObject)(e)&&(r=(i=e).languageCode),function(e,t){e!==l&&Object(n.a)(t,h(l))}(r,i),c(r,Object(o.deepClone)(i)),Object(o.deepClone)(i)}function f(e){return m(e)?Object(o.deepClone)(h(e)):null}function m(e){return d(e)}function p(){return u()}g(a)},,function(e,t,r){"use strict";r.d(t,"a",(function(){return o})),r.d(t,"b",(function(){return n}));new Map([["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","xlsx"],["application/vnd.ms-excel","xls"],["application/vnd.openxmlformats-officedocument.spreadsheetml.template","xltx"],["application/vnd.ms-excel.sheet.macroenabled.12","xlsm"],["application/vnd.ms-excel.template.macroenabled.12","xltm"],["application/vnd.ms-excel.addin.macroenabled.12","xlam"],["application/vnd.ms-excel.sheet.binary.macroenabled.12","xlsb"],["application/vnd.oasis.opendocument.spreadsheet","ods"],["application/x-iwork-numbers-sffnumbers","numbers"],["application/vnd.apple.numbers","numbers"]]);var o=e=>void 0!==e.fileName||"arraybuffer"===e.type?e.fileName:(e=>{if(!e.startsWith("data:")){var t=e.split("/").pop();if(void 0!==t&&0!==t.length)return t}})(e.url),n="Unnamed file"},function(e,t,r){"use strict";var o=r(63);r.o(o,"useModals")&&r.d(t,"useModals",(function(){return o.useModals}));var n=r(66);r.d(t,"useModals",(function(){return n.a}))},,,,,function(e,t,r){"use strict";r.d(t,"d",(function(){return f})),r.d(t,"a",(function(){return g})),r.d(t,"b",(function(){return u})),r.d(t,"c",(function(){return c}));var o=r(53),n=r(59),i=new WeakMap,{register:s,getItem:a,hasItem:l,getNames:c,getValues:h}=Object(o.a)("editors");function d(e){var t={},r=e;this.getConstructor=function(){return e},this.getInstance=function(e){return e.guid in t||(t[e.guid]=new r(e)),t[e.guid]},n.a.getSingleton().add("afterDestroy",(function(){t[this.guid]=null}))}function u(e,t){var r;if("function"==typeof e)i.get(e)||f(null,e),r=i.get(e);else{if("string"!=typeof e)throw Error('Only strings and functions can be passed as "editor" parameter');r=a(e)}if(!r)throw Error('No editor registered under name "'.concat(e,'"'));return r.getInstance(t)}function g(e){if(!l(e))throw Error('No registered editor found under "'.concat(e,'" name'));return a(e).getConstructor()}function f(e,t){var r=new d(t);"string"==typeof e&&s(e,r),i.set(t,r)}},function(e,t,r){"use strict";r.d(t,"c",(function(){return n})),r.d(t,"b",(function(){return c})),r.d(t,"a",(function(){return a}));var o=r(53),{register:n,getItem:i,hasItem:s,getNames:a,getValues:l}=Object(o.a)("validators");function c(e){if("function"==typeof e)return e;if(!s(e))throw Error('No registered validator found under "'.concat(e,'" name'));return i(e)}},function(e,t,r){"use strict";r.d(t,"c",(function(){return g})),r.d(t,"a",(function(){return u})),r.d(t,"b",(function(){return h}));var o=r(53),n=r(87),i=r(97),s=r(88),{register:a,getItem:l,hasItem:c,getNames:h,getValues:d}=Object(o.a)("cellTypes");function u(e){if(!c(e))throw Error('You declared cell type "'.concat(e,'" as a string that is not mapped to a known object.\n                 Cell type must be an object or a string mapped to an object registered by "Handsontable.cellTypes.registerCellType" method'));return l(e)}function g(e,t){var{editor:r,renderer:o,validator:l}=t;r&&Object(n.d)(e,r),o&&Object(i.c)(e,o),l&&Object(s.c)(e,l),a(e,t)}},function(e,t,r){"use strict";r.r(t),r.d(t,"instanceToHTML",(function(){return l})),r.d(t,"_dataToHTML",(function(){return c})),r.d(t,"htmlToGridSettings",(function(){return h}));var o=r(34),n=r(40),i={"&nbsp;":" ","&amp;":"&","&lt;":"<","&gt;":">"},s=new RegExp(Object.keys(i).map(e=>"(".concat(e,")")).join("|"),"gi");function a(e){return"TABLE"===(e&&e.nodeName||"")}function l(e){for(var t=e.hasColHeaders(),r=e.hasRowHeaders(),o=[t?-1:0,r?-1:0,e.countRows()-1,e.countCols()-1],i=e.getData(...o),s=i.length,a=s>0?i[0].length:0,l=["<table>","</table>"],c=t?["<thead>","</thead>"]:[],h=["<tbody>","</tbody>"],d=r?1:0,u=t?1:0,g=0;g<s;g+=1){for(var f=t&&0===g,m=[],p=0;p<a;p+=1){var v=!f&&r&&0===p,w="";if(f)w="<th>".concat(e.getColHeader(p-d),"</th>");else if(v)w="<th>".concat(e.getRowHeader(g-u),"</th>");else{var b=i[g][p],{hidden:C,rowspan:E,colspan:O}=e.getCellMeta(g-d,p-u);if(!C){var y=[];if(E&&y.push('rowspan="'.concat(E,'"')),O&&y.push('colspan="'.concat(O,'"')),Object(n.isEmpty)(b))w="<td ".concat(y.join(" "),"></td>");else{var R=b.toString().replace("<","&lt;").replace(">","&gt;").replace(/(<br(\s*|\/)>(\r\n|\n)?|\r\n|\n)/g,"<br>\r\n").replace(/\x20/gi,"&nbsp;").replace(/\t/gi,"&#9;");w="<td ".concat(y.join(" "),">").concat(R,"</td>")}}}m.push(w)}var T=["<tr>",...m,"</tr>"].join("");f?c.splice(1,0,T):h.splice(-1,0,T)}return l.splice(1,0,c.join(""),h.join("")),l.join("")}function c(e){for(var t=e.length,r=["<table>"],o=0;o<t;o+=1){var i=e[o],s=i.length,a=[];0===o&&r.push("<tbody>");for(var l=0;l<s;l+=1){var c=i[l],h=Object(n.isEmpty)(c)?"":c.toString().replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/(<br(\s*|\/)>(\r\n|\n)?|\r\n|\n)/g,"<br>\r\n").replace(/\x20/gi,"&nbsp;").replace(/\t/gi,"&#9;");a.push("<td>".concat(h,"</td>"))}r.push("<tr>",...a,"</tr>"),o+1===t&&r.push("</tbody>")}return r.push("</table>"),r.join("")}function h(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document,r={},n=t.createDocumentFragment(),l=t.createElement("div");n.appendChild(l);var c=e;if("string"==typeof c){var h=c.replace(/<td\b[^>]*?>([\s\S]*?)<\/\s*td>/g,e=>{var t=e.match(/<td\b[^>]*?>/g)[0],r=e.substring(t.length,e.lastIndexOf("<")).replace(/(<(?!br)([^>]+)>)/gi,"");return"".concat(t).concat(r).concat("</td>")});l.insertAdjacentHTML("afterbegin","".concat(h)),c=l.querySelector("table")}if(c&&a(c)){var d=l.querySelector("style"),u=null,g=[];d&&(t.body.appendChild(d),d.disabled=!0,u=d.sheet,g=u?Array.from(u.cssRules):[],t.body.removeChild(d));var f=l.querySelector('meta[name$="enerator"]'),m=null!==c.querySelector("tbody th"),p=Array.from(c.querySelector("tr").cells).reduce((e,t)=>e+t.colSpan,0)-(m?1:0),v=c.tFoot&&Array.from(c.tFoot.rows)||[],w=[],b=!1,C=0,E=0;if(c.tHead){var O=Array.from(c.tHead.rows).filter(e=>{var t=null!==e.querySelector("td");return t&&w.push(e),!t});b=(C=O.length)>0,C>1?r.nestedHeaders=Array.from(O).reduce((e,t)=>{var r=Array.from(t.cells).reduce((e,t,r)=>{if(m&&0===r)return e;var{colSpan:o,innerHTML:n}=t,i=o>1?{label:n,colspan:o}:n;return e.push(i),e},[]);return e.push(r),e},[]):b&&(r.colHeaders=Array.from(O[0].children).reduce((e,t,r)=>(m&&0===r||e.push(t.innerHTML),e),[]))}w.length&&(r.fixedRowsTop=w.length),v.length&&(r.fixedRowsBottom=v.length);var y=[...w,...Array.from(c.tBodies).reduce((e,t)=>(e.push(...Array.from(t.rows)),e),[]),...v];E=y.length;for(var R=new Array(E),T=0;T<E;T++)R[T]=new Array(p);for(var S=[],M=[],N=0;N<E;N++)for(var H=y[N],L=Array.from(H.cells),x=L.length,A=function(e){var t=L[e],{nodeName:r,innerHTML:n,rowSpan:a,colSpan:l}=t,c=R[N].findIndex(e=>void 0===e);if("TD"===r){if(a>1||l>1){for(var h=N;h<N+a;h++)if(h<E)for(var d=c;d<c+l;d++)R[h][d]=null;var u=t.getAttribute("style");u&&u.includes("mso-ignore:colspan")||S.push({col:c,row:N,rowspan:a,colspan:l})}var m="";m="nowrap"===g.reduce((e,r)=>{if(Object(o.matchesCSSRules)(t,r)){var{whiteSpace:n}=r.style;n&&(e.whiteSpace=n)}return e},{}).whiteSpace?n.replace(/[\r\n][\x20]{0,2}/gim," ").replace(/<br(\s*|\/)>/gim,"\r\n"):f&&/excel/gi.test(f.content)?n.replace(/[\r\n][\x20]{0,2}/g," ").replace(/<br(\s*|\/)>[\r\n]?[\x20]{0,3}/gim,"\r\n"):n.replace(/<br(\s*|\/)>[\r\n]?/gim,"\r\n"),R[N][c]=m.replace(s,e=>i[e])}else M.push(n)},I=0;I<x;I++)A(I);return S.length&&(r.mergeCells=S),M.length&&(r.rowHeaders=M),R.length&&(r.data=R),r}}},function(e,t,r){"use strict";r.d(t,"a",(function(){return a}));var o=r(57),n=r(38);function i(e){var{isShiftKey:t,isLeftClick:r,isRightClick:o,coords:i,selection:s,controller:a}=e,l=s.isSelected()?s.getSelectedRange().current():null,c=s.isSelectedByCorner(),h=s.isSelectedByRowHeader();if(t&&l)i.row>=0&&i.col>=0&&!a.cells?s.setRangeEnd(i):(c||h)&&i.row>=0&&i.col>=0&&!a.cells?s.setRangeEnd(new n.a(i.row,i.col)):c&&i.row<0&&!a.column?s.setRangeEnd(new n.a(l.to.row,i.col)):h&&i.col<0&&!a.row?s.setRangeEnd(new n.a(i.row,l.to.col)):(!c&&!h&&i.col<0||c&&i.col<0)&&!a.row?s.selectRows(l.from.row,i.row):(!c&&!h&&i.row<0||h&&i.row<0)&&!a.column&&s.selectColumns(l.from.col,i.col);else{var d=new n.a(i.row,i.col);d.row<0&&(d.row=0),d.col<0&&(d.col=0);var u=!s.inInSelection(d),g=r||o&&u;i.row<0&&i.col>=0&&!a.column?g&&s.selectColumns(i.col):i.col<0&&i.row>=0&&!a.row?g&&s.selectRows(i.row):i.col>=0&&i.row>=0&&!a.cells?g&&s.setRangeStart(i):i.col<0&&i.row<0&&s.selectAll()}}var s=new Map([["mousedown",i],["mouseover",function(e){var{isLeftClick:t,coords:r,selection:o,controller:i}=e;if(t){var s=o.isSelectedByRowHeader(),a=o.isSelectedByColumnHeader(),l=o.tableProps.countCols(),c=o.tableProps.countRows();a&&!i.column?o.setRangeEnd(new n.a(c-1,r.col)):s&&!i.row?o.setRangeEnd(new n.a(r.row,l-1)):i.cell||o.setRangeEnd(r)}}],["touchstart",i]]);function a(e,t){var{coords:r,selection:n,controller:i}=t;s.get(e.type)({coords:r,selection:n,controller:i,isShiftKey:e.shiftKey,isLeftClick:Object(o.isLeftClick)(e)||"touchstart"===e.type,isRightClick:Object(o.isRightClick)(e)})}},function(e,t,r){"use strict";r.d(t,"b",(function(){return l}));var o=r(40),n=r(32),i=r(62),s=0;class a{constructor(){this.collection=new Map}register(e,t){!1===this.collection.has(e)&&(this.collection.set(e,t),t.addLocalHook("change",()=>this.runLocalHooks("change",t)),s+=1)}unregister(e){var t=this.collection.get(e);Object(o.isDefined)(t)&&(t.clearLocalHooks(),this.collection.delete(e),this.runLocalHooks("change",t),s-=1)}get(e){return Object(o.isUndefined)(e)?Array.from(this.collection.values()):this.collection.get(e)}getLength(){return this.collection.size}removeFromEvery(e){this.collection.forEach(t=>{t.remove(e)})}insertToEvery(e,t){this.collection.forEach(r=>{r.insert(e,t)})}initEvery(e){this.collection.forEach(t=>{t.init(e)})}}function l(){return s}Object(n.mixin)(a,i.a),t.a=a},function(e,t,r){"use strict";var o=r(34),n=r(33),i=r(56);t.a=class{constructor(e){this.hot=e,this.container=null,this.injected=!1,this.rows=[],this.columns=[],this.samples=null,this.settings={useHeaders:!0}}addRow(e,t){if(this.columns.length)throw new Error("Doesn't support multi-dimensional table");this.rows.length||(this.container=this.createContainer(this.hot.rootElement.className));var r={row:e};this.rows.push(r),this.samples=t,this.table=this.createTable(this.hot.table.className),this.table.colGroup.appendChild(this.createColGroupsCol()),this.table.tr.appendChild(this.createRow(e)),this.container.container.appendChild(this.table.fragment),r.table=this.table.table}addColumnHeadersRow(e){var t=this.hot.getColHeader(0);if(null!=t){var r={row:-1};this.rows.push(r),this.container=this.createContainer(this.hot.rootElement.className),this.samples=e,this.table=this.createTable(this.hot.table.className),this.table.colGroup.appendChild(this.createColGroupsCol()),this.table.tHead.appendChild(this.createColumnHeadersRow()),this.container.container.appendChild(this.table.fragment),r.table=this.table.table,Object(o.removeClass)(this.table.tBody,"afterEmptyThead")}else Object(o.addClass)(this.table.tBody,"afterEmptyThead")}addColumn(e,t){if(this.rows.length)throw new Error("Doesn't support multi-dimensional table");this.columns.length||(this.container=this.createContainer(this.hot.rootElement.className));var r={col:e};this.columns.push(r),this.samples=t,this.table=this.createTable(this.hot.table.className),this.getSetting("useHeaders")&&null!==this.hot.getColHeader(e)&&this.hot.view.appendColHeader(e,this.table.th),this.table.tBody.appendChild(this.createCol(e)),this.container.container.appendChild(this.table.fragment),r.table=this.table.table}getHeights(e){this.injected||this.injectTable(),Object(n.arrayEach)(this.rows,t=>{e(t.row,Object(o.outerHeight)(t.table)-i.a)})}getWidths(e){this.injected||this.injectTable(),Object(n.arrayEach)(this.columns,t=>{var r=t.table.getBoundingClientRect().width,o=Math.ceil(r);e(t.col,o)})}setSettings(e){this.settings=e}setSetting(e,t){this.settings||(this.settings={}),this.settings[e]=t}getSettings(){return this.settings}getSetting(e){return this.settings?this.settings[e]:null}createColGroupsCol(){var e=this.hot.rootDocument.createDocumentFragment();return this.hot.hasRowHeaders()&&e.appendChild(this.createColElement(-1)),this.samples.forEach(t=>{Object(n.arrayEach)(t.strings,t=>{e.appendChild(this.createColElement(t.col))})}),e}createRow(e){var{rootDocument:t}=this.hot,r=t.createDocumentFragment(),o=t.createElement("th");return this.hot.hasRowHeaders()&&(this.hot.view.appendRowHeader(e,o),r.appendChild(o)),this.samples.forEach(o=>{Object(n.arrayEach)(o.strings,o=>{var n=o.col,i=this.hot.getCellMeta(e,n);i.col=n,i.row=e;var s=this.hot.getCellRenderer(i),a=t.createElement("td");a.setAttribute("ghost-table",1),s(this.hot,a,e,n,this.hot.colToProp(n),o.value,i),r.appendChild(a)})}),r}createColumnHeadersRow(){var{rootDocument:e}=this.hot,t=e.createDocumentFragment();if(this.hot.hasRowHeaders()){var r=e.createElement("th");this.hot.view.appendColHeader(-1,r),t.appendChild(r)}return this.samples.forEach(r=>{Object(n.arrayEach)(r.strings,r=>{var o=r.col,n=e.createElement("th");this.hot.view.appendColHeader(o,n),t.appendChild(n)})}),t}createCol(e){var{rootDocument:t}=this.hot,r=t.createDocumentFragment();return this.samples.forEach(o=>{Object(n.arrayEach)(o.strings,o=>{var n=o.row,i=this.hot.getCellMeta(n,e);i.col=e,i.row=n;var s=this.hot.getCellRenderer(i),a=t.createElement("td"),l=t.createElement("tr");a.setAttribute("ghost-table",1),s(this.hot,a,n,e,this.hot.colToProp(e),o.value,i),l.appendChild(a),r.appendChild(l)})}),r}clean(){this.rows.length=0,this.rows[-1]=void 0,this.columns.length=0,this.samples&&this.samples.clear(),this.samples=null,this.removeTable()}injectTable(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.injected||((e||this.hot.rootElement).appendChild(this.container.fragment),this.injected=!0)}removeTable(){this.injected&&this.container.container.parentNode&&(this.container.container.parentNode.removeChild(this.container.container),this.container=null,this.injected=!1)}createColElement(e){var t=this.hot.rootDocument.createElement("col");return t.style.width="".concat(this.hot.view.wt.columnUtils.getStretchedColumnWidth(e),"px"),t}createTable(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",{rootDocument:t}=this.hot,r=t.createDocumentFragment(),n=t.createElement("table"),i=t.createElement("thead"),s=t.createElement("tbody"),a=t.createElement("colgroup"),l=t.createElement("tr"),c=t.createElement("th");return this.isVertical()&&n.appendChild(a),this.isHorizontal()&&(l.appendChild(c),i.appendChild(l),n.style.tableLayout="auto",n.style.width="auto"),n.appendChild(i),this.isVertical()&&s.appendChild(l),Object(o.addClass)(s,"afterEmptyThead"),n.appendChild(s),Object(o.addClass)(n,e),r.appendChild(n),{fragment:r,table:n,tHead:i,tBody:s,colGroup:a,tr:l,th:c}}createContainer(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",{rootDocument:t}=this.hot,r=t.createDocumentFragment(),n=t.createElement("div"),i="htGhostTable htAutoSize ".concat(e.trim());return Object(o.addClass)(n,i),r.appendChild(n),{fragment:r,container:n}}isVertical(){return!(!this.rows.length||this.columns.length)}isHorizontal(){return!(!this.columns.length||this.rows.length)}}},function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"a",(function(){return i})),r.d(t,"c",(function(){return s})),r.d(t,"d",(function(){return a}));var o=0,n=()=>{1===(o+=1)&&delete document.body.dataset.embedsLoaded},i=()=>{0===(o=Math.max(o-1,0))&&setTimeout(()=>{document.body.dataset.embedsLoaded=""},0)},s=()=>{o=0,n()},a=()=>{i()}},function(e,t,r){"use strict";r.d(t,"a",(function(){return y})),r.d(t,"b",(function(){return m}));var o=r(33);function n(e,t,r){return[...e.slice(0,t),...r,...e.slice(t)]}function i(e,t){return Object(o.arrayFilter)(e,e=>!1===t.includes(e))}var s=r(44),a=r(32),l=r(61),c=r(62);class h{constructor(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.indexedValues=[],this.initValueOrFn=e}getValues(){return this.indexedValues}getValueAtIndex(e){var t=this.getValues();if(e<t.length)return t[e]}setValues(e){this.indexedValues=e.slice(),this.runLocalHooks("change")}setValueAtIndex(e,t){return e<this.getLength()&&(this.indexedValues[e]=t,this.runLocalHooks("change"),!0)}clear(){this.setDefaultValues()}getLength(){return this.getValues().length}setDefaultValues(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.indexedValues.length;this.indexedValues.length=0,Object(l.isFunction)(this.initValueOrFn)?Object(s.rangeEach)(e-1,e=>this.indexedValues.push(this.initValueOrFn(e))):Object(s.rangeEach)(e-1,()=>this.indexedValues.push(this.initValueOrFn)),this.runLocalHooks("change")}init(e){return this.setDefaultValues(e),this.runLocalHooks("init"),this}insert(){this.runLocalHooks("change")}remove(){this.runLocalHooks("change")}}Object(a.mixin)(h,c.a);var d=h;function u(e,t,r,o){var n=r[0];return[...e.slice(0,n),...r.map((e,t)=>Object(l.isFunction)(o)?o(e,t):o),...e.slice(n)]}function g(e,t){return Object(o.arrayFilter)(e,(e,r)=>!1===t.includes(r))}new Map([["indexesSequence",{getListWithInsertedItems:n,getListWithRemovedItems:i}],["physicallyIndexed",{getListWithInsertedItems:u,getListWithRemovedItems:g}]]);var f=class extends d{constructor(){super(e=>e)}insert(e,t){var r=function(e,t,r){var n=r[0],i=r.length;return Object(o.arrayMap)(e,e=>e>=n?e+i:e)}(this.indexedValues,0,t);this.indexedValues=n(r,e,t),super.insert(e,t)}remove(e){var t=i(this.indexedValues,e);this.indexedValues=function(e,t){return Object(o.arrayMap)(e,e=>e-t.filter(t=>t<e).length)}(t,e),super.remove(e)}};var m=class extends d{insert(e,t){this.indexedValues=u(this.indexedValues,0,t,this.initValueOrFn),super.insert(e,t)}remove(e){this.indexedValues=g(this.indexedValues,e),super.remove(e)}};var p=class extends m{constructor(){super(arguments.length>0&&void 0!==arguments[0]&&arguments[0])}getTrimmedIndexes(){return Object(o.arrayReduce)(this.getValues(),(e,t,r)=>(t&&e.push(r),e),[])}};var v=class extends m{constructor(){super(arguments.length>0&&void 0!==arguments[0]&&arguments[0])}getHiddenIndexes(){return Object(o.arrayReduce)(this.getValues(),(e,t,r)=>(t&&e.push(r),e),[])}},w=r(92),b=r(40);class C extends w.a{constructor(e,t){super(),this.mergedValuesCache=[],this.aggregationFunction=e,this.fallbackValue=t}getMergedValues(){if(!0===(!(arguments.length>0&&void 0!==arguments[0])||arguments[0]))return this.mergedValuesCache;if(0===this.getLength())return[];for(var e=Object(o.arrayMap)(this.get(),e=>e.getValues()),t=[],r=Object(b.isDefined)(e[0])&&e[0].length||0,n=0;n<r;n+=1){for(var i=[],s=0;s<this.getLength();s+=1)i.push(e[s][n]);t.push(i)}return Object(o.arrayMap)(t,this.aggregationFunction)}getMergedValueAtIndex(e,t){var r=this.getMergedValues(t)[e];return Object(b.isDefined)(r)?r:this.fallbackValue}updateCache(){this.mergedValuesCache=this.getMergedValues(!1)}}var E=C;class O{constructor(){this.indexesSequence=new f,this.trimmingMapsCollection=new E(e=>e.some(e=>!0===e),!1),this.hidingMapsCollection=new E(e=>e.some(e=>!0===e),!1),this.variousMapsCollection=new w.a,this.notTrimmedIndexesCache=[],this.notHiddenIndexesCache=[],this.isBatched=!1,this.indexesSequenceChanged=!1,this.trimmedIndexesChanged=!1,this.hiddenIndexesChanged=!1,this.renderablePhysicalIndexesCache=[],this.fromPhysicalToVisualIndexesCache=new Map,this.fromVisualToRenderableIndexesCache=new Map,this.indexesSequence.addLocalHook("change",()=>{this.indexesSequenceChanged=!0,this.updateCache(),this.runLocalHooks("change",this.indexesSequence,null)}),this.trimmingMapsCollection.addLocalHook("change",e=>{this.trimmedIndexesChanged=!0,this.updateCache(),this.runLocalHooks("change",e,this.trimmingMapsCollection)}),this.hidingMapsCollection.addLocalHook("change",e=>{this.hiddenIndexesChanged=!0,this.updateCache(),this.runLocalHooks("change",e,this.hidingMapsCollection)}),this.variousMapsCollection.addLocalHook("change",e=>{this.runLocalHooks("change",e,this.variousMapsCollection)})}executeBatchOperations(e){var t=this.isBatched;this.isBatched=!0,e(),this.isBatched=t,this.updateCache()}registerMap(e,t){if(this.trimmingMapsCollection.get(e)||this.hidingMapsCollection.get(e)||this.variousMapsCollection.get(e))throw Error('Map with name "'.concat(e,'" has been already registered.'));t instanceof p?this.trimmingMapsCollection.register(e,t):t instanceof v?this.hidingMapsCollection.register(e,t):this.variousMapsCollection.register(e,t);var r=this.getNumberOfIndexes();return r>0&&t.init(r),t}unregisterMap(e){this.trimmingMapsCollection.unregister(e),this.hidingMapsCollection.unregister(e),this.variousMapsCollection.unregister(e)}getPhysicalFromVisualIndex(e){var t=this.notTrimmedIndexesCache[e];return Object(b.isDefined)(t)?t:null}getPhysicalFromRenderableIndex(e){var t=this.renderablePhysicalIndexesCache[e];return Object(b.isDefined)(t)?t:null}getVisualFromPhysicalIndex(e){var t=this.fromPhysicalToVisualIndexesCache.get(e);return Object(b.isDefined)(t)?t:null}getVisualFromRenderableIndex(e){return this.getVisualFromPhysicalIndex(this.getPhysicalFromRenderableIndex(e))}getRenderableFromVisualIndex(e){var t=this.fromVisualToRenderableIndexesCache.get(e);return Object(b.isDefined)(t)?t:null}getFirstNotHiddenIndex(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e-t,n=this.getPhysicalFromVisualIndex(e);return null===n?!0===r&&o!==e-t?this.getFirstNotHiddenIndex(o,-t,!1,o):null:!1===this.isHidden(n)?e:this.getFirstNotHiddenIndex(e+t,t,r,o)}initToLength(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getNumberOfIndexes();this.notTrimmedIndexesCache=[...new Array(e).keys()],this.notHiddenIndexesCache=[...new Array(e).keys()],this.executeBatchOperations(()=>{this.indexesSequence.init(e),this.trimmingMapsCollection.initEvery(e)}),this.executeBatchOperations(()=>{this.hidingMapsCollection.initEvery(e),this.variousMapsCollection.initEvery(e)}),this.runLocalHooks("init")}getIndexesSequence(){return this.indexesSequence.getValues()}setIndexesSequence(e){this.indexesSequence.setValues(e)}getNotTrimmedIndexes(){return!0===(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])?this.notTrimmedIndexesCache:Object(o.arrayFilter)(this.getIndexesSequence(),e=>!1===this.isTrimmed(e))}getNotTrimmedIndexesLength(){return this.getNotTrimmedIndexes().length}getNotHiddenIndexes(){return!0===(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])?this.notHiddenIndexesCache:Object(o.arrayFilter)(this.getIndexesSequence(),e=>!1===this.isHidden(e))}getNotHiddenIndexesLength(){return this.getNotHiddenIndexes().length}getRenderableIndexes(){if(!0===(!(arguments.length>0&&void 0!==arguments[0])||arguments[0]))return this.renderablePhysicalIndexesCache;var e=this.getNotTrimmedIndexes(),t=this.getNotHiddenIndexes();return e.filter(e=>t.includes(e))}getRenderableIndexesLength(){return this.getRenderableIndexes().length}getNumberOfIndexes(){return this.getIndexesSequence().length}moveIndexes(e,t){"number"==typeof e&&(e=[e]);var r=Object(o.arrayMap)(e,e=>this.getPhysicalFromVisualIndex(e)),s=this.getNotTrimmedIndexesLength(),a=e.length,l=i(this.getIndexesSequence(),r),c=s-a;if(t+a<s){var h=l.filter(e=>!1===this.isTrimmed(e))[t];c=l.indexOf(h)}this.setIndexesSequence(n(l,c,r))}isTrimmed(e){return this.trimmingMapsCollection.getMergedValueAtIndex(e)}isHidden(e){return this.hidingMapsCollection.getMergedValueAtIndex(e)}insertIndexes(e,t){var r=this.getNotTrimmedIndexes()[e],n=Object(b.isDefined)(r)?r:this.getNumberOfIndexes(),i=this.getIndexesSequence().includes(r)?this.getIndexesSequence().indexOf(r):this.getNumberOfIndexes(),s=Object(o.arrayMap)(new Array(t).fill(n),(e,t)=>e+t);this.executeBatchOperations(()=>{this.indexesSequence.insert(i,s),this.trimmingMapsCollection.insertToEvery(i,s),this.hidingMapsCollection.insertToEvery(i,s),this.variousMapsCollection.insertToEvery(i,s)})}removeIndexes(e){this.executeBatchOperations(()=>{this.indexesSequence.remove(e),this.trimmingMapsCollection.removeFromEvery(e),this.hidingMapsCollection.removeFromEvery(e),this.variousMapsCollection.removeFromEvery(e)})}updateCache(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.indexesSequenceChanged||this.trimmedIndexesChanged||this.hiddenIndexesChanged;(!0===e||!1===this.isBatched&&!0===t)&&(this.trimmingMapsCollection.updateCache(),this.hidingMapsCollection.updateCache(),this.notTrimmedIndexesCache=this.getNotTrimmedIndexes(!1),this.notHiddenIndexesCache=this.getNotHiddenIndexes(!1),this.renderablePhysicalIndexesCache=this.getRenderableIndexes(!1),this.cacheFromPhysicalToVisualIndexes(),this.cacheFromVisualToRenderabIendexes(),this.runLocalHooks("cacheUpdated",this.indexesSequenceChanged,this.trimmedIndexesChanged,this.hiddenIndexesChanged),this.indexesSequenceChanged=!1,this.trimmedIndexesChanged=!1,this.hiddenIndexesChanged=!1)}cacheFromPhysicalToVisualIndexes(){var e=this.notTrimmedIndexesCache,t=this.getIndexesSequence(),r=t.length;this.fromPhysicalToVisualIndexesCache.clear();for(var o=0;o<r;o+=1){var n=t[o],i=e.indexOf(n);-1!==i&&this.fromPhysicalToVisualIndexesCache.set(n,i)}}cacheFromVisualToRenderabIendexes(){var e=this.notTrimmedIndexesCache,t=e.length,r=e.map(e=>this.isHidden(e));this.fromVisualToRenderableIndexesCache.clear();for(var o=0,n=0;n<t;n+=1)if(r[n])o+=1;else{var i=n-o;this.fromVisualToRenderableIndexesCache.set(n,i)}}}Object(a.mixin)(O,c.a);var y=O},function(e,t,r){"use strict";r.d(t,"c",(function(){return w})),r.d(t,"b",(function(){return C})),r.d(t,"a",(function(){return E}));var o=r(1),n=r.n(o),i=()=>n.a.createElement("svg",{viewBox:"0 0 18 18"},n.a.createElement("defs",null,n.a.createElement("linearGradient",{id:"sv_svg__b",x1:"1",x2:"0",y2:"1",gradientUnits:"objectBoundingBox"},n.a.createElement("stop",{offset:"0",stopColor:"#0443c1"}),n.a.createElement("stop",{offset:"1",stopColor:"#c2c8f0"})),n.a.createElement("clipPath",{id:"sv_svg__a"}," ",n.a.createElement("path",{d:"M2 0h14a2 2 0 012 2v14a2 2 0 01-2 2H2a2 2 0 01-2-2V2a2 2 0 012-2z",fill:"#0f9d58"}))),n.a.createElement("g",{clipPath:"url(#sv_svg__a)"},n.a.createElement("path",{transform:"rotate(90 9 9)",fill:"url(#sv_svg__b)",d:"M0 0h18v18H0z"}),n.a.createElement("path",{d:"M0 0l9-9 6 6-9 9z",fill:"#c2c8f0"}),n.a.createElement("path",{d:"M-9 9l9-9 6 6-9 9z",fill:"#98a5e6"}),n.a.createElement("path",{d:"M6 6l9-9 6 6-9 9z",fill:"#6c82dc"}),n.a.createElement("path",{d:"M-3 15l9-9 6 6-9 9z",fill:"#4767d4"}),n.a.createElement("path",{d:"M12 12l9-9 6 6-9 9z",fill:"#104acc"}),n.a.createElement("path",{d:"M3 21l9-9 6 6-9 9z",fill:"#0443c1"}))),s=r(77),a=()=>n.a.createElement("svg",{width:"1em",height:"1em",viewBox:"0 0 24 24"},n.a.createElement("path",{fill:"none",d:"M0 0h24v24H0z"}),n.a.createElement("rect",{className:"svg-icon",width:14,height:2,rx:.099,transform:"translate(5 19)"}),n.a.createElement("path",{className:"svg-icon",d:"M8.124 12h2.777a.1.1 0 00.1-.1V3.1a.1.1 0 01.1-.1h1.8a.1.1 0 01.1.1v8.8a.1.1 0 00.1.1h2.778a.1.1 0 01.069.168l-3.876 3.866a.1.1 0 01-.139 0l-3.876-3.866A.1.1 0 018.124 12z"})),l=r(69),c=r(52),h=r(72),d=e=>{var{workbookLocation:t,fileName:r,mobile:o}=e,i=Object(l.a)(t);if(!Object(h.a)())return null;var s=i?"download-anchor":void 0,d=s&&o?"download-anchor-mobile":s;return n.a.createElement("button",{type:"button",className:"icon",id:"download-icon"},n.a.createElement("a",{href:i,download:r,"data-cy":d},n.a.createElement(a,null),o&&n.a.createElement(c.a,{description:"Download"})),!o&&n.a.createElement("div",{role:"tooltip",className:"icon-tooltip"},n.a.createElement("span",null,"Download")))},u=r(78),g=e=>n.a.createElement("svg",Object.assign({width:"1em",height:"1em",viewBox:"0 0 24 24"},e),n.a.createElement("path",{fill:"none",d:"M0 0h24v24H0z"}),n.a.createElement("path",{d:"M14.25 6.007a2.007 2.007 0 10-2.007 2.007 2.007 2.007 0 002.007-2.007zm0 6.074a2.007 2.007 0 10-2.007 2.007 2.007 2.007 0 002.007-2.007zm0 6.074a2.007 2.007 0 10-2.007 2.007 2.007 2.007 0 002.007-2.007z",fill:"#fff"})),f=r(187),m=e=>{var{children:t}=e;return n.a.createElement(f.a,Object.assign({elevation:0,getContentAnchorEl:null,anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},PaperProps:{style:{maxHeight:192,width:200}},classes:{paper:"toolbar-mobile-menu-wrapper",list:"toolbar-mobile-menu-list"}},e),t)},p=e=>{var{children:t}=e,[r,o]=n.a.useState(void 0);return n.a.createElement("button",{type:"button",className:"icon icon-mobile","data-cy":"mobile-menu-icon"},n.a.createElement(g,{onClick:e=>{o(e.currentTarget)}}),n.a.createElement(m,{anchorEl:r,keepMounted:!0,open:Boolean(r),onClose:()=>{o(void 0)}},t))},v=r(17),w=e=>{var{children:t}=e;return n.a.createElement("div",{className:"toolbar"},t)},b=e=>{var{children:t,fileName:r}=e;return n.a.createElement("div",{className:"toolbar-left-pane"},n.a.createElement("div",{className:"icon",id:"sv-icon"},t),n.a.createElement("p",{className:"file-title"},r))},C=e=>{var{children:t}=e;return n.a.createElement("div",{className:"toolbar-right-pane"},n.a.createElement("div",{className:"icons-wrapper"},t))},E=e=>{var{fileName:t,workbookLocation:r,renderWorkbook:o,openModal:a}=e,l=Object(v.b)();return n.a.createElement(w,null,n.a.createElement(b,{fileName:t},n.a.createElement(i,null)),n.a.createElement(C,null,l?n.a.createElement(p,null,n.a.createElement(u.a,{open:a,mobile:!0}),n.a.createElement(s.a,{renderWorkbook:o,mobile:!0}),n.a.createElement(d,{workbookLocation:r,fileName:t,mobile:!0})):n.a.createElement(n.a.Fragment,null,n.a.createElement(s.a,{renderWorkbook:o}),n.a.createElement(d,{workbookLocation:r,fileName:t}),n.a.createElement(u.a,{open:a}))))}},function(e,t,r){"use strict";r.d(t,"c",(function(){return l})),r.d(t,"b",(function(){return g})),r.d(t,"a",(function(){return d}));var o=r(53),n=r(34);var i=function(e,t,r,o,i,s,a){var l=[],c=[];a.className&&Object(n.addClass)(t,a.className),a.readOnly&&l.push(a.readOnlyCellClassName),!1===a.valid&&a.invalidCellClassName?l.push(a.invalidCellClassName):c.push(a.invalidCellClassName),!1===a.wordWrap&&a.noWordWrapClassName&&l.push(a.noWordWrapClassName),!s&&a.placeholder&&l.push(a.placeholderCellClassName),Object(n.removeClass)(t,c),Object(n.addClass)(t,l)},s=r(40);var a=function(e,t,r,o,i,a,l){g("base").apply(this,[e,t,r,o,i,a,l]);var c=a;if(!c&&l.placeholder&&(c=l.placeholder),c=Object(s.stringify)(c),e.getSettings().trimWhitespace||e.getSettings().wordWrap||(c=c.replace(/ /g,String.fromCharCode(160))),l.rendererTemplate){Object(n.empty)(t);var h=e.rootDocument.createElement("TEMPLATE");h.setAttribute("bind","{{}}"),h.innerHTML=l.rendererTemplate,HTMLTemplateElement.decorate(h),h.model=e.getSourceDataAtRow(r),t.appendChild(h)}else Object(n.fastInnerText)(t,c)},{register:l,getItem:c,hasItem:h,getNames:d,getValues:u}=Object(o.a)("renderers");function g(e){if("function"==typeof e)return e;if(!h(e))throw Error('No registered renderer found under "'.concat(e,'" name'));return c(e)}l("base",i),l("text",a)},,,,,,,,,,,,,,,,,,,,,function(e,t,r){"use strict";r.d(t,"d",(function(){return n})),r.d(t,"c",(function(){return i})),r.d(t,"a",(function(){return s})),r.d(t,"b",(function(){return a}));var o=new WeakMap,n=Symbol("rootInstance");function i(e){o.set(e,!0)}function s(e){return e===n}function a(e){return o.has(e)}},function(e,t,r){"use strict";r.d(t,"a",(function(){return c})),r.d(t,"c",(function(){return h})),r.d(t,"b",(function(){return d})),r.d(t,"d",(function(){return u}));var o=r(40),n=r(32),i=r(60),s=r(54),a=r(79);function l(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(['Language with code "','" was not found. You should register particular language \n    before using it. Read more about this issue at: https://docs.handsontable.com/i18n/missing-language-code.'],['Language with code "','" was not found. You should register particular language\\x20\n    before using it. Read more about this issue at: https://docs.handsontable.com/i18n/missing-language-code.']);return l=function(){return e},e}function c(e,t){return Object(n.objectEach)(t,(t,r)=>{Object(o.isUndefined)(e[r])&&(e[r]=t)}),e}function h(e){var t=/^([a-zA-Z]{2})-([a-zA-Z]{2})$/.exec(e);return t?"".concat(t[1].toLowerCase(),"-").concat(t[2].toUpperCase()):e}function d(e){var t=h(e);return Object(a.d)(t)||(t=a.a,u(e)),t}function u(e){Object(o.isDefined)(e)&&Object(i.a)(Object(s.a)(l(),e))}},function(e,t,r){"use strict";r.d(t,"a",(function(){return h})),r.d(t,"b",(function(){return l})),r.d(t,"c",(function(){return c}));var o=r(43),n=r(74),i=new o.a,s=new Set,a=0;function l(e){0===a&&(i.addEventListener(e,"keydown",e=>{s.has(e.keyCode)||s.add(e.keyCode)}),i.addEventListener(e,"keyup",e=>{s.has(e.keyCode)&&s.delete(e.keyCode)}),i.addEventListener(e,"visibilitychange",()=>{e.hidden&&s.clear()}),i.addEventListener(e.defaultView,"blur",()=>{s.clear()})),a+=1}function c(){a>0&&(a-=1),0===a&&(i.clearEvents(),s.clear(),a=0)}function h(){return Array.from(s.values()).some(e=>Object(n.isCtrlMetaKey)(e))}},function(e,t,r){var o,n,i,s;window,o=/^(\r\n|\n\r|\r|\n)/,n=/^[^\t\r\n]+/,i=/^\t/,s={parse(e){var t=[[""]];if(0===e.length)return t;for(var r,s=0,a=0;e.length>0&&r!==e.length;)if(r=e.length,e.match(i))e=e.replace(i,""),s+=1,t[a][s]="";else if(e.match(o))e=e.replace(o,""),s=0,t[a+=1]=[""];else{var l="";if(e.startsWith('"')){for(var c=0,h=!0;h;){var d=e.slice(0,1);'"'===d&&(c+=1),l+=d,(0===(e=e.slice(1)).length||e.match(/^[\t\r\n]/)&&c%2==0)&&(h=!1)}l=l.replace(/^"/,"").replace(/"$/,"").replace(/["]*/g,e=>new Array(Math.floor(e.length/2)).fill('"').join(""))}else{var u=e.match(n);l=u?u[0]:"",e=e.slice(l.length)}t[a][s]=l}return t},stringify(e){var t,r,o,n,i,s="";for(t=0,r=e.length;t<r;t+=1){for(n=e[t].length,o=0;o<n;o+=1)o>0&&(s+="\t"),"string"==typeof(i=e[t][o])?i.indexOf("\n")>-1?s+='"'.concat(i.replace(/"/g,'""'),'"'):s+=i:s+=null==i?"":i;t!==r-1&&(s+="\n")}return s}},t.parse=s.parse,t.stringify=s.stringify},,function(e,t,r){"use strict";r.d(t,"a",(function(){return z})),r.d(t,"b",(function(){return k})),r.d(t,"c",(function(){return P}));var o=r(53),n=r(38);class i extends n.c{constructor(){super(...arguments),this.visualCellRange=null}add(e){return null===this.visualCellRange?this.visualCellRange=new n.b(e):this.visualCellRange.expand(e),this}clear(){return this.visualCellRange=null,super.clear()}findVisibleCoordsInRange(e,t,r){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:r,{row:i,col:s}=e,{row:a,col:l}=t,{row:c,col:h}=this.settings.visualToRenderableCoords(e);return a===i&&null===c||l===s&&null===h?null:null===c&&null===h?this.findVisibleCoordsInRange(new n.a(i+r,s+o),t,r,o):null===c?this.findVisibleCoordsInRange(new n.a(i+r,s),t,r,o):null===h?this.findVisibleCoordsInRange(new n.a(i,s+o),t,r,o):e}commit(){if(null===this.visualCellRange)return this;var e=this.getRowSearchDirection(this.visualCellRange),t=this.getColumnSearchDirection(this.visualCellRange),r=this.findVisibleCoordsInRange(this.visualCellRange.from,this.visualCellRange.to,e,t),o=this.findVisibleCoordsInRange(this.visualCellRange.to,this.visualCellRange.from,-e,-t);if(null===r)return this.cellRange=null,this;var i=this.settings.visualToRenderableCoords(r),s=this.settings.visualToRenderableCoords(o);return this.cellRange=new n.b(i,i,s),this}adjustCoordinates(e){if(null===this.cellRange&&e){var t=this.getRowSearchDirection(e),r=this.getColumnSearchDirection(e),o=this.findVisibleCoordsInRange(e.from,e.to,t,r);if(null!==o){var i=this.settings.visualToRenderableCoords(o);return this.cellRange=new n.b(i),e.setHighlight(o),this}}return e.setHighlight(e.from),this}getVisualCorners(){var e=this.settings.renderableToVisualCoords(this.cellRange.getTopLeftCorner()),t=this.settings.renderableToVisualCoords(this.cellRange.getBottomRightCorner());return[e.row,e.col,t.row,t.col]}getRowSearchDirection(e){return e.from.row<e.to.row?1:-1}getColumnSearchDirection(e){return e.from.col<e.to.col?1:-1}}var s=i;var a=function(e){var{visualToRenderableCoords:t,renderableToVisualCoords:r,activeHeaderClassName:o}=e;return new s({visualToRenderableCoords:t,renderableToVisualCoords:r,highlightHeaderClassName:o})};var l=function(e){var{BorderStyle:t,visualToRenderableCoords:r,renderableToVisualCoords:o,layerLevel:n,areaCornerVisible:i}=e,a=new t;return a.cornerVisible=i,new s({visualToRenderableCoords:r,renderableToVisualCoords:o,className:"area",markIntersections:!0,layerLevel:Math.min(n,7),border:a})};var c=function(e){var{BorderStyle:t,visualToRenderableCoords:r,renderableToVisualCoords:o,cellCornerVisible:n}=e,i=new t;return i.cornerVisible=n,new s({visualToRenderableCoords:r,renderableToVisualCoords:o,className:"current",border:i})};function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,o)}return r}function d(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var u=function(e){var{visualToRenderableCoords:t,renderableToVisualCoords:r,border:o,cellRange:n}=e;return new s(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach((function(t){d(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({visualToRenderableCoords:t,renderableToVisualCoords:r},o),n)};var g=function(e){var{BorderStyle:t,visualToRenderableCoords:r,renderableToVisualCoords:o}=e,n=new t;return new s({visualToRenderableCoords:r,renderableToVisualCoords:o,className:"fill",border:n})};var f=function(e){var{visualToRenderableCoords:t,renderableToVisualCoords:r,headerClassName:o,rowClassName:n,columnClassName:i}=e;return new s({visualToRenderableCoords:t,renderableToVisualCoords:r,className:"highlight",highlightHeaderClassName:o,highlightRowClassName:n,highlightColumnClassName:i})},m=new Map,{register:p,getItem:v}=Object(o.a)("highlight/types");function w(e,t,r){r&&m.set(e,r),p(e,t)}function b(e,t){return v(e)(t)}w("active-header",a),w("area",l,{width:1,color:"#4b89ff",strokeAlignment:"inside"}),w("cell",c,{width:2,color:"#4b89ff",strokeAlignment:"inside"}),w("custom-selection",u),w("fill",g,{width:1,color:"#ff0000",strokeAlignment:"inside"}),w("header",f);var C=r(33),E=r(32);function O(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,o)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O(Object(r),!0).forEach((function(t){R(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function R(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var T=class{constructor(e){this.options=e,this.layerLevel=0,this.borderStyles=new Map([["cell",this._createBorderStyleClass("cell")],["fill",this._createBorderStyleClass("fill")],["area",this._createBorderStyleClass("area")]]),this.cell=b("cell",y({BorderStyle:this.borderStyles.get("cell")},e)),this.fill=b("fill",y({BorderStyle:this.borderStyles.get("fill")},e)),this.areas=new Map,this.headers=new Map,this.activeHeaders=new Map,this.customSelections=new Map}isEnabledFor(e){var t="current"===e?"cell":e,r=this.options.disableHighlight;return"string"==typeof r&&(r=[r]),!1===r||Array.isArray(r)&&!r.includes(t)}useLayerLevel(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.layerLevel=e,this}getCell(){return this.cell}getFill(){return this.fill}createOrGetArea(){var e,t=this.layerLevel;return this.areas.has(t)?e=this.areas.get(t):(e=b("area",y({BorderStyle:this.borderStyles.get("area"),layerLevel:t},this.options)),this.areas.set(t,e)),e}getAreas(){return[...this.areas.values()]}createOrGetHeader(){var e,t=this.layerLevel;return this.headers.has(t)?e=this.headers.get(t):(e=b("header",y({},this.options)),this.headers.set(t,e)),e}getHeaders(){return[...this.headers.values()]}createOrGetActiveHeader(){var e,t=this.layerLevel;return this.activeHeaders.has(t)?e=this.activeHeaders.get(t):(e=b("active-header",y({},this.options)),this.activeHeaders.set(t,e)),e}getCommonBorderStyle(e){var t;return null===(t=this.borderStyles.get(e))||void 0===t?void 0:t.prototype}getActiveHeaders(){return[...this.activeHeaders.values()]}getCustomSelections(){return[...this.customSelections.values()]}addCustomSelection(e,t){this.customSelections.set(e,b("custom-selection",y(y({},this.options),t)))}clear(){this.cell.clear(),this.fill.clear(),Object(C.arrayEach)(this.areas.values(),e=>{e.clear()}),Object(C.arrayEach)(this.headers.values(),e=>{e.clear()}),Object(C.arrayEach)(this.activeHeaders.values(),e=>{e.clear()})}getAll(){return[this.cell,this.fill,...this.areas.values(),...this.headers.values(),...this.activeHeaders.values(),...this.customSelections.values()]}_createBorderStyleClass(e){var t,r,o=function(e){return m.get(e)||{}}(e);if(o)return t=o,r=class{},Object(E.extend)(r.prototype,t),class extends r{}}};class S{constructor(){this.ranges=[]}isEmpty(){return 0===this.size()}set(e){return this.clear(),this.ranges.push(new n.b(e)),this}add(e){return this.ranges.push(new n.b(e)),this}current(){return this.peekByIndex(0)}previous(){return this.peekByIndex(-1)}includes(e){return this.ranges.some(t=>t.includes(e))}clear(){return this.ranges.length=0,this}size(){return this.ranges.length}peekByIndex(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,r=this.size()+t-1;return r>=0&&(e=this.ranges[r]),e}[Symbol.iterator](){return this.ranges[Symbol.iterator]()}}var M=S,N=r(120),H=r(40),L=r(62);class x{constructor(e,t){this.range=e,this.options=t}transformStart(e,t,r){var o=new n.a(e,t),i=this.range.current().highlight,{row:s,col:a}=this.options.visualToRenderableCoords(i),l=i,c=0,h=0;if(this.runLocalHooks("beforeTransformStart",o),null!==s&&null!==a){var d=this.options.countRows(),u=this.options.countCols(),g=this.options.fixedRowsBottom(),f=this.options.minSpareRows(),m=this.options.minSpareCols(),p=this.options.autoWrapRow(),v=this.options.autoWrapCol();s+e>d-1?r&&f>0&&!(g&&s>=d-g-1)?(this.runLocalHooks("insertRowRequire",d),d=this.options.countRows()):v&&(o.row=1-d,o.col=a+o.col===u-1?1-u:1):v&&s+o.row<0&&a+o.col>=0&&(o.row=d-1,o.col=a+o.col===0?u-1:-1),a+o.col>u-1?r&&m>0?(this.runLocalHooks("insertColRequire",u),u=this.options.countCols()):p&&(o.row=s+o.row===d-1?1-d:1,o.col=1-u):p&&a+o.col<0&&s+o.row>=0&&(o.row=s+o.row===0?d-1:-1,o.col=u-1);var w=new n.a(s+o.row,a+o.col);c=0,h=0,w.row<0?(c=-1,w.row=0):w.row>0&&w.row>=d&&(c=1,w.row=d-1),w.col<0?(h=-1,w.col=0):w.col>0&&w.col>=u&&(h=1,w.col=u-1),l=this.options.renderableToVisualCoords(w)}return this.runLocalHooks("afterTransformStart",l,c,h),l}transformEnd(e,t){var r=new n.a(e,t),o=this.range.current(),i=o.to,s=0,a=0;this.runLocalHooks("beforeTransformEnd",r);var{row:l,col:c}=this.options.visualToRenderableCoords(o.highlight);if(null!==l&&null!==c){var h=this.options.countRows(),d=this.options.countCols(),{row:u,col:g}=this.options.visualToRenderableCoords(o.to),f=new n.a(u+r.row,g+r.col);s=0,a=0,f.row<0?(s=-1,f.row=0):f.row>0&&f.row>=h&&(s=1,f.row=h-1),f.col<0?(a=-1,f.col=0):f.col>0&&f.col>=d&&(a=1,f.col=d-1),i=this.options.renderableToVisualCoords(f)}return this.runLocalHooks("afterTransformEnd",i,s,a),i}}Object(E.mixin)(x,L.a);var A=x,I=[3,2],j=[["number"],["number","string"],["number","undefined"],["number","string","undefined"]],D=Symbol("root"),_=Symbol("child");function k(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:D;if(t!==D&&t!==_)throw new Error("The second argument is used internally only and cannot be overwritten.");var r=Array.isArray(e),o=t===D,i=0;if(r){var s=e[0];if(0===e.length)i=1;else if(o&&s instanceof n.b)i=3;else if(o&&Array.isArray(s))i=k(s,_);else if(e.length>=2&&e.length<=4){var a=!e.some((e,t)=>!j[t].includes(typeof e));a&&(i=2)}}return i}function P(e){var{keepDirection:t=!1,propToCol:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!I.includes(e))throw new Error("Unsupported selection ranges schema type was provided.");return function(o){var n=3===e,i=n?o.from.row:o[0],s=n?o.from.col:o[1],a=n?o.to.row:o[2],l=n?o.to.col:o[3];if("function"==typeof r&&("string"==typeof s&&(s=r(s)),"string"==typeof l&&(l=r(l))),Object(H.isUndefined)(a)&&(a=i),Object(H.isUndefined)(l)&&(l=s),!t){var c=i,h=s,d=a,u=l;i=Math.min(c,d),s=Math.min(h,u),a=Math.max(c,d),l=Math.max(h,u)}return[i,s,a,l]}}function B(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1/0;return"number"==typeof e&&e>=0&&e<t}var F=r(54);function V(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(["Unsupported format of the selection ranges was passed. To select cells pass \n        the coordinates as an array of arrays ([[rowStart, columnStart/columnPropStart, rowEnd, columnEnd/columnPropEnd]]) \n        or as an array of CellRange objects."],["Unsupported format of the selection ranges was passed. To select cells pass\\x20\n        the coordinates as an array of arrays ([[rowStart, columnStart/columnPropStart, rowEnd, columnEnd/columnPropEnd]])\\x20\n        or as an array of CellRange objects."]);return V=function(){return e},e}class W{constructor(e,t){var r=this;this.settings=e,this.tableProps=t,this.inProgress=!1,this.selectedByCorner=!1,this.selectedByRowHeader=new Set,this.selectedByColumnHeader=new Set,this.selectedRange=new M,this.highlight=new T({headerClassName:e.currentHeaderClassName,activeHeaderClassName:e.activeHeaderClassName,rowClassName:e.currentRowClassName,columnClassName:e.currentColClassName,disableHighlight:e.disableVisualSelection,cellCornerVisible:function(){return r.isCellCornerVisible(...arguments)},areaCornerVisible:function(){return r.isAreaCornerVisible(...arguments)},visualToRenderableCoords:e=>this.tableProps.visualToRenderableCoords(e),renderableToVisualCoords:e=>this.tableProps.renderableToVisualCoords(e)}),this.transformation=new A(this.selectedRange,{countRows:()=>this.tableProps.countRowsTranslated(),countCols:()=>this.tableProps.countColsTranslated(),visualToRenderableCoords:e=>this.tableProps.visualToRenderableCoords(e),renderableToVisualCoords:e=>this.tableProps.renderableToVisualCoords(e),fixedRowsBottom:()=>e.fixedRowsBottom,minSpareRows:()=>e.minSpareRows,minSpareCols:()=>e.minSpareCols,autoWrapRow:()=>e.autoWrapRow,autoWrapCol:()=>e.autoWrapCol}),this.transformation.addLocalHook("beforeTransformStart",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runLocalHooks("beforeModifyTransformStart",...t)})),this.transformation.addLocalHook("afterTransformStart",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runLocalHooks("afterModifyTransformStart",...t)})),this.transformation.addLocalHook("beforeTransformEnd",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runLocalHooks("beforeModifyTransformEnd",...t)})),this.transformation.addLocalHook("afterTransformEnd",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runLocalHooks("afterModifyTransformEnd",...t)})),this.transformation.addLocalHook("insertRowRequire",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runLocalHooks("insertRowRequire",...t)})),this.transformation.addLocalHook("insertColRequire",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runLocalHooks("insertColRequire",...t)}))}updateBorderStyle(e){Object(C.arrayEach)(["cell","area","fill"],t=>{var r,o,n=Object(E.isObject)(e[t])?e[t]:{};r=this.highlight.getCommonBorderStyle(t),(o=n).borderWidth?r.width=o.borderWidth:r.hasOwnProperty("width")&&delete r.width,o.borderColor?r.color=o.borderColor:r.hasOwnProperty("color")&&delete r.color})}getSelectedRange(){return this.selectedRange}begin(){this.inProgress=!0}finish(){this.runLocalHooks("afterSelectionFinished",Array.from(this.selectedRange)),this.inProgress=!1}isInProgress(){return this.inProgress}setRangeStart(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o="multiple"===this.settings.selectionMode,n=Object(H.isUndefined)(t)?Object(N.a)():t,i=e.row<0,s=e.col<0,a=i&&s;i&&(e.row=0),s&&(e.col=0),this.selectedByCorner=a,this.runLocalHooks("beforeSetRangeStart".concat(r?"Only":""),e),(!o||o&&!n&&Object(H.isUndefined)(t))&&this.selectedRange.clear(),this.selectedRange.add(e),0===this.getLayerLevel()&&(this.selectedByRowHeader.clear(),this.selectedByColumnHeader.clear()),!a&&s&&this.selectedByRowHeader.add(this.getLayerLevel()),!a&&i&&this.selectedByColumnHeader.add(this.getLayerLevel()),r||this.setRangeEnd(e)}setRangeStartOnly(e,t){this.setRangeStart(e,t,!0)}setRangeEnd(e){if(!this.selectedRange.isEmpty()){this.runLocalHooks("beforeSetRangeEnd",e),this.begin();var t=this.selectedRange.current();if("single"!==this.settings.selectionMode&&t.setTo(new n.a(e.row,e.col)),this.highlight.getCell().clear(),this.highlight.isEnabledFor("cell")){var r=this.tableProps.expandCoordsToRangeIncludingSpans(this.selectedRange.current().highlight);this.highlight.getCell().add(r.from).add(r.to).commit().adjustCoordinates(t)}var o=this.getLayerLevel();o<this.highlight.layerLevel&&(Object(C.arrayEach)(this.highlight.getAreas(),e=>{e.clear()}),Object(C.arrayEach)(this.highlight.getHeaders(),e=>{e.clear()}),Object(C.arrayEach)(this.highlight.getActiveHeaders(),e=>{e.clear()})),this.highlight.useLayerLevel(o);var i=this.highlight.createOrGetArea(),s=this.highlight.createOrGetHeader(),a=this.highlight.createOrGetActiveHeader();if(i.clear(),s.clear(),a.clear(),this.highlight.isEnabledFor("area")&&(this.isMultiple()||o>=1)&&(i.add(t.from).add(t.to).commit(),1===o&&(this.highlight.useLayerLevel(o-1).createOrGetArea().add(this.selectedRange.previous().from).commit(),this.highlight.useLayerLevel(o))),this.highlight.isEnabledFor("header")&&("single"===this.settings.selectionMode?s.add(t.highlight).commit():s.add(t.from).add(t.to).commit()),this.isSelectedByRowHeader())this.tableProps.countCols()===t.getWidth()&&a.add(new n.a(t.from.row,-1)).add(new n.a(t.to.row,-1)).commit();if(this.isSelectedByColumnHeader())this.tableProps.countRows()===t.getHeight()&&a.add(new n.a(-1,t.from.col)).add(new n.a(-1,t.to.col)).commit();this.runLocalHooks("afterSetRangeEnd",e)}}isMultiple(){var e=Object(E.createObjectPropListener)(!this.selectedRange.current().isSingle());return this.runLocalHooks("afterIsMultipleSelection",e),e.value}transformStart(e,t,r){var o=this.transformation.transformStart(e,t,r);this.getSelectedRange().current().highlight!==o&&this.setRangeStart(o)}transformEnd(e,t){this.setRangeEnd(this.transformation.transformEnd(e,t))}getLayerLevel(){return this.selectedRange.size()-1}isSelected(){return!this.selectedRange.isEmpty()}isSelectedByRowHeader(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getLayerLevel();return-1===e?this.selectedByRowHeader.size>0:this.selectedByRowHeader.has(e)}isSelectedByColumnHeader(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getLayerLevel();return-1===e?this.selectedByColumnHeader.size>0:this.selectedByColumnHeader.has(e)}isSelectedByAnyHeader(){return this.isSelectedByRowHeader(-1)||this.isSelectedByColumnHeader(-1)}isSelectedByCorner(){return this.selectedByCorner}inInSelection(e){return this.selectedRange.includes(e)}isCellCornerVisible(){return this.settings.fillHandle&&!this.tableProps.isEditorOpened()&&!this.isMultiple()}isAreaCornerVisible(e){return(!Number.isInteger(e)||e===this.getLayerLevel())&&(this.settings.fillHandle&&!this.tableProps.isEditorOpened()&&this.isMultiple())}clear(){this.selectedRange.clear(),this.highlight.clear()}deselect(){this.isSelected()&&(this.inProgress=!1,this.clear(),this.runLocalHooks("afterDeselect"))}selectAll(){var e=this.tableProps.countRows(),t=this.tableProps.countCols();0!==e&&0!==t&&(this.clear(),this.setRangeStartOnly(new n.a(-1,-1)),this.selectedByRowHeader.add(this.getLayerLevel()),this.selectedByColumnHeader.add(this.getLayerLevel()),this.setRangeEnd(new n.a(e-1,t-1)),this.finish())}selectCells(e){var t=k(e);if(1===t)return!1;if(0===t)throw new Error(Object(F.a)(V()));var r=P(t,{propToCol:e=>this.tableProps.propToCol(e),keepDirection:!0}),o=this.tableProps.countRows(),i=this.tableProps.countCols(),s=!e.some(e=>{var[t,n,s,a]=r(e);return!(B(t,o)&&B(n,i)&&B(s,o)&&B(a,i))});return s&&(this.clear(),Object(C.arrayEach)(e,e=>{var[t,o,i,s]=r(e);this.setRangeStartOnly(new n.a(t,o),!1),this.setRangeEnd(new n.a(i,s)),this.finish()})),s}selectColumns(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r="string"==typeof e?this.tableProps.propToCol(e):e,o="string"==typeof t?this.tableProps.propToCol(t):t,i=this.tableProps.countCols(),s=this.tableProps.countRows(),a=s>0&&B(r,i)&&B(o,i);return a&&(this.setRangeStartOnly(new n.a(-1,r)),this.setRangeEnd(new n.a(s-1,o)),this.finish()),a}selectRows(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=this.tableProps.countRows(),o=this.tableProps.countCols(),i=B(e,r)&&B(t,r);return i&&(this.setRangeStartOnly(new n.a(e,-1)),this.setRangeEnd(new n.a(t,o>0?o-1:0)),this.finish()),i}refresh(){if(this.isSelected()){var e=this.highlight.getCell(),t=this.getLayerLevel();e.commit().adjustCoordinates(this.selectedRange.current());for(var r=0;r<this.selectedRange.size();r+=1){this.highlight.useLayerLevel(r);var o=this.highlight.createOrGetArea(),n=this.highlight.createOrGetHeader(),i=this.highlight.createOrGetActiveHeader();o.commit(),n.commit(),i.commit()}this.highlight.useLayerLevel(t)}}}Object(E.mixin)(W,L.a);var z=W;r(91)},function(e,t,r){"use strict";r.d(t,"a",(function(){return u})),r.d(t,"b",(function(){return T})),r.d(t,"c",(function(){return w}));var o=r(121),n=r.n(o),i=r(75),s=r(32),a=r(33),l=r(44),c=r(40),h=Object(i.cellMethodLookupFactory)("copyable",!1);class d{static get DESTINATION_RENDERER(){return 1}static get DESTINATION_CLIPBOARD_GENERATOR(){return 2}constructor(e,t,r){this.instance=e,this.tableMeta=r,this.dataSource=t,this.duckSchema=this.dataSource&&this.dataSource[0]?Object(s.duckSchema)(this.dataSource[0]):{},this.colToPropCache=void 0,this.propToColCache=void 0,this.createMap()}createMap(){var e=this.getSchema();if(void 0===e)throw new Error("trying to create `columns` definition but you didn't provide `schema` nor `data`");var t,r=this.tableMeta.columns;if(this.colToPropCache=[],this.propToColCache=new Map,r){var o=0,n=0,i=!1;if("function"==typeof r){var a=Object(s.deepObjectSize)(e);o=a>0?a:this.countFirstRowKeys(),i=!0}else{var l=this.tableMeta.maxCols;o=Math.min(l,r.length)}for(t=0;t<o;t++){var c=i?r(t):r[t];if(Object(s.isObject)(c)){if(void 0!==c.data){var h=i?n:t;this.colToPropCache[h]=c.data,this.propToColCache.set(c.data,h)}n+=1}}}else this.recursiveDuckColumns(e)}countFirstRowKeys(){return Object(i.countFirstRowKeys)(this.dataSource)}recursiveDuckColumns(e,t,r){var o,n=t,i=r;return void 0===n&&(n=0,i=""),"object"!=typeof e||Array.isArray(e)||Object(s.objectEach)(e,(e,t)=>{null===e?(o=i+t,this.colToPropCache.push(o),this.propToColCache.set(o,n),n+=1):n=this.recursiveDuckColumns(e,n,"".concat(t,"."))}),n}colToProp(e){if(!1===Number.isInteger(e))return e;var t=this.instance.toPhysicalColumn(e);return null===t?e:this.colToPropCache&&Object(c.isDefined)(this.colToPropCache[t])?this.colToPropCache[t]:t}propToCol(e){var t=this.propToColCache.get(e);if(Object(c.isDefined)(t))return this.instance.toVisualColumn(t);var r=this.instance.toVisualColumn(e);return null===r?e:r}getSchema(){var e=this.tableMeta.dataSchema;return e?"function"==typeof e?e():e:this.duckSchema}createRow(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,o=arguments.length>2?arguments[2]:void 0,n=this.instance.countSourceRows(),i=n,a=0,c=e;if(("number"!=typeof c||c>=n)&&(c=n),c<this.instance.countRows()&&(i=this.instance.toPhysicalRow(c)),!1===this.instance.runHooks("beforeCreateRow",c,r,o)||null===i)return 0;for(var h=this.tableMeta.maxRows,d=this.instance.countCols(),u=[],g=function(){var e=null;"array"===t.instance.dataType?t.tableMeta.dataSchema?e=Object(s.deepClone)(t.getSchema()):(e=[],Object(l.rangeEach)(d-1,()=>e.push(null))):"function"===t.instance.dataType?e=t.tableMeta.dataSchema(c):(e={},Object(s.deepExtend)(e,t.getSchema())),u.push(e),a+=1};a<r&&n+a<h;)g();return this.instance.rowIndexMapper.insertIndexes(c,a),this.spliceData(i,0,...u),this.instance.runHooks("afterCreateRow",c,a,o),this.instance.forceFullRender=!0,a}createCol(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments.length>2?arguments[2]:void 0;if(!this.instance.isColumnModificationAllowed())throw new Error("Cannot create new column. When data source in an object, you can only have as much columns as defined in first data row, data schema or in the 'columns' setting.If you want to be able to add new columns, you have to use array datasource.");var o=this.dataSource,n=this.tableMeta.maxCols,i=e;if(("number"!=typeof i||i>=this.instance.countSourceCols())&&(i=this.instance.countSourceCols()),!1===this.instance.runHooks("beforeCreateCol",i,t,r))return 0;var s=this.instance.countSourceCols();i<this.instance.countCols()&&(s=this.instance.toPhysicalColumn(i));for(var a=this.instance.countSourceRows(),l=this.instance.countCols(),c=0,h=s;c<t&&l<n;){if("number"!=typeof i||i>=l)if(a>0)for(var d=0;d<a;d+=1)void 0===o[d]&&(o[d]=[]),o[d].push(null);else o.push([null]);else for(var u=0;u<a;u++)o[u].splice(h,0,null);c+=1,h+=1,l+=1}return this.instance.columnIndexMapper.insertIndexes(i,c),this.instance.runHooks("afterCreateCol",i,c,r),this.instance.forceFullRender=!0,c}removeRow(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments.length>2?arguments[2]:void 0,o="number"!=typeof e?-t:e,n=this.instance.runHooks("modifyRemovedAmount",t,o),i=this.instance.countSourceRows();o=(i+o)%i;var s=this.visualRowsToPhysical(o,n);if(!1===this.instance.runHooks("beforeRemoveRow",o,n,s,r))return!1;var a=this.dataSource,l=this.filterData(o,n);if(l&&(a.length=0,Array.prototype.push.apply(a,l)),o<this.instance.countRows()){this.instance.rowIndexMapper.removeIndexes(s);var h=Object(c.isDefined)(this.tableMeta.columns)||Object(c.isDefined)(this.tableMeta.dataSchema);0===this.instance.rowIndexMapper.getNotTrimmedIndexesLength()&&!1===h&&this.instance.columnIndexMapper.setIndexesSequence([])}return this.instance.runHooks("afterRemoveRow",o,n,s,r),this.instance.forceFullRender=!0,!0}removeCol(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments.length>2?arguments[2]:void 0;if("object"===this.instance.dataType||this.tableMeta.columns)throw new Error("cannot remove column with object data source or columns option specified");var o="number"!=typeof e?-t:e;o=(this.instance.countCols()+o)%this.instance.countCols();var n=this.visualColumnsToPhysical(o,t),i=n.slice(0).sort((e,t)=>t-e);if(!1===this.instance.runHooks("beforeRemoveCol",o,t,n,r))return!1;for(var s=!0,a=i.length,l=this.dataSource,c=0;c<a;c++)s&&n[0]!==n[c]-c&&(s=!1);if(s)for(var h=0,d=this.instance.countSourceRows();h<d;h++)l[h].splice(n[0],t);else for(var u=0,g=this.instance.countSourceRows();u<g;u++)for(var f=0;f<a;f++)l[u].splice(i[f],1);return o<this.instance.countCols()&&(this.instance.columnIndexMapper.removeIndexes(n),0===this.instance.columnIndexMapper.getNotTrimmedIndexesLength()&&this.instance.rowIndexMapper.setIndexesSequence([])),this.instance.runHooks("afterRemoveCol",o,t,n,r),this.instance.forceFullRender=!0,!0}spliceCol(e,t,r){for(var o=this.instance.getDataAtCol(e),n=o.slice(t,t+r),i=o.slice(t+r),s=arguments.length,l=new Array(s>3?s-3:0),c=3;c<s;c++)l[c-3]=arguments[c];Object(a.extendArray)(l,i);for(var h=0;h<r;)l.push(null),h+=1;return Object(a.to2dArray)(l),this.instance.populateFromArray(t,e,l,null,null,"spliceCol"),n}spliceRow(e,t,r){for(var o=this.instance.getSourceDataAtRow(e),n=o.slice(t,t+r),i=o.slice(t+r),s=arguments.length,l=new Array(s>3?s-3:0),c=3;c<s;c++)l[c-3]=arguments[c];Object(a.extendArray)(l,i);for(var h=0;h<r;)l.push(null),h+=1;return this.instance.populateFromArray(e,t,[l],null,null,"spliceRow"),n}spliceData(e,t){for(var r=arguments.length,o=new Array(r>2?r-2:0),n=2;n<r;n++)o[n-2]=arguments[n];!1!==this.instance.runHooks("beforeDataSplice",e,t,o)&&this.dataSource.splice(e,t,...o)}filterData(e,t){var r=this.visualRowsToPhysical(e,t);if(!1!==this.instance.runHooks("beforeDataFilter",e,t,r))return this.dataSource.filter((e,t)=>-1===r.indexOf(t))}get(e,t){var r=this.instance.toPhysicalRow(e),o=this.dataSource[r],n=this.instance.runHooks("modifyRowData",r),i=null;if((o=isNaN(n)?n:o)&&o.hasOwnProperty&&Object(s.hasOwnProperty)(o,t))i=o[t];else if("string"==typeof t&&t.indexOf(".")>-1){var a=t.split("."),l=o;if(!l)return null;for(var c=0,h=a.length;c<h;c++)if(void 0===(l=l[a[c]]))return null;i=l}else"function"==typeof t&&(i=t(this.dataSource.slice(r,r+1)[0]));if(this.instance.hasHook("modifyData")){var d=Object(s.createObjectPropListener)(i);this.instance.runHooks("modifyData",r,this.propToCol(t),d,"get"),d.isTouched()&&(i=d.value)}return i}getCopyable(e,t){return h.call(this.instance,e,this.propToCol(t))?this.get(e,t):""}set(e,t,r){var o=this.instance.toPhysicalRow(e),n=r,i=this.dataSource[o],a=this.instance.runHooks("modifyRowData",o);if(i=isNaN(a)?a:i,this.instance.hasHook("modifyData")){var l=Object(s.createObjectPropListener)(n);this.instance.runHooks("modifyData",o,this.propToCol(t),l,"set"),l.isTouched()&&(n=l.value)}if(i&&i.hasOwnProperty&&Object(s.hasOwnProperty)(i,t))i[t]=n;else if("string"==typeof t&&t.indexOf(".")>-1){var c,h=t.split("."),d=i,u=0;for(u=0,c=h.length-1;u<c;u++)void 0===d[h[u]]&&(d[h[u]]={}),d=d[h[u]];d[h[u]]=n}else"function"==typeof t?t(this.dataSource.slice(o,o+1)[0],n):i[t]=n}visualRowsToPhysical(e,t){for(var r,o=this.instance.countSourceRows(),n=[],i=(o+e)%o,s=t;i<o&&s;)r=this.instance.toPhysicalRow(i),n.push(r),s-=1,i+=1;return n}visualColumnsToPhysical(e,t){for(var r=this.instance.countCols(),o=[],n=(r+e)%r,i=t;n<r&&i;){var s=this.instance.toPhysicalColumn(n);o.push(s),i-=1,n+=1}return o}clear(){for(var e=0;e<this.instance.countSourceRows();e++)for(var t=0;t<this.instance.countCols();t++)this.set(e,this.colToProp(t),"")}getLength(){var e,t=this.tableMeta.maxRows;e=t<0||0===t?0:t||1/0;var r=this.instance.rowIndexMapper.getNotTrimmedIndexesLength();return Math.min(r,e)}getAll(){var e={row:0,col:0},t={row:Math.max(this.instance.countRows()-1,0),col:Math.max(this.instance.countCols()-1,0)};return e.row-t.row!=0||this.instance.countSourceRows()?this.getRange(e,t,d.DESTINATION_RENDERER):[]}countCachedColumns(){return this.colToPropCache.length}getRange(e,t,r){var o,n,i,s=[],a=this.tableMeta.maxRows,l=this.tableMeta.maxCols;if(0===a||0===l)return[];var c=r===d.DESTINATION_CLIPBOARD_GENERATOR?this.getCopyable:this.get,h=Math.min(Math.max(a-1,0),Math.max(e.row,t.row)),u=Math.min(Math.max(l-1,0),Math.max(e.col,t.col));for(o=Math.min(e.row,t.row);o<=h;o++){i=[];var g=o>=0?this.instance.toPhysicalRow(o):o;for(n=Math.min(e.col,t.col);n<=u&&null!==g;n++)i.push(c.call(this,o,this.colToProp(n)));null!==g&&s.push(i)}return s}getText(e,t){return n.a.stringify(this.getRange(e,t,d.DESTINATION_RENDERER))}getCopyableText(e,t){return n.a.stringify(this.getRange(e,t,d.DESTINATION_CLIPBOARD_GENERATOR))}destroy(){this.instance=null,this.tableMeta=null,this.dataSource=null,this.duckSchema=null,this.colToPropCache.length=0,this.propToColCache.clear(),this.propToColCache=void 0}}var u=d,g=r(89);function f(e,t){var r="string"==typeof e?Object(g.a)(e):e;if(Object(s.isObject)(r)){var o=Object(s.isObject)(t),n={};return Object(s.objectEach)(r,(e,r)=>{(!o||o&&!Object(s.hasOwnProperty)(t,r))&&(n[r]=e)}),n}}function m(e){return Number.isInteger(e)&&e>=0}function p(e,t){if(!e())throw new Error("Assertion failed: ".concat(t))}function v(e){return null==e}var w=()=>({licenseKey:void 0,data:void 0,dataSchema:void 0,width:void 0,height:void 0,startRows:5,startCols:5,rowHeaders:void 0,colHeaders:null,colWidths:void 0,rowHeights:void 0,columns:void 0,cells:void 0,cell:[],comments:!1,customBorders:!1,minRows:0,minCols:0,maxRows:1/0,maxCols:1/0,minSpareRows:0,minSpareCols:0,allowInsertRow:!0,allowInsertColumn:!0,allowRemoveRow:!0,allowRemoveColumn:!0,selectionMode:"multiple",fillHandle:{autoInsertRow:!1},fixedRowsTop:0,fixedRowsBottom:0,fixedColumnsLeft:0,outsideClickDeselects:!0,enterBeginsEditing:!0,enterMoves:{row:1,col:0},tabMoves:{row:0,col:1},autoWrapRow:!0,autoWrapCol:!0,persistentState:void 0,currentRowClassName:void 0,currentColClassName:void 0,currentHeaderClassName:"ht__highlight",activeHeaderClassName:"ht__active_highlight",className:void 0,tableClassName:void 0,stretchH:"none",isEmptyRow(e){var t,r,o,n;for(t=0,r=this.countCols();t<r;t++)if(""!==(o=this.getDataAtCell(e,t))&&null!==o&&Object(c.isDefined)(o))return"object"==typeof o&&(n=this.getCellMeta(e,t),Object(s.isObjectEqual)(this.getSchema()[n.prop],o));return!0},isEmptyCol(e){var t,r,o;for(t=0,r=this.countRows();t<r;t++)if(""!==(o=this.getDataAtCell(t,e))&&null!==o&&Object(c.isDefined)(o))return!1;return!0},observeDOMVisibility:!0,allowInvalid:!0,allowEmpty:!0,invalidCellClassName:"htInvalid",placeholder:void 0,placeholderCellClassName:"htPlaceholder",readOnlyCellClassName:"htDimmed",renderer:void 0,commentedCellClassName:"htCommentCell",fragmentSelection:!1,readOnly:!1,skipColumnOnPaste:!1,skipRowOnPaste:!1,search:!1,type:"text",copyable:!0,editor:void 0,visibleRows:10,trimDropdown:!0,wordWrap:!0,noWordWrapClassName:"htNoWrap",contextMenu:void 0,copyPaste:!0,undo:void 0,columnSorting:void 0,manualColumnMove:void 0,manualColumnResize:void 0,manualRowMove:void 0,manualRowResize:void 0,mergeCells:!1,multiColumnSorting:void 0,viewportRowRenderingOffset:"auto",viewportColumnRenderingOffset:"auto",validator:void 0,disableVisualSelection:!1,manualColumnFreeze:void 0,trimWhitespace:!0,source:void 0,title:void 0,checkedTemplate:void 0,uncheckedTemplate:void 0,label:void 0,numericFormat:void 0,language:"en-US",selectOptions:void 0,autoColumnSize:void 0,autoRowSize:void 0,dateFormat:"DD/MM/YYYY",correctFormat:!1,defaultDate:void 0,strict:void 0,allowHtml:!1,renderAllRows:void 0,preventOverflow:!1,preventWheel:!1,bindRowsWithHeaders:void 0,collapsibleColumns:void 0,columnSummary:void 0,dropdownMenu:void 0,filters:void 0,formulas:void 0,ganttChart:void 0,headerTooltips:void 0,hiddenColumns:void 0,hiddenRows:void 0,nestedHeaders:void 0,trimRows:void 0,rowHeaderWidth:void 0,columnHeaderHeight:void 0,observeChanges:void 0,sortByRelevance:!0,filter:!0,filteringCaseSensitive:!1,dragToScroll:!0,nestedRows:void 0,selectionStyle:void 0});class b{constructor(){this.metaCtor=class{},this.meta=this.metaCtor.prototype,Object(s.extend)(this.meta,w())}getMetaConstructor(){return this.metaCtor}getMeta(){return this.meta}updateMeta(e){Object(s.extend)(this.meta,e),Object(s.extend)(this.meta,f(e.type,e))}}class C{constructor(e){var t=e.getMetaConstructor();this.meta=new t}getMeta(){return this.meta}updateMeta(e){Object(s.extend)(this.meta,e),Object(s.extend)(this.meta,f(e.type,e))}}class E{constructor(e){this.valueFactory=e,this.data=[],this.index=[],this.holes=new Set}obtain(e){p(()=>m(e),"Expecting an unsigned number.");var t,r=this._getStorageIndexByKey(e);if(r>=0)void 0===(t=this.data[r])&&(t=this.valueFactory(e),this.data[r]=t);else if(t=this.valueFactory(e),this.holes.size>0){var o=this.holes.values().next().value;this.holes.delete(o),this.data[o]=t,this.index[e]=o}else this.data.push(t),this.index[e]=this.data.length-1;return t}insert(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;p(()=>m(e)||v(e),"Expecting an unsigned number or null/undefined argument.");for(var r=[],o=this.data.length,n=0;n<t;n++)r.push(o+n),this.data.push(void 0);this.index.splice(v(e)?this.index.length:e,0,...r)}remove(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;p(()=>m(e)||v(e),"Expecting an unsigned number or null/undefined argument.");for(var r=this.index.splice(v(e)?this.index.length-t:e,t),o=0;o<r.length;o++){var n=r[o];"number"==typeof n&&this.holes.add(n)}}size(){return this.data.length-this.holes.size}values(){return Object(a.arrayFilter)(this.data,(e,t)=>!this.holes.has(t))[Symbol.iterator]()}entries(){for(var e=[],t=0;t<this.data.length;t++){var r=this._getKeyByStorageIndex(t);-1!==r&&e.push([r,this.data[t]])}var o=0;return{next:()=>{if(o<e.length){var t=e[o];return o+=1,{value:t,done:!1}}return{done:!0}}}}clear(){this.data=[],this.index=[],this.holes.clear()}_getStorageIndexByKey(e){return this.index.length>e?this.index[e]:-1}_getKeyByStorageIndex(e){return this.index.indexOf(e)}[Symbol.iterator](){return this.entries()}}var O=["data","width"];class y{constructor(e){this.globalMeta=e,this.metas=new E(()=>this._createMeta())}updateMeta(e,t){var r=this.getMeta(e);Object(s.extend)(r,t),Object(s.extend)(r,f(t.type,r))}createColumn(e,t){this.metas.insert(e,t)}removeColumn(e,t){this.metas.remove(e,t)}getMeta(e){return this.metas.obtain(e)}getMetaConstructor(e){return this.metas.obtain(e).constructor}clearCache(){this.metas.clear()}_createMeta(){return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];class r extends e{}for(var o=0;o<t.length;o++)r.prototype[t[o]]=void 0;return r}(this.globalMeta.getMetaConstructor(),O).prototype}}class R{constructor(e){this.columnMeta=e,this.metas=new E(()=>this._createRow())}updateMeta(e,t,r){var o=this.getMeta(e,t);Object(s.extend)(o,r),Object(s.extend)(o,f(r.type,o))}createRow(e,t){this.metas.insert(e,t)}createColumn(e,t){for(var r=0;r<this.metas.size();r++)this.metas.obtain(r).insert(e,t)}removeRow(e,t){this.metas.remove(e,t)}removeColumn(e,t){for(var r=0;r<this.metas.size();r++)this.metas.obtain(r).remove(e,t)}getMeta(e,t,r){var o=this.metas.obtain(e).obtain(t);return void 0===r?o:o[r]}setMeta(e,t,r,o){this.metas.obtain(e).obtain(t)[r]=o}removeMeta(e,t,r){delete this.metas.obtain(e).obtain(t)[r]}getMetas(){for(var e=[],t=Array.from(this.metas.values()),r=0;r<t.length;r++)e.push(...t[r].values());return e}getMetasAtRow(e){p(()=>m(e),"Expecting an unsigned number.");var t=new Map(this.metas);return t.has(e)?Array.from(t.get(e).values()):[]}clearCache(){this.metas.clear()}_createRow(){return new E(e=>this._createMeta(e))}_createMeta(e){return new(this.columnMeta.getMetaConstructor(e))}}class T{constructor(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.globalMeta=new b,this.globalMeta.updateMeta(e),this.tableMeta=new C(this.globalMeta),this.columnMeta=new y(this.globalMeta),this.cellMeta=new R(this.columnMeta)}getGlobalMeta(){return this.globalMeta.getMeta()}updateGlobalMeta(e){this.globalMeta.updateMeta(e)}getTableMeta(){return this.tableMeta.getMeta()}updateTableMeta(e){this.tableMeta.updateMeta(e)}getColumnMeta(e){return this.columnMeta.getMeta(e)}updateColumnMeta(e,t){this.columnMeta.updateMeta(e,t)}getCellMeta(e,t,r){return this.cellMeta.getMeta(e,t,r)}setCellMeta(e,t,r,o){this.cellMeta.setMeta(e,t,r,o)}updateCellMeta(e,t,r){this.cellMeta.updateMeta(e,t,r)}removeCellMeta(e,t,r){this.cellMeta.removeMeta(e,t,r)}getCellsMeta(){return this.cellMeta.getMetas()}getCellsMetaAtRow(e){return this.cellMeta.getMetasAtRow(e)}createRow(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;this.cellMeta.createRow(e,t)}removeRow(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;this.cellMeta.removeRow(e,t)}createColumn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;this.cellMeta.createColumn(e,t),this.columnMeta.createColumn(e,t)}removeColumn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;this.cellMeta.removeColumn(e,t),this.columnMeta.removeColumn(e,t)}clearCellsCache(){this.cellMeta.clearCache()}clearCache(){this.cellMeta.clearCache(),this.columnMeta.clearCache()}}},,,function(e,t,r){"use strict";r.d(t,"a",(function(){return c}));var o=r(33),n=r(79),i=r(53);var{register:s,getValues:a}=Object(i.a)("phraseFormatters");s("pluralize",(function(e,t){return Array.isArray(e)&&Number.isInteger(t)?e[t]:e}));var l=r(40);function c(e,t,r){var i=Object(n.b)(e);if(null===i)return null;var s=i[t];if(Object(l.isUndefined)(s))return null;var c=function(e,t){var r=e;return Object(o.arrayEach)(function(){return a()}(),o=>{r=o(e,t)}),r}(s,r);return Array.isArray(c)?c[0]:c}},function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));var o=r(1),n=r.n(o),i=()=>n.a.createElement("div",{className:"sv-chart-image-wrapper"},n.a.createElement("div",{className:"sv-chart-image-placeholder"},n.a.createElement("svg",{width:"100%",height:"100%",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},n.a.createElement("path",{fill:"#f5f5f5",d:"M11.094,13.019h7.735c0.057,0,0.105,0.049,0.104,0.106c-0.068,4.946-5.015,\n          7.789-9.648,6.554c-2.773-0.739-4.59-2.882-5.139-5.665c-0.433-2.197,0.142-4.497,1.572-6.223c1.379-1.665,\n          2.977-2.684,5.142-2.723c0.057-0.001,0.106,0.047,0.106,0.104l0.028,7.748C10.994,12.975,11.039,13.019,\n          11.094,13.019z"}),n.a.createElement("path",{fill:"#f5f5f5",d:"M13.009,10.878V3.294c0-0.057,0.048-0.105,0.105-0.104C17.163,3.247,21,6.917,21,\n          10.978c0,0.002,0,0.004,0,0.006l-7.891-0.006C13.054,10.978,13.009,10.934,13.009,10.878z"}))))},,,,,,,,,function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},,function(e,t,r){"use strict";(function(e,o){r.d(t,"a",(function(){return I}));var n=r(34),i=r(61),s=r(40),a=r(46),l=r(151),c=r(43),h=r(32),d=r(33),u=r(90),g=r(47),f=r(97),m=r(88),p=r(49),v=r(44),w=r(144),b=r(145),C=r(75),E=r(95),O=r(118),y=r(38),R=r(59),T=r(127),S=r(79),M=r(119),N=r(120),H=r(123),L=r(124),x=r(146),A=null;function I(t,r){var I,j,D,_,k=arguments.length>2&&void 0!==arguments[2]&&arguments[2],P=!1,B=this,F=new c.a(B),V=!0;r.language=Object(M.b)(r.language);var W=new L.b(r),z=W.getTableMeta(),U=W.getGlobalMeta();Object(O.a)(k)&&Object(O.c)(this),this.rootElement=t,this.rootDocument=t.ownerDocument,this.rootWindow=this.rootDocument.defaultView,Object(N.b)(this.rootDocument),this.isDestroyed=!1,this.container=this.rootDocument.createElement("div"),this.renderCall=!1,t.insertBefore(this.container,t.firstChild),Object(O.b)(this)&&Object(s._injectProductInfo)(r.licenseKey,t),this.guid="ht_".concat(Object(p.randomString)()),this.columnIndexMapper=new E.a,this.rowIndexMapper=new E.a,j=new b.a(B),this.rootElement.id&&"ht_"!==this.rootElement.id.substring(0,3)||(this.rootElement.id=this.guid);var G=e=>{var{row:t,col:r}=e;return new y.a(t>=0?B.rowIndexMapper.getRenderableFromVisualIndex(t):t,r>=0?B.columnIndexMapper.getRenderableFromVisualIndex(r):r)},X=e=>{var{row:t,col:r}=e;return new y.a(t>=0?B.rowIndexMapper.getVisualFromRenderableIndex(t):t,r>=0?B.columnIndexMapper.getVisualFromRenderableIndex(r):r)},K=new H.a(z,{countCols:()=>B.countCols(),countRows:()=>B.countRows(),propToCol:e=>I.propToCol(e),isEditorOpened:()=>!!B.getActiveEditor()&&B.getActiveEditor().isOpened(),countColsTranslated:()=>this.view.countRenderableColumns(),countRowsTranslated:()=>this.view.countRenderableRows(),visualToRenderableCoords:G,renderableToVisualCoords:X,expandCoordsToRangeIncludingSpans:e=>Object(x.a)(B,e)});this.selection=K;var Y=(e,t,r)=>{r&&this.selection.refresh()};function q(e){var t=Object(M.c)(e);Object(S.d)(t)?(B.runHooks("beforeLanguageChange",t),U.language=t,B.runHooks("afterLanguageChange",t)):Object(M.d)(e)}function Q(e,t){var r="className"===e?B.rootElement:B.table;if(V)Object(n.addClass)(r,t);else{var o=[],i=[];U[e]&&(o=Array.isArray(U[e])?U[e]:Object(d.stringToArray)(U[e])),t&&(i=Array.isArray(t)?t:Object(d.stringToArray)(t));var s=Object(d.getDifferenceOfArrays)(o,i),a=Object(d.getDifferenceOfArrays)(i,o);s.length&&Object(n.removeClass)(r,s),a.length&&Object(n.addClass)(r,a)}U[e]=t}function Z(){var e=!1;return{validatorsInQueue:0,valid:!0,addValidatorToQueue(){this.validatorsInQueue+=1,e=!1},removeValidatorFormQueue(){this.validatorsInQueue=this.validatorsInQueue-1<0?0:this.validatorsInQueue-1,this.checkIfQueueIsEmpty()},onQueueEmpty(){},checkIfQueueIsEmpty(){0===this.validatorsInQueue&&!1===e&&(e=!0,this.onQueueEmpty(this.valid))}}}function J(e){var t=e.replace(",",".");return!1===isNaN(parseFloat(t))?parseFloat(t):e}function $(e,t,r){if(e.length){var o=B.getActiveEditor(),i=B.runHooks("beforeChange",e,t||"edit"),s=!0;if(!1!==i){var a,l=new Z;l.onQueueEmpty=e=>{o&&s&&o.cancelChanges(),r(e)};for(var c=e.length-1;c>=0;c--)if(null===e[c])e.splice(c,1);else{var[h,d,,u]=e[c],g=I.propToCol(d),f=B.getCellMeta(h,g);"numeric"===f.type&&"string"==typeof u&&((a=u).length>0&&/^\s*[+-.]?\s*(?:(?:\d+(?:(\.|,)\d+)?(?:e[+-]?\d+)?)|(?:0x[a-f\d]+))\s*$/.test(a))&&(e[c][3]=J(u)),B.getCellValidator(f)&&(l.addValidatorToQueue(),B.validateCell(e[c][3],f,function(t,r){return function(o){if("boolean"!=typeof o)throw new Error("Validation error: result is not boolean");if(!1===o&&!1===r.allowInvalid){s=!1,e.splice(t,1),r.valid=!0;var i=B.getCell(r.visualRow,r.visualCol);null!==i&&Object(n.removeClass)(i,z.invalidCellClassName)}l.removeValidatorFormQueue()}}(c,f),t))}l.checkIfQueueIsEmpty()}else o&&o.cancelChanges()}}function ee(e,t){var r=e.length-1;if(!(r<0)){for(;r>=0;r--){var o=!1;if(null!==e[r]){if(null!==e[r][2]&&void 0!==e[r][2]||null!==e[r][3]&&void 0!==e[r][3]){if(z.allowInsertRow)for(;e[r][0]>B.countRows()-1;){var n=I.createRow(void 0,void 0,t);if(!(n>=1)){o=!0;break}W.createRow(null,n)}if("array"===B.dataType&&(!z.columns||0===z.columns.length)&&z.allowInsertColumn)for(;I.propToCol(e[r][1])>B.countCols()-1;){var i=I.createCol(void 0,void 0,t);if(!(i>=1)){o=!0;break}W.createColumn(null,i)}o||I.set(e[r][0],e[r][1],e[r][3])}}else e.splice(r,1)}B.forceFullRender=!0,D.adjustRowsAndCols(),B.runHooks("beforeChangeRender",e,t),_.lockEditor(),B._refreshBorders(null),_.unlockEditor(),B.view.wt.wtOverlays.adjustElementsSizes(),B.runHooks("afterChange",e,t||"edit");var a=B.getActiveEditor();a&&Object(s.isDefined)(a.refreshValue)&&a.refreshValue()}}function te(e,t,r){return"object"==typeof e?e:[[e,t,r]]}this.columnIndexMapper.addLocalHook("cacheUpdated",Y),this.rowIndexMapper.addLocalHook("cacheUpdated",Y),this.selection.addLocalHook("beforeSetRangeStart",e=>{this.runHooks("beforeSetRangeStart",e)}),this.selection.addLocalHook("beforeSetRangeStartOnly",e=>{this.runHooks("beforeSetRangeStartOnly",e)}),this.selection.addLocalHook("beforeSetRangeEnd",e=>{this.runHooks("beforeSetRangeEnd",e),e.row<0&&(e.row=this.view.wt.wtTable.getFirstVisibleRow()),e.col<0&&(e.col=this.view.wt.wtTable.getFirstVisibleColumn())}),this.selection.addLocalHook("afterSetRangeEnd",e=>{var t=Object(h.createObjectPropListener)(!1),r=this.selection.getSelectedRange(),{from:o,to:i}=r.current(),s=r.size()-1;this.runHooks("afterSelection",o.row,o.col,i.row,i.col,t,s),this.runHooks("afterSelectionByProp",o.row,B.colToProp(o.col),i.row,B.colToProp(i.col),t,s);var a=this.selection.isSelectedByAnyHeader(),l=this.selection.selectedRange.current(),c=!0;P&&(c=!1),t.isTouched()&&(c=!t.value);var d=this.selection.isSelectedByRowHeader(),u=this.selection.isSelectedByColumnHeader();!1!==c&&(a?d?this.view.scrollViewportVertically(B.rowIndexMapper.getRenderableFromVisualIndex(e.row)):u&&this.view.scrollViewportHorizontally(B.columnIndexMapper.getRenderableFromVisualIndex(e.col)):l&&!this.selection.isMultiple()?this.view.scrollViewport(G(l.from)):this.view.scrollViewport(G(e))),d&&u?Object(n.addClass)(this.rootElement,["ht__selection--rows","ht__selection--columns"]):d?(Object(n.removeClass)(this.rootElement,"ht__selection--columns"),Object(n.addClass)(this.rootElement,"ht__selection--rows")):u?(Object(n.removeClass)(this.rootElement,"ht__selection--rows"),Object(n.addClass)(this.rootElement,"ht__selection--columns")):Object(n.removeClass)(this.rootElement,["ht__selection--rows","ht__selection--columns"]),this._refreshBorders(null)}),this.selection.addLocalHook("afterSelectionFinished",e=>{var t=e.length-1,{from:r,to:o}=e[t];this.runHooks("afterSelectionEnd",r.row,r.col,o.row,o.col,t),this.runHooks("afterSelectionEndByProp",r.row,B.colToProp(r.col),o.row,B.colToProp(o.col),t)}),this.selection.addLocalHook("afterIsMultipleSelection",e=>{var t=this.runHooks("afterIsMultipleSelection",e.value);e.value&&(e.value=t)}),this.selection.addLocalHook("beforeModifyTransformStart",e=>{this.runHooks("modifyTransformStart",e)}),this.selection.addLocalHook("afterModifyTransformStart",(e,t,r)=>{this.runHooks("afterModifyTransformStart",e,t,r)}),this.selection.addLocalHook("beforeModifyTransformEnd",e=>{this.runHooks("modifyTransformEnd",e)}),this.selection.addLocalHook("afterModifyTransformEnd",(e,t,r)=>{this.runHooks("afterModifyTransformEnd",e,t,r)}),this.selection.addLocalHook("afterDeselect",()=>{_.destroyEditor(),this._refreshBorders(),Object(n.removeClass)(this.rootElement,["ht__selection--rows","ht__selection--columns"]),this.runHooks("afterDeselect")}),this.selection.addLocalHook("insertRowRequire",e=>{this.alter("insert_row",e,1,"auto")}),this.selection.addLocalHook("insertColRequire",e=>{this.alter("insert_col",e,1,"auto")}),D={alter(e,t){var r,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0,a=e=>{if(0===e.length)return[];var t=[...e];return t.sort((e,t)=>{var[r]=e,[o]=t;return r===o?0:r>o?1:-1}),Object(d.arrayReduce)(t,(e,t)=>{var[r,o]=t,n=e[e.length-1],[i,s]=n,a=i+s;if(r<=a){var l=Math.max(o-(a-r),0);n[1]+=l}else e.push([r,o]);return e},[t[0]])};switch(e){case"insert_row":var l=B.countSourceRows();if(z.maxRows===l)return;if(t=Object(s.isDefined)(t)?t:l,r=I.createRow(t,o,n)){W.createRow(B.toPhysicalRow(t),o);var c=K.selectedRange.current(),h=null==c?void 0:c.from,u=null==h?void 0:h.row;if(Object(s.isDefined)(u)&&u>=t){var{row:g,col:f}=c.to,m=h.col;K.isSelectedByRowHeader()&&(m=-1),K.setRangeStartOnly(new y.a(u+r,m)),K.setRangeEnd(new y.a(g+r,f))}else B._refreshBorders()}break;case"insert_col":if(r=I.createCol(t,o,n)){if(W.createColumn(B.toPhysicalColumn(t),o),Array.isArray(z.colHeaders)){var p=[t,0];p.length+=r,Array.prototype.splice.apply(z.colHeaders,p)}var v=K.selectedRange.current(),w=null==v?void 0:v.from,b=null==w?void 0:w.col;if(Object(s.isDefined)(b)&&b>=t){var{row:C,col:E}=v.to,O=w.row;K.isSelectedByColumnHeader()&&(O=-1),K.setRangeStartOnly(new y.a(O,b+r)),K.setRangeEnd(new y.a(C,E+r))}else B._refreshBorders()}break;case"remove_row":var R=e=>{var t=0;Object(d.arrayEach)(e,e=>{var[r,o]=e,i=Object(s.isEmpty)(r)?B.countRows()-1:Math.max(r-t,0);if(Number.isInteger(r)&&(r=Math.max(r-t,0)),I.removeRow(r,o,n)){W.removeRow(B.toPhysicalRow(i),o);var a=B.countRows(),l=z.fixedRowsTop;l>=i+1&&(z.fixedRowsTop-=Math.min(o,l-i));var c=z.fixedRowsBottom;c&&i>=a-c&&(z.fixedRowsBottom-=Math.min(o,c)),t+=o}})};Array.isArray(t)?R(a(t)):R([[t,o]]),D.adjustRowsAndCols(),B._refreshBorders();break;case"remove_col":var T=e=>{var t=0;Object(d.arrayEach)(e,e=>{var[r,o]=e,i=Object(s.isEmpty)(r)?B.countCols()-1:Math.max(r-t,0),a=B.toPhysicalColumn(i);if(Number.isInteger(r)&&(r=Math.max(r-t,0)),I.removeCol(r,o,n)){W.removeColumn(a,o);var l=z.fixedColumnsLeft;l>=i+1&&(z.fixedColumnsLeft-=Math.min(o,l-i)),Array.isArray(z.colHeaders)&&(void 0===a&&(a=-1),z.colHeaders.splice(a,o)),t+=o}})};Array.isArray(t)?T(a(t)):T([[t,o]]),D.adjustRowsAndCols(),B._refreshBorders();break;default:throw new Error('There is no such action "'.concat(e,'"'))}i||D.adjustRowsAndCols()},adjustRowsAndCols(){if(z.minRows){var e=B.countRows();if(e<z.minRows)for(var t=0,r=z.minRows;t<r-e;t++)I.createRow(B.countRows(),1,"auto")}if(z.minSpareRows){var o=B.countEmptyRows(!0);if(o<z.minSpareRows)for(;o<z.minSpareRows&&B.countSourceRows()<z.maxRows;o++)I.createRow(B.countRows(),1,"auto")}var n;if((z.minCols||z.minSpareCols)&&(n=B.countEmptyCols(!0)),z.minCols&&!z.columns&&B.countCols()<z.minCols)for(;B.countCols()<z.minCols;n++)I.createCol(B.countCols(),1,"auto");if(z.minSpareCols&&!z.columns&&"array"===B.dataType&&n<z.minSpareCols)for(;n<z.minSpareCols&&B.countCols()<z.maxCols;n++)I.createCol(B.countCols(),1,"auto");var i=B.countRows(),s=B.countCols();0!==i&&0!==s||K.deselect(),K.isSelected()&&Object(d.arrayEach)(K.selectedRange,e=>{var t=!1,r=e.from.row,o=e.from.col,n=e.to.row,a=e.to.col;r>i-1?(t=!0,n>(r=i-1)&&(n=r)):n>i-1&&(t=!0,r>(n=i-1)&&(r=n)),o>s-1?(t=!0,a>(o=s-1)&&(a=o)):a>s-1&&(t=!0,o>(a=s-1)&&(o=a)),t&&B.selectCell(r,o,n,a)}),B.view&&B.view.wt.wtOverlays.adjustElementsSizes()},populateFromArray(e,t,r,o,n,i,a){var l,c,d,u,g,f,m,p,v=[],w={};if(0===(c=t.length))return!1;switch(n){case"shift_down":for(g=r?r.col-e.col+1:0,f=r?r.row-e.row+1:0,d=0,u=(t=Object(C.translateRowsToColumns)(t)).length,m=Math.max(u,g);d<m;d++)if(d<u){for(l=0,c=t[d].length;l<f-c;l++)t[d].push(t[d][l%c]);t[d].unshift(e.col+d,e.row,0),B.spliceCol(...t[d])}else t[d%u][0]=e.col+d,B.spliceCol(...t[d%u]);break;case"shift_right":for(g=r?r.col-e.col+1:0,f=r?r.row-e.row+1:0,l=0,c=t.length,p=Math.max(c,f);l<p;l++)if(l<c){for(d=0,u=t[l].length;d<g-u;d++)t[l].push(t[l][d%u]);t[l].unshift(e.row+l,e.col,0),B.spliceRow(...t[l])}else t[l%c][0]=e.row+l,B.spliceRow(...t[l%c]);break;case"overwrite":default:w.row=e.row,w.col=e.col;var b,E={row:r&&e?r.row-e.row+1:1,col:r&&e?r.col-e.col+1:1},O=0,y=0,R=!0,T=function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,o=t[e%t.length];return null!==r?o[r%o.length]:o},S=t.length,M=r?r.row-e.row+1:0;for(c=r?M:Math.max(S,M),l=0;l<c&&!(r&&w.row>r.row&&M>S||!z.allowInsertRow&&w.row>B.countRows()-1||w.row>=z.maxRows);l++){var N=l-O,H=T(N).length,L=r?r.col-e.col+1:0;if(u=r?L:Math.max(H,L),w.col=e.col,b=B.getCellMeta(w.row,w.col),"CopyPaste.paste"!==o&&"Autofill.fill"!==o||!b.skipRowOnPaste){for(y=0,d=0;d<u&&!(r&&w.col>r.col&&L>H||!z.allowInsertColumn&&w.col>B.countCols()-1||w.col>=z.maxCols);d++)if(b=B.getCellMeta(w.row,w.col),"CopyPaste.paste"!==o&&"Autofill.fill"!==o||!b.skipColumnOnPaste)if(b.readOnly)w.col+=1;else{var x=d-y,A=T(N,x),I=B.getDataAtCell(w.row,w.col),j={row:N,col:x};if("Autofill.fill"===o){var D=B.runHooks("beforeAutofillInsidePopulate",j,i,t,a,{},E);D&&(A=Object(s.isUndefined)(D.value)?A:D.value)}if(null!==A&&"object"==typeof A)if(Array.isArray(A)&&null===I&&(I=[]),null===I||"object"!=typeof I)R=!1;else{var _=Object(h.duckSchema)(Array.isArray(I)?I:I[0]||I),k=Object(h.duckSchema)(Array.isArray(A)?A:A[0]||A);Object(h.isObjectEqual)(_,k)?A=Object(h.deepClone)(A):R=!1}else null!==I&&"object"==typeof I&&(R=!1);R&&v.push([w.row,w.col,A]),R=!0,w.col+=1}else y+=1,w.col+=1,u+=1;w.row+=1}else O+=1,w.row+=1,c+=1}B.setDataAtCell(v,null,null,o||"populateFromArray")}}},this.executeBatchOperations=function(e){this.columnIndexMapper.executeBatchOperations(()=>{this.rowIndexMapper.executeBatchOperations(()=>{e()})})},this.init=function(){j.setData(z.data),B.runHooks("beforeInit"),Object(a.isMobileBrowser)()&&Object(n.addClass)(B.rootElement,"mobile"),this.updateSettings(z,!0),this.view=new w.a(this),_=l.a.getInstance(B,z,K),B.runHooks("init"),this.forceFullRender=!0,this.view.render(),"object"==typeof V&&(B.runHooks("afterChange",V[0],V[1]),V=!1),B.runHooks("afterInit")},this.validateCell=function(e,t,r,o){var n,a=B.getCellValidator(t);function l(e){var o=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(o&&!0!==t.hidden){var n=t.visualCol,i=t.visualRow,s=B.getCell(i,n,!0);s&&"TH"!==s.nodeName&&B.view.wt.wtSettings.settings.cellRenderer(i,n,s),r(e)}else r(e)}Object(s.isRegExp)(a)&&(n=a,a=function(e,t){t(n.test(e))}),Object(i.isFunction)(a)?(e=B.runHooks("beforeValidate",e,t.visualRow,t.prop,o),B._registerImmediate(()=>{a.call(t,e,r=>{B&&(r=B.runHooks("afterValidate",r,e,t.visualRow,t.prop,o),t.valid=r,l(r),B.runHooks("postAfterValidate",r,e,t.visualRow,t.prop,o))})})):B._registerImmediate(()=>{t.valid=!0,l(t.valid,!1)})},this.setDataAtCell=function(e,t,r,o){var n,i,s,a=te(e,t,r),l=[],c=o;for(n=0,i=a.length;n<i;n++){if("object"!=typeof a[n])throw new Error("Method `setDataAtCell` accepts row number or changes array of arrays as its first parameter");if("number"!=typeof a[n][1])throw new Error("Method `setDataAtCell` accepts row and column number as its parameters. If you want to use object property name, use method `setDataAtRowProp`");s=a[n][1]>=this.countCols()?a[n][1]:I.colToProp(a[n][1]),l.push([a[n][0],s,j.getAtCell(this.toPhysicalRow(a[n][0]),a[n][1]),a[n][2]])}c||"object"!=typeof e||(c=t),B.runHooks("afterSetDataAtCell",l,c),$(l,c,()=>{ee(l,c)})},this.setDataAtRowProp=function(e,t,r,o){var n,i,s=te(e,t,r),a=[],l=o;for(n=0,i=s.length;n<i;n++)a.push([s[n][0],s[n][1],j.getAtCell(this.toPhysicalRow(s[n][0]),s[n][1]),s[n][2]]);l||"object"!=typeof e||(l=t),B.runHooks("afterSetDataAtRowProp",a,l),$(a,l,()=>{ee(a,l)})},this.listen=function(){B&&!B.isListening()&&(A=B.guid,B.runHooks("afterListen"))},this.unlisten=function(){this.isListening()&&(A=null,B.runHooks("afterUnlisten"))},this.isListening=function(){return A===B.guid},this.destroyEditor=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];B._refreshBorders(e,t)},this.populateFromArray=function(e,t,r,o,n,i,s,a,l){if("object"!=typeof r||"object"!=typeof r[0])throw new Error("populateFromArray parameter `input` must be an array of arrays");var c="number"==typeof o?new y.a(o,n):null;return D.populateFromArray(new y.a(e,t),r,c,i,s,a,l)},this.spliceCol=function(e,t,r){for(var o=arguments.length,n=new Array(o>3?o-3:0),i=3;i<o;i++)n[i-3]=arguments[i];return I.spliceCol(e,t,r,...n)},this.spliceRow=function(e,t,r){for(var o=arguments.length,n=new Array(o>3?o-3:0),i=3;i<o;i++)n[i-3]=arguments[i];return I.spliceRow(e,t,r,...n)},this.getSelected=function(){if(K.isSelected())return Object(d.arrayMap)(K.getSelectedRange(),e=>{var{from:t,to:r}=e;return[t.row,t.col,r.row,r.col]})},this.getSelectedLast=function(){var e,t=this.getSelected();return t&&t.length>0&&(e=t[t.length-1]),e},this.getSelectedRange=function(){if(K.isSelected())return Array.from(K.getSelectedRange())},this.getSelectedRangeLast=function(){var e,t=this.getSelectedRange();return t&&t.length>0&&(e=t[t.length-1]),e},this.emptySelectedCells=function(e){if(K.isSelected()){var t=[];Object(d.arrayEach)(K.getSelectedRange(),e=>{var r=e.getTopLeftCorner(),o=e.getBottomRightCorner();Object(v.rangeEach)(r.row,o.row,e=>{Object(v.rangeEach)(r.col,o.col,r=>{this.getCellMeta(e,r).readOnly||t.push([e,r,null])})})}),t.length>0&&this.setDataAtCell(t,e)}},this.render=function(){B.view&&(B.renderCall=!0,B.forceFullRender=!0,_.lockEditor(),B._refreshBorders(null),_.unlockEditor())},this.refreshDimensions=function(){if(B.view){var{width:e,height:t}=B.view.getLastSize(),{width:r,height:o}=Object(n.getBoundingClientRect)(B.rootElement),i=r!==e||o!==t;!1===B.runHooks("beforeRefreshDimensions",{width:e,height:t},{width:r,height:o},i)||((i||B.view.wt.wtOverlays.scrollableElement===B.rootWindow)&&(B.view.setLastSize(r,o),B.render()),B.runHooks("afterRefreshDimensions",{width:e,height:t},{width:r,height:o},i))}},this.loadData=function(e){if(Array.isArray(z.dataSchema)?B.dataType="array":Object(i.isFunction)(z.dataSchema)?B.dataType="function":B.dataType="object",I&&I.destroy(),I=new L.a(B,e,z),"object"==typeof e&&null!==e)e.push&&e.splice||(e=[e]);else{if(null!==e)throw new Error("loadData only accepts array of objects or array of arrays (".concat(typeof e," given)"));var t,r=I.getSchema();e=[];var o,n=0;for(n=0,o=z.startRows;n<o;n++)if("object"!==B.dataType&&"function"!==B.dataType||!z.dataSchema)if("array"===B.dataType)t=Object(h.deepClone)(r[0]),e.push(t);else{t=[];for(var s=0,a=z.startCols;s<a;s++)t.push(null);e.push(t)}else t=Object(h.deepClone)(r),e.push(t)}Array.isArray(e[0])&&(B.dataType="array"),z.data=e,B.runHooks("beforeLoadData",e,V),I.dataSource=e,j.data=e,j.dataType=B.dataType,j.colToProp=I.colToProp.bind(I),j.propToCol=I.propToCol.bind(I),j.countCachedColumns=I.countCachedColumns.bind(I),W.clearCellsCache(),B.initIndexMappers(),D.adjustRowsAndCols(),B.runHooks("afterLoadData",e,V),V?V=[null,"loadData"]:(B.runHooks("afterChange",null,"loadData"),B.render())},this.initIndexMappers=function(){var e=z.columns,t=0;if(Array.isArray(e))t=e.length;else if(Object(i.isFunction)(e))if("array"===B.dataType)for(var r=this.countSourceCols(),o=0;o<r;o+=1)e(o)&&(t+=1);else"object"!==B.dataType&&"function"!==B.dataType||(t=I.colToPropCache.length);else if(Object(s.isDefined)(z.dataSchema)){var n=I.getSchema();t=Array.isArray(n)?n.length:Object(h.deepObjectSize)(n)}else t=this.countSourceCols();this.columnIndexMapper.initToLength(t),this.rowIndexMapper.initToLength(this.countSourceRows())},this.getData=function(e,t,r,o){return Object(s.isUndefined)(e)?I.getAll():I.getRange(new y.a(e,t),new y.a(r,o),I.DESTINATION_RENDERER)},this.getCopyableText=function(e,t,r,o){return I.getCopyableText(new y.a(e,t),new y.a(r,o))},this.getCopyableData=function(e,t){return I.getCopyable(e,I.colToProp(t))},this.getSchema=function(){return I.getSchema()},this.updateSettings=function(e){var t,r,o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!1;if(Object(s.isDefined)(e.rows))throw new Error('"rows" setting is no longer supported. do you mean startRows, minRows or maxRows?');if(Object(s.isDefined)(e.cols))throw new Error('"cols" setting is no longer supported. do you mean startCols, minCols or maxCols?');for(t in e)"data"!==t&&("language"!==t?"className"===t?Q("className",e.className):"tableClassName"===t&&B.table?(Q("tableClassName",e.tableClassName),B.view.wt.wtOverlays.syncOverlayTableClassNames()):R.a.getSingleton().isRegistered(t)||R.a.getSingleton().isDeprecated(t)?(Object(i.isFunction)(e[t])||Array.isArray(e[t]))&&(e[t].initialHook=!0,B.addHook(t,e[t])):!o&&Object(h.hasOwnProperty)(e,t)&&(U[t]=e[t]):q(e.language));void 0===e.data&&void 0===z.data?B.loadData(null):void 0!==e.data?B.loadData(e.data):void 0!==e.columns&&(I.createMap(),B.initIndexMappers());var a=B.countCols(),l=z.columns;if(l&&Object(i.isFunction)(l)&&(n=!0),void 0===e.cell&&void 0===e.cells&&void 0===e.columns||W.clearCache(),a>0)for(t=0,r=0;t<a;t++){if(l){var c=n?l(t):l[r];c&&W.updateColumnMeta(r,c)}r+=1}Object(s.isDefined)(e.cell)&&Object(h.objectEach)(e.cell,e=>{B.setCellMetaObject(e.row,e.col,e)}),B.runHooks("afterCellMetaReset");var d=B.rootElement.style.height;""!==d&&(d=parseInt(B.rootElement.style.height,10));var u=e.height;if(Object(i.isFunction)(u)&&(u=u()),o){var g=B.rootElement.getAttribute("style");g&&B.rootElement.setAttribute("data-initialstyle",B.rootElement.getAttribute("style"))}if(null===u){var f=B.rootElement.getAttribute("data-initialstyle");f&&(f.indexOf("height")>-1||f.indexOf("overflow")>-1)?B.rootElement.setAttribute("style",f):(B.rootElement.style.height="",B.rootElement.style.overflow="")}else void 0!==u&&(B.rootElement.style.height=isNaN(u)?"".concat(u):"".concat(u,"px"),B.rootElement.style.overflow="hidden");if(void 0!==e.width){var m=e.width;Object(i.isFunction)(m)&&(m=m()),B.rootElement.style.width=isNaN(m)?"".concat(m):"".concat(m,"px")}if(void 0!==e.selectionStyle){if(!Object(h.isObject)(e.selectionStyle))throw new Error("The value of the option `selectionStyle` must be an object. To reset `selectionStyle` to the defaults, provide an empty object as the value.");this.selection.updateBorderStyle(e.selectionStyle)}o||(B.view&&B.view.wt.wtViewport.resetHasOversizedColumnHeadersMarked(),B.runHooks("afterUpdateSettings",e)),D.adjustRowsAndCols(),B.view&&!V&&(B.forceFullRender=!0,_.lockEditor(),B._refreshBorders(null),_.unlockEditor()),o||!B.view||""!==d&&""!==u&&void 0!==u||d===u||B.view.wt.wtOverlays.updateMainScrollableElements()},this.getValue=function(){var e=B.getSelectedLast();if(z.getValue){if(Object(i.isFunction)(z.getValue))return z.getValue.call(B);if(e)return B.getData()[e[0][0]][z.getValue]}else if(e)return B.getDataAtCell(e[0],e[1])},this.getSettings=function(){return z},this.clear=function(){this.selectAll(),this.emptySelectedCells()},this.alter=function(e,t,r,o,n){D.alter(e,t,r,o,n)},this.getCell=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=t,n=e;if(t>=0){if(this.columnIndexMapper.isHidden(this.toPhysicalColumn(t)))return null;o=this.columnIndexMapper.getRenderableFromVisualIndex(t)}if(e>=0){if(this.rowIndexMapper.isHidden(this.toPhysicalRow(e)))return null;n=this.rowIndexMapper.getRenderableFromVisualIndex(e)}return null===n||null===o?null:B.view.getCellAtCoords(new y.a(n,o),r)},this.getCoords=function(e){var t=this.view.wt.wtTable.getCoords(e);if(null===t)return null;var{row:r,col:o}=t,n=r,i=o;return r>=0&&(n=this.rowIndexMapper.getVisualFromRenderableIndex(r)),o>=0&&(i=this.columnIndexMapper.getVisualFromRenderableIndex(o)),new y.a(n,i)},this.colToProp=function(e){return I.colToProp(e)},this.propToCol=function(e){return I.propToCol(e)},this.toVisualRow=e=>this.rowIndexMapper.getVisualFromPhysicalIndex(e),this.toVisualColumn=e=>this.columnIndexMapper.getVisualFromPhysicalIndex(e),this.toPhysicalRow=e=>this.rowIndexMapper.getPhysicalFromVisualIndex(e),this.toPhysicalColumn=e=>this.columnIndexMapper.getPhysicalFromVisualIndex(e),this.getDataAtCell=function(e,t){return I.get(e,I.colToProp(t))},this.getDataAtRowProp=function(e,t){return I.get(e,t)},this.getDataAtCol=function(e){return[].concat(...I.getRange(new y.a(0,e),new y.a(z.data.length-1,e),I.DESTINATION_RENDERER))},this.getDataAtProp=function(e){var t=I.getRange(new y.a(0,I.propToCol(e)),new y.a(z.data.length-1,I.propToCol(e)),I.DESTINATION_RENDERER);return[].concat(...t)},this.getSourceData=function(e,t,r,o){return void 0===e?j.getData():j.getByRange(new y.a(e,t),new y.a(r,o))},this.getSourceDataArray=function(e,t,r,o){return void 0===e?j.getData(!0):j.getByRange(new y.a(e,t),new y.a(r,o),!0)},this.getSourceDataAtCol=function(e){return j.getAtColumn(e)},this.setSourceDataAtCell=function(e,t,r,o){var n=te(e,t,r),i=[];n.forEach((e,t)=>{var[r,o,s]=e;i.push([n[t][0],n[t][1],j.getAtCell(n[t][0],n[t][1]),n[t][2]]),j.setAtCell(r,o,s)}),this.runHooks("afterSetSourceDataAtCell",i,o),this.render()},this.getSourceDataAtRow=function(e){return j.getAtRow(e)},this.getSourceDataAtCell=function(e,t){return j.getAtCell(e,t)},this.getDataAtRow=function(e){return I.getRange(new y.a(e,0),new y.a(e,this.countCols()-1),I.DESTINATION_RENDERER)[0]||[]},this.getDataType=function(e,t,r,o){var n=void 0===e?[0,0,this.countRows(),this.countCols()]:[e,t,r,o],[i,s]=n,[,,a,l]=n,c=null,h=null;void 0===a&&(a=i),void 0===l&&(l=s);var d="mixed";return Object(v.rangeEach)(Math.max(Math.min(i,a),0),Math.max(i,a),e=>{var t=!0;return Object(v.rangeEach)(Math.max(Math.min(s,l),0),Math.max(s,l),r=>{var o=this.getCellMeta(e,r);return h=o.type,c?t=c===h:c=h,t}),d=t?h:"mixed",t}),d},this.removeCellMeta=function(e,t,r){var[o,n]=[this.toPhysicalRow(e),this.toPhysicalColumn(t)],i=W.getCellMeta(o,n,r);!1!==B.runHooks("beforeRemoveCellMeta",e,t,r,i)&&(W.removeCellMeta(o,n,r),B.runHooks("afterRemoveCellMeta",e,t,r,i)),i=null},this.spliceCellsMeta=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length,o=new Array(r>2?r-2:0),n=2;n<r;n++)o[n-2]=arguments[n];if(o.length>0&&!Array.isArray(o[0]))throw new Error("The 3rd argument (cellMetaRows) has to be passed as an array of cell meta objects array.");t>0&&W.removeRow(this.toPhysicalRow(e),t),o.length>0&&Object(d.arrayEach)(o.reverse(),t=>{W.createRow(this.toPhysicalRow(e)),Object(d.arrayEach)(t,(t,r)=>this.setCellMetaObject(e,r,t))})},this.setCellMetaObject=function(e,t,r){"object"==typeof r&&Object(h.objectEach)(r,(r,o)=>{this.setCellMeta(e,t,o,r)})},this.setCellMeta=function(e,t,r,o){if(!1!==B.runHooks("beforeSetCellMeta",e,t,r,o)){var n=e,i=t;e<this.countRows()&&(n=this.toPhysicalRow(e)),t<this.countCols()&&(i=this.toPhysicalColumn(t)),W.setCellMeta(n,i,r,o),B.runHooks("afterSetCellMeta",e,t,r,o)}},this.getCellsMeta=function(){return W.getCellsMeta()},this.getCellMeta=function(e,t){var r=this.toPhysicalRow(e),o=this.toPhysicalColumn(t);null===r&&(r=e),null===o&&(o=t);var n=I.colToProp(t),i=W.getCellMeta(r,o);if(i.row=r,i.col=o,i.visualRow=e,i.visualCol=t,i.prop=n,i.instance=B,B.runHooks("beforeGetCellMeta",e,t,i),B.hasHook("beforeGetCellMeta")&&Object(h.hasOwnProperty)(i,"type")&&W.updateCellMeta(r,o,{type:i.type}),i.cells){var s=i.cells(r,o,n);s&&W.updateCellMeta(r,o,s)}return B.runHooks("afterGetCellMeta",e,t,i),i},this.getCellMetaAtRow=function(e){return W.getCellsMetaAtRow(e)},this.isColumnModificationAllowed=function(){return!("object"===B.dataType||z.columns)};var re=Object(C.cellMethodLookupFactory)("renderer");this.getCellRenderer=function(e,t){return Object(f.b)(re.call(this,e,t))},this.getCellEditor=Object(C.cellMethodLookupFactory)("editor");var oe=Object(C.cellMethodLookupFactory)("validator");this.getCellValidator=function(e,t){var r=oe.call(this,e,t);return"string"==typeof r&&(r=Object(m.b)(r)),r},this.validateCells=function(e){this._validateCells(e)},this.validateRows=function(e,t){if(!Array.isArray(e))throw new Error("validateRows parameter `rows` must be an array");this._validateCells(t,e)},this.validateColumns=function(e,t){if(!Array.isArray(e))throw new Error("validateColumns parameter `columns` must be an array");this._validateCells(t,void 0,e)},this._validateCells=function(e,t,r){var o=new Z;e&&(o.onQueueEmpty=e);for(var n=B.countRows()-1;n>=0;)if(void 0===t||-1!==t.indexOf(n)){for(var i=B.countCols()-1;i>=0;)void 0===r||-1!==r.indexOf(i)?(o.addValidatorToQueue(),B.validateCell(B.getDataAtCell(n,i),B.getCellMeta(n,i),e=>{if("boolean"!=typeof e)throw new Error("Validation error: result is not boolean");!1===e&&(o.valid=!1),o.removeValidatorFormQueue()},"validateCells"),i-=1):i-=1;n-=1}else n-=1;o.checkIfQueueIsEmpty()},this.getRowHeader=function(e){var t=z.rowHeaders,r=e;return void 0!==r&&(r=B.runHooks("modifyRowHeader",r)),void 0===r?(t=[],Object(v.rangeEach)(B.countRows()-1,e=>{t.push(B.getRowHeader(e))})):Array.isArray(t)&&void 0!==t[r]?t=t[r]:Object(i.isFunction)(t)?t=t(r):t&&"string"!=typeof t&&"number"!=typeof t&&(t=r+1),t},this.hasRowHeaders=function(){return!!z.rowHeaders},this.hasColHeaders=function(){if(void 0!==z.colHeaders&&null!==z.colHeaders)return!!z.colHeaders;for(var e=0,t=B.countCols();e<t;e++)if(B.getColHeader(e))return!0;return!1},this.getColHeader=function(e){var t=B.runHooks("modifyColHeader",e),r=z.colHeaders;if(void 0===t){for(var o=[],n=B.countCols(),s=0;s<n;s++)o.push(B.getColHeader(s));r=o}else{var a=B.toPhysicalColumn(t),l=function(e){for(var t=[],r=B.countCols(),o=0;o<r;o++)Object(i.isFunction)(z.columns)&&z.columns(o)&&t.push(o);return t[e]}(a);!1===z.colHeaders?r=null:z.columns&&Object(i.isFunction)(z.columns)&&z.columns(l)&&z.columns(l).title?r=z.columns(l).title:z.columns&&z.columns[a]&&z.columns[a].title?r=z.columns[a].title:Array.isArray(z.colHeaders)&&void 0!==z.colHeaders[a]?r=z.colHeaders[a]:Object(i.isFunction)(z.colHeaders)?r=z.colHeaders(a):z.colHeaders&&"string"!=typeof z.colHeaders&&"number"!=typeof z.colHeaders&&(r=Object(C.spreadsheetColumnLabel)(t))}return r},this._getColWidthFromSettings=function(e){var t;e>=0&&(t=B.getCellMeta(0,e).width);if(void 0!==t&&t!==z.width||(t=z.colWidths),null!=t){switch(typeof t){case"object":t=t[e];break;case"function":t=t(e)}"string"==typeof t&&(t=parseInt(t,10))}return t},this.getColWidth=function(e){var t=B._getColWidthFromSettings(e);return void 0===(t=B.runHooks("modifyColWidth",t,e))&&(t=y.d.DEFAULT_WIDTH),t},this._getRowHeightFromSettings=function(e){var t=z.rowHeights;if(null!=t){switch(typeof t){case"object":t=t[e];break;case"function":t=t(e)}"string"==typeof t&&(t=parseInt(t,10))}return t},this.getRowHeight=function(e){var t=B._getRowHeightFromSettings(e);return t=B.runHooks("modifyRowHeight",t,e)},this.countSourceRows=function(){return j.countRows()},this.countSourceCols=function(){return j.countFirstRowKeys()},this.countRows=function(){return I.getLength()},this.countCols=function(){var e=z.maxCols,t=this.columnIndexMapper.getNotTrimmedIndexesLength();return Math.min(e,t)},this.countRenderedRows=function(){return B.view.wt.drawn?B.view.wt.wtTable.getRenderedRowsCount():-1},this.countVisibleRows=function(){return B.view.wt.drawn?B.view.wt.wtTable.getVisibleRowsCount():-1},this.countRenderedCols=function(){return B.view.wt.drawn?B.view.wt.wtTable.getRenderedColumnsCount():-1},this.countVisibleCols=function(){return B.view.wt.drawn?B.view.wt.wtTable.getVisibleColumnsCount():-1},this.countEmptyRows=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=0;return Object(v.rangeEachReverse)(B.countRows()-1,r=>{if(B.isEmptyRow(r))t+=1;else if(!0===e)return!1}),t},this.countEmptyCols=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(B.countRows()<1)return 0;var t=0;return Object(v.rangeEachReverse)(B.countCols()-1,r=>{if(B.isEmptyCol(r))t+=1;else if(!0===e)return!1}),t},this.isEmptyRow=function(e){return z.isEmptyRow.call(B,e)},this.isEmptyCol=function(e){return z.isEmptyCol.call(B,e)},this.selectCell=function(e,t,r,o){var n=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],i=!(arguments.length>5&&void 0!==arguments[5])||arguments[5];return!Object(s.isUndefined)(e)&&!Object(s.isUndefined)(t)&&this.selectCells([[e,t,r,o]],n,i)},this.selectCells=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[[]],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];!1===t&&(P=!0);var o=K.selectCells(e);return o&&r&&B.listen(),P=!1,o},this.selectColumns=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e;return K.selectColumns(e,t)},this.selectRows=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e;return K.selectRows(e,t)},this.deselectCell=function(){K.deselect()},this.selectAll=function(){P=!0,K.selectAll(),P=!1};var ne=(e,t)=>e.getFirstNotHiddenIndex(t,1,!0);function ie(e){return()=>{throw new Error('The "'.concat(e,'" method cannot be called because this Handsontable instance has been destroyed'))}}this.scrollViewportTo=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3&&void 0!==arguments[3]&&arguments[3],n=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],i=!r,s=!o,a=e,l=t;if(n){var c=Number.isInteger(e),h=Number.isInteger(t),d=c?ne(this.rowIndexMapper,e):void 0,u=h?ne(this.columnIndexMapper,t):void 0;if(null===d||null===u)return!1;a=c?B.rowIndexMapper.getRenderableFromVisualIndex(d):void 0,l=h?B.columnIndexMapper.getRenderableFromVisualIndex(u):void 0}var g=Number.isInteger(a),f=Number.isInteger(l);return g&&f?B.view.scrollViewport(new y.a(a,l),i,o,r,s):g&&!1===f?B.view.scrollViewportVertically(a,i,r):!(!f||!1!==g)&&B.view.scrollViewportHorizontally(l,o,s)},this.destroy=function(){if(B._clearTimeouts(),B._clearImmediates(),B.view&&B.view.destroy(),j&&j.destroy(),j=null,W.clearCache(),Object(N.c)(),Object(O.b)(B)){var e=this.rootDocument.querySelector("#hot-display-license-info");e&&e.parentNode.removeChild(e)}Object(n.empty)(B.rootElement),F.destroy(),_&&_.destroy(),B.executeBatchOperations(()=>{B.runHooks("afterDestroy")}),R.a.getSingleton().destroy(B),Object(h.objectEach)(B,(e,t,r)=>{Object(i.isFunction)(e)?r[t]=ie(t):"guid"!==t&&(r[t]=null)}),B.isDestroyed=!0,I&&I.destroy(),B.rowIndexMapper=null,B.columnIndexMapper=null,I=null,D=null,K=null,_=null,B=null},this.getActiveEditor=function(){return _.getActiveEditor()},this.getPlugin=function(e){return Object(g.a)(this,e)},this.getInstance=function(){return B},this.addHook=function(e,t){R.a.getSingleton().add(e,t,B)},this.hasHook=function(e){return R.a.getSingleton().has(e,B)},this.addHookOnce=function(e,t){R.a.getSingleton().once(e,t,B)},this.removeHook=function(e,t){R.a.getSingleton().remove(e,t,B)},this.runHooks=function(e,t,r,o,n,i,s){return R.a.getSingleton().run(B,e,t,r,o,n,i,s)},this.getTranslatedPhrase=function(e,t){return Object(T.a)(z.language,e,t)},this.toHTML=()=>Object(u.instanceToHTML)(this),this.toTableElement=()=>{var e=this.rootDocument.createElement("div");return e.insertAdjacentHTML("afterbegin",Object(u.instanceToHTML)(this)),e.firstElementChild},this.timeouts=[],this._registerTimeout=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=e;"function"==typeof r&&(r=setTimeout(r,t)),this.timeouts.push(r)},this._clearTimeouts=function(){Object(d.arrayEach)(this.timeouts,e=>{clearTimeout(e)})},this.immediates=[],this._registerImmediate=function(t){this.immediates.push(e(t))},this._clearImmediates=function(){Object(d.arrayEach)(this.immediates,e=>{o(e)})},this._refreshBorders=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];_.destroyEditor(e),B.view.render(),t&&K.isSelected()&&_.prepareEditor()},R.a.getSingleton().run(B,"construct")}}).call(this,r(116).setImmediate,r(116).clearImmediate)},,function(e,t,r){"use strict";var o=r(34),n=r(43),i=r(57),s=r(38),a=r(91),l=new WeakMap;t.a=class{constructor(e){this.instance=e,this.eventManager=new n.a(e),this.settings=e.getSettings(),this.THEAD=void 0,this.TBODY=void 0,this.wt=void 0,this.activeWt=void 0,l.set(this,{selectionMouseDown:!1,mouseDown:void 0,table:void 0,lastWidth:0,lastHeight:0}),this.createElements(),this.registerEvents(),this.initializeWalkontable()}render(){this.wt.draw(!this.instance.forceFullRender),this.instance.forceFullRender=!1,this.instance.renderCall=!1}getCellAtCoords(e,t){var r=this.wt.getCell(e,t);return r<0?null:r}scrollViewport(e,t,r,o,n){return this.wt.scrollViewport(e,t,r,o,n)}scrollViewportHorizontally(e,t,r){return this.wt.scrollViewportHorizontally(e,t,r)}scrollViewportVertically(e,t,r){return this.wt.scrollViewportVertically(e,t,r)}createElements(){var e=l.get(this),{rootElement:t,rootDocument:r}=this.instance,n=t.getAttribute("style");n&&t.setAttribute("data-originalstyle",n),Object(o.addClass)(t,"handsontable"),e.table=r.createElement("TABLE"),Object(o.addClass)(e.table,"htCore"),this.instance.getSettings().tableClassName&&Object(o.addClass)(e.table,this.instance.getSettings().tableClassName),this.THEAD=r.createElement("THEAD"),e.table.appendChild(this.THEAD),this.TBODY=r.createElement("TBODY"),e.table.appendChild(this.TBODY),this.instance.table=e.table,this.instance.container.insertBefore(e.table,this.instance.container.firstChild)}registerEvents(){var e=l.get(this),{rootElement:t,rootDocument:r}=this.instance,n=r.documentElement;this.eventManager.addEventListener(t,"mousedown",t=>{if(e.selectionMouseDown=!0,!this.isTextSelectionAllowed(t.target)){var{rootWindow:r}=this.instance;Object(o.clearTextSelection)(r),t.preventDefault(),r.focus()}}),this.eventManager.addEventListener(t,"mouseup",()=>{e.selectionMouseDown=!1}),this.eventManager.addEventListener(t,"mousemove",t=>{e.selectionMouseDown&&!this.isTextSelectionAllowed(t.target)&&(this.settings.fragmentSelection&&Object(o.clearTextSelection)(this.instance.rootWindow),t.preventDefault())}),this.eventManager.addEventListener(n,"keyup",e=>{this.instance.selection.isInProgress()&&!e.shiftKey&&this.instance.selection.finish()}),this.eventManager.addEventListener(n,"mouseup",t=>{this.instance.selection.isInProgress()&&Object(i.isLeftClick)(t)&&this.instance.selection.finish(),e.mouseDown=!1,(Object(o.isOutsideInput)(r.activeElement)||!this.instance.selection.isSelected()&&!Object(i.isRightClick)(t))&&this.instance.unlisten()}),this.eventManager.addEventListener(n,"contextmenu",t=>{this.instance.selection.isInProgress()&&Object(i.isRightClick)(t)&&(this.instance.selection.finish(),e.mouseDown=!1)}),this.eventManager.addEventListener(n,"touchend",()=>{this.instance.selection.isInProgress()&&this.instance.selection.finish(),e.mouseDown=!1}),this.eventManager.addEventListener(n,"mousedown",i=>{var s=i.target,a=i.x||i.clientX,l=i.y||i.clientY,c=i.target;if(!e.mouseDown&&t&&this.instance.view){var{holder:h}=this.instance.view.wt.wtTable;if(c===h){var d=Object(o.getScrollbarWidth)(r);if(r.elementFromPoint(a+d,l)!==h||r.elementFromPoint(a,l+d)!==h)return}else for(;c!==n;){if(null===c){if(i.isTargetWebComponent)break;return}if(c===t)return;c=c.parentNode}("function"==typeof this.settings.outsideClickDeselects?this.settings.outsideClickDeselects(s):this.settings.outsideClickDeselects)?this.instance.deselectCell():this.instance.destroyEditor(!1,!1)}}),this.eventManager.addEventListener(e.table,"selectstart",e=>{this.settings.fragmentSelection||Object(o.isInput)(e.target)||e.preventDefault()})}translateFromRenderableToVisualCoords(e){var{row:t,col:r}=e;return new s.a(...this.translateFromRenderableToVisualIndex(t,r))}translateFromRenderableToVisualIndex(e,t){var r=e>=0?this.instance.rowIndexMapper.getVisualFromRenderableIndex(e):e,o=t>=0?this.instance.columnIndexMapper.getVisualFromRenderableIndex(t):t;return null===r&&(r=e),null===o&&(o=t),[r,o]}countRenderableColumns(){return Math.min(this.instance.columnIndexMapper.getRenderableIndexesLength(),this.settings.maxCols)}countRenderableRows(){return Math.min(this.instance.rowIndexMapper.getRenderableIndexesLength(),this.settings.maxRows)}countNotHiddenRowIndexes(e,t){return this.countNotHiddenIndexes(e,t,this.instance.rowIndexMapper,this.countRenderableRows())}countNotHiddenColumnIndexes(e,t){return this.countNotHiddenIndexes(e,t,this.instance.columnIndexMapper,this.countRenderableColumns())}countNotHiddenIndexes(e,t,r,o){if(isNaN(e)||e<0)return 0;var n=r.getFirstNotHiddenIndex(e,t),i=r.getRenderableFromVisualIndex(n);if(!Number.isInteger(i))return 0;var s=0;return t<0?s=i+1:t>0&&(s=o-i),s}initializeWalkontable(){var e=l.get(this),t={externalRowCalculator:this.instance.getPlugin("autoRowSize")&&this.instance.getPlugin("autoRowSize").isEnabled(),table:e.table,preventOverflow:()=>this.settings.preventOverflow,preventWheel:()=>this.settings.preventWheel,stretchH:()=>this.settings.stretchH,data:(e,t)=>this.instance.getDataAtCell(...this.translateFromRenderableToVisualIndex(e,t)),totalRows:()=>this.countRenderableRows(),totalColumns:()=>this.countRenderableColumns(),fixedColumnsLeft:()=>{var e=parseInt(this.settings.fixedColumnsLeft,10);return this.countNotHiddenColumnIndexes(e-1,-1)},fixedRowsTop:()=>{var e=parseInt(this.settings.fixedRowsTop,10);return this.countNotHiddenRowIndexes(e-1,-1)},fixedRowsBottom:()=>{var e=parseInt(this.settings.fixedRowsBottom,10);return this.countNotHiddenRowIndexes(this.instance.countRows()-e,1)},minSpareRows:()=>this.settings.minSpareRows,renderAllRows:this.settings.renderAllRows,rowHeaders:()=>{var e=[];return this.instance.hasRowHeaders()&&e.push((e,t)=>{var r=e>=0?this.instance.rowIndexMapper.getVisualFromRenderableIndex(e):e;this.appendRowHeader(r,t)}),this.instance.runHooks("afterGetRowHeaderRenderers",e),e},columnHeaders:()=>{var e=[];return this.instance.hasColHeaders()&&e.push((e,t)=>{var r=e>=0?this.instance.columnIndexMapper.getVisualFromRenderableIndex(e):e;this.appendColHeader(r,t)}),this.instance.runHooks("afterGetColumnHeaderRenderers",e),e},columnWidth:e=>{var t=this.instance.columnIndexMapper.getVisualFromRenderableIndex(e);return this.instance.getColWidth(null===t?e:t)},rowHeight:e=>{var t=this.instance.rowIndexMapper.getVisualFromRenderableIndex(e);return this.instance.getRowHeight(null===t?e:t)},cellRenderer:(e,t,r)=>{var[o,n]=this.translateFromRenderableToVisualIndex(e,t),i=this.instance.getCellMeta(o,n),s=this.instance.colToProp(n),a=this.instance.getDataAtRowProp(o,s);this.instance.hasHook("beforeValueRender")&&(a=this.instance.runHooks("beforeValueRender",a,i)),this.instance.runHooks("beforeRenderer",r,o,n,s,a,i),this.instance.getCellRenderer(i)(this.instance,r,o,n,s,a,i),this.instance.runHooks("afterRenderer",r,o,n,s,a,i)},selections:this.instance.selection.highlight,hideBorderOnMouseDownOver:()=>this.settings.fragmentSelection,onWindowResize:()=>{this.instance&&!this.instance.isDestroyed&&this.instance.refreshDimensions()},onCellMouseDown:(t,r,o,n)=>{var s=this.translateFromRenderableToVisualCoords(r),l={row:!1,column:!1,cell:!1};this.instance.listen(),this.activeWt=n,e.mouseDown=!0,this.instance.runHooks("beforeOnCellMouseDown",t,s,o,l),Object(i.isImmediatePropagationStopped)(t)||(Object(a.a)(t,{coords:s,selection:this.instance.selection,controller:l}),this.instance.runHooks("afterOnCellMouseDown",t,s,o),this.activeWt=this.wt)},onCellContextMenu:(t,r,o,n)=>{var s=this.translateFromRenderableToVisualCoords(r);this.activeWt=n,e.mouseDown=!1,this.instance.selection.isInProgress()&&this.instance.selection.finish(),this.instance.runHooks("beforeOnCellContextMenu",t,s,o),Object(i.isImmediatePropagationStopped)(t)||(this.instance.runHooks("afterOnCellContextMenu",t,s,o),this.activeWt=this.wt)},onCellMouseOut:(e,t,r,o)=>{var n=this.translateFromRenderableToVisualCoords(t);this.activeWt=o,this.instance.runHooks("beforeOnCellMouseOut",e,n,r),Object(i.isImmediatePropagationStopped)(e)||(this.instance.runHooks("afterOnCellMouseOut",e,n,r),this.activeWt=this.wt)},onCellMouseOver:(t,r,o,n)=>{var s=this.translateFromRenderableToVisualCoords(r),l={row:!1,column:!1,cell:!1};this.activeWt=n,this.instance.runHooks("beforeOnCellMouseOver",t,s,o,l),Object(i.isImmediatePropagationStopped)(t)||(e.mouseDown&&Object(a.a)(t,{coords:s,selection:this.instance.selection,controller:l}),this.instance.runHooks("afterOnCellMouseOver",t,s,o),this.activeWt=this.wt)},onCellMouseUp:(e,t,r,o)=>{var n=this.translateFromRenderableToVisualCoords(t);this.activeWt=o,this.instance.runHooks("beforeOnCellMouseUp",e,n,r),Object(i.isImmediatePropagationStopped)(e)||(this.instance.runHooks("afterOnCellMouseUp",e,n,r),this.activeWt=this.wt)},onCellCornerMouseDown:e=>{e.preventDefault(),this.instance.runHooks("afterOnCellCornerMouseDown",e)},onCellCornerDblClick:e=>{e.preventDefault(),this.instance.runHooks("afterOnCellCornerDblClick",e)},beforeDraw:(e,t)=>this.beforeRender(e,t),onDraw:e=>this.onDraw(e),onScrollVertically:()=>this.instance.runHooks("afterScrollVertically"),onScrollHorizontally:()=>this.instance.runHooks("afterScrollHorizontally"),onBeforeRemoveCellClassNames:()=>this.instance.runHooks("beforeRemoveCellClassNames"),onAfterDrawSelection:(e,t,r,o)=>{var[n,i]=this.translateFromRenderableToVisualIndex(e,t);return this.instance.runHooks("afterDrawSelection",n,i,r,o)},onBeforeDrawBorders:(e,t)=>{var[r,o,n,i]=e,s=[this.instance.rowIndexMapper.getVisualFromRenderableIndex(r),this.instance.columnIndexMapper.getVisualFromRenderableIndex(o),this.instance.rowIndexMapper.getVisualFromRenderableIndex(n),this.instance.columnIndexMapper.getVisualFromRenderableIndex(i)];return this.instance.runHooks("beforeDrawBorders",s,t)},onBeforeTouchScroll:()=>this.instance.runHooks("beforeTouchScroll"),onAfterMomentumScroll:()=>this.instance.runHooks("afterMomentumScroll"),onBeforeStretchingColumnWidth:(e,t)=>{var r=this.instance.columnIndexMapper.getVisualFromRenderableIndex(t);return this.instance.runHooks("beforeStretchingColumnWidth",e,r)},onModifyRowHeaderWidth:e=>this.instance.runHooks("modifyRowHeaderWidth",e),onModifyGetCellCoords:(e,t,r)=>{var o=t>=0?this.instance.columnIndexMapper.getVisualFromRenderableIndex(t):t,n=e>=0?this.instance.rowIndexMapper.getVisualFromRenderableIndex(e):e;return this.instance.runHooks("modifyGetCellCoords",n,o,r)},viewportRowCalculatorOverride:e=>{var t=this.settings.viewportRowRenderingOffset;if("auto"===t&&this.settings.fixedRowsTop&&(t=10),t>0||"auto"===t){var r=this.countRenderableRows();if("number"==typeof t)e.startRow=Math.max(e.startRow-t,0),e.endRow=Math.min(e.endRow+t,r-1);else if("auto"===t){var o=e.startRow+e.endRow-e.startRow,n=Math.ceil(o/r*12);e.startRow=Math.max(e.startRow-n,0),e.endRow=Math.min(e.endRow+n,r-1)}}this.instance.runHooks("afterViewportRowCalculatorOverride",e)},viewportColumnCalculatorOverride:e=>{var t=this.settings.viewportColumnRenderingOffset;if("auto"===t&&this.settings.fixedColumnsLeft&&(t=10),t>0||"auto"===t){var r=this.countRenderableColumns();if("number"==typeof t&&(e.startColumn=Math.max(e.startColumn-t,0),e.endColumn=Math.min(e.endColumn+t,r-1)),"auto"===t){var o=e.startColumn+e.endColumn-e.startColumn,n=Math.ceil(o/r*12);e.startRow=Math.max(e.startColumn-n,0),e.endColumn=Math.min(e.endColumn+n,r-1)}}this.instance.runHooks("afterViewportColumnCalculatorOverride",e)},rowHeaderWidth:()=>this.settings.rowHeaderWidth,columnHeaderHeight:()=>{var e=this.instance.runHooks("modifyColumnHeaderHeight");return this.settings.columnHeaderHeight||e}};this.instance.runHooks("beforeInitWalkontable",t),this.wt=new s.e(t),this.activeWt=this.wt;var r=this.wt.wtTable.spreader,{width:n,height:c}=Object(o.getBoundingClientRect)(this.instance.rootElement);this.setLastSize(n,c),this.eventManager.addEventListener(r,"mousedown",e=>{e.target===r&&3===e.which&&e.stopPropagation()}),this.eventManager.addEventListener(r,"contextmenu",e=>{e.target===r&&3===e.which&&e.stopPropagation()}),this.eventManager.addEventListener(this.instance.rootDocument.documentElement,"click",()=>{this.settings.observeDOMVisibility&&this.wt.drawInterrupted&&(this.instance.forceFullRender=!0,this.render())})}isTextSelectionAllowed(e){if(Object(o.isInput)(e))return!0;var t=Object(o.isChildOf)(e,this.instance.view.wt.wtTable.spreader);return!(!0!==this.settings.fragmentSelection||!t)||(!("cell"!==this.settings.fragmentSelection||!this.isSelectedOnlyCell()||!t)||!(this.settings.fragmentSelection||!this.isCellEdited()||!this.isSelectedOnlyCell()))}isMouseDown(){return l.get(this).mouseDown}isSelectedOnlyCell(){var[e,t,r,o]=this.instance.getSelectedLast()||[];return void 0!==e&&e===r&&t===o}isCellEdited(){var e=this.instance.getActiveEditor();return e&&e.isOpened()}beforeRender(e,t){e&&this.instance.runHooks("beforeRender",this.instance.forceFullRender,t)}onDraw(e){e&&this.instance.runHooks("afterRender",this.instance.forceFullRender)}appendRowHeader(e,t){if(t.firstChild){var r=t.firstChild;if(!Object(o.hasClass)(r,"relative"))return Object(o.empty)(t),void this.appendRowHeader(e,t);this.updateCellHeader(r.querySelector(".rowHeader"),e,this.instance.getRowHeader)}else{var{rootDocument:n,getRowHeader:i}=this.instance,s=n.createElement("div"),a=n.createElement("span");s.className="relative",a.className="rowHeader",this.updateCellHeader(a,e,i),s.appendChild(a),t.appendChild(s)}this.instance.runHooks("afterGetRowHeader",e,t)}appendColHeader(e,t){if(t.firstChild){var r=t.firstChild;Object(o.hasClass)(r,"relative")?this.updateCellHeader(r.querySelector(".colHeader"),e,this.instance.getColHeader):(Object(o.empty)(t),this.appendColHeader(e,t))}else{var{rootDocument:n}=this.instance,i=n.createElement("div"),s=n.createElement("span");i.className="relative",s.className="colHeader",this.updateCellHeader(s,e,this.instance.getColHeader),i.appendChild(s),t.appendChild(i)}this.instance.runHooks("afterGetColHeader",e,t)}updateCellHeader(e,t,r){var n=t,i=this.wt.wtOverlays.getParentOverlay(e)||this.wt;e.parentNode&&(Object(o.hasClass)(e,"colHeader")?n=i.wtTable.columnFilter.sourceToRendered(t):Object(o.hasClass)(e,"rowHeader")&&(n=i.wtTable.rowFilter.sourceToRendered(t))),n>-1?Object(o.fastInnerHTML)(e,r(t)):(Object(o.fastInnerText)(e,String.fromCharCode(160)),Object(o.addClass)(e,"cornerHeader"))}maximumVisibleElementWidth(e){var t=this.wt.wtViewport.getWorkspaceWidth()-e;return t>0?t:0}maximumVisibleElementHeight(e){var t=this.wt.wtViewport.getWorkspaceHeight()-e;return t>0?t:0}setLastSize(e,t){var r=l.get(this);[r.lastWidth,r.lastHeight]=[e,t]}getLastSize(){var e=l.get(this);return{width:e.lastWidth,height:e.lastHeight}}mainViewIsActive(){return this.wt===this.activeWt}destroy(){this.wt.destroy(),this.eventManager.destroy()}}},function(e,t,r){"use strict";var o=r(32),n=r(75),i=r(33),s=r(44),a=r(61);t.a=class{constructor(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];this.hot=e,this.data=t,this.dataType="array",this.colToProp=()=>{},this.propToCol=()=>{}}modifyRowData(e){var t;return this.hot.hasHook("modifyRowData")&&(t=this.hot.runHooks("modifyRowData",e)),void 0===t||Number.isInteger(t)?this.data[e]:t}getData(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this.data&&0!==this.data.length?this.getByRange(null,null,e):this.data}setData(e){this.data=e}getAtColumn(e){var t=[];return Object(i.arrayEach)(this.data,(r,o)=>{var n=this.getAtCell(o,e);t.push(n)}),t}getAtRow(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=void 0===t&&void 0===r,l=null,c=null;if(l=this.modifyRowData(e),Array.isArray(l))c=[],i?l.forEach((t,r)=>{c[r]=this.getAtPhysicalCell(e,r,l)}):Object(s.rangeEach)(t,r,r=>{c[r-t]=this.getAtPhysicalCell(e,r,l)});else if(Object(o.isObject)(l)||Object(a.isFunction)(l))if(c=n?[]:{},!i||n){var h=this.countFirstRowKeys()-1;Object(s.rangeEach)(0,h,i=>{var s=this.colToProp(i);if(i>=(t||0)&&i<=(r||h)&&!Number.isInteger(s)){var a=this.getAtPhysicalCell(e,s,l);n?c.push(a):Object(o.setProperty)(c,s,a)}})}else Object(o.objectEach)(l,(t,r)=>{Object(o.setProperty)(c,r,this.getAtPhysicalCell(e,r,l))});return c}setAtCell(e,t,r){if(!(e>=this.countRows()||t>=this.countFirstRowKeys())){if(this.hot.hasHook("modifySourceData")){var n=Object(o.createObjectPropListener)(r);this.hot.runHooks("modifySourceData",e,this.propToCol(t),n,"set"),n.isTouched()&&(r=n.value)}Number.isInteger(t)?this.data[e][t]=r:Object(o.setProperty)(this.data[e],t,r)}}getAtPhysicalCell(e,t,r){var n=null;if(r&&(n="string"==typeof t?Object(o.getProperty)(r,t):"function"==typeof t?t(r):r[t]),this.hot.hasHook("modifySourceData")){var i=Object(o.createObjectPropListener)(n);this.hot.runHooks("modifySourceData",e,this.colToProp(t),i,"get"),i.isTouched()&&(n=i.value)}return n}getAtCell(e,t){var r=this.modifyRowData(e);return this.getAtPhysicalCell(e,this.colToProp(t),r)}getByRange(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=!1,n=null,i=null,a=null,l=null;null===e||null===t?(o=!0,n=0,a=this.countRows()-1):(n=Math.min(e.row,t.row),i=Math.min(e.col,t.col),a=Math.max(e.row,t.row),l=Math.max(e.col,t.col));var c=[];return Object(s.rangeEach)(n,a,e=>{c.push(o?this.getAtRow(e,void 0,void 0,r):this.getAtRow(e,i,l,r))}),c}countRows(){if(this.hot.hasHook("modifySourceLength")){var e=this.hot.runHooks("modifySourceLength");if(Number.isInteger(e))return e}return this.data.length}countFirstRowKeys(){return Object(n.countFirstRowKeys)(this.data)}destroy(){this.data=null,this.hot=null}}},function(e,t,r){"use strict";r.d(t,"a",(function(){return n}));var o=r(38);function n(e,t){var r=new o.b(t),n=e.getCellMeta(t.row,t.col);if(n.rowspan||n.colspan){var i=n.rowspan||1,s=n.colspan||1;r.to=new o.a(t.row+i-1,t.col+s-1)}return r}},function(e,t,r){"use strict";function o(e){var t="undefined"!=typeof window&&window.jQuery;t&&(t.fn.handsontable=function(t){var r,o=this.first(),n=o.data("handsontable");if("string"!=typeof t){var i=t||{};return n?n.updateSettings(i):(n=new e.Core(o[0],i),o.data("handsontable",n),n.init()),o}if(n){if(void 0===n[t])throw new Error("Handsontable do not provide action: ".concat(t));for(var s=arguments.length,a=new Array(s>1?s-1:0),l=1;l<s;l++)a[l-1]=arguments[l];r=n[t].call(n,...a),"destroy"===t&&o.removeData()}return r})}r.d(t,"a",(function(){return o}))},function(e,t,r){"use strict";function o(e){var t=new Date(e);return isNaN(new Date("".concat(e,"T00:00")).getDate())?t:new Date(t.getTime()+6e4*t.getTimezoneOffset())}r.r(t),r.d(t,"getNormalizedDate",(function(){return o}))},,function(e,t,r){"use strict";r.r(t),r.d(t,"AutoColumnSize",(function(){return C})),r.d(t,"AutoRowSize",(function(){return y})),r.d(t,"Base",(function(){return l})),r.d(t,"CopyPaste",(function(){return z})),r.d(t,"CustomBorders",(function(){return ee})),r.d(t,"MergeCells",(function(){return we})),r.d(t,"TouchScroll",(function(){return Ce}));var o=r(32),n=r(33),i=r(47),s=new WeakMap,a=null;var l=class{constructor(e){Object(o.defineGetter)(this,"hot",e,{writable:!1}),s.set(this,{hooks:{}}),a=null,this.pluginName=null,this.pluginsInitializedCallbacks=[],this.isPluginsReady=!1,this.enabled=!1,this.initialized=!1,this.hot.addHook("afterPluginsInitialized",()=>this.onAfterPluginsInitialized()),this.hot.addHook("afterUpdateSettings",e=>this.onUpdateSettings(e)),this.hot.addHook("beforeInit",()=>this.init())}init(){this.pluginName=Object(i.b)(this.hot,this),this.isEnabled&&this.isEnabled()&&this.enablePlugin(),a||(a=Object(i.c)(this.hot)),a.indexOf(this.pluginName)>=0&&a.splice(a.indexOf(this.pluginName),1),a.length||this.hot.runHooks("afterPluginsInitialized"),this.initialized=!0}enablePlugin(){this.enabled=!0}disablePlugin(){this.eventManager&&this.eventManager.clear(),this.clearHooks(),this.enabled=!1}addHook(e,t){s.get(this).hooks[e]=s.get(this).hooks[e]||[];var r=s.get(this).hooks[e];this.hot.addHook(e,t),r.push(t),s.get(this).hooks[e]=r}removeHooks(e){Object(n.arrayEach)(s.get(this).hooks[e]||[],t=>{this.hot.removeHook(e,t)})}clearHooks(){var e=s.get(this).hooks;Object(o.objectEach)(e,(e,t)=>this.removeHooks(t)),e.length=0}callOnPluginsReady(e){this.isPluginsReady?e():this.pluginsInitializedCallbacks.push(e)}onAfterPluginsInitialized(){Object(n.arrayEach)(this.pluginsInitializedCallbacks,e=>e()),this.pluginsInitializedCallbacks.length=0,this.isPluginsReady=!0}onUpdateSettings(){this.isEnabled&&(this.enabled&&!this.isEnabled()&&this.disablePlugin(),!this.enabled&&this.isEnabled()&&this.enablePlugin(),this.enabled&&this.isEnabled()&&this.updatePlugin())}updatePlugin(){}destroy(){this.eventManager&&this.eventManager.destroy(),this.clearHooks(),Object(o.objectEach)(this,(e,t)=>{"hot"!==t&&(this[t]=null)}),delete this.t,delete this.hot}},c=r(55),h=r(93),d=r(44),u=r(40);class g{static get SAMPLE_COUNT(){return 3}constructor(e){this.samples=null,this.dataFactory=e,this.customSampleCount=null,this.allowDuplicates=!1}getSampleCount(){return this.customSampleCount?this.customSampleCount:g.SAMPLE_COUNT}setSampleCount(e){this.customSampleCount=e}setAllowDuplicates(e){this.allowDuplicates=e}generateRowSamples(e,t){return this.generateSamples("row",t,e)}generateColumnSamples(e,t){return this.generateSamples("col",t,e)}generateSamples(e,t,r){var o=new Map,{from:n,to:i}="number"==typeof r?{from:r,to:r}:r;return Object(d.rangeEach)(n,i,r=>{var n=this.generateSample(e,t,r);o.set(r,n)}),o}generateSample(e,t,r){if("row"!==e&&"col"!==e)throw new Error("Unsupported sample type");var n=new Map,i="row"===e?"col":"row",s=[];return Object(d.rangeEach)(t.from,t.to,t=>{var a,{value:l,bundleCountSeed:c}="row"===e?this.dataFactory(r,t):this.dataFactory(t,r),h=c>0;a=Object(o.isObject)(l)?Object.keys(l).length:Array.isArray(l)?l.length:Object(u.stringify)(l).length,h&&(a+=c),n.has(a)||n.set(a,{needed:this.getSampleCount(),strings:[]});var d=n.get(a);d.needed&&((!(s.indexOf(l)>-1)||this.allowDuplicates||h)&&(d.strings.push({value:l,[i]:t}),s.push(l),d.needed-=1))}),n}}var f=g,m=r(49),p=r(38),v=r(95),w=new WeakMap;class b extends l{static get CALCULATION_STEP(){return 50}static get SYNC_CALCULATION_LIMIT(){return 50}constructor(e){super(e),w.set(this,{cachedColumnHeaders:[]}),this.ghostTable=new h.a(this.hot),this.samplesGenerator=new f((e,t)=>{var r=this.hot.getCellMeta(e,t),o="";r.spanned||(o=this.hot.getDataAtCell(e,t));var n=0;if(r.label){var{value:i,property:s}=r.label,a="";if(i)a="function"==typeof i?i(e,t,this.hot.colToProp(t),o):i;else if(s){var l=this.hot.getDataAtRowProp(e,s);a=null!==l?l:""}n=a.length}return{value:o,bundleCountSeed:n}}),this.firstCalculation=!0,this.inProgress=!1,this.measuredColumns=0,this.columnWidthsMap=new v.b,this.addHook("beforeColumnResize",(e,t,r)=>this.onBeforeColumnResize(e,t,r)),this.hot.columnIndexMapper.registerMap("autoColumnSize",this.columnWidthsMap)}isEnabled(){return!1!==this.hot.getSettings().autoColumnSize&&!this.hot.getSettings().colWidths}enablePlugin(){if(!this.enabled){var e=this.hot.getSettings().autoColumnSize;e&&null!==e.useHeaders&&void 0!==e.useHeaders&&this.ghostTable.setSetting("useHeaders",e.useHeaders),this.setSamplingOptions(),this.addHook("afterLoadData",()=>this.onAfterLoadData()),this.addHook("beforeChange",e=>this.onBeforeChange(e)),this.addHook("beforeRender",e=>this.onBeforeRender(e)),this.addHook("modifyColWidth",(e,t)=>this.getColumnWidth(t,e)),this.addHook("afterInit",()=>this.onAfterInit()),super.enablePlugin()}}updatePlugin(){var e=this.findColumnsWhereHeaderWasChanged();e.length&&this.clearCache(e),super.updatePlugin()}calculateColumnsWidth(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{from:0,to:this.hot.countCols()-1},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{from:0,to:this.hot.countRows()-1},r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o="number"==typeof e?{from:e,to:e}:e,i="number"==typeof t?{from:t,to:t}:t;Object(d.rangeEach)(o.from,o.to,e=>{var t=this.hot.toPhysicalColumn(e);if(null===t&&(t=e),r||null===this.columnWidthsMap.getValueAtIndex(t)&&!this.hot._getColWidthFromSettings(t)){var o=this.samplesGenerator.generateColumnSamples(e,i);Object(n.arrayEach)(o,e=>{var[t,r]=e;return this.ghostTable.addColumn(t,r)})}}),this.ghostTable.columns.length&&(this.hot.executeBatchOperations(()=>{this.ghostTable.getWidths((e,t)=>{var r=this.hot.toPhysicalColumn(e);this.columnWidthsMap.setValueAtIndex(r,t)})}),this.measuredColumns=o.to+1,this.ghostTable.clean())}calculateAllColumnsWidth(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{from:0,to:this.hot.countRows()-1},t=0,r=this.hot.countCols()-1,o=null;this.inProgress=!0;var n=()=>{if(!this.hot)return Object(c.cancelAnimationFrame)(o),void(this.inProgress=!1);this.calculateColumnsWidth({from:t,to:Math.min(t+b.CALCULATION_STEP,r)},e),(t=t+b.CALCULATION_STEP+1)<r?o=Object(c.requestAnimationFrame)(n):(Object(c.cancelAnimationFrame)(o),this.inProgress=!1,this.hot.view.wt.wtOverlays.adjustElementsSizes())},i=this.getSyncCalculationLimit();this.firstCalculation&&i>=0&&(this.calculateColumnsWidth({from:0,to:i},e),this.firstCalculation=!1,t=i+1),t<r?n():this.inProgress=!1}setSamplingOptions(){var e=this.hot.getSettings().autoColumnSize,t=e&&Object(o.hasOwnProperty)(e,"samplingRatio")?this.hot.getSettings().autoColumnSize.samplingRatio:void 0,r=e&&Object(o.hasOwnProperty)(e,"allowSampleDuplicates")?this.hot.getSettings().autoColumnSize.allowSampleDuplicates:void 0;t&&!isNaN(t)&&this.samplesGenerator.setSampleCount(parseInt(t,10)),r&&this.samplesGenerator.setAllowDuplicates(r)}recalculateAllColumnsWidth(){this.hot.view&&this.hot.view.wt.wtTable.isVisible()&&(this.clearCache(),this.calculateAllColumnsWidth())}getSyncCalculationLimit(){var e=b.SYNC_CALCULATION_LIMIT,t=this.hot.countCols()-1;return Object(o.isObject)(this.hot.getSettings().autoColumnSize)&&(e=this.hot.getSettings().autoColumnSize.syncLimit,Object(m.isPercentValue)(e)?e=Object(d.valueAccordingPercent)(t,e):e>>=0),Math.min(e,t)}getColumnWidth(e){var t=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;return void 0===r&&(r=this.columnWidthsMap.getValueAtIndex(this.hot.toPhysicalColumn(e)),t&&"number"==typeof r&&(r=Math.max(r,p.d.DEFAULT_WIDTH))),r}getFirstVisibleColumn(){var e=this.hot.view.wt;if(e.wtViewport.columnsVisibleCalculator){var t=e.wtTable.getFirstVisibleColumn();if(-1!==t)return this.hot.columnIndexMapper.getVisualFromRenderableIndex(t)}if(e.wtViewport.columnsRenderCalculator){var r=e.wtTable.getFirstRenderedColumn();if(-1!==r)return this.hot.columnIndexMapper.getVisualFromRenderableIndex(r)}return-1}getLastVisibleColumn(){var e=this.hot.view.wt;if(e.wtViewport.columnsVisibleCalculator){var t=e.wtTable.getLastVisibleColumn();if(-1!==t)return this.hot.columnIndexMapper.getVisualFromRenderableIndex(t)}if(e.wtViewport.columnsRenderCalculator){var r=e.wtTable.getLastRenderedColumn();if(-1!==r)return this.hot.columnIndexMapper.getVisualFromRenderableIndex(r)}return-1}findColumnsWhereHeaderWasChanged(){var e=this.hot.getColHeader(),{cachedColumnHeaders:t}=w.get(this);return Object(n.arrayReduce)(e,(e,r,o)=>{var n=t.length;return(n-1<o||t[o]!==r)&&e.push(o),n-1<o?t.push(r):t[o]=r,e},[])}clearCache(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];e.length?this.hot.executeBatchOperations(()=>{Object(n.arrayEach)(e,e=>{this.columnWidthsMap.setValueAtIndex(e,null)})}):this.columnWidthsMap.clear()}isNeedRecalculate(){return!!Object(n.arrayFilter)(this.columnWidthsMap.getValues().slice(0,this.measuredColumns),e=>null===e).length}onBeforeRender(){var e=this.hot.renderCall,t=this.hot.countRows(),r=this.getFirstVisibleColumn(),o=this.getLastVisibleColumn();-1!==r&&-1!==o&&t&&(this.calculateColumnsWidth({from:r,to:o},void 0,e),this.isNeedRecalculate()&&!this.inProgress&&this.calculateAllColumnsWidth())}onAfterLoadData(){this.hot.view?this.recalculateAllColumnsWidth():setTimeout(()=>{this.hot&&this.recalculateAllColumnsWidth()},0)}onBeforeChange(e){var t=Object(n.arrayMap)(e,e=>{var[,t]=e;return this.hot.toPhysicalColumn(this.hot.propToCol(t))});this.clearCache(Array.from(new Set(t)))}onBeforeColumnResize(e,t,r){var o=e;return r&&(this.calculateColumnsWidth(t,void 0,!0),o=this.getColumnWidth(t,void 0,!1)),o}onAfterInit(){w.get(this).cachedColumnHeaders=this.hot.getColHeader()}disablePlugin(){super.disablePlugin()}destroy(){this.hot.columnIndexMapper.unregisterMap("autoColumnSize"),this.ghostTable.clean(),super.destroy()}}Object(i.d)("autoColumnSize",b);var C=b,E=r(34);class O extends l{static get CALCULATION_STEP(){return 50}static get SYNC_CALCULATION_LIMIT(){return 500}constructor(e){super(e),this.rowHeightsMap=void 0,this.headerHeight=null,this.ghostTable=new h.a(this.hot),this.samplesGenerator=new f((e,t)=>{var r;return e>=0?r=this.hot.getDataAtCell(e,t):-1===e&&(r=this.hot.getColHeader(t)),{value:r}}),this.firstCalculation=!0,this.inProgress=!1,this.measuredRows=0,this.addHook("beforeRowResize",(e,t,r)=>this.onBeforeRowResize(e,t,r)),this.rowHeightsMap=new v.b,this.hot.rowIndexMapper.registerMap("autoRowSize",this.rowHeightsMap)}isEnabled(){return!0===this.hot.getSettings().autoRowSize||Object(o.isObject)(this.hot.getSettings().autoRowSize)}enablePlugin(){this.enabled||(this.setSamplingOptions(),this.addHook("afterLoadData",()=>this.onAfterLoadData()),this.addHook("beforeChange",e=>this.onBeforeChange(e)),this.addHook("beforeColumnResize",()=>this.recalculateAllRowsHeight()),this.addHook("beforeRender",e=>this.onBeforeRender(e)),this.addHook("modifyRowHeight",(e,t)=>this.getRowHeight(t,e)),this.addHook("modifyColumnHeaderHeight",()=>this.getColumnHeaderHeight()),super.enablePlugin())}disablePlugin(){this.headerHeight=null,super.disablePlugin()}calculateRowsHeight(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{from:0,to:this.hot.countRows()-1},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{from:0,to:this.hot.countCols()-1},r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o="number"==typeof e?{from:e,to:e}:e,i="number"==typeof t?{from:t,to:t}:t;if(null!==this.hot.getColHeader(0)){var s=this.samplesGenerator.generateRowSamples(-1,i);this.ghostTable.addColumnHeadersRow(s.get(-1))}Object(d.rangeEach)(o.from,o.to,e=>{if(r||null===this.rowHeightsMap.getValueAtIndex(e)){var t=this.samplesGenerator.generateRowSamples(e,i);Object(n.arrayEach)(t,e=>{var[t,r]=e;return this.ghostTable.addRow(t,r)})}}),this.ghostTable.rows.length&&(this.hot.executeBatchOperations(()=>{this.ghostTable.getHeights((e,t)=>{e<0?this.headerHeight=t:this.rowHeightsMap.setValueAtIndex(this.hot.toPhysicalRow(e),t)})}),this.measuredRows=o.to+1,this.ghostTable.clean())}calculateAllRowsHeight(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{from:0,to:this.hot.countCols()-1},t=0,r=this.hot.countRows()-1,o=null;this.inProgress=!0;var n=()=>{if(!this.hot)return Object(c.cancelAnimationFrame)(o),void(this.inProgress=!1);this.calculateRowsHeight({from:t,to:Math.min(t+O.CALCULATION_STEP,r)},e),(t=t+O.CALCULATION_STEP+1)<r?o=Object(c.requestAnimationFrame)(n):(Object(c.cancelAnimationFrame)(o),this.inProgress=!1,this.hot.view.wt.wtOverlays.adjustElementsSizes(!0),this.hot.view.wt.wtOverlays.leftOverlay.needFullRender&&this.hot.view.wt.wtOverlays.leftOverlay.clone.drawClone())},i=this.getSyncCalculationLimit();this.firstCalculation&&i>=0&&(this.calculateRowsHeight({from:0,to:i},e),this.firstCalculation=!1,t=i+1),t<r?n():(this.inProgress=!1,this.hot.view.wt.wtOverlays.adjustElementsSizes(!1))}setSamplingOptions(){var e=this.hot.getSettings().autoRowSize,t=e&&Object(o.hasOwnProperty)(e,"samplingRatio")?this.hot.getSettings().autoRowSize.samplingRatio:void 0,r=e&&Object(o.hasOwnProperty)(e,"allowSampleDuplicates")?this.hot.getSettings().autoRowSize.allowSampleDuplicates:void 0;t&&!isNaN(t)&&this.samplesGenerator.setSampleCount(parseInt(t,10)),r&&this.samplesGenerator.setAllowDuplicates(r)}recalculateAllRowsHeight(){Object(E.isVisible)(this.hot.view.wt.wtTable.TABLE)&&(this.clearCache(),this.calculateAllRowsHeight())}getSyncCalculationLimit(){var e=O.SYNC_CALCULATION_LIMIT,t=this.hot.countRows()-1;return Object(o.isObject)(this.hot.getSettings().autoRowSize)&&(e=this.hot.getSettings().autoRowSize.syncLimit,Object(m.isPercentValue)(e)?e=Object(d.valueAccordingPercent)(t,e):e>>=0),Math.min(e,t)}getRowHeight(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,r=e<0?this.headerHeight:this.rowHeightsMap.getValueAtIndex(this.hot.toPhysicalRow(e)),o=t;return null!==r&&r>(t||0)&&(o=r),o}getColumnHeaderHeight(){return this.headerHeight}getFirstVisibleRow(){var e=this.hot.view.wt;return e.wtViewport.rowsVisibleCalculator?e.wtTable.getFirstVisibleRow():e.wtViewport.rowsRenderCalculator?e.wtTable.getFirstRenderedRow():-1}getLastVisibleRow(){var e=this.hot.view.wt;return e.wtViewport.rowsVisibleCalculator?e.wtTable.getLastVisibleRow():e.wtViewport.rowsRenderCalculator?e.wtTable.getLastRenderedRow():-1}clearCache(){this.headerHeight=null,this.rowHeightsMap.init()}clearCacheByRange(e){var{from:t,to:r}="number"==typeof e?{from:e,to:e}:e;this.hot.executeBatchOperations(()=>{Object(d.rangeEach)(Math.min(t,r),Math.max(t,r),e=>{this.rowHeightsMap.setValueAtIndex(e,null)})})}isNeedRecalculate(){return!!Object(n.arrayFilter)(this.rowHeightsMap.getValues().slice(0,this.measuredRows),e=>null===e).length}onBeforeRender(){var e=this.hot.renderCall,t=this.hot.getSettings().fixedRowsBottom,r=this.getFirstVisibleRow(),o=this.getLastVisibleRow();if(-1!==r&&-1!==o){if(this.calculateRowsHeight({from:r,to:o},void 0,e),t){var n=this.hot.countRows()-1;this.calculateRowsHeight({from:n-t,to:n})}this.isNeedRecalculate()&&!this.inProgress&&this.calculateAllRowsHeight()}}onBeforeRowMove(e,t){this.clearCacheByRange({from:e,to:t}),this.calculateAllRowsHeight()}onBeforeRowResize(e,t,r){var o=e;return r&&(this.calculateRowsHeight(t,void 0,!0),o=this.getRowHeight(t)),o}onAfterLoadData(){this.hot.view?this.recalculateAllRowsHeight():setTimeout(()=>{this.hot&&this.recalculateAllRowsHeight()},0)}onBeforeChange(e){var t=null;1===e.length?t=e[0][0]:e.length>1&&(t={from:e[0][0],to:e[e.length-1][0]}),null!==t&&this.clearCacheByRange(t)}destroy(){this.hot.rowIndexMapper.unregisterMap("autoRowSize"),this.ghostTable.clean(),super.destroy()}}Object(i.d)("autoRowSize",O);var y=O,R=r(59),T=r(121),S=r.n(T),M=r(45);class N{constructor(){this.data={}}setData(e,t){this.data[e]=t}getData(e){return this.data[e]||void 0}}class H{constructor(){this.clipboardData=new N}}var L=r(43),x=r(62),A=r(46);class I{constructor(e){this.rootDocument=e.defaultView?e:e.ownerDocument,this.mainElement=null,this.eventManager=new L.a(this),this.listenersCount=new WeakSet,this.container=e}useSecondaryElement(){var e=function(e){var t=k.get(e);if(t)return t.parentElement||e.appendChild(t),t;var r=(e.defaultView?e:e.ownerDocument).createElement("textarea");return k.set(e,r),r.setAttribute("data-hot-input",""),r.className="HandsontableCopyPaste",r.tabIndex=-1,r.autocomplete="off",r.wrap="hard",r.value=" ",e.appendChild(r),r}(this.container);this.listenersCount.has(e)||(this.listenersCount.add(e),_(this.eventManager,e,this)),this.mainElement=e}setFocusableElement(e){this.listenersCount.has(e)||(this.listenersCount.add(e),_(this.eventManager,e,this)),this.mainElement=e}getFocusableElement(){return this.mainElement}focus(){this.mainElement.value=" ",Object(A.isMobileBrowser)()||Object(E.selectElementIfAllowed)(this.mainElement)}}Object(o.mixin)(I,x.a);var j=new WeakMap;var D=(e,t)=>r=>t.runLocalHooks(e,r);function _(e,t,r){e.addEventListener(t,"copy",D("copy",r)),e.addEventListener(t,"cut",D("cut",r)),e.addEventListener(t,"paste",D("paste",r))}var k=new WeakMap;function P(e){if(e instanceof I){var t=j.get(e.container);if((t=isNaN(t)?0:t)>0&&(t-=1),function(e){e.eventManager.clear()}(e),t<=0){t=0;var r=k.get(e.container);r&&r.parentNode&&(r.parentNode.removeChild(r),k.delete(e.container)),e.mainElement=null}j.set(e.container,t)}}var B=r(90);R.a.getSingleton().register("afterCopyLimit"),R.a.getSingleton().register("modifyCopyableRange"),R.a.getSingleton().register("beforeCut"),R.a.getSingleton().register("afterCut"),R.a.getSingleton().register("beforePaste"),R.a.getSingleton().register("afterPaste"),R.a.getSingleton().register("beforeCopy"),R.a.getSingleton().register("afterCopy");var F=new WeakMap,V=['<meta name="generator" content="Handsontable"/>','<style type="text/css">td{white-space:normal}br{mso-data-placement:same-cell}</style>'].join("");class W extends l{constructor(e){super(e),this.columnsLimit=1e3,this.copyableRanges=[],this.focusableElement=void 0,this.pasteMode="overwrite",this.rowsLimit=1e3,this.uiContainer=this.hot.rootDocument.body,F.set(this,{isTriggeredByCopy:!1,isTriggeredByCut:!1,isBeginEditing:!1,isFragmentSelectionEnabled:!1})}isEnabled(){return!!this.hot.getSettings().copyPaste}enablePlugin(){if(!this.enabled){var e,t,r,{copyPaste:o,fragmentSelection:n}=this.hot.getSettings();F.get(this).isFragmentSelectionEnabled=!!n,"object"==typeof o&&(this.pasteMode=o.pasteMode||this.pasteMode,this.rowsLimit=isNaN(o.rowsLimit)?this.rowsLimit:o.rowsLimit,this.columnsLimit=isNaN(o.columnsLimit)?this.columnsLimit:o.columnsLimit,this.uiContainer=o.uiContainer||this.uiContainer),this.addHook("afterContextMenuDefaultOptions",e=>this.onAfterContextMenuDefaultOptions(e)),this.addHook("afterOnCellMouseUp",()=>this.onAfterOnCellMouseUp()),this.addHook("afterSelectionEnd",()=>this.onAfterSelectionEnd()),this.addHook("beforeKeyDown",()=>this.onBeforeKeyDown()),this.focusableElement=(e=this.uiContainer,t=new I(e),r=j.get(e),r=isNaN(r)?0:r,j.set(e,r+1),t),this.focusableElement.addLocalHook("copy",e=>this.onCopy(e)).addLocalHook("cut",e=>this.onCut(e)).addLocalHook("paste",e=>this.onPaste(e)),super.enablePlugin()}}updatePlugin(){this.disablePlugin(),this.enablePlugin(),this.getOrCreateFocusableElement(),super.updatePlugin()}disablePlugin(){this.focusableElement&&P(this.focusableElement),super.disablePlugin()}copy(){F.get(this).isTriggeredByCopy=!0,this.getOrCreateFocusableElement(),this.focusableElement.focus(),this.hot.rootDocument.execCommand("copy")}cut(){F.get(this).isTriggeredByCut=!0,this.getOrCreateFocusableElement(),this.focusableElement.focus(),this.hot.rootDocument.execCommand("cut")}getRangedCopyableData(e){var t=[],r=[],o=[];return Object(n.arrayEach)(e,e=>{Object(d.rangeEach)(e.startRow,e.endRow,e=>{-1===r.indexOf(e)&&r.push(e)}),Object(d.rangeEach)(e.startCol,e.endCol,e=>{-1===o.indexOf(e)&&o.push(e)})}),Object(n.arrayEach)(r,e=>{var r=[];Object(n.arrayEach)(o,t=>{r.push(this.hot.getCopyableData(e,t))}),t.push(r)}),S.a.stringify(t)}getRangedData(e){var t=[],r=[],o=[];return Object(n.arrayEach)(e,e=>{Object(d.rangeEach)(e.startRow,e.endRow,e=>{-1===r.indexOf(e)&&r.push(e)}),Object(d.rangeEach)(e.startCol,e.endCol,e=>{-1===o.indexOf(e)&&o.push(e)})}),Object(n.arrayEach)(r,e=>{var r=[];Object(n.arrayEach)(o,t=>{r.push(this.hot.getCopyableData(e,t))}),t.push(r)}),t}paste(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e;if(e||t){var r=new H;e&&r.clipboardData.setData("text/plain",e),t&&r.clipboardData.setData("text/html",t),this.getOrCreateFocusableElement(),this.onPaste(r)}}setCopyableText(){var e=this.hot.getSelectedRangeLast();if(e){var t=e.getTopLeftCorner(),r=e.getBottomRightCorner(),o=t.row,n=t.col,i=r.row,s=r.col,a=Math.min(i,o+this.rowsLimit-1),l=Math.min(s,n+this.columnsLimit-1);this.copyableRanges.length=0,this.copyableRanges.push({startRow:o,startCol:n,endRow:a,endCol:l}),this.copyableRanges=this.hot.runHooks("modifyCopyableRange",this.copyableRanges),i===a&&s===l||this.hot.runHooks("afterCopyLimit",i-o+1,s-n+1,this.rowsLimit,this.columnsLimit)}}getOrCreateFocusableElement(){var e=this.hot.getActiveEditor(),t=e?e.TEXTAREA:void 0;t?this.focusableElement.setFocusableElement(t):this.focusableElement.useSecondaryElement()}isEditorOpened(){var e=this.hot.getActiveEditor();return e&&e.isOpened()}populateValues(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.hot.getSelectedLast();if(e.length){for(var r=e.length-1,o=e[0].length-1,n=Math.min(t[0],t[2]),i=Math.max(t[0],t[2],r+n),s=Math.min(t[1],t[3]),a=Math.max(t[1],t[3],o+s),l=[],c=n,h=0;c<=i;c+=1){for(var d=[],u=s,g=0;u<=a;u+=1)d.push(e[h][g]),g=g===o?0:g+=1;l.push(d),h=h===r?0:h+=1}return this.hot.populateFromArray(n,s,l,void 0,void 0,"CopyPaste.paste",this.pasteMode),[n,s,i,a]}}onCopy(e){var t=F.get(this);if((this.hot.isListening()||t.isTriggeredByCopy)&&!this.isEditorOpened()){this.setCopyableText(),t.isTriggeredByCopy=!1;var r=this.getRangedData(this.copyableRanges);if(!!this.hot.runHooks("beforeCopy",r,this.copyableRanges)){var o=S.a.stringify(r);if(e&&e.clipboardData){var n=Object(B._dataToHTML)(r,this.hot.rootDocument);e.clipboardData.setData("text/plain",o),e.clipboardData.setData("text/html",[V,n].join(""))}else"undefined"==typeof ClipboardEvent&&this.hot.rootWindow.clipboardData.setData("Text",o);this.hot.runHooks("afterCopy",r,this.copyableRanges)}e.preventDefault()}}onCut(e){var t=F.get(this);if((this.hot.isListening()||t.isTriggeredByCut)&&!this.isEditorOpened()){this.setCopyableText(),t.isTriggeredByCut=!1;var r=this.getRangedData(this.copyableRanges);if(!!this.hot.runHooks("beforeCut",r,this.copyableRanges)){var o=S.a.stringify(r);if(e&&e.clipboardData){var n=Object(B._dataToHTML)(r,this.hot.rootDocument);e.clipboardData.setData("text/plain",o),e.clipboardData.setData("text/html",[V,n].join(""))}else"undefined"==typeof ClipboardEvent&&this.hot.rootWindow.clipboardData.setData("Text",o);this.hot.emptySelectedCells("CopyPaste.cut"),this.hot.runHooks("afterCut",r,this.copyableRanges)}e.preventDefault()}}onPaste(e){if(this.hot.isListening()&&!this.isEditorOpened()){var t;if(e&&e.preventDefault&&e.preventDefault(),e&&void 0!==e.clipboardData){var r=e.clipboardData.getData("text/html");if(r&&/(<table)|(<TABLE)/g.test(r))t=Object(B.htmlToGridSettings)(r,this.hot.rootDocument).data;else t=e.clipboardData.getData("text/plain")}else"undefined"==typeof ClipboardEvent&&void 0!==this.hot.rootWindow.clipboardData&&(t=this.hot.rootWindow.clipboardData.getData("Text"));if("string"==typeof t&&(t=S.a.parse(t)),(!t||0!==t.length)&&!1!==this.hot.runHooks("beforePaste",t,this.copyableRanges)){var[o,n,i,s]=this.populateValues(t);this.hot.selectCell(o,n,Math.min(this.hot.countRows()-1,i),Math.min(this.hot.countCols()-1,s)),this.hot.runHooks("afterPaste",t,this.copyableRanges)}}}onAfterContextMenuDefaultOptions(e){var t;e.items.push({name:"---------"},(t=this,{key:"copy",name(){return this.getTranslatedPhrase(M.CONTEXTMENU_ITEMS_COPY)},callback(){t.copy()},disabled(){var e=this.getSelected();return!e||e.length>1},hidden:!1}),function(e){return{key:"cut",name(){return this.getTranslatedPhrase(M.CONTEXTMENU_ITEMS_CUT)},callback(){e.cut()},disabled(){var e=this.getSelected();return!e||e.length>1},hidden:!1}}(this))}onAfterOnCellMouseUp(){this.hot.isListening()&&!this.isEditorOpened()&&(this.getOrCreateFocusableElement(),this.focusableElement.focus())}onAfterSelectionEnd(){var{isFragmentSelectionEnabled:e}=F.get(this);this.isEditorOpened()||(this.getOrCreateFocusableElement(),e&&this.focusableElement.getFocusableElement()!==this.hot.rootDocument.activeElement&&Object(E.getSelectionText)()||(this.setCopyableText(),this.focusableElement.focus()))}onBeforeKeyDown(){if(this.hot.isListening()&&!this.isEditorOpened()){var e=this.hot.rootDocument.activeElement,t=this.hot.getActiveEditor();!t||e!==this.focusableElement.getFocusableElement()&&e!==t.select||(this.getOrCreateFocusableElement(),this.focusableElement.focus())}}destroy(){this.focusableElement&&(P(this.focusableElement),this.focusableElement=null),super.destroy()}}Object(i.d)("CopyPaste",W);var z=W;function U(e,t){return"border_row".concat(e,"col").concat(t)}function G(e,t){return{id:U(e,t),border:{width:1,color:"#000",cornerVisible:!1},row:e,col:t,top:{hide:!0},right:{hide:!0},bottom:{hide:!0},left:{hide:!0}}}function X(e,t){var r=!1;return Object(n.arrayEach)(e.getSelectedRange(),n=>{n.forAll((n,i)=>{var s=e.getCellMeta(n,i).borders;if(s){if(!t)return r=!0,!1;if(!Object(o.hasOwnProperty)(s[t],"hide")||!1===s[t].hide)return r=!0,!1}})}),r}function K(e){return'<span class="selected">'.concat(String.fromCharCode(10003),"</span>").concat(e)}function Y(e){return{key:"borders:bottom",name(){var e=this.getTranslatedPhrase(M.CONTEXTMENU_ITEMS_BORDERS_BOTTOM);return X(this,"bottom")&&(e=K(e)),e},callback(t,r){var o=X(this,"bottom");e.prepareBorder(r,"bottom",o),e.render()}}}function q(e){return{key:"borders:left",name(){var e=this.getTranslatedPhrase(M.CONTEXTMENU_ITEMS_BORDERS_LEFT);return X(this,"left")&&(e=K(e)),e},callback(t,r){var o=X(this,"left");e.prepareBorder(r,"left",o),e.render()}}}function Q(e){return{key:"borders:no_borders",name(){return this.getTranslatedPhrase(M.CONTEXTMENU_ITEMS_REMOVE_BORDERS)},callback(t,r){e.prepareBorder(r,"noBorders"),e.render()},disabled(){return!X(this)}}}function Z(e){return{key:"borders:right",name(){var e=this.getTranslatedPhrase(M.CONTEXTMENU_ITEMS_BORDERS_RIGHT);return X(this,"right")&&(e=K(e)),e},callback(t,r){var o=X(this,"right");e.prepareBorder(r,"right",o),e.render()}}}var J=r(123);class $ extends l{constructor(e){super(e),this.savedBorders=[],this.savedBordersById=new Map}isEnabled(){return!!this.hot.getSettings().customBorders}enablePlugin(){this.enabled||(this.addHook("afterContextMenuDefaultOptions",e=>this.onAfterContextMenuDefaultOptions(e)),this.addHook("init",()=>this.onAfterInit()),super.enablePlugin())}disablePlugin(){this.hideBorders(),this.render(),super.disablePlugin()}updatePlugin(){this.disablePlugin(),this.enablePlugin(),this.changeBorderSettings(),super.updatePlugin()}setBorders(e,t){var r=this,o=t?Object.keys(t):["top","right","bottom","left"],i=Object(J.b)(e),s=Object(J.c)(i);Object(n.arrayEach)(e,e=>{for(var[i,a,l,c]=s(e),h=function(e){for(var i=function(i){Object(n.arrayEach)(o,o=>{r.prepareBorderFromCustomAdded(e,i,t,o)})},s=a;s<=c;s+=1)i(s)},d=i;d<=l;d+=1)h(d)}),this.render()}render(){this.hot.view.render()}getBorders(e){var t=this;if(!Array.isArray(e))return this.savedBorders;var r=Object(J.b)(e),o=Object(J.c)(r),i=[];return Object(n.arrayEach)(e,e=>{for(var[r,s,a,l]=o(e),c=function(e){for(var r=function(r){Object(n.arrayEach)(t.savedBorders,t=>{t.row===e&&t.col===r&&i.push(t)})},o=s;o<=l;o+=1)r(o)},h=r;h<=a;h+=1)c(h)}),i}clearBorders(e){e?this.setBorders(e):(Object(n.arrayEach)(this.savedBorders,e=>{this.clearBordersFromSelectionSettings(e.id),this.hot.removeCellMeta(e.row,e.col,"borders")}),this.savedBorders.length=0,this.savedBordersById.clear()),this.render()}insertBorderIntoSettings(e){var t=this.savedBordersById.get(e.id);if(t){var r=this.savedBorders.indexOf(t);this.savedBorders[r]=e}else this.savedBorders.push(e);this.savedBordersById.set(e.id,e);var o={row:e.row,col:e.col},n=new p.b(o,o,o);this.checkCustomSelections(e,n)||this.hot.selection.highlight.addCustomSelection(e.id,{border:e,cellRange:n})}prepareBorderFromCustomAdded(e,t,r,n){var i,s,a=G(e,t);if(r){i=a,s=r,Object(o.hasOwnProperty)(s,"border")&&(i.border=s.border),Object(o.hasOwnProperty)(s,"top")&&(s.top?(Object(o.isObject)(s.top)||(s.top={width:1,color:"#000"}),i.top=s.top):(s.top={hide:!0},i.top=s.top)),Object(o.hasOwnProperty)(s,"right")&&(s.right?(Object(o.isObject)(s.right)||(s.right={width:1,color:"#000"}),i.right=s.right):(s.right={hide:!0},i.right=s.right)),Object(o.hasOwnProperty)(s,"bottom")&&(s.bottom?(Object(o.isObject)(s.bottom)||(s.bottom={width:1,color:"#000"}),i.bottom=s.bottom):(s.bottom={hide:!0},i.bottom=s.bottom)),Object(o.hasOwnProperty)(s,"left")&&(s.left?(Object(o.isObject)(s.left)||(s.left={width:1,color:"#000"}),i.left=s.left):(s.left={hide:!0},i.left=s.left)),a=i;var l=this.hot.selection.highlight.customSelections.get(a.id);l&&(Object.assign(l.settings,r),a=l.settings)}this.hot.setCellMeta(e,t,"borders",a),this.insertBorderIntoSettings(a,n)}prepareBorderFromCustomAddedRange(e){var t=e.range;Object(d.rangeEach)(t.from.row,t.to.row,r=>{Object(d.rangeEach)(t.from.col,t.to.col,n=>{var i=G(r,n),s=0;r===t.from.row&&Object(o.hasOwnProperty)(e,"top")&&(s+=1,i.top=e.top),r===t.to.row&&Object(o.hasOwnProperty)(e,"bottom")&&(s+=1,i.bottom=e.bottom),n===t.from.col&&Object(o.hasOwnProperty)(e,"left")&&(s+=1,i.left=e.left),n===t.to.col&&Object(o.hasOwnProperty)(e,"right")&&(s+=1,i.right=e.right),s>0&&(this.hot.setCellMeta(r,n,"borders",i),this.insertBorderIntoSettings(i))})})}removeAllBorders(e,t){var r=U(e,t);this.spliceBorder(r),this.clearBordersFromSelectionSettings(r),this.hot.removeCellMeta(e,t,"borders")}setBorder(e,t,r,o){var n=this.hot.getCellMeta(e,t).borders;(n&&void 0!==n.border||(n=G(e,t)),o)?(n[r]={hide:!0},this.areAllEdgesDisabled(n)?this.removeAllBorders(e,t):(this.checkCustomSelectionsFromContextMenu(n,r,o)||this.insertBorderIntoSettings(n),this.hot.setCellMeta(e,t,"borders",n))):(n[r]={width:1,color:"#000"},this.checkCustomSelectionsFromContextMenu(n,r,o)||this.insertBorderIntoSettings(n),this.hot.setCellMeta(e,t,"borders",n))}prepareBorder(e,t,r){Object(n.arrayEach)(e,e=>{var{start:o,end:n}=e;if(o.row===n.row&&o.col===n.col)"noBorders"===t?this.removeAllBorders(o.row,o.col):this.setBorder(o.row,o.col,t,r);else switch(t){case"noBorders":Object(d.rangeEach)(o.col,n.col,e=>{Object(d.rangeEach)(o.row,n.row,t=>{this.removeAllBorders(t,e)})});break;case"top":Object(d.rangeEach)(o.col,n.col,e=>{this.setBorder(o.row,e,t,r)});break;case"right":Object(d.rangeEach)(o.row,n.row,e=>{this.setBorder(e,n.col,t,r)});break;case"bottom":Object(d.rangeEach)(o.col,n.col,e=>{this.setBorder(n.row,e,t,r)});break;case"left":Object(d.rangeEach)(o.row,n.row,e=>{this.setBorder(e,o.col,t,r)})}})}createCustomBorders(e){Object(n.arrayEach)(e,e=>{e.range?this.prepareBorderFromCustomAddedRange(e):this.prepareBorderFromCustomAdded(e.row,e.col,e)})}areAllEdgesDisabled(e){return!this.isEdgeEnabled(e.left)&&(!this.isEdgeEnabled(e.right)&&(!this.isEdgeEnabled(e.top)&&!this.isEdgeEnabled(e.bottom)))}isEdgeEnabled(e){return void 0===e||void 0===e.hide||!e.hide}clearBordersFromSelectionSettings(e){var t=this.hot.selection.highlight.customSelections.get(e);t&&(t.clear(),this.hot.selection.highlight.customSelections.delete(t.settings.id))}hideBorders(){Object(n.arrayEach)(this.savedBorders,e=>{this.clearBordersFromSelectionSettings(e.id)})}spliceBorder(e){var t=this.savedBordersById.get(e);if(t){var r=this.savedBorders.indexOf(t);this.savedBorders.splice(r,1),this.savedBordersById.delete(e)}}checkCustomSelectionsFromContextMenu(e){return this.hot.selection.highlight.customSelections.has(e.id)}checkCustomSelections(e,t){if(this.areAllEdgesDisabled(e))return this.removeAllBorders(e.row,e.col),!0;var r=this.hot.selection.highlight.customSelections.get(e.id);return!!r&&(r.cellRange=t,!0)}changeBorderSettings(){var e=this.hot.getSettings().customBorders;Array.isArray(e)?(e.length||(this.savedBorders=e),this.createCustomBorders(e)):void 0!==e&&this.createCustomBorders(this.savedBorders)}onAfterContextMenuDefaultOptions(e){var t;this.hot.getSettings().customBorders&&e.items.push({name:"---------"},{key:"borders",name(){return this.getTranslatedPhrase(M.CONTEXTMENU_ITEMS_BORDERS)},disabled(){return this.selection.isSelectedByCorner()},submenu:{items:[(t=this,{key:"borders:top",name(){var e=this.getTranslatedPhrase(M.CONTEXTMENU_ITEMS_BORDERS_TOP);return X(this,"top")&&(e=K(e)),e},callback(e,r){var o=X(this,"top");t.prepareBorder(r,"top",o),t.render()}}),Z(this),Y(this),q(this),Q(this)]}})}onAfterInit(){this.changeBorderSettings()}destroy(){super.destroy()}}Object(i.d)("customBorders",$);var ee=$,te=r(57),re=r(54);function oe(){var e=ae(["The merged cell declared at [",", ",'] has "rowspan" or "colspan" declared as \n      "0", which is not supported. It cannot be added to the collection.'],["The merged cell declared at [",", ",'] has "rowspan" or "colspan" declared as\\x20\n      "0", which is not supported. It cannot be added to the collection.']);return oe=function(){return e},e}function ne(){var e=ae(["The merged cell declared at [",", ",'] has both "rowspan" \n     and "colspan" declared as "1", which makes it a single cell. It cannot be added to the collection.'],["The merged cell declared at [",", ",'] has both "rowspan"\\x20\n     and "colspan" declared as "1", which makes it a single cell. It cannot be added to the collection.']);return ne=function(){return e},e}function ie(){var e=ae(["The merged cell declared at [",", ","] is positioned (or positioned partially) \n       outside of the table range. It was not added to the table, please fix your setup."],["The merged cell declared at [",", ","] is positioned (or positioned partially)\\x20\n       outside of the table range. It was not added to the table, please fix your setup."]);return ie=function(){return e},e}function se(){var e=ae(["The merged cell declared with {row: ",", col: ",", rowspan: \n    ",", colspan: ","} contains negative values, which is not supported. It \n    will not be added to the collection."],["The merged cell declared with {row: ",", col: ",", rowspan:\\x20\n    ",", colspan: ","} contains negative values, which is not supported. It\\x20\n    will not be added to the collection."]);return se=function(){return e},e}function ae(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}var le=class{constructor(e,t,r,o){this.row=e,this.col=t,this.rowspan=r,this.colspan=o,this.removed=!1}static NEGATIVE_VALUES_WARNING(e){return Object(re.a)(se(),e.row,e.col,e.rowspan,e.colspan)}static IS_OUT_OF_BOUNDS_WARNING(e){return Object(re.a)(ie(),e.row,e.col)}static IS_SINGLE_CELL(e){return Object(re.a)(ne(),e.row,e.col)}static ZERO_SPAN_WARNING(e){return Object(re.a)(oe(),e.row,e.col)}static containsNegativeValues(e){return e.row<0||e.col<0||e.rowspan<0||e.colspan<0}static isSingleCell(e){return 1===e.colspan&&1===e.rowspan}static containsZeroSpan(e){return 0===e.colspan||0===e.rowspan}static isOutOfBounds(e,t,r){return e.row<0||e.col<0||e.row>=t||e.row+e.rowspan-1>=t||e.col>=r||e.col+e.colspan-1>=r}normalize(e){var t=e.countRows(),r=e.countCols();this.row<0?this.row=0:this.row>t-1&&(this.row=t-1),this.col<0?this.col=0:this.col>r-1&&(this.col=r-1),this.row+this.rowspan>t-1&&(this.rowspan=t-this.row),this.col+this.colspan>r-1&&(this.colspan=r-this.col)}includes(e,t){return this.row<=e&&this.col<=t&&this.row+this.rowspan-1>=e&&this.col+this.colspan-1>=t}includesHorizontally(e){return this.col<=e&&this.col+this.colspan-1>=e}includesVertically(e){return this.row<=e&&this.row+this.rowspan-1>=e}shift(e,t){var r=e[0]||e[1],o=t+Math.abs(e[0]||e[1])-1,n=e[0]?"colspan":"rowspan",i=e[0]?"col":"row",s=Math.min(t,o),a=Math.max(t,o),l=this[i],c=this[i]+this[n]-1;if(l>=t&&(this[i]+=r),r>0)t<=c&&t>l&&(this[n]+=r);else if(r<0){if(s<=l&&a>=c)return this.removed=!0,!1;if(l>=s&&l<=a){var h=a-l+1,d=Math.abs(r)-h;this[i]-=d+r,this[n]-=h}else if(l<=s&&c>=a)this[n]+=r;else if(l<=s&&c>=s&&c<a){var u=c-s+1;this[n]-=u}}return!0}isFarther(e,t){return!e||("down"===t?e.row+e.rowspan-1<this.row+this.rowspan-1:"up"===t?e.row>this.row:"right"===t?e.col+e.colspan-1<this.col+this.colspan-1:"left"===t?e.col>this.col:null)}getLastRow(){return this.row+this.rowspan-1}getLastColumn(){return this.col+this.colspan-1}getRange(){return new p.b(new p.a(this.row,this.col),new p.a(this.row,this.col),new p.a(this.getLastRow(),this.getLastColumn()))}},ce=r(60);function he(e,t,r,o){t?t.row===r&&t.col===o?(e.setAttribute("rowspan",t.rowspan.toString()),e.setAttribute("colspan",t.colspan.toString())):(e.removeAttribute("rowspan"),e.removeAttribute("colspan"),e.style.display="none"):(e.removeAttribute("rowspan"),e.removeAttribute("colspan"),e.style.display="")}function de(){var e=function(e,t){t||(t=e.slice(0));return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(["The merged cell declared at [",", ","], overlaps with the other declared merged \n    cell. The overlapping merged cell was not added to the table, please fix your setup."],["The merged cell declared at [",", ","], overlaps with the other declared merged\\x20\n    cell. The overlapping merged cell was not added to the table, please fix your setup."]);return de=function(){return e},e}class ue{constructor(e){this.plugin=e,this.mergedCells=[],this.hot=e.hot}static IS_OVERLAPPING_WARNING(e){return Object(re.a)(de(),e.row,e.col)}get(e,t){var r=this.mergedCells,o=!1;return Object(n.arrayEach)(r,r=>!(r.row<=e&&r.row+r.rowspan-1>=e&&r.col<=t&&r.col+r.colspan-1>=t)||(o=r,!1)),o}getByRange(e){var t=this.mergedCells,r=!1;return Object(n.arrayEach)(t,t=>!(t.row<=e.from.row&&t.row+t.rowspan-1>=e.to.row&&t.col<=e.from.col&&t.col+t.colspan-1>=e.to.col)||(r=t)),r}getWithinRange(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.mergedCells,o=[],i=e;if(!i.includesRange){var s=new p.a(i.from.row,i.from.col),a=new p.a(i.to.row,i.to.col);i=new p.b(s,s,a)}return Object(n.arrayEach)(r,e=>{var r=new p.a(e.row,e.col),n=new p.a(e.row+e.rowspan-1,e.col+e.colspan-1),s=new p.b(r,r,n);t?i.overlaps(s)&&o.push(e):i.includesRange(s)&&o.push(e)}),!!o.length&&o}add(e){var t=this.mergedCells,r=e.row,o=e.col,n=e.rowspan,i=e.colspan,s=new le(r,o,n,i),a=this.get(r,o),l=this.isOverlapping(s);return a||l?(Object(ce.b)(ue.IS_OVERLAPPING_WARNING(s)),!1):(this.hot&&s.normalize(this.hot),t.push(s),s)}remove(e,t){var r=this.mergedCells,o=this.get(e,t),n=o?this.mergedCells.indexOf(o):null;return!(!o||!1===n)&&(r.splice(n,1),o)}clear(){var e=this.mergedCells,t=[],r=[];Object(n.arrayEach)(e,e=>{var r=this.hot.getCell(e.row,e.col);r&&t.push([r,this.get(e.row,e.col),e.row,e.col])}),this.mergedCells.length=0,Object(n.arrayEach)(t,(e,o)=>{Object(d.rangeEach)(0,e.rowspan-1,t=>{Object(d.rangeEach)(0,e.colspan-1,o=>{if(0!==o||0!==t){var n=this.hot.getCell(e.row+t,e.col+o);n&&r.push([n,null,null,null])}})}),t[o][1]=null}),Object(n.arrayEach)(t,e=>{he(...e)}),Object(n.arrayEach)(r,e=>{he(...e)})}isOverlapping(e){var t=new p.b(null,new p.a(e.row,e.col),new p.a(e.row+e.rowspan-1,e.col+e.colspan-1)),r=!1;return Object(n.arrayEach)(this.mergedCells,e=>!new p.b(null,new p.a(e.row,e.col),new p.a(e.row+e.rowspan-1,e.col+e.colspan-1)).overlaps(t)||(r=!0,!1)),r}isMergedParent(e,t){var r=this.mergedCells,o=!1;return Object(n.arrayEach)(r,r=>r.row!==e||r.col!==t||(o=!0,!1)),o}shiftCollections(e,t,r){var o=[0,0];switch(e){case"right":o[0]+=r;break;case"left":o[0]-=r;break;case"down":o[1]+=r;break;case"up":o[1]-=r}Object(n.arrayEach)(this.mergedCells,e=>{e.shift(o,t)}),Object(d.rangeEachReverse)(this.mergedCells.length-1,0,e=>{var t=this.mergedCells[e];t&&t.removed&&this.mergedCells.splice(this.mergedCells.indexOf(t),1)})}}var ge=ue;var fe=class{constructor(e){this.plugin=e,this.mergedCellsCollection=this.plugin.mergedCellsCollection,this.currentFillData=null}correctSelectionAreaSize(e){if(e[0]===e[2]&&e[1]===e[3]){var t=this.mergedCellsCollection.get(e[0],e[1]);t&&(e[2]=e[0]+t.rowspan-1,e[3]=e[1]+t.colspan-1)}}getDirection(e,t){return t[0]===e[0]&&t[1]===e[1]&&t[3]===e[3]?"down":t[2]===e[2]&&t[1]===e[1]&&t[3]===e[3]?"up":t[1]===e[1]&&t[2]===e[2]?"right":"left"}snapDragArea(e,t,r,o){var n=t.slice(0),i=this.getAutofillSize(e,t,r),[s,a,l,c]=e,h=["up","down"].indexOf(r)>-1?l-s+1:c-a+1,d=i-Math.floor(i/h)*h,u=this.getFarthestCollection(e,t,r,o);if(u)if("down"===r){var g=u.row+u.rowspan-s-d;n[2]+g>=this.plugin.hot.countRows()?n[2]-=d:n[2]+=d?g:0}else if("right"===r){var f=u.col+u.colspan-a-d;n[3]+f>=this.plugin.hot.countCols()?n[3]-=d:n[3]+=d?f:0}else if("up"===r){var m=l-d-u.row+1;n[0]+m<0?n[0]+=d:n[0]-=d?m:0}else if("left"===r){var p=c-d-u.col+1;n[1]+p<0?n[1]+=d:n[1]-=d?p:0}return this.updateCurrentFillCache({baseArea:e,dragDirection:r,foundMergedCells:o,fillSize:i,dragArea:n,cycleLength:h}),n}updateCurrentFillCache(e){this.currentFillData||(this.currentFillData={}),Object(o.extend)(this.currentFillData,e)}getAutofillSize(e,t,r){var[o,n,i,s]=e,[a,l,c,h]=t;switch(r){case"up":return o-a;case"down":return c-i;case"left":return n-l;case"right":return h-s;default:return null}}getDragArea(e,t,r){var[o,n,i,s]=e,[a,l,c,h]=t;switch(r){case"up":return[a,l,o-1,s];case"down":return[i+1,n,c,s];case"left":return[a,l,i,n-1];case"right":return[o,s+1,c,h];default:return null}}getFarthestCollection(e,t,r,o){var[i,s,a,l]=e,c=["up","down"].indexOf(r)>-1,h=c?a:l,d=c?i:s,u=this.getAutofillSize(e,t,r),g=c?a-i+1:l-s+1,f=u-Math.floor(u/g)*g,m=null,p=null,v=null;switch(r){case"up":m="includesVertically",v=h-f+1;break;case"left":m="includesHorizontally",v=h-f+1;break;case"down":m="includesVertically",v=d+f-1;break;case"right":m="includesHorizontally",v=d+f-1}return Object(n.arrayEach)(o,e=>{e[m](v)&&e.isFarther(p,r)&&(p=e)}),p}recreateAfterDataPopulation(e){if(this.currentFillData){var t=this.getRangeFromChanges(e),r=this.currentFillData.foundMergedCells,o=this.currentFillData.dragDirection,n=(e,r)=>{switch(o){case"up":return e.row-r>=t.from.row;case"down":return e.row+e.rowspan-1+r<=t.to.row;case"left":return e.col-r>=t.from.column;case"right":return e.col+e.colspan-1+r<=t.to.column;default:return null}},i=0,s=null,a=1;do{for(var l=0;l<r.length;l+=1){if(n(s=r[l],i=a*this.currentFillData.cycleLength))switch(o){case"up":this.plugin.mergedCellsCollection.add({row:s.row-i,rowspan:s.rowspan,col:s.col,colspan:s.colspan});break;case"down":this.plugin.mergedCellsCollection.add({row:s.row+i,rowspan:s.rowspan,col:s.col,colspan:s.colspan});break;case"left":this.plugin.mergedCellsCollection.add({row:s.row,rowspan:s.rowspan,col:s.col-i,colspan:s.colspan});break;case"right":this.plugin.mergedCellsCollection.add({row:s.row,rowspan:s.rowspan,col:s.col+i,colspan:s.colspan})}l===r.length-1&&(a+=1)}}while(n(s,i));this.currentFillData=null,this.plugin.hot.render()}}getRangeFromChanges(e){var t={min:null,max:null},r={min:null,max:null};return Object(n.arrayEach)(e,e=>{var o=e[0],n=this.plugin.hot.propToCol(e[1]);(null===t.min||o<t.min)&&(t.min=o),(null===t.max||o>t.max)&&(t.max=o),(null===r.min||n<r.min)&&(r.min=n),(null===r.max||n>r.max)&&(r.max=n)}),{from:{row:t.min,column:r.min},to:{row:t.max,column:r.max}}}dragAreaOverlapsCollections(e,t,r){var o=this.getDragArea(e,t,r),[n,i,s,a]=o,l=new p.a(n,i),c=new p.a(s,a),h=new p.b(l,l,c);return!!this.mergedCellsCollection.getWithinRange(h,!0)}};var me=class{constructor(e){this.plugin=e,this.fullySelectedMergedCellClassName="fullySelectedMergedCell"}snapDelta(e,t,r){var o=t.to,n=o.row+e.row,i=o.col+e.col;e.row?this.jumpOverMergedCell(e,r,n):e.col&&this.jumpOverMergedCell(e,r,i)}jumpOverMergedCell(e,t,r){var o=e.row||e.col,n=null,i=null,s=null;e.row?(n=t.includesVertically(r),i=t.row,s=t.getLastRow()):e.col&&(n=t.includesHorizontally(r),i=t.col,s=t.getLastColumn()),0!==o&&(o>0?n&&r!==i&&(o+=s-r+1):n&&r!==s&&(o-=r-i+1),e.row?e.row=o:e.col&&(e.col=o))}getUpdatedSelectionRange(e,t){return new p.b(e.highlight,e.from,new p.a(e.to.row+t.row,e.to.col+t.col))}getSelectedMergedCellClassName(e,t,r,o){var[n,i,s,a]=r;if(void 0!==o&&e>=n&&e<=s&&t>=i&&t<=a){if(!this.plugin.mergedCellsCollection.isMergedParent(e,t))return;var l=this.plugin.mergedCellsCollection.get(e,t);if(!l)return;if(l.row+l.rowspan-1<=s&&l.col+l.colspan-1<=a)return"".concat(this.fullySelectedMergedCellClassName,"-").concat(o);if(this.plugin.selectionCalculations.isMergeCellFullySelected(l,this.plugin.hot.getSelectedRange()))return"".concat(this.fullySelectedMergedCellClassName,"-multiple")}}isMergeCellFullySelected(e,t){var r=[];if(!t||!e)return!1;for(var o=0;o<e.rowspan;o+=1)for(var n=0;n<e.colspan;n+=1)r.push(new p.a(e.row+o,e.col+n));for(var i=0;i<r.length;i+=1){for(var s=[],a=0;a<t.length;a+=1)s[a]=t[a].includes(r[i]);if(!s.includes(!0))return!1}return!0}getSelectedMergedCellClassNameToRemove(){for(var e=[],t=0;t<=7;t+=1)e.push("".concat(this.fullySelectedMergedCellClassName,"-").concat(t));return e.push("".concat(this.fullySelectedMergedCellClassName,"-multiple")),e}};R.a.getSingleton().register("beforeMergeCells"),R.a.getSingleton().register("afterMergeCells"),R.a.getSingleton().register("beforeUnmergeCells"),R.a.getSingleton().register("afterUnmergeCells");var pe=new WeakMap;class ve extends l{constructor(e){super(e),pe.set(this,{lastDesiredCoords:null}),this.mergedCellsCollection=null,this.autofillCalculations=null,this.selectionCalculations=null}isEnabled(){return!!this.hot.getSettings().mergeCells}enablePlugin(){var e=this;this.enabled||(this.mergedCellsCollection=new ge(this),this.autofillCalculations=new fe(this),this.selectionCalculations=new me(this),this.addHook("afterInit",(function(){return e.onAfterInit(...arguments)})),this.addHook("beforeKeyDown",(function(){return e.onBeforeKeyDown(...arguments)})),this.addHook("modifyTransformStart",(function(){return e.onModifyTransformStart(...arguments)})),this.addHook("afterModifyTransformStart",(function(){return e.onAfterModifyTransformStart(...arguments)})),this.addHook("modifyTransformEnd",(function(){return e.onModifyTransformEnd(...arguments)})),this.addHook("modifyGetCellCoords",(function(){return e.onModifyGetCellCoords(...arguments)})),this.addHook("beforeSetRangeEnd",(function(){return e.onBeforeSetRangeEnd(...arguments)})),this.addHook("afterIsMultipleSelection",(function(){return e.onAfterIsMultipleSelection(...arguments)})),this.addHook("afterRenderer",(function(){return e.onAfterRenderer(...arguments)})),this.addHook("afterContextMenuDefaultOptions",(function(){return e.addMergeActionsToContextMenu(...arguments)})),this.addHook("afterGetCellMeta",(function(){return e.onAfterGetCellMeta(...arguments)})),this.addHook("afterViewportRowCalculatorOverride",(function(){return e.onAfterViewportRowCalculatorOverride(...arguments)})),this.addHook("afterViewportColumnCalculatorOverride",(function(){return e.onAfterViewportColumnCalculatorOverride(...arguments)})),this.addHook("modifyAutofillRange",(function(){return e.onModifyAutofillRange(...arguments)})),this.addHook("afterCreateCol",(function(){return e.onAfterCreateCol(...arguments)})),this.addHook("afterRemoveCol",(function(){return e.onAfterRemoveCol(...arguments)})),this.addHook("afterCreateRow",(function(){return e.onAfterCreateRow(...arguments)})),this.addHook("afterRemoveRow",(function(){return e.onAfterRemoveRow(...arguments)})),this.addHook("afterChange",(function(){return e.onAfterChange(...arguments)})),this.addHook("beforeDrawBorders",(function(){return e.onBeforeDrawAreaBorders(...arguments)})),this.addHook("afterDrawSelection",(function(){return e.onAfterDrawSelection(...arguments)})),this.addHook("beforeRemoveCellClassNames",(function(){return e.onBeforeRemoveCellClassNames(...arguments)})),super.enablePlugin())}disablePlugin(){this.clearCollections(),this.hot.render(),super.disablePlugin()}updatePlugin(){var e=this.hot.getSettings().mergeCells;this.disablePlugin(),this.enablePlugin(),this.generateFromSettings(e),super.updatePlugin()}validateSetting(e){var t=!0;return!!e&&(le.containsNegativeValues(e)?(Object(ce.b)(le.NEGATIVE_VALUES_WARNING(e)),t=!1):le.isOutOfBounds(e,this.hot.countRows(),this.hot.countCols())?(Object(ce.b)(le.IS_OUT_OF_BOUNDS_WARNING(e)),t=!1):le.isSingleCell(e)?(Object(ce.b)(le.IS_SINGLE_CELL(e)),t=!1):le.containsZeroSpan(e)&&(Object(ce.b)(le.ZERO_SPAN_WARNING(e)),t=!1),t)}generateFromSettings(e){if(Array.isArray(e)){var t=[];Object(n.arrayEach)(e,e=>{if(this.validateSetting(e)){var r=new p.a(e.row,e.col),o=new p.a(e.row+e.rowspan-1,e.col+e.colspan-1),n=new p.b(r,r,o);t.push(this.mergeRange(n,!0,!0))}}),t=t.filter(e=>!0!==e);var r=this.getBulkCollectionData(t);this.hot.populateFromArray(...r)}}getBulkCollectionData(e){var t=this.getBulkCollectionDataRange(e),r=this.hot.getData(...t).splice(0);return Object(n.arrayEach)(e,e=>{var[o,i,s]=e;Object(n.arrayEach)(s,(e,s)=>{Object(n.arrayEach)(e,(e,n)=>{r[o-t[0]+s][i-t[1]+n]=e})})}),[t[0],t[1],r]}getBulkCollectionDataRange(e){var t=[0,0],r=[0,0],o=null,i=null,s=null;return Object(n.arrayEach)(e,e=>{o=e[0],i=e[1],s=e[2],t[0]=Math.min(o,t[0]),t[1]=Math.min(i,t[1]),r[0]=Math.max(o+s.length-1,r[0]),r[1]=Math.max(i+s[0].length-1,r[1])}),[...t,...r]}clearCollections(){this.mergedCellsCollection.clear()}canMergeRange(e){return!!(arguments.length>1&&void 0!==arguments[1]&&arguments[1])||this.validateSetting(e)}toggleMergeOnSelection(){var e=this.hot.getSelectedRangeLast();if(e){e.setDirection("NW-SE");var{from:t,to:r}=e;this.toggleMerge(e),this.hot.selectCell(t.row,t.col,r.row,r.col,!1)}}mergeSelection(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.hot.getSelectedRangeLast();if(e){e.setDirection("NW-SE");var{from:t,to:r}=e;this.unmergeRange(e,!0),this.mergeRange(e),this.hot.selectCell(t.row,t.col,r.row,r.col,!1)}}unmergeSelection(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.hot.getSelectedRangeLast();if(e){var{from:t,to:r}=e;this.unmergeRange(e,!0),this.hot.selectCell(t.row,t.col,r.row,r.col,!1)}}mergeRange(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=e.getTopLeftCorner(),n=e.getBottomRightCorner(),i={row:o.row,col:o.col,rowspan:n.row-o.row+1,colspan:n.col-o.col+1},s=[],a=null;return!!this.canMergeRange(i,t)&&(this.hot.runHooks("beforeMergeCells",e,t),Object(d.rangeEach)(0,i.rowspan-1,e=>{Object(d.rangeEach)(0,i.colspan-1,t=>{var r=null;s[e]||(s[e]=[]),0===e&&0===t?r=this.hot.getDataAtCell(i.row,i.col):this.hot.setCellMeta(i.row+e,i.col+t,"hidden",!0),s[e][t]=r})}),this.hot.setCellMeta(i.row,i.col,"spanned",!0),!this.mergedCellsCollection.add(i)||(r?a=[i.row,i.col,s]:this.hot.populateFromArray(i.row,i.col,s,void 0,void 0,this.pluginName),this.hot.runHooks("afterMergeCells",e,i,t),a))}unmergeRange(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.mergedCellsCollection.getWithinRange(e);r&&(this.hot.runHooks("beforeUnmergeCells",e,t),Object(n.arrayEach)(r,e=>{this.mergedCellsCollection.remove(e.row,e.col),Object(d.rangeEach)(0,e.rowspan-1,t=>{Object(d.rangeEach)(0,e.colspan-1,r=>{this.hot.removeCellMeta(e.row+t,e.col+r,"hidden")})}),this.hot.removeCellMeta(e.row,e.col,"spanned")}),this.hot.runHooks("afterUnmergeCells",e,t),this.hot.render())}toggleMerge(e){var t=this.mergedCellsCollection.get(e.from.row,e.from.col);t.row===e.from.row&&t.col===e.from.col&&t.row+t.rowspan-1===e.to.row&&t.col+t.colspan-1===e.to.col?this.unmergeRange(e):this.mergeSelection(e)}merge(e,t,r,o){var n=new p.a(e,t),i=new p.a(r,o);this.mergeRange(new p.b(n,n,i))}unmerge(e,t,r,o){var n=new p.a(e,t),i=new p.a(r,o);this.unmergeRange(new p.b(n,n,i))}onAfterInit(){this.generateFromSettings(this.hot.getSettings().mergeCells),this.hot.render()}onBeforeKeyDown(e){(e.ctrlKey||e.metaKey)&&!e.altKey&&77===e.keyCode&&(this.toggleMerge(this.hot.getSelectedRangeLast()),this.hot.render(),Object(te.stopImmediatePropagation)(e))}onAfterIsMultipleSelection(e){if(e)for(var t=this.mergedCellsCollection.mergedCells,r=this.hot.getSelectedRangeLast(),o=0;o<t.length;o+=1)if(r.highlight.row===t[o].row&&r.highlight.col===t[o].col&&r.to.row===t[o].row+t[o].rowspan-1&&r.to.col===t[o].col+t[o].colspan-1)return!1;return e}onModifyTransformStart(e){var t,r=pe.get(this),o=this.hot.getSelectedRangeLast(),n={row:e.row,col:e.col},i=new p.a(o.highlight.row,o.highlight.col),s=this.mergedCellsCollection.get(i.row,i.col);if(r.lastDesiredCoords||(r.lastDesiredCoords=new p.a(null,null)),s){var a=new p.a(s.row,s.col),l=new p.a(s.row+s.rowspan-1,s.col+s.colspan-1);new p.b(a,a,l).includes(r.lastDesiredCoords)||(r.lastDesiredCoords=new p.a(null,null)),n.row=r.lastDesiredCoords.row?r.lastDesiredCoords.row-i.row:n.row,n.col=r.lastDesiredCoords.col?r.lastDesiredCoords.col-i.col:n.col,e.row>0?n.row=s.row+s.rowspan-1-i.row+e.row:e.row<0&&(n.row=i.row-s.row+e.row),e.col>0?n.col=s.col+s.colspan-1-i.col+e.col:e.col<0&&(n.col=i.col-s.col+e.col)}t=new p.a(o.highlight.row+n.row,o.highlight.col+n.col);var c=this.mergedCellsCollection.get(t.row,t.col);c&&(r.lastDesiredCoords=t,n={row:c.row-i.row,col:c.col-i.col}),0!==n.row&&(e.row=n.row),0!==n.col&&(e.col=n.col)}onModifyTransformEnd(e){var t=this.hot.getSelectedRangeLast(),r=Object(o.clone)(e),i=this.selectionCalculations.getUpdatedSelectionRange(t,e),s=Object(o.clone)(r),a=this.mergedCellsCollection.getWithinRange(i,!0);do{s=Object(o.clone)(r),this.selectionCalculations.getUpdatedSelectionRange(t,r),Object(n.arrayEach)(a,e=>{this.selectionCalculations.snapDelta(r,t,e)})}while(r.row!==s.row||r.col!==s.col);e.row=r.row,e.col=r.col}onModifyGetCellCoords(e,t){if(!(e<0||t<0)){var r=this.mergedCellsCollection.get(e,t);if(r){var{row:o,col:n,colspan:i,rowspan:s}=r;return[...this.translateMergedCellToRenderable(o,0,n,0),...this.translateMergedCellToRenderable(o,s,n,i)]}}}addMergeActionsToContextMenu(e){var t;e.items.push({name:"---------"},(t=this,{key:"mergeCells",name(){var e=this.getSelectedLast();if(e){var r=t.mergedCellsCollection.get(e[0],e[1]);if(r.row===e[0]&&r.col===e[1]&&r.row+r.rowspan-1===e[2]&&r.col+r.colspan-1===e[3])return this.getTranslatedPhrase(M.CONTEXTMENU_ITEMS_UNMERGE_CELLS)}return this.getTranslatedPhrase(M.CONTEXTMENU_ITEMS_MERGE_CELLS)},callback(){t.toggleMergeOnSelection()},disabled(){var e=this.getSelectedLast();return!e||le.isSingleCell({row:e[0],col:e[1],rowspan:e[2]-e[0]+1,colspan:e[3]-e[1]+1})||this.selection.isSelectedByCorner()},hidden:!1}))}onAfterRenderer(e,t,r){var n=this.mergedCellsCollection.get(t,r),i=Object(o.isObject)(n)?Object(o.clone)(n):void 0;if(Object(o.isObject)(i)){var{rowIndexMapper:s,columnIndexMapper:a}=this.hot,{row:l,col:c,colspan:h,rowspan:d}=i,[u,g]=this.translateMergedCellToRenderable(l,d,c,h),f=u-s.getRenderableFromVisualIndex(t)+1,m=g-a.getRenderableFromVisualIndex(r)+1;i.row=s.getFirstNotHiddenIndex(i.row,1),i.col=a.getFirstNotHiddenIndex(i.col,1),i.rowspan=Math.min(i.rowspan,f),i.colspan=Math.min(i.colspan,m)}he(e,i,t,r)}onBeforeSetRangeEnd(e){var t=this.hot.getSelectedRangeLast();t.highlight=new p.a(t.highlight.row,t.highlight.col),t.to=e;var r=!1;if(!this.hot.selection.isSelectedByColumnHeader()&&!this.hot.selection.isSelectedByRowHeader())do{r=!1;for(var o=0;o<this.mergedCellsCollection.mergedCells.length;o+=1){var n=this.mergedCellsCollection.mergedCells[o].getRange();t.expandByRange(n)&&(e.row=t.to.row,e.col=t.to.col,r=!0)}}while(r)}onAfterGetCellMeta(e,t,r){var o=this.mergedCellsCollection.get(e,t);o&&(o.row!==e||o.col!==t?r.copyable=!1:(r.rowspan=o.rowspan,r.colspan=o.colspan))}onAfterViewportRowCalculatorOverride(e){var t=this.hot.countCols();this.modifyViewportRowStart(e,t),this.modifyViewportRowEnd(e,t)}modifyViewportRowStart(e,t){for(var r=this.hot.rowIndexMapper,n=r.getVisualFromRenderableIndex(e.startRow),i=0;i<t;i+=1){var s=this.mergedCellsCollection.get(n,i);if(Object(o.isObject)(s)){var a=r.getRenderableFromVisualIndex(r.getFirstNotHiddenIndex(s.row,1));if(a<e.startRow)return e.startRow=a,void this.modifyViewportRowStart(e,t)}}}modifyViewportRowEnd(e,t){for(var r=this.hot.rowIndexMapper,n=r.getVisualFromRenderableIndex(e.endRow),i=0;i<t;i+=1){var s=this.mergedCellsCollection.get(n,i);if(Object(o.isObject)(s)){var a=s.row+s.rowspan-1,l=r.getRenderableFromVisualIndex(r.getFirstNotHiddenIndex(a,-1));if(l>e.endRow)return e.endRow=l,void this.modifyViewportRowEnd(e,t)}}}onAfterViewportColumnCalculatorOverride(e){var t=this.hot.countRows();this.modifyViewportColumnStart(e,t),this.modifyViewportColumnEnd(e,t)}modifyViewportColumnStart(e,t){for(var r=this.hot.columnIndexMapper,n=r.getVisualFromRenderableIndex(e.startColumn),i=0;i<t;i+=1){var s=this.mergedCellsCollection.get(i,n);if(Object(o.isObject)(s)){var a=r.getRenderableFromVisualIndex(r.getFirstNotHiddenIndex(s.col,1));if(a<e.startColumn)return e.startColumn=a,void this.modifyViewportColumnStart(e,t)}}}modifyViewportColumnEnd(e,t){for(var r=this.hot.columnIndexMapper,n=r.getVisualFromRenderableIndex(e.endColumn),i=0;i<t;i+=1){var s=this.mergedCellsCollection.get(i,n);if(Object(o.isObject)(s)){var a=s.col+s.colspan-1,l=r.getRenderableFromVisualIndex(r.getFirstNotHiddenIndex(a,-1));if(l>e.endColumn)return e.endColumn=l,void this.modifyViewportColumnEnd(e,t)}}}translateMergedCellToRenderable(e,t,r,o){var n,i,{rowIndexMapper:s,columnIndexMapper:a}=this.hot;return n=0===t?s.getFirstNotHiddenIndex(e,1):s.getFirstNotHiddenIndex(e+t-1,-1),i=0===o?a.getFirstNotHiddenIndex(r,1):a.getFirstNotHiddenIndex(r+o-1,-1),[e>=0?s.getRenderableFromVisualIndex(n):e,r>=0?a.getRenderableFromVisualIndex(i):r]}onModifyAutofillRange(e,t){this.autofillCalculations.correctSelectionAreaSize(t);var r=this.autofillCalculations.getDirection(t,e),o=e;if(this.autofillCalculations.dragAreaOverlapsCollections(t,o,r))return o=t;var n=this.mergedCellsCollection.getWithinRange({from:{row:t[0],col:t[1]},to:{row:t[2],col:t[3]}});return n?o=this.autofillCalculations.snapDragArea(t,o,r,n):o}onAfterCreateCol(e,t){this.mergedCellsCollection.shiftCollections("right",e,t)}onAfterRemoveCol(e,t){this.mergedCellsCollection.shiftCollections("left",e,t)}onAfterCreateRow(e,t,r){"auto"!==r&&this.mergedCellsCollection.shiftCollections("down",e,t)}onAfterRemoveRow(e,t){this.mergedCellsCollection.shiftCollections("up",e,t)}onAfterChange(e,t){"Autofill.fill"===t&&this.autofillCalculations.recreateAfterDataPopulation(e)}onBeforeDrawAreaBorders(e,t){if(t&&"area"===t){var r=this.hot.getSelectedRangeLast(),o=this.mergedCellsCollection.getWithinRange(r);Object(n.arrayEach)(o,t=>{r.getBottomRightCorner().row===t.getLastRow()&&r.getBottomRightCorner().col===t.getLastColumn()&&(e[2]=t.row,e[3]=t.col)})}}onAfterModifyTransformStart(e,t,r){if(this.enabled){var o=this.mergedCellsCollection.get(e.row,e.col);if(o){var n=t>0,i=t<0,s=r<0,a=r>0,l=o.row+o.rowspan-1==this.hot.countRows()-1,c=0===o.row,h=o.col+o.colspan-1==this.hot.countCols()-1,d=0===o.col;(n&&l||i&&c||a&&h||s&&d)&&(e.row=o.row,e.col=o.col)}}}onAfterDrawSelection(e,t,r,o){return this.selectionCalculations.getSelectedMergedCellClassName(e,t,r,o)}onBeforeRemoveCellClassNames(){return this.selectionCalculations.getSelectedMergedCellClassNameToRemove()}}Object(i.d)("mergeCells",ve);var we=ve;class be extends l{constructor(e){super(e),this.scrollbars=[],this.clones=[],this.lockedCollection=!1,this.freezeOverlays=!1}isEnabled(){return Object(c.isTouchSupported)()}enablePlugin(){this.enabled||(this.addHook("afterRender",()=>this.onAfterRender()),this.registerEvents(),super.enablePlugin())}updatePlugin(){this.lockedCollection=!1,super.updatePlugin()}disablePlugin(){super.disablePlugin()}registerEvents(){this.addHook("beforeTouchScroll",()=>this.onBeforeTouchScroll()),this.addHook("afterMomentumScroll",()=>this.onAfterMomentumScroll())}onAfterRender(){if(!this.lockedCollection){var{topOverlay:e,bottomOverlay:t,leftOverlay:r,topLeftCornerOverlay:o,bottomLeftCornerOverlay:n}=this.hot.view.wt.wtOverlays;this.lockedCollection=!0,this.scrollbars.length=0,this.scrollbars.push(e),t.clone&&this.scrollbars.push(t),this.scrollbars.push(r),o&&this.scrollbars.push(o),n&&n.clone&&this.scrollbars.push(n),this.clones.length=0,e.needFullRender&&this.clones.push(e.clone.wtTable.wtRootElement),t.needFullRender&&this.clones.push(t.clone.wtTable.wtRootElement),r.needFullRender&&this.clones.push(r.clone.wtTable.wtRootElement),o&&this.clones.push(o.clone.wtTable.wtRootElement),n&&n.clone&&this.clones.push(n.clone.wtTable.wtRootElement)}}onBeforeTouchScroll(){this.freezeOverlays=!0,Object(n.arrayEach)(this.clones,e=>{Object(E.addClass)(e,"hide-tween")})}onAfterMomentumScroll(){this.freezeOverlays=!1,Object(n.arrayEach)(this.clones,e=>{Object(E.removeClass)(e,"hide-tween"),Object(E.addClass)(e,"show-tween")}),setTimeout(()=>{Object(n.arrayEach)(this.clones,e=>{Object(E.removeClass)(e,"show-tween")})},400),Object(n.arrayEach)(this.scrollbars,e=>{e.redrawClone(),e.adjustElementsPosition()}),this.hot.view.wt.wtOverlays.propagateMasterScrollPositionsToClones()}}Object(i.d)("touchScroll",be);var Ce=be},function(e,t,r){"use strict";var o=r(38),n=r(74),i=r(57),s=r(87),a=r(43),l=r(40),c=r(32),h=r(33),d={_hooksStorage:Object.create(null),addHook(e,t){return this._hooksStorage[e]||(this._hooksStorage[e]=[]),this.hot.addHook(e,t),this._hooksStorage[e].push(t),this},removeHooksByKey(e){Object(h.arrayEach)(this._hooksStorage[e]||[],t=>{this.hot.removeHook(e,t)})},clearHooks(){Object(c.objectEach)(this._hooksStorage,(e,t)=>this.removeHooksByKey(t)),this._hooksStorage={}}};Object(c.defineGetter)(d,"MIXIN_NAME","hooksRefRegisterer",{writable:!1,enumerable:!1});var u=d,g="STATE_VIRGIN",f="STATE_EDITING",m="STATE_WAITING",p="STATE_FINISHED";class v{constructor(e){this.hot=e,this.instance=e,this.state=g,this._opened=!1,this._fullEditMode=!1,this._closeCallback=null,this.TD=null,this.row=null,this.col=null,this.prop=null,this.originalValue=null,this.cellProperties=null,this.init()}_fireCallbacks(e){this._closeCallback&&(this._closeCallback(e),this._closeCallback=null)}init(){}getValue(){throw Error("Editor getValue() method unimplemented")}setValue(){throw Error("Editor setValue() method unimplemented")}open(){throw Error("Editor open() method unimplemented")}close(){throw Error("Editor close() method unimplemented")}prepare(e,t,r,o,n,i){this.TD=o,this.row=e,this.col=t,this.prop=r,this.originalValue=n,this.cellProperties=i,this.state=g}extend(){return class extends this.constructor{}}saveValue(e,t){var r,o;t?((r=this.hot.getSelectedLast())[0]>r[2]&&(o=r[0],r[0]=r[2],r[2]=o),r[1]>r[3]&&(o=r[1],r[1]=r[3],r[3]=o)):r=[this.row,this.col,null,null],this.hot.populateFromArray(r[0],r[1],e,r[2],r[3],"edit")}beginEditing(e,t){if(this.state===g){if(this.hot.view.scrollViewport(new o.a(this.row,this.col)),this.state=f,this.isInFullEditMode()){var r="string"==typeof e?e:Object(l.stringify)(this.originalValue);this.setValue(r)}this.open(t),this._opened=!0,this.focus(),this.hot.view.render(),this.hot.runHooks("afterBeginEditing",this.row,this.col)}}finishEditing(e,t,r){var o;if(r){var n=this._closeCallback;this._closeCallback=e=>{n&&n(e),r(e),this.hot.view.render()}}if(!this.isWaiting())if(this.state!==g){if(this.state===f){if(e)return this.cancelChanges(),void this.hot.view.render();var i=this.getValue();o=this.hot.getSettings().trimWhitespace?[["string"==typeof i?String.prototype.trim.call(i||""):i]]:[[i]],this.state=m,this.saveValue(o,t),this.hot.getCellValidator(this.cellProperties)?this.hot.addHookOnce("postAfterValidate",e=>{this.state=p,this.discardEditor(e)}):(this.state=p,this.discardEditor(!0))}}else this.hot._registerTimeout(()=>{this._fireCallbacks(!0)})}cancelChanges(){this.state=p,this.discardEditor()}discardEditor(e){this.state===p&&(!1===e&&!0!==this.cellProperties.allowInvalid?(this.hot.selectCell(this.row,this.col),this.focus(),this.state=f,this._fireCallbacks(!1)):(this.close(),this._opened=!1,this._fullEditMode=!1,this.state=g,this._fireCallbacks(!0)))}enableFullEditMode(){this._fullEditMode=!0}isInFullEditMode(){return this._fullEditMode}isOpened(){return this._opened}isWaiting(){return this.state===m}getEditedCellsLayerClass(){switch(this.checkEditorSection()){case"right":return"ht_clone_right";case"left":return"ht_clone_left";case"bottom":return"ht_clone_bottom";case"bottom-right-corner":return"ht_clone_bottom_right_corner";case"bottom-left-corner":return"ht_clone_bottom_left_corner";case"top":return"ht_clone_top";case"top-right-corner":return"ht_clone_top_right_corner";case"top-left-corner":return"ht_clone_top_left_corner";default:return"ht_clone_master"}}getEditedCell(){return this.hot.getCell(this.row,this.col,!0)}checkEditorSection(){var e=this.hot.countRows(),t="";return this.row<this.hot.getSettings().fixedRowsTop?t=this.col<this.hot.getSettings().fixedColumnsLeft?"top-left-corner":"top":this.hot.getSettings().fixedRowsBottom&&this.row>=e-this.hot.getSettings().fixedRowsBottom?t=this.col<this.hot.getSettings().fixedColumnsLeft?"bottom-left-corner":"bottom":this.col<this.hot.getSettings().fixedColumnsLeft&&(t="left"),t}}Object(c.mixin)(v,u);var w=r(34);class b{constructor(e,t,r){this.instance=e,this.tableMeta=t,this.selection=r,this.eventManager=new a.a(e),this.destroyed=!1,this.lock=!1,this.activeEditor=void 0,this.cellProperties=void 0,this.lastKeyCode=void 0,this.instance.addHook("afterDocumentKeyDown",e=>this.onAfterDocumentKeyDown(e));for(var o=this.instance.rootWindow;o;)this.eventManager.addEventListener(o.document.documentElement,"keydown",e=>{this.destroyed||this.instance.runHooks("afterDocumentKeyDown",e)}),o=Object(w.getParentWindow)(o);this.eventManager.addEventListener(this.instance.rootDocument.documentElement,"compositionstart",e=>{!this.destroyed&&this.activeEditor&&!this.activeEditor.isOpened()&&this.instance.isListening()&&this.openEditor("",e)}),this.instance.view.wt.update("onCellDblClick",(e,t,r)=>this.onCellDblClick(e,t,r))}lockEditor(){this.lock=!0}unlockEditor(){this.lock=!1}destroyEditor(e){this.lock||this.closeEditor(e)}getActiveEditor(){return this.activeEditor}prepareEditor(){if(!this.lock)if(this.activeEditor&&this.activeEditor.isWaiting())this.closeEditor(!1,!1,e=>{e&&this.prepareEditor()});else{var{row:e,col:t}=this.instance.selection.selectedRange.current().highlight;this.cellProperties=this.instance.getCellMeta(e,t);var{activeElement:r}=this.instance.rootDocument;if(r&&r.blur(),this.cellProperties.readOnly)this.clearActiveEditor();else{var o=this.instance.getCellEditor(this.cellProperties),n=this.instance.getCell(e,t,!0);if(o&&n){var i=this.instance.colToProp(t),a=this.instance.getSourceDataAtCell(this.instance.toPhysicalRow(e),t);this.activeEditor=Object(s.b)(o,this.instance),this.activeEditor.prepare(e,t,i,n,a,this.cellProperties)}else this.clearActiveEditor()}}}isEditorOpened(){return this.activeEditor&&this.activeEditor.isOpened()}openEditor(e,t){this.activeEditor&&this.activeEditor.beginEditing(e,t)}closeEditor(e,t,r){this.activeEditor?this.activeEditor.finishEditing(e,t,r):r&&r(!1)}closeEditorAndSaveChanges(e){this.closeEditor(!1,e)}closeEditorAndRestoreOriginalValue(e){this.closeEditor(!0,e)}clearActiveEditor(){this.activeEditor=void 0}moveSelectionAfterEnter(e){var t="function"==typeof this.tableMeta.enterMoves?this.tableMeta.enterMoves(event):this.tableMeta.enterMoves;e?this.selection.transformStart(-t.row,-t.col):this.selection.transformStart(t.row,t.col,!0)}moveSelectionUp(e){e?this.selection.transformEnd(-1,0):this.selection.transformStart(-1,0)}moveSelectionDown(e){e?this.selection.transformEnd(1,0):this.selection.transformStart(1,0)}moveSelectionRight(e){e?this.selection.transformEnd(0,1):this.selection.transformStart(0,1)}moveSelectionLeft(e){e?this.selection.transformEnd(0,-1):this.selection.transformStart(0,-1)}onAfterDocumentKeyDown(e){if(this.instance.isListening()&&(this.instance.runHooks("beforeKeyDown",e),!this.destroyed&&229!==e.keyCode&&!Object(i.isImmediatePropagationStopped)(e)&&(this.lastKeyCode=e.keyCode,this.selection.isSelected()))){var t=(e.ctrlKey||e.metaKey)&&!e.altKey;if(!this.activeEditor||this.activeEditor.isWaiting()||Object(n.isMetaKey)(e.keyCode)||Object(n.isCtrlMetaKey)(e.keyCode)||t||this.isEditorOpened()){var r,s=e.shiftKey,a=s?this.selection.setRangeEnd:this.selection.setRangeStart;switch(e.keyCode){case n.KEY_CODES.A:!this.isEditorOpened()&&t&&(this.instance.selectAll(),e.preventDefault(),e.stopPropagation());break;case n.KEY_CODES.ARROW_UP:this.isEditorOpened()&&!this.activeEditor.isWaiting()&&this.closeEditorAndSaveChanges(t),this.moveSelectionUp(s),e.preventDefault(),e.stopPropagation();break;case n.KEY_CODES.ARROW_DOWN:this.isEditorOpened()&&!this.activeEditor.isWaiting()&&this.closeEditorAndSaveChanges(t),this.moveSelectionDown(s),e.preventDefault(),e.stopPropagation();break;case n.KEY_CODES.ARROW_RIGHT:this.isEditorOpened()&&!this.activeEditor.isWaiting()&&this.closeEditorAndSaveChanges(t),this.moveSelectionRight(s),e.preventDefault(),e.stopPropagation();break;case n.KEY_CODES.ARROW_LEFT:this.isEditorOpened()&&!this.activeEditor.isWaiting()&&this.closeEditorAndSaveChanges(t),this.moveSelectionLeft(s),e.preventDefault(),e.stopPropagation();break;case n.KEY_CODES.TAB:r="function"==typeof this.tableMeta.tabMoves?this.tableMeta.tabMoves(e):this.tableMeta.tabMoves,s?this.selection.transformStart(-r.row,-r.col):this.selection.transformStart(r.row,r.col,!0),e.preventDefault(),e.stopPropagation();break;case n.KEY_CODES.BACKSPACE:case n.KEY_CODES.DELETE:this.instance.emptySelectedCells(),this.prepareEditor(),e.preventDefault();break;case n.KEY_CODES.F2:this.activeEditor&&this.activeEditor.enableFullEditMode(),this.openEditor(null,e),e.preventDefault();break;case n.KEY_CODES.ENTER:this.isEditorOpened()?(this.activeEditor&&this.activeEditor.state!==m&&this.closeEditorAndSaveChanges(t),this.moveSelectionAfterEnter(s)):this.instance.getSettings().enterBeginsEditing?this.cellProperties.readOnly?this.moveSelectionAfterEnter():this.activeEditor&&(this.activeEditor.enableFullEditMode(),this.openEditor(null,e)):this.moveSelectionAfterEnter(s),e.preventDefault(),Object(i.stopImmediatePropagation)(e);break;case n.KEY_CODES.ESCAPE:this.isEditorOpened()&&(this.closeEditorAndRestoreOriginalValue(t),this.activeEditor.focus()),e.preventDefault();break;case n.KEY_CODES.HOME:e.ctrlKey||e.metaKey?a.call(this.selection,new o.a(0,this.selection.selectedRange.current().from.col)):a.call(this.selection,new o.a(this.selection.selectedRange.current().from.row,0)),e.preventDefault(),e.stopPropagation();break;case n.KEY_CODES.END:e.ctrlKey||e.metaKey?a.call(this.selection,new o.a(this.instance.countRows()-1,this.selection.selectedRange.current().from.col)):a.call(this.selection,new o.a(this.selection.selectedRange.current().from.row,this.instance.countCols()-1)),e.preventDefault(),e.stopPropagation();break;case n.KEY_CODES.PAGE_UP:this.selection.transformStart(-this.instance.countVisibleRows(),0),e.preventDefault(),e.stopPropagation();break;case n.KEY_CODES.PAGE_DOWN:this.selection.transformStart(this.instance.countVisibleRows(),0),e.preventDefault(),e.stopPropagation()}}else this.openEditor("",e)}}onCellDblClick(e,t,r){"TD"===r.nodeName&&(this.activeEditor&&this.activeEditor.enableFullEditMode(),this.openEditor(null,e))}destroy(){this.destroyed=!0,this.eventManager.destroy()}}var C=new WeakMap;b.getInstance=function(e,t,r){var o=C.get(e);return o||(o=new b(e,t,r),C.set(e,o)),o};t.a=b},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,r){"use strict";r.r(t);var o=r(1),n=r.n(o),i=()=>n.a.createElement("div",{className:"developer-screen"},n.a.createElement("h1",{className:"developer-screen--title"},"Handsontable Spreadsheet Viewer"),n.a.createElement("p",null,"Hello, developer!"),n.a.createElement("p",null,"This app is intended to be run:"),n.a.createElement("ul",null,n.a.createElement("li",null,"with URL parameters, as explained in"," ",n.a.createElement("a",{href:"https://github.com/handsontable/spreadsheet-viewer/wiki/Query-String-API",target:"_blank",rel:"noopener noreferrer"},"Query String API")),n.a.createElement("li",null,"or, in an iframe driven by asynchronous messages, as explained in"," ",n.a.createElement("a",{href:"https://github.com/handsontable/spreadsheet-viewer/wiki/JavaScript-API",target:"_blank",rel:"noopener noreferrer"},"JavaScript API")))),s=r(14),a=r(82),l=r(136),c=r(3),h=r(7),d=r(15),u=r(8),g=r(73);class f extends g.a.plugins.BasePlugin{constructor(e){super(e),this.floatingBoxSettings=[],this.floatingBoxMeta=new WeakMap,this.isFirstRender=!0,this.floatingBoxSettings=[],this.floatingBoxMeta=new WeakMap,this.resetState()}resetState(){this.floatingBoxSettings=[],this.floatingBoxMeta=new WeakMap}getExtendedHotSettings(){return this.hot.getSettings()}isEnabled(){return!!this.getExtendedHotSettings().floatingBox}enablePlugin(){this.enabled||(this.addHook("afterLoadData",this.afterLoadData.bind(this)),this.addHook("afterUpdateSettings",this.afterUpdateSettings.bind(this)),this.addHook("afterRenderer",this.afterRenderer.bind(this)),this.addHook("beforeRender",this.beforeRender.bind(this)),this.addHook("afterRender",this.afterRender.bind(this)),super.enablePlugin())}disablePlugin(){this.resetState(),super.disablePlugin()}updatePlugin(){this.disablePlugin(),this.enablePlugin(),super.updatePlugin()}afterLoadData(){this.afterUpdateSettings()}afterRenderer(e,t,r){this.floatingBoxSettings.forEach(o=>{var n,i,s,a,l=this.floatingBoxMeta.get(o);if(l&&o.topLeftRow<=t&&o.topLeftColumn<=r&&o.bottomRightRow>=t&&o.bottomRightColumn>=r){var c=(null===(a=null===(s=null===(i=null===(n=e.parentElement)||void 0===n?void 0:n.parentElement)||void 0===i?void 0:i.parentElement)||void 0===s?void 0:s.parentElement)||void 0===a?void 0:a.parentElement)||void 0;c&&!l.containerParents.has(c)&&l.containerParents.add(c)}})}beforeRender(){this.floatingBoxSettings.forEach(e=>{var t=this.floatingBoxMeta.get(e);t||(t={top:void 0,left:void 0,width:void 0,height:void 0,containerParents:new Set,containers:new Map},this.floatingBoxMeta.set(e,t))})}afterRender(){if(this.isFirstRender&&(this.isFirstRender=!1,this.getExtendedHotSettings().mergeCells.length>0))return;0!==this.floatingBoxSettings.length&&(this.floatingBoxSettings.forEach(e=>{var t=this.floatingBoxMeta.get(e);if(t&&t.containerParents.size>0&&void 0===t.width){var r=this.hot.view.wt,o=-r.getSetting("columnHeaders").length,n=-r.getSetting("rowHeaders").length;t.left=-1+e.topLeftOffsetX+r.wtOverlays.leftOverlay.sumCellSizes(o,e.topLeftColumn),t.top=3+e.topLeftOffsetY+r.wtOverlays.topOverlay.sumCellSizes(n,e.topLeftRow);var i=r.wtOverlays.leftOverlay.sumCellSizes(e.topLeftColumn,e.bottomRightColumn+1);t.width=-1+e.bottomRightOffsetX-e.topLeftOffsetX+i;var s=r.wtOverlays.topOverlay.sumCellSizes(e.topLeftRow,e.bottomRightRow+1);t.height=-1+e.bottomRightOffsetY-e.topLeftOffsetY+s}}),this.floatingBoxSettings.forEach(e=>{var t=this.floatingBoxMeta.get(e);t&&t.containerParents.size>t.containers.size&&t.containerParents.forEach(r=>{if(!t.containers.has(r)){var o=function(e,t,r){var o=document.createElement("div");if(o.classList.add("sv-handsontable-floating-box"),o.style.left="".concat(t.left,"px"),o.style.top="".concat(t.top,"px"),o.style.width="".concat(t.width,"px"),o.style.height="".concat(t.height,"px"),r.appendChild(o),void 0===t.width||void 0===t.height)throw new Error("Could not retrieve sizes of the floating box");return e.renderer(o,t.width,t.height),o}(e,t,r);t.containers.set(r,o)}})}))}afterUpdateSettings(){var e=this.getExtendedHotSettings();Array.isArray(e.floatingBox)?this.floatingBoxSettings=e.floatingBox:this.floatingBoxSettings=[]}destroy(){super.destroy()}}g.a.plugins.registerPlugin("FloatingBoxPlugin",f);var m=r(4),p=r(149),v=r.n(p),w=r(10),b={Arial:"Liberation Sans","Times New Roman":"Liberation Serif","Courier New":"Liberation Mono",Calibri:"Carlito",Cambria:"Caladea"},C={"Liberation Serif":"serif","Liberation Mono":"monospace","Liberation Sans":"sans-serif",Carlito:"sans-serif",Caladea:"sans-serif"},E=new Map,O=1;var y,R;function T(e){var t=document.createElement("span");t.innerHTML=Array(100).join("wi"),t.style.position="absolute",t.style.fontSize="128px",t.style.left="-99999px",t.style.fontFamily=e,document.body.appendChild(t);var r=t.clientWidth;if(document.body.removeChild(t),r<1)throw new Error("Could not measure the text size. Maybe you called too early?");return r}var S=new Map;var M=()=>{0===(O-=1)&&(document.body.dataset.webfontsLoaded="")};function N(e,t,r){var o=[],n=Object(w.b)(b,e,void 0);if(function(e){if(void 0===y&&(y=T("sans-serif")),void 0===R&&(R=T("serif")),S.has(e))return S.get(e);var t=T("".concat(e,", sans-serif")),r=T("".concat(e,", serif")),o=!(y===t&&R===r);return S.set(e,o),o}(e))o.push('"'.concat(e,'"'));else if(n){o.push('"'.concat(n,'"')),o.push('"'.concat((e=>Object(w.b)(C,e,"sans-serif"))(n),'"'));var i="".concat(n," ").concat(t," ").concat(r);if(!E.has(i)){1===(O+=1)&&delete document.body.dataset.webfontsLoaded;var s=function(e,t,r){return new v.a(e,{weight:t,style:r}).load(null,1e4)}(n,t,r).then(()=>{console.log("Font loaded",n,t,r),M()}).catch(()=>{console.log("Font failed to load",n,t,r),M()});E.set(i,s)}}else o.push('"'.concat("Arial",'"')),o.push('"'.concat("sans-serif",'"'));return o.join(", ")}M();var H,L=r(16),x=r(94),A=function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function s(e){try{l(o.next(e))}catch(e){i(e)}}function a(e){try{l(o.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}l((o=o.apply(e,t||[])).next())}))},I=(e,t)=>function(o,n,i){Object(x.b)(),(()=>{A(this,void 0,void 0,(function*(){(yield Promise.all([r.e(6),r.e(3)]).then(r.bind(null,184))).getChartRenderer(e,t)(o,n,i)}))})()};!function(e){e.CHART="chart",e.IMAGE="image"}(H||(H={}));var j,D=r(128),_=r(20),k=(e,t)=>e.type===H.IMAGE?(e=>function(t,r,n){t.classList.add("sv-handsontable-image"),Object(L.render)(o.createElement("img",{src:e.imageData,alt:"",style:{width:r,height:n}}),t)})(e):Object(_.a)()?I(e,t):function(e,t,r){e.classList.add("sv-handsontable-chart"),Object(L.render)(n.a.createElement(D.a,null),e)},P=e=>null===e,B=r(17),F=["$","₿","£","¤","¥","₱","₭","₦","₮","€","฿","₡","₲","₪","₹","₺","Ar","B/.","Br","Bs.","Bs.S.","BTC","C$","CHF","EC$","HK$","kr","Ksh","L","MOP","Nfk","NT$","P","Q","R","R$","RF","RM","Rp","Rs","Rs.","S","S/","US$","XBT","XDR","रू","රු","ብር","AED","ADP","AFA","AFN","ALL","AMD","ANG","AOA","ARS","ATS","AUD","AWG","AZM","AZN","BAM","BBD","BDT","BEF","BGN","BGL","BHD","BIF","BMD","BND","BOB","BOV","BRL","BSD","BTC","BTN","BWP","BYB","BYR","BYN","BZD","CAD","CDF","CHE","CHW","CLF","CLN","CLP","CNY","COP","COU","CSD","CRC","CUC","CUP","CVE","CYP","CZK","DJF","DEM","DKK","DOP","DZD","ECS","ECV","EEK","ESP","EGP","ERN","ETB","ETH","EUR","FJD","FIM","FKP","FRF","GBP","GEL","GGP","GHC","GHS","GIP","GMD","GNF","GTQ","GYD","GRD","HKD","HNL","HRK","HTG","HUF","IDR","ILS","IEP","IMP","INR","IQD","IRR","ILS","IQD","ISK","ITL","JEP","JMD","JOD","JPY","KAF","KES","KGS","KHR","KMF","KPW","KRW","KPW","KWD","KYD","KZT","LAK","LBP","LKR","LRD","LSL","LTC","LTL","LVL","LYD","MAD","MDL","MGA","MGF","MKD","MMK","MNT","MOP","MRO","MRU","MUR","MVR","MWK","MXN","MYR","MZN","MZM","NAD","NGN","NIO","NOK","NLG","NPR","NTD","NZD","OMR","PAB","PEN","PGK","PHP","PKR","PLN","PYG","PTE","QAR","RMB","RON","ROL","RSD","RUB","RUR","RWF","SAR","SBD","SCR","SDD","SDP","SDG","SEK","SGD","SHP","SLL","SKK","SIT","SOS","SRD","SSP","SPL","STD","STN","SVC","SYP","SZL","THB","TJS","TJR","TMT","TND","TMM","TOP","TRL","TRY","TTD","TVD","TWD","TZS","UAH","UGX","USD","USN","USS","UYU","UYI","UZS","VEB","VEF","VES","VND","VUV","WST","XAF","XAG","XBT","XAU","XCD","XOF","XB5","XBA","XBB","XBC","XBD","XDR","XFO","XFU","XPD","XPF","XPT","XTS","XXX","YER","YUM","ZAR","ZWD","ZWL","ZWN","ZWR"],V={viewportRowRenderingOffset:1e4,viewportColumnRenderingOffset:256},W=e=>e/9525,z=(e,t)=>e/9525-t,U=(e,t)=>{var r=e/9525-t;return-Math.round(Math.abs(r))};function G(){Object(c.c)(c.a.presentationHotRendering)}function X(){this.getSettings().showGrid||this.rootElement.classList.add("sv-hide-gridlines")}!function(e){e.General="general",e.Accounting="accounting",e.Currency="currency"}(j||(j={}));var K=["color","fontSize","lineHeight","textDecoration","textShadow","overflow","textAlign","verticalAlign","direction","wordBreak","whiteSpace","display"],Y=e=>{var t,r,o="solid"===e.patternType,n=Object.assign({backgroundColor:o&&e.fgColor?"#".concat(e.fgColor.rgb):"",fontFamily:null!==(t=e.fontFamily)&&void 0!==t?t:"",fontStyle:null!==(r=e.fontStyle)&&void 0!==r?r:"",borderRightColor:o&&!P(e.border)?"transparent":"",borderBottomColor:o&&!P(e.border)?"transparent":""},e.fontFamily?(()=>{var t,r=function(e){if("number"==typeof e)return e;if(void 0===e)return 400;var t=parseInt(e,10);return t||("bold"===e?700:400)}(e.fontWeight||400),o=null!==(t=e.fontStyle)&&void 0!==t?t:"normal";return{fontWeight:r,fontStyle:o,fontFamily:N(e.fontFamily,r,o)}})():{});return K.forEach(t=>{e[t]&&(n[t]=e[t])}),n},q=e=>{var{s:t,e:r}=e,o=r.r-t.r,n=r.c-t.c;return o>=0&&(o+=1),n>=0&&(n+=1),{row:t.r,col:t.c,rowspan:o,colspan:n,mergedColumn:o>1e4,mergedRow:n>256}};function Q(e,t,r,o,n,i,s){t.firstElementChild||(e=>{var t=document.createElement("div"),r=document.createElement("div");r.className="sv-cell-text-wrapper__currency";var o=document.createElement("div");o.className="sv-cell-text-wrapper__value",t.appendChild(r),t.appendChild(o),e.appendChild(t)})(t);var a=t.firstElementChild,l=null==a?void 0:a.firstElementChild,c=null==a?void 0:a.lastElementChild;a.removeAttribute("style"),a.className="sv-cell-text-wrapper";var h=re(r,o);if(!s.svMultiRowCells.has(h)&&Array.isArray(s.rowHeights)){var d=s.rowHeights[r],u=Number(d)-1,{styleMap:f}=s.horizontalOverlap;if(a.style.maxHeight="".concat(u,"px"),s.cellClassName){var m=f[s.cellClassName],{lineHeight:p}=m;if(p){var v=parseInt(p,10);a.style.lineHeight="".concat(Math.min(u,v),"px ")}}}var w=s.horizontalOverlap.obscuredNonText.get(h),{numberFormat:b}=s;(function(e){return(null==e?void 0:e.format)===j.Accounting&&e.currencySymbol&&e.currencyValue})(s.numberFormat)&&!w?(a.classList.add("sv-cell-text-wrapper--accounting"),g.a.dom.fastInnerText(l," ".concat(b.currencySymbol," ")),g.a.dom.fastInnerText(c,b.currencyValue)):g.a.dom.fastInnerText(c,w||i||"");var C=s.rowClassNames.get(r);C&&t.classList.add(C);var E=s.columnClassNames.get(o);E&&t.classList.add(E),s.cellClassName&&t.classList.add(s.cellClassName);var{horizontalOverlap:O}=s,y=O.overlapStartCells.get(h);return y&&(a.classList.add("sv-handsontable-horizontal-overlay"),Object.assign(a.style,y)),O.hideRightGridlineTds.has(h)&&t.classList.add("sv-handsontable-hide-right-gridline"),t}var Z,J=(e,t,r)=>{var o={row:t,col:r};return["left","right","bottom","top"].forEach(t=>{var r,n=e[t];(null==n?void 0:n.width)&&(o[t]={width:(r=n.width,"thin"===r?1:"medium"===r?2:"thick"===r?3:1),color:"#".concat(n.color||"000")})}),o},$=(e,t,r)=>{if(!e||"General"===t)return j.General;var o=e.trim()!==e;return"n"===r&&o?j.Accounting:"n"!==r||o?j.General:j.Currency},ee=(e,t)=>{if(t===j.Accounting&&e){var r,o,n=e.search(/[-\\(0-9]/);if(n>0){var[i,s]=(o=n,[(r=e).substring(0,o),r.substring(o)]),a=i.trim();if(F.includes(a))return{currencySymbol:a,currencyValue:s}}}},te=(e,t,r)=>Math.min(r,Math.max(t,e)),re=(e,t)=>"".concat(e,",").concat(t),oe=new Map,ne=new Map,ie=()=>{oe.clear(),ne.clear(),Z=function(e){if(document.getElementById(e))throw new Error("measuringContainer already exists");var t=document.createElement("div");return t.setAttribute("id",e),document.body.appendChild(t),t}("sv-handsontable-horizontal-overlay-measuring")},se=function(e,t){var r="".concat(e,"|").concat(t),o=ne.get(r);if(void 0!==o)return o;var n=oe.get(t);n||(n=(e=>{var t=document.createElement("canvas");if(void 0===Z)throw new Error("measuringContainer doesn't exist");Z.appendChild(t);var r=t.getContext("2d");if(null===r)throw new Error("Failed to obtain CanvasRenderingContext2D");return r.font=e,r})(t),oe.set(t,n));var i=n.measureText(e),{width:s}=i;return ne.set(r,s),s};class ae extends g.a.plugins.BasePlugin{resetState(){this.measureAllCells()}getExtendedHotSettings(){return this.hot.getSettings()}isEnabled(){return!!this.getExtendedHotSettings().horizontalOverlap}enablePlugin(){this.enabled||(this.addHook("init",this.onInit.bind(this)),super.enablePlugin())}disablePlugin(){this.resetState(),super.disablePlugin()}updatePlugin(){this.disablePlugin(),this.enablePlugin(),super.updatePlugin()}onInit(){this.resetState()}createCustomBordersMap(e){for(var t=new Map,r=0;r<e.length;r++){var{row:o,col:n}=e[r],i=re(o,n);t.set(i,e[r])}return t}measureAllCells(){var e,{customBorders:t,horizontalOverlap:r}=this.getExtendedHotSettings(),o=this.createCustomBordersMap(t),n=this.hot.getData(),i=n[0].length;ie(),r.startCells.forEach(e=>{var[t,r]=e.split(",").map(e=>parseInt(e,10)),s=n[t];this.performTextOverlapping(t,r,i,s,o)}),null===(e=null==Z?void 0:Z.parentElement)||void 0===e||e.removeChild(Z)}hideBorderLeftEdge(e,t,r){var o=re(t,r),n=e.get(o);n&&this.hideBorderEdge(n.left)}hideBorderRightEdge(e,t,r){var o=re(t,r),n=e.get(o);n&&this.hideBorderEdge(n.right)}hideBorderEdge(e){e&&!e.hide&&(e.hide=!0)}obscureString(e){return Array(e.length).fill("#").join("")}performTextOverlapping(e,t,r,o,n){var i="left",s=!1,a=0,l=this.getCellProperties(e,t),{columns:c,colWidths:h,rowHeights:d,horizontalOverlap:u}=this.getExtendedHotSettings(),g=u.stopCells.has(re(e,t-1)),f=u.stopCells.has(re(e,t+1));if(!(l.isTextContent&&g&&f)){var m=l.cellClassName||"",p=u.styleMap[m];if(p&&(p.textAlign&&(i=p.textAlign),p.indent&&(a=9*p.indent),"break-word"===p.wordBreak&&(s=!0)),l.isTextContent){if(s)return;if("right"===i){if(0===t)return;if(g)return}else if(f)return}var v=re(e,t);if(!Array.isArray(h))throw new Error("colWidths must be an Array");if(!Array.isArray(d))throw new Error("rowHeights must be an Array");var w=h[t],b=p?"".concat(p.fontStyle||""," ").concat(p.fontWeight||""," ").concat(p.fontStretch||""," ").concat(p.fontSize," ").concat(p.fontFamily):"",C=o[t];if(null===C)throw new Error("rowData[".concat(t,"] must be a string"));var E=se(C,b)+a;if(E){var O=E+5;if(O>w){if(!l.isTextContent)return void u.obscuredNonText.set(v,this.obscureString(C));var y=0,R=w,T=!1;"right"===i&&(T=!0);for(var S=t-1,M=t+1;R<O;){if(T){if(S<0)break;if(u.stopCells.has(re(e,S)))break;var N=h[S];R+=Number(N),y-=Number(N),u.hideRightGridlineTds.add(re(e,S)),this.hideBorderLeftEdge(n,e,S+1),this.hideBorderRightEdge(n,e,S),S-=1}else{if(M===r){c.length===r&&(c.length=r+1,h[r]=0,this.hot.initIndexMappers());var H=O-R+4;h[r]<H&&(h[r]=H)}if(u.stopCells.has(re(e,M)))break;var L=h[M];R+=Number(L),u.hideRightGridlineTds.add(re(e,M-1)),this.hideBorderLeftEdge(n,e,M),this.hideBorderRightEdge(n,e,M-1),M+=1}"center"===i&&(T=!T)}var x=Number(d[e])-1;u.overlapStartCells.set(v,{left:"".concat(y,"px"),width:"".concat(R-4-1,"px"),height:"".concat(x,"px")})}}}}getCellProperties(e,t){return this.hot.getCellMeta(e,t)}destroy(){super.destroy()}}g.a.plugins.registerPlugin("HorizontalOverlapPlugin",ae);var le=r(2),ce=e=>{var{children:t,holdDownChildRenderPromise:r}=e,[i,s]=Object(o.useState)();return Object(o.useEffect)(()=>{var e=!0;return r?r.then(()=>{e&&s(t)}):s(t),()=>{e=!1}},[t,r]),n.a.createElement(n.a.Fragment,null,i)},he=e=>(t,r,o,n)=>{Object(h.c)(e,[t,r,o,n])};class de extends o.PureComponent{componentDidCatch(e){throw new le.k(null==e?void 0:e.message)}render(){var{children:e}=this.props;return e}}var ue=e=>{var{parsedData:t,sheet:r,shouldSimulateHandsontableError:i,shouldSimulateInterpreterError:s,requestMessageOnError:a,fullPage:h,svId:f}=e,p=Object(o.useRef)(),v=Object(B.b)();Object(d.a)(u.f,u.c,a,e=>{var t,{range:r}=e;null===(t=p.current)||void 0===t||t.selectCells([r])});var w=e=>{p.current=null==e?void 0:e.hotInstance},{cssText:b,tableProps:C,fontsLoadedPromise:O}=Object(o.useMemo)(()=>{Object(x.c)();try{if(Object(c.c)(c.a.interpreter),s)throw new Error("This is a simulation of an error in the interpreter");Object(c.c)(c.a.interpreterStyleClone);var e=JSON.parse(JSON.stringify(t.styleMap));Object(c.b)(c.a.interpreterStyleClone),Object(c.c)(c.a.interpreterCss);var o=(l=e,Object.entries(l).map(e=>{var[t,r]=e,o=document.createElement("style");document.head.appendChild(o);var n=o.sheet,i=t.includes("cell")?4:3,s=new Array(i).fill(".".concat(t)).join("");n.insertRule("".concat(s," {}"),0);var a=n.cssRules[0],l=Y(r);Object.assign(a.style,l),Object.assign(r,l);var c=a.cssText;if(document.head.removeChild(o),r.indent&&r.indent>0){var{indent:h}=r,d=9*Number(h),u="left"===r.textAlign?"padding-left":"padding-right";c+="\n        .handsontable td.".concat(t," .sv-cell-text-wrapper {\n          ").concat(u,": ").concat(d,"px;\n         }\n        ")}return c+=((e,t)=>{switch(e.textAlign){case"right":return"\n      .handsontable td.".concat(t," .sv-cell-text-wrapper {\n        margin-right: ").concat(4,"px;\n        justify-content: flex-end;\n        }\n    ");case"center":return"\n      .handsontable td.".concat(t," .sv-cell-text-wrapper {\n        justify-content: center;\n        min-width: 100%;\n        }\n    ");default:return"\n    .handsontable td.".concat(t," .sv-cell-text-wrapper {\n      margin-left: ").concat(4,"px;\n      justify-content: flex-start;\n      }\n  ")}})(r,t),"break-word"===r.wordBreak?c+=((e,t)=>{var r="";switch(e.verticalAlign){case"top":r="\n        position: absolute;\n        top: 0;\n        align-items: flex-start;\n      ";break;case"bottom":r="\n        position: absolute;\n        bottom: 0;\n        align-items: flex-end;\n      ";break;default:r="\n        position: relative;\n        align-items: center;\n      "}return"\n    .handsontable td.".concat(t," {\n      position: relative;\n    }\n    .handsontable td.").concat(t," .sv-cell-text-wrapper {\n      display: flex;\n      white-space: pre-wrap;\n      ").concat(r,"\n    }\n  ")})(r,t):c+=((e,t)=>{var r="";switch(e.verticalAlign){case"top":r="\n        align-items: flex-start;\n      ";break;case"bottom":r="\n        align-items: flex-end;\n      ";break;default:r="\n        align-items: center;\n      "}return"\n    .handsontable td.".concat(t," .sv-handsontable-horizontal-overlay {\n      ").concat(r,"\n    }\n  ")})(r,t),c}).join(" "));Object(c.b)(c.a.interpreterCss),Object(c.c)(c.a.interpreterHotConfig);var n=Object.values(t.sheetsData)[r];if(!n)throw new Error("Could not retrieve parsed data for the given sheet number (".concat(r,")"));var i=n.data;if(!i)throw new Error("Could not retrieve parsed data for the given sheet (".concat(n.title,")"));var{tableProps:a}=((e,t)=>{var r,o;if(!e||e.hidden)return{};var n=new Map,i={data:[],cell:[],afterLoadData:G,afterInit:X,readOnly:!0,colHeaders:!0,rowHeaders:!0,licenseKey:"non-commercial-and-evaluation",showGrid:!0,cells:()=>({renderer:Q}),beforePaste:()=>!1,columns:[],mergeCells:[],colWidths:[],rowHeights:[],fixedRowsTop:0,fixedColumnsLeft:0,fillHandle:!1,customBorders:new Array,floatingBox:[],selectionMode:"range",selectionStyle:{cell:{borderWidth:2,borderColor:"rgb(16, 74, 204)"},area:{borderWidth:1,borderColor:"rgb(16, 74, 204)"}},horizontalOverlap:{startCells:new Set,stopCells:new Set,styleMap:t,overlapStartCells:new Map,hideRightGridlineTds:new Set,obscuredNonText:new Map},svMultiRowCells:new Set,rowClassNames:new Map,columnClassNames:new Map,beforeInitWalkontable:e=>{e.externalRowCalculator=!0}},s=e[m.b],a=s.columns.wpx,l=s.rows.hpx,c=-1,h=-1;for(var[d,u]of Object.entries(e))switch(d){case m.c:u.forEach(e=>{var{s:t,e:r}=e,o=q({s:t,e:r});i.mergeCells.push(o);var n=o.mergedColumn||o.mergedRow,s=t.c,a=t.r,l=Math.min(255,r.c),d=Math.min(9999,r.r);if(l>c&&!n&&(c=l),d>h&&!n&&(h=d),a<d){var u=re(a,s);i.svMultiRowCells.add(u)}for(var g=a;g<=d;g++)for(var f=s;f<=l;f++){var m=re(g,f);i.horizontalOverlap.stopCells.add(m),i.horizontalOverlap.startCells.delete(m)}});break;case m.a:var f=u;f.length>256&&(f.length=256);for(var p=0;p<f.length;p++){var v=f[p];i.colWidths[p]=(null==v?void 0:v.wpx)||a,(null==v?void 0:v.svStyleId)&&i.columnClassNames.set(p,v.svStyleId),(null==v?void 0:v.hidden)&&i.fixedColumnsLeft&&i.fixedColumnsLeft>0&&p<i.fixedColumnsLeft&&(console.log("This sheet contains an unsupported hidden column inside a frozen pane. To avoid potentially incorrect rendering, we have disabled the frozen pane on the left."),i.fixedColumnsLeft=0)}break;case m.e:var w=u;w.length>1e4&&(w.length=1e4);for(var b=0;b<w.length;b++){var C=w[b],E=null==C?void 0:C.hpx;i.rowHeights[b]=E||l,(null==C?void 0:C.svStyleId)&&i.rowClassNames.set(b,C.svStyleId),(null==C?void 0:C.hidden)&&i.fixedRowsTop&&i.fixedRowsTop>0&&b<i.fixedRowsTop&&(console.log("This sheet contains an unsupported hidden row inside a frozen pane. To avoid potentially incorrect rendering, we have disabled the frozen pane on top."),i.fixedRowsTop=0)}break;case m.h:Object(B.b)()||(i.fixedColumnsLeft=parseInt(u,10));break;case m.i:Object(B.b)()||(i.fixedRowsTop=parseInt(u,10));break;case m.f:!1!==u&&0!==u||(i.showGrid=!1);break;case m.d:u.forEach(t=>{if(t&&t.anchor&&t.type){var r,{anchor:o}=t,n="number"==typeof o.dxL,s=parseInt(o.colR,10),d=parseInt(o.rwB,10),u=void 0===i.colWidths[s]?a:i.colWidths[s],g=void 0===i.rowHeights[d]?l:i.rowHeights[d],f={topLeftRow:parseInt(o.rwT,10),topLeftColumn:parseInt(o.colL,10),bottomRightColumn:s,bottomRightRow:d,topLeftOffsetX:n?0:(r=parseInt(o.dxL,10),r/9525),topLeftOffsetY:n?0:W(parseInt(o.dyT,10)),bottomRightOffsetX:n?0:z(parseInt(o.dxR,10),u),bottomRightOffsetY:n?0:U(parseInt(o.dyB,10),g),renderer:k(t,e)};i.floatingBox.push(f),s>c&&(c=s),d>h&&(h=d)}else console.warn("Skipping an embedded object of an unsupported type")});break;default:if(!u)break;var O=d.match(/^([a-zA-Z]+)([0-9]+)/);if(!O||!O[1]||!O[2]||Number(O[2])>1e4)break;var y=Number(O[2])-1,R=g.a.helper.spreadsheetColumnIndex(O[1]);if(R+1>256)break;var T=u,S=i.data&&i.data[y]?i.data[y]:[],M=!1,N=!1;if(T.svStyleId){var{border:H,wordBreak:L}=t[T.svStyleId];if(H){M=!0;var x=J(H,y,R);i.customBorders.push(x)}N=void 0===L}var A=T.w||T.v||"";N&&"string"==typeof A&&(A=A.replace(/[\r\n]+/gm,""));var I=!!A;if((I||M)&&(S[R]=A,i.data&&(i.data[y]=S),R>c&&(c=R),y>h&&(h=y),I)){var j=re(y,R);i.horizontalOverlap.stopCells.add(j),i.horizontalOverlap.startCells.add(j)}var D=$(T.w,T.z,T.t),_=ee(T.w,D),P={row:y,col:R,cellClassName:T.svStyleId,isTextContent:T.isTextContent,numberFormat:Object.assign({format:D},_&&Object.assign({},_))};n.set(re(y,R),T),null===(r=i.cell)||void 0===r||r.push(P)}var F=te(c+1,6,256),V=te(h+1,20,1e4);if(i.mergeCells=i.mergeCells.map(e=>Object.assign(Object.assign({},e),{rowspan:Math.min(V-e.row,e.rowspan),colspan:Math.min(F-e.col,e.colspan)})),i.data)if(i.data.length>V)i.data.splice(V);else if(i.data.length<V){var K=i.data.length;i.data.length=V,i.data.fill([],K,V)}if(i.rowHeights.length<V){var Y=i.rowHeights.length;i.rowHeights.length=V,i.rowHeights.fill(l,Y,V)}if(i.colWidths.length<F){var Z=i.colWidths.length;i.colWidths.length=F,i.colWidths.fill(a,Z,F)}for(var oe=0;oe<V;oe++)for(var ne=0;ne<F;ne++){var ie=null===(o=i.data)||void 0===o?void 0:o[oe],se=ie&&ie[ne],ae=n.get(re(oe,ne));if(void 0===se&&!ae){var le=i.columnClassNames.get(ne);if(le){var ce=t[le];if(ce.border){var he=J(ce.border,oe,ne);i.customBorders.push(he)}}var de=i.rowClassNames.get(oe);if(de){var ue=t[de];if(ue.border){var ge=J(ue.border,oe,ne);i.customBorders.push(ge)}}}}return i.columns.length=F,{tableProps:i}})(i,e);if(!(a=h?Object.assign(Object.assign({},a),V):Object.assign(Object.assign({},a),{width:"100%",height:"100%"})))throw new Error("Could not create a `tableProps` object for the given sheet");return{cssText:o,tableProps:a,fontsLoadedPromise:Promise.all(Array.from(E.values()))}}catch(e){throw console.error("interpreter:",e),new le.f(null==e?void 0:e.message)}finally{Object(c.b)(c.a.interpreterHotConfig),Object(c.b)(c.a.interpreter)}var l},[t,r,s,h]),y=Object(o.useMemo)(()=>{var e=Math.random().toString(32).substring(2);return n.a.createElement(l.a,{settings:C,afterSelection:(e,t,r,o,n)=>{v&&(n.value=!0)},afterSelectionEnd:he(f),afterRender:()=>{if(Object(c.b)(c.a.presentationHotRendering),Object(c.b)(c.a.presentation),Object(x.d)(),i)throw new Error("This is a simulation of an error in rendering of Handsontable")},copyPaste:!v||{uiContainer:document.createElement("div")},ref:w,key:e})},[i,C,v,f]);return n.a.createElement("div",{className:"sv-hottable-wrapper",role:"tabpanel"},n.a.createElement(ce,{holdDownChildRenderPromise:O},n.a.createElement("style",null,b),h&&n.a.createElement("style",null,"\n.sv-hottable-wrapper {\n  width: initial;\n  height: initial;\n  margin-bottom: initial;\n  margin-top: initial;\n  position: static;\n}\n.handsontable {\n  transform: none !important; /*disable row and column headings fixing to the viewport edge*/\n}\n"),n.a.createElement(de,null,y)))},ge=r(192),fe=r(181),me=r(183),pe=r(41),ve=r(186),we=e=>o.createElement("svg",Object.assign({width:"1em",height:"1em",viewBox:"0 0 16 16",preserveAspectRatio:"none"},e),o.createElement("path",{d:"M11.201 11.999V4.085a.074.074 0 00-.123-.066l-6.33 3.958a.074.074 0 000 .131l6.329 3.957a.074.074 0 00.123-.066z"})),be=e=>o.createElement("svg",Object.assign({width:"1em",height:"1em",viewBox:"0 0 16 16",preserveAspectRatio:"none"},e),o.createElement("path",{d:"M5 4.082v7.92a.082.082 0 00.126.07l6.335-3.96a.082.082 0 000-.14L5.126 4.011A.083.083 0 005 4.082z"})),Ce=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]])}return r},Ee=e=>Object.entries(e).filter(e=>e[1]).map(e=>e[0]).join(" "),Oe=o.forwardRef((e,t)=>{var{classes:r,className:n,direction:i,orientation:s,disabled:a}=e,l=Ce(e,["classes","className","direction","orientation","disabled"]);return o.createElement(ve.a,Object.assign({component:"div",className:Ee({[r.root||""]:!0,[r.vertical||""]:"vertical"===s,"sv-tab-scroll-button-disabled":Boolean(a),[n||""]:!0,"sv-tab-scroll-button":!0}),ref:t},l),"left"===i?o.createElement(we,{"data-cy":"tabbar-left-caret"}):o.createElement(be,{"data-cy":"tabbar-right-caret"}))}),ye=Object(pe.a)({root:{width:40,flexShrink:0},disabled:{}})(Oe),Re=r(190),Te=e=>{var{isActive:t}=e;return n.a.createElement(n.a.Fragment,null,n.a.createElement("svg",{height:"16",width:"16",viewBox:"5 4 16 16",className:t?"active":"inactive"},n.a.createElement("path",{className:"sv-hamburger-icon",d:"M19.922 11H4.078a.09.09 0 00-.078.1v1.8a.089.089 0 00.078.1h15.844a.089.089 0 00.078-.1v-1.8a.09.09 0 00-.078-.1zM19.922 6H4.078A.09.09 0 004 6.1v1.8a.089.089 0 00.078.1h15.844A.089.089 0 0020 7.9V6.1a.09.09 0 00-.078-.1zM19.922 16H4.078a.09.09 0 00-.078.1v1.8a.089.089 0 00.078.1h15.844a.089.089 0 00.078-.1v-1.8a.09.09 0 00-.078-.1z"})),n.a.createElement("div",{role:"tooltip",className:"icon-tooltip"},n.a.createElement("span",null,"More sheets")))},Se=e=>{var{isActive:t}=e;return n.a.createElement(n.a.Fragment,null,n.a.createElement("svg",{height:"24",width:"24",viewBox:"0 0 24 24",className:t?"active":"inactive"},n.a.createElement("g",{transform:"translate(-679.64 -429.029)"},n.a.createElement("rect",{className:"sv-hamburger-icon",width:20.001,height:2,rx:.098,transform:"translate(681.64 433.029)"}),n.a.createElement("rect",{className:"sv-hamburger-icon",width:20.001,height:2,rx:.098,transform:"translate(681.64 440.029)"}),n.a.createElement("rect",{className:"sv-hamburger-icon",width:20.001,height:2,rx:.098,transform:"translate(681.64 447.029)"}))))},Me=e=>{var{onClick:t,isClicked:r,isMobile:o}=e;return n.a.createElement("div",{className:"sv-dropup-menu-button",role:"button",tabIndex:0,onClick:t,"data-cy":"dropup-menu-button"},o?n.a.createElement(Se,{isActive:r}):n.a.createElement(Te,{isActive:r}))},Ne=r(187),He=e=>{var{anchorEl:t,handleCloseMenu:r,children:o,isMobile:i}=e;return n.a.createElement(Ne.a,{anchorEl:t,keepMounted:!0,open:Boolean(t),onClose:r,anchorOrigin:{vertical:"top",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},PaperProps:{style:i?{maxHeight:232,width:232}:{maxHeight:267,width:152}},classes:{paper:"sv-dropup-menu-container",list:"sv-dropup-menu-list"}},o)},Le=e=>{var{currentTabIndex:t,onTabChange:r,titles:i}=e,[s,a]=Object(o.useState)(void 0),l=Object(B.b)();return n.a.createElement(n.a.Fragment,null,n.a.createElement(Me,{onClick:e=>{a(e.currentTarget)},isClicked:!!s,isMobile:l}),n.a.createElement(He,{anchorEl:s,handleCloseMenu:()=>{a(void 0)},isMobile:l},i.map((e,o)=>{var i=t===o?"active":"";return n.a.createElement(Re.a,{key:o,selected:Boolean(i),classes:{root:"sv-dropup-menu-item",selected:i},onMouseDown:()=>r(o),disableRipple:!0,"data-cy":"dropup-menu-item-".concat(o)},n.a.createElement("div",null,e))})))},xe=e=>{var{currentTabIndex:t,onTabChange:r,titles:i}=e;Object(o.useMemo)(()=>Object(c.c)(c.a.presentationTabsRendering),[]),Object(o.useLayoutEffect)(()=>Object(c.b)(c.a.presentationTabsRendering),[]),Object(c.c)(c.a.presentationTabsParsing);var s=n.a.createElement(fe.a,{position:"static",color:"default",className:"sv-tabbar"},n.a.createElement(Le,{onTabChange:r,currentTabIndex:t,titles:i}),n.a.createElement(me.a,{value:t,onChange:(e,t)=>r(Number(t)),textColor:"primary",variant:"scrollable",scrollButtons:"auto",ScrollButtonComponent:ye,"aria-label":"scrollable auto tabs example",classes:{indicator:"sv-tab-indicator",scrollable:"sv-tabs-scrollable"},className:"sv-tabs","data-cy":"tabbar"},i.map((e,t)=>((e,t,r)=>n.a.createElement(ge.a,{label:e,key:t,className:"sv-tab","data-cy":"tabbar-workbook-tab",disableRipple:!0,onMouseDown:e=>{e.preventDefault(),r()}}))(e,t,()=>r(t)))));return Object(c.b)(c.a.presentationTabsParsing),s},Ae=r(96),Ie=r(6),je=r(81),De=e=>{e.preventDefault()};t.default=e=>{var{onSheetChange:t,simulateError:r,loadWorkbook:l,workbookState:c,latestLoadedSheet:h,requestMessageOnError:d,svId:u}=e,{Modals:g,openModal:f}=Object(a.useModals)(),m=(e=>{var[t,r]=Object(o.useState)(!1);return Object(o.useEffect)(()=>{var t=setTimeout(()=>r(!0),e);return()=>clearTimeout(t)},[e]),t})(1e3);return Object(o.useEffect)(()=>{if(r===le.l.REACT_INITIALIZATION_ERROR)throw new Error(le.l.REACT_INITIALIZATION_ERROR)},[r]),(()=>{var e;if(void 0===c)return m?n.a.createElement(i,null):n.a.createElement(s.a,null);if("initialising"===c.type)return null;var{latestSheet:o}=c.sheetHistory,a=void 0===h?void 0:c.sheets.get(h),p=Object(_.b)();Object(Ie.a)(p,c.sheetNames.length);var v=c.input;return n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"sv-app-container",onContextMenu:De},n.a.createElement(g,null),!p&&n.a.createElement(Ae.a,{fileName:null!==(e=Object(je.a)(v))&&void 0!==e?e:je.b,workbookLocation:"url"===v.type?v.url:v.arrayBuffer,renderWorkbook:l,openModal:f}),void 0!==h&&"ready"===(null==a?void 0:a.type)&&n.a.createElement(ue,{shouldSimulateHandsontableError:r===le.l.RENDER_ERROR,shouldSimulateInterpreterError:r===le.l.INTERPRETER_ERROR,parsedData:a.parsedData,sheet:h,requestMessageOnError:d,fullPage:p,svId:u}),!p&&n.a.createElement(xe,{currentTabIndex:o,onTabChange:t,titles:c.sheetNames})))})()||n.a.createElement(s.a,null)}}])]);