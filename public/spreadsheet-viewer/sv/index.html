<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Spreadsheet Viewer frame</title>
    <style>
      .icons-wrapper{
        display: none !important;
      }
      #download-icon {
        display: none !important;
      }
      #feedback-icon {
        display: none !important;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>

    <!-- Gets the non-polyfilled/non-babel entry point with `type="module"` inlined, ensuring that it only loads in modern browsers. -->
    <script type="module">
      /*!
 * Spreadsheet Viewer
 * 
 * Version: 1.0.1
 * Code version: 06577ad
 * Build date: Thu, October 21, 2021, 12:29 PM GMT+2
 */!function(e){function t(t){for(var n,r,a=t[0],i=t[1],l=0,u=[];l<a.length;l++)r=a[l],Object.prototype.hasOwnProperty.call(o,r)&&o[r]&&u.push(o[r][0]),o[r]=0;for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n]);for(s&&s(t);u.length;)u.shift()()}var n={},r={4:0},o={4:0};function a(t){if(n[t])return n[t].exports;var r=n[t]={i:t,l:!1,exports:{}};return e[t].call(r.exports,r,r.exports,a),r.l=!0,r.exports}a.e=function(e){var t=[];r[e]?t.push(r[e]):0!==r[e]&&{2:1}[e]&&t.push(r[e]=new Promise((function(t,n){for(var o=({0:"vendors~CrashScreen~SpreadsheetViewer",1:"CrashScreen",2:"SpreadsheetViewer",3:"embedsChartRenderer",5:"vendors~SpreadsheetViewer",6:"vendors~embedsChartRenderer"}[e]||e)+"."+{0:"31d6cfe0d16ae931b73c",1:"31d6cfe0d16ae931b73c",2:"9e038aabc7f9bfa77853",3:"31d6cfe0d16ae931b73c",5:"31d6cfe0d16ae931b73c",6:"31d6cfe0d16ae931b73c"}[e]+".extracted.chunk.css",i=a.p+o,l=document.getElementsByTagName("link"),u=0;u<l.length;u++){var s=(d=l[u]).getAttribute("data-href")||d.getAttribute("href");if("stylesheet"===d.rel&&(s===o||s===i))return t()}var c=document.getElementsByTagName("style");for(u=0;u<c.length;u++){var d;if((s=(d=c[u]).getAttribute("data-href"))===o||s===i)return t()}var f=document.createElement("link");f.rel="stylesheet",f.type="text/css",f.onload=t,f.onerror=function(t){var o=t&&t.target&&t.target.src||i,a=new Error("Loading CSS chunk "+e+" failed.\n("+o+")");a.code="CSS_CHUNK_LOAD_FAILED",a.request=o,delete r[e],f.parentNode.removeChild(f),n(a)},f.href=i,document.getElementsByTagName("head")[0].appendChild(f)})).then((function(){r[e]=0})));var n=o[e];if(0!==n)if(n)t.push(n[2]);else{var i=new Promise((function(t,r){n=o[e]=[t,r]}));t.push(n[2]=i);var l,u=document.createElement("script");u.charset="utf-8",u.timeout=120,a.nc&&u.setAttribute("nonce",a.nc),u.src=function(e){return a.p+"modern."+({0:"vendors~CrashScreen~SpreadsheetViewer",1:"CrashScreen",2:"SpreadsheetViewer",3:"embedsChartRenderer",5:"vendors~SpreadsheetViewer",6:"vendors~embedsChartRenderer"}[e]||e)+"."+{0:"8ca49bc3d3f93e353a54",1:"e1fb7537a0b82eb8e087",2:"81bfc511baeaa02d999a",3:"073961864854d4803a45",5:"c88a22625e49026e7505",6:"c2a5c12c15d5c86b9457"}[e]+".chunk.js"}(e);var s=new Error;l=function(t){u.onerror=u.onload=null,clearTimeout(c);var n=o[e];if(0!==n){if(n){var r=t&&("load"===t.type?"missing":t.type),a=t&&t.target&&t.target.src;s.message="Loading chunk "+e+" failed.\n("+r+": "+a+")",s.name="ChunkLoadError",s.type=r,s.request=a,n[1](s)}o[e]=void 0}};var c=setTimeout((function(){l({type:"timeout",target:u})}),12e4);u.onerror=u.onload=l,document.head.appendChild(u)}return Promise.all(t)},a.m=e,a.c=n,a.d=function(e,t,n){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(a.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)a.d(n,r,function(t){return e[t]}.bind(null,r));return n},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="",a.oe=function(e){throw console.error(e),e};var i=window.webpackJsonp=window.webpackJsonp||[],l=i.push.bind(i);i.push=t,i=i.slice();for(var u=0;u<i.length;u++)t(i[u]);var s=l;a(a.s=31)}([function(e,t,n){"use strict";var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0});var a=n(5),i=n(29),l=n(13),u=n(30),s=function(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)},c=function(e,t){var n="";switch(typeof t){case"undefined":n="undefined";break;case"object":n=null===t?"null":Array.isArray(t)?"an array with value "+JSON.stringify(t):"an object with value "+JSON.stringify(t);break;case"boolean":n="a boolean"}return"Expected "+e+", but received "+(n=n||"a "+typeof t+" with value "+JSON.stringify(t))};t.Codec={interface:function(e){var t=function(t){var n,r;if(!s(t))return a.Left(c("an object",t));var i={},l=Object.keys(e);try{for(var u=o(l),d=u.next();!d.done;d=u.next()){var f=d.value;if(!t.hasOwnProperty(f)&&!e[f]._isOptional)return a.Left('Problem with property "'+f+'": it does not exist in received object '+JSON.stringify(t));var p=e[f].decode(t[f]);if(p.isLeft())return a.Left('Problem with the value of property "'+f+'": '+p.extract());i[f]=p.extract()}}catch(e){n={error:e}}finally{try{d&&!d.done&&(r=u.return)&&r.call(u)}finally{if(n)throw n.error}}return a.Right(i)};return{decode:t,encode:function(t){var n,r,a={},i=Object.keys(e);try{for(var l=o(i),u=l.next();!u.done;u=l.next()){var s=u.value;a[s]=e[s].encode(t[s])}}catch(e){n={error:e}}finally{try{u&&!u.done&&(r=l.return)&&r.call(l)}finally{if(n)throw n.error}}return a},unsafeDecode:function(e){return t(e).unsafeCoerce()}}},custom:function(e){var t=e.decode,n=e.encode;return{decode:t,encode:n,unsafeDecode:function(e){return t(e).unsafeCoerce()}}}},t.string=t.Codec.custom({decode:function(e){return"string"==typeof e?a.Right(e):a.Left(c("a string",e))},encode:i.identity}),t.number=t.Codec.custom({decode:function(e){return"number"==typeof e?a.Right(e):a.Left(c("a number",e))},encode:i.identity}),t.nullType=t.Codec.custom({decode:function(e){return null===e?a.Right(e):a.Left(c("a null",e))},encode:i.identity});var d=t.Codec.custom({decode:function(e){return void 0===e?a.Right(e):a.Left(c("an undefined",e))},encode:i.identity});t.optional=function(e){return r(r({},t.oneOf([e,d])),{_isOptional:!0})},t.boolean=t.Codec.custom({decode:function(e){return"boolean"==typeof e?a.Right(e):a.Left(c("a boolean",e))},encode:i.identity}),t.unknown=t.Codec.custom({decode:a.Right,encode:i.identity}),t.oneOf=function(e){return t.Codec.custom({decode:function(t){var n,r,i=[];try{for(var l=o(e),u=l.next();!u.done;u=l.next()){var s=u.value.decode(t);if(s.isRight())return s;i.push(s.extract())}}catch(e){n={error:e}}finally{try{u&&!u.done&&(r=l.return)&&r.call(l)}finally{if(n)throw n.error}}return a.Left("One of the following problems occured: "+i.map((function(e,t){return"("+t+") "+e})).join(", "))},encode:function(t){var n,r;try{for(var a=o(e),i=a.next();!i.done;i=a.next()){var l=i.value;if(l.decode(t).isRight())return l.encode(t)}}catch(e){n={error:e}}finally{try{i&&!i.done&&(r=a.return)&&r.call(a)}finally{if(n)throw n.error}}return t}})},t.array=function(e){return t.Codec.custom({decode:function(t){if(Array.isArray(t)){for(var n=[],r=0;r<t.length;r++){var o=e.decode(t[r]);if(!o.isRight())return a.Left("Problem with value at index "+r+": "+o.extract());n.push(o.extract())}return a.Right(n)}return a.Left(c("an array",t))},encode:function(t){return t.map(e.encode)}})};var f=t.Codec.custom({decode:function(e){return t.string.decode(e).chain((function(t){return isFinite(+t)?a.Right(t):a.Left(c("a number key",e))}))},encode:i.identity});t.record=function(e,n){return t.Codec.custom({decode:function(r){var i,l,u={},d=e===t.number?f:e;if(!s(r))return a.Left(c("an object",r));try{for(var p=o(Object.keys(r)),h=p.next();!h.done;h=p.next()){var m=h.value;if(r.hasOwnProperty(m)){var g=d.decode(m),y=n.decode(r[m]);if(g.isRight()&&y.isRight())u[g.extract()]=y.extract();else{if(g.isLeft())return a.Left('Problem with key type of property "'+m+'": '+g.extract());if(y.isLeft())return a.Left('Problem with value of property "'+m+'": '+y.extract())}}}}catch(e){i={error:e}}finally{try{h&&!h.done&&(l=p.return)&&l.call(p)}finally{if(i)throw i.error}}return a.Right(u)},encode:function(t){var r={};for(var o in t)t.hasOwnProperty(o)&&(r[e.encode(o)]=n.encode(t[o]));return r}})},t.exactly=function(e){return t.Codec.custom({decode:function(t){return t===e?a.Right(e):a.Left(typeof t==typeof e?"Expected a "+typeof t+" with a value of exactly "+JSON.stringify(e)+", the types match, but the received value is "+JSON.stringify(t):c("a "+typeof e+" with a value of exactly "+e,t))},encode:i.identity})},t.lazy=function(e){return t.Codec.custom({decode:function(t){return e().decode(t)},encode:function(t){return e().encode(t)}})},t.maybe=function(e){return t.Codec.custom({decode:function(t){return l.Maybe.fromNullable(t).caseOf({Just:function(t){return e.decode(t).map(l.Just)},Nothing:function(){return a.Right(l.Nothing)}})},encode:function(e){return e.toJSON()}})},t.nonEmptyList=function(e){var n=t.array(e);return t.Codec.custom({decode:function(e){return n.decode(e).chain((function(e){return u.NonEmptyList.fromArray(e).toEither("Expected an array with one or more elements, but received an empty array")}))},encode:n.encode})},t.tuple=function(e){return t.Codec.custom({decode:function(t){if(Array.isArray(t)){if(e.length!==t.length)return a.Left("Expected an array of length "+e.length+", but received an array with length of "+t.length);for(var n=[],r=0;r<e.length;r++){var o=e[r].decode(t[r]);if(!o.isRight())return a.Left("Problem with value at index "+r+": "+o.extract());n.push(o.extract())}return a.Right(n)}return a.Left(c("an array",t))},encode:function(t){return t.map((function(t,n){return e[n].encode(t)}))}})},t.date=t.Codec.custom({decode:function(e){return t.string.decode(e).mapLeft((function(e){return"Problem with date string: "+e})).chain((function(e){return Number.isNaN(Date.parse(e))?a.Left("Expected a valid date string, but received a string that cannot be parsed"):a.Right(new Date(e))}))},encode:function(e){return e.toISOString()}})},function(e,t,n){"use strict";e.exports=n(23)},function(e,t,n){"use strict";var r;n.d(t,"l",(function(){return r})),n.d(t,"m",(function(){return i})),n.d(t,"n",(function(){return l})),n.d(t,"b",(function(){return u})),n.d(t,"c",(function(){return c})),n.d(t,"a",(function(){return d})),n.d(t,"e",(function(){return f})),n.d(t,"i",(function(){return p})),n.d(t,"j",(function(){return h})),n.d(t,"f",(function(){return m})),n.d(t,"k",(function(){return g})),n.d(t,"o",(function(){return y})),n.d(t,"g",(function(){return v})),n.d(t,"h",(function(){return b})),n.d(t,"d",(function(){return w})),n.d(t,"p",(function(){return F})),n.d(t,"q",(function(){return C})),function(e){e.RENDER_ERROR="RENDER_ERROR",e.INTERPRETER_ERROR="INTERPRETER_ERROR",e.REACT_INITIALIZATION_ERROR="REACT_INITIALIZATION_ERROR"}(r||(r={}));class o extends Error{}var a=o;class i extends a{}class l extends a{}class u extends a{}class s extends a{}class c extends a{}class d extends a{}class f extends a{}class p extends a{}class h extends a{}class m extends a{}class g extends a{}class y extends a{}class v extends a{}class b extends a{}class w extends a{}var F=e=>e instanceof i?"UNSUPPORTED_FILE_FORMAT_ERROR":e instanceof l?"UNSUPPORTED_WORKBOOK_FORMAT_ERROR":e instanceof u?"FILE_LOADING_STATUS_ERROR":e instanceof s?"FILE_LOADING_MIME_TYPE_ERROR":e instanceof c?"FILE_LOADING_TIMEOUT_ERROR":e instanceof d?"FILE_LOADING_NETWORK_ERROR":e instanceof f?"FILE_SIZE_ERROR":e instanceof p?"PARSER_ERROR":e instanceof h?"SHEET_LIMIT_ERROR":e instanceof m?"INTERPRETER_ERROR":e instanceof g?"RENDER_ERROR":e instanceof y?"WORKER_CACHE_INTEGRITY_ERROR":e instanceof v?"INVALID_QUERY_STRING_API_PARAMETER_ERROR":e instanceof b?"INVALID_REQUEST_MESSAGE_ERROR":e instanceof w?"FILE_PROTECTION_ERROR":"UNKNOWN_ERROR",C=e=>"FILE_PROTECTION_ERROR"===e?"We cannot display password protected files yet. \n Please use the below button to download the file.":"UNSUPPORTED_WORKBOOK_FORMAT_ERROR"===e?"Sorry, this workbook file format is not supported yet.":"UNSUPPORTED_FILE_FORMAT_ERROR"===e?"Sorry, this file cannot be previewed.":"Sorry, we can't present you a preview of this file right now"},function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"b",(function(){return i}));var r={parser:"parser",parserReading:"parserReading",parserFixing:"parserFixing",interpreter:"interpreter",interpreterStyleClone:"interpreterStyleClone",interpreterCss:"interpreterCss",interpreterHotConfig:"interpreterHotConfig",presentation:"presentation",presentationTabsParsing:"presentationTabsParsing",presentationTabsRendering:"presentationTabsRendering",presentationHotRendering:"presentationHotRendering"},o=new Set,[a,i]=[e=>{o.add(e),console.time(e),performance.mark("".concat(e,"Start"))},e=>{o.has(e)&&(o.delete(e),console.timeEnd(e)),performance.mark("".concat(e,"End"))}]},function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return o})),n.d(t,"e",(function(){return a})),n.d(t,"h",(function(){return i})),n.d(t,"i",(function(){return l})),n.d(t,"f",(function(){return u})),n.d(t,"d",(function(){return s})),n.d(t,"b",(function(){return c})),n.d(t,"g",(function(){return d}));var r="!merges",o="!cols",a="!rows",i="xSplit",l="ySplit",u="showGrid",s="!objects",c="!sv-defaultsizes",d="!sv-tables"},function(e,t,n){"use strict";var r=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0});var o=n(13);t.Either={of:function(e){return u(e)},lefts:function(e){var t,n,o=[];try{for(var a=r(e),i=a.next();!i.done;i=a.next()){var l=i.value;l.isLeft()&&o.push(l.extract())}}catch(e){t={error:e}}finally{try{i&&!i.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}return o},rights:function(e){var t,n,o=[];try{for(var a=r(e),i=a.next();!i.done;i=a.next()){var l=i.value;l.isRight()&&o.push(l.extract())}}catch(e){t={error:e}}finally{try{i&&!i.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}return o},encase:function(e){try{return u(e())}catch(e){return l(e)}},"fantasy-land/of":function(e){return t.Either.of(e)}};var a=function(){function e(e){this.__value=e}return e.prototype.isLeft=function(){return!1},e.prototype.isRight=function(){return!0},e.prototype.toJSON=function(){return this.__value},e.prototype.inspect=function(){return"Right("+JSON.stringify(this.__value)+")"},e.prototype.toString=function(){return this.inspect()},e.prototype.bimap=function(e,t){return u(t(this.__value))},e.prototype.map=function(e){return u(e(this.__value))},e.prototype.mapLeft=function(e){return this},e.prototype.ap=function(e){return e.isLeft()?e:this.map(e.__value)},e.prototype.equals=function(e){return!!e.isRight()&&this.__value===e.__value},e.prototype.chain=function(e){return e(this.__value)},e.prototype.chainLeft=function(e){return this},e.prototype.join=function(){return this.__value},e.prototype.alt=function(e){return this},e.prototype.reduce=function(e,t){return e(t,this.__value)},e.prototype.extend=function(e){return u(e(this))},e.prototype.unsafeCoerce=function(){return this.__value},e.prototype.caseOf=function(e){return"_"in e?e._():e.Right(this.__value)},e.prototype.leftOrDefault=function(e){return e},e.prototype.orDefault=function(e){return this.__value},e.prototype.orDefaultLazy=function(e){return this.__value},e.prototype.leftOrDefaultLazy=function(e){return e()},e.prototype.ifLeft=function(e){return this},e.prototype.ifRight=function(e){return e(this.__value),this},e.prototype.toMaybe=function(){return o.Just(this.__value)},e.prototype.leftToMaybe=function(){return o.Nothing},e.prototype.either=function(e,t){return t(this.__value)},e.prototype.extract=function(){return this.__value},e.prototype.swap=function(){return l(this.__value)},e.prototype["fantasy-land/bimap"]=function(e,t){return this.bimap(e,t)},e.prototype["fantasy-land/map"]=function(e){return this.map(e)},e.prototype["fantasy-land/ap"]=function(e){return this.ap(e)},e.prototype["fantasy-land/equals"]=function(e){return this.equals(e)},e.prototype["fantasy-land/chain"]=function(e){return this.chain(e)},e.prototype["fantasy-land/alt"]=function(e){return this.alt(e)},e.prototype["fantasy-land/reduce"]=function(e,t){return this.reduce(e,t)},e.prototype["fantasy-land/extend"]=function(e){return this.extend(e)},e}();a.prototype.constructor=t.Either;var i=function(){function e(e){this.__value=e}return e.prototype.isLeft=function(){return!0},e.prototype.isRight=function(){return!1},e.prototype.toJSON=function(){return this.__value},e.prototype.inspect=function(){return"Left("+JSON.stringify(this.__value)+")"},e.prototype.toString=function(){return this.inspect()},e.prototype.bimap=function(e,t){return l(e(this.__value))},e.prototype.map=function(e){return this},e.prototype.mapLeft=function(e){return l(e(this.__value))},e.prototype.ap=function(e){return e.isLeft()?e:this},e.prototype.equals=function(e){return!!e.isLeft()&&e.__value===this.__value},e.prototype.chain=function(e){return this},e.prototype.chainLeft=function(e){return e(this.__value)},e.prototype.join=function(){return this},e.prototype.alt=function(e){return e},e.prototype.reduce=function(e,t){return t},e.prototype.extend=function(e){return this},e.prototype.unsafeCoerce=function(){throw new Error("Either got coerced to a Left")},e.prototype.caseOf=function(e){return"_"in e?e._():e.Left(this.__value)},e.prototype.leftOrDefault=function(e){return this.__value},e.prototype.orDefault=function(e){return e},e.prototype.orDefaultLazy=function(e){return e()},e.prototype.leftOrDefaultLazy=function(e){return this.__value},e.prototype.ifLeft=function(e){return e(this.__value),this},e.prototype.ifRight=function(e){return this},e.prototype.toMaybe=function(){return o.Nothing},e.prototype.leftToMaybe=function(){return o.Just(this.__value)},e.prototype.either=function(e,t){return e(this.__value)},e.prototype.extract=function(){return this.__value},e.prototype.swap=function(){return u(this.__value)},e.prototype["fantasy-land/bimap"]=function(e,t){return this.bimap(e,t)},e.prototype["fantasy-land/map"]=function(e){return this.map(e)},e.prototype["fantasy-land/ap"]=function(e){return this.ap(e)},e.prototype["fantasy-land/equals"]=function(e){return this.equals(e)},e.prototype["fantasy-land/chain"]=function(e){return this.chain(e)},e.prototype["fantasy-land/alt"]=function(e){return this.alt(e)},e.prototype["fantasy-land/reduce"]=function(e,t){return this.reduce(e,t)},e.prototype["fantasy-land/extend"]=function(e){return this.extend(e)},e}();i.prototype.constructor=t.Either;var l=function(e){return new i(e)};t.Left=l;var u=function(e){return new a(e)};t.Right=u},function(e,t,n){"use strict";n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return i}));var r="ABCDEFGHIJKLMNOPQRSTUVWXYZ".length;function o(e){for(var t,n=e+1,o="";n>0;)t=(n-1)%r,o=String.fromCharCode(65+t)+o,n=parseInt((n-t)/r,10);return o}function a(e){var t=0;if(e)for(var n=0,o=e.length-1;n<e.length;n+=1,o-=1)t+=Math.pow(r,o)*("ABCDEFGHIJKLMNOPQRSTUVWXYZ".indexOf(e[n])+1);return t-=1}var i=(e,t)=>{e&&!document.body.dataset.sheetCount&&(document.body.dataset.sheetCount=t.toString())}},function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"b",(function(){return l})),n.d(t,"d",(function(){return u}));var r=n(11),o=(e,t)=>{window.parent.postMessage(Object.assign(Object.assign({},t),{id:Object(r.b)(e)}),"*")},a=(e,t)=>{o(e,{name:"activeSheetChanged",sheet:t})},i=(e,t)=>{o(e,{name:"cellSelectionChanged",range:t})},l=e=>{o(e,{name:"readyForMessages"})},u=(e,t)=>{t.addEventListener("keydown",t=>((e,t)=>{o(e,{name:"keydown",key:t})})(e,t.key)),t.addEventListener("keyup",t=>((e,t)=>{o(e,{name:"keyup",key:t})})(e,t.key)),t.addEventListener("drop",t=>{t.preventDefault(),t.dataTransfer&&((e,t)=>{o(e,{name:"drop",files:t})})(e,t.dataTransfer.files)}),t.addEventListener("dragover",t=>{t.preventDefault(),(e=>{o(e,{name:"dragover"})})(e)}),t.addEventListener("dragleave",t=>(e=>{o(e,{name:"dragleave"})})(e))}},function(e,t,n){"use strict";n.d(t,"e",(function(){return l})),n.d(t,"b",(function(){return s})),n.d(t,"f",(function(){return c})),n.d(t,"c",(function(){return d})),n.d(t,"d",(function(){return f})),n.d(t,"a",(function(){return p}));var r=n(0),o=n(5),a=n(9),i=r.Codec.custom({decode:e=>e instanceof ArrayBuffer?Object(o.Right)(e):Object(o.Left)("Specified value isn't an instance of ArrayBuffer"),encode:e=>e}),l=(Object(r.oneOf)([r.string,i]),"loadWorkbook"),u={name:Object(r.exactly)(l),sheet:r.number},s=Object(r.oneOf)([r.Codec.interface(Object.assign({workbook:i,fileName:r.string},u)),r.Codec.interface(Object.assign({workbook:r.string,fileName:Object(r.optional)(r.string)},u))]),c="selectCells",d=r.Codec.interface({name:Object(r.exactly)(c),range:a.a}),f="configure",p=r.Codec.interface(Object.assign({name:Object(r.exactly)(f)},a.b))},function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return a})),n.d(t,"b",(function(){return i}));var r=n(0),o=Object(r.tuple)([r.number,r.number,r.number,r.number]),a=Object(r.oneOf)([Object(r.exactly)("light"),Object(r.exactly)("dark")]),i={themeStylesheet:Object(r.optional)(a),licenseKey:Object(r.optional)(r.string)};r.Codec.interface(i)},function(e,t,n){"use strict";function r(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function o(e,t,n){return r(e,t)?e[t]:n}n.d(t,"c",(function(){return r})),n.d(t,"b",(function(){return o})),n.d(t,"a",(function(){return a}));var a=(e,t)=>{if(r(e,t))return e[t]}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return a}));var r=Symbol("sv id"),o=e=>({[r]:e}),a=e=>e[r]},function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return c}));var r=n(5),o=n(0),a=n(9);function i(e){for(var t=new Map,n=e.substr(1).split("&"),r=0;r<n.length;r++){var o=n[r].split("=");t.set(decodeURIComponent(o[0]),decodeURIComponent(o[1]||""))}return t}var l=o.Codec.custom({decode:e=>{var t=Number(e);return isNaN(t)?Object(r.Left)("Expected numeric value to not be NaN"):Object(r.Right)(t)},encode:e=>e}),u={svId:void 0,workbookUrl:void 0,sheet:void 0,fileName:void 0,licenseKey:void 0,simulateError:void 0,themeStylesheet:void 0},s=o.Codec.interface({svId:Object(o.optional)(o.string),workbookUrl:Object(o.optional)(o.string),sheet:Object(o.optional)(l),fileName:Object(o.optional)(o.string),licenseKey:Object(o.optional)(o.string),simulateError:Object(o.optional)(o.string),themeStylesheet:Object(o.optional)(a.c)}),c=e=>{var t=e.indexOf("?");if(-1===t)return{error:void 0,queryParameters:u};var n=i(e.substr(t)),r={svId:n.get("svId"),workbookUrl:n.get("workbookUrl"),sheet:n.get("sheet"),fileName:n.get("fileName"),licenseKey:n.get("licenseKey"),simulateError:n.get("simulateError"),themeStylesheet:n.get("themeStylesheet")};return s.decode(r).either(e=>({error:e,queryParameters:u}),e=>({error:void 0,queryParameters:e}))}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(5);t.Maybe={of:function(e){return i(e)},empty:function(){return l},zero:function(){return l},fromNullable:function(e){return null==e?l:i(e)},fromFalsy:function(e){return e?i(e):l},fromPredicate:function(e,n){switch(arguments.length){case 1:return function(n){return t.Maybe.fromPredicate(e,n)};default:return e(n)?i(n):l}},mapMaybe:function(e,n){switch(arguments.length){case 1:return function(n){return t.Maybe.mapMaybe(e,n)};default:return t.Maybe.catMaybes(n.map(e))}},catMaybes:function(e){return e.filter((function(e){return e.isJust()})).map((function(e){return e.__value}))},encase:function(e){try{return i(e())}catch(e){return l}},"fantasy-land/of":function(e){return this.of(e)},"fantasy-land/empty":function(){return this.empty()},"fantasy-land/zero":function(){return this.zero()}};var o=function(){function e(e){this.__value=e}return e.prototype.isJust=function(){return!0},e.prototype.isNothing=function(){return!1},e.prototype.inspect=function(){return"Just("+JSON.stringify(this.__value)+")"},e.prototype.toString=function(){return this.inspect()},e.prototype.toJSON=function(){return this.__value},e.prototype.equals=function(e){return this.__value===e.__value},e.prototype.map=function(e){return i(e(this.__value))},e.prototype.ap=function(e){return e.isNothing()?l:this.map(e.__value)},e.prototype.alt=function(e){return this},e.prototype.chain=function(e){return e(this.__value)},e.prototype.chainNullable=function(e){return t.Maybe.fromNullable(e(this.__value))},e.prototype.join=function(){return this.__value},e.prototype.reduce=function(e,t){return e(t,this.__value)},e.prototype.extend=function(e){return i(e(this))},e.prototype.unsafeCoerce=function(){return this.__value},e.prototype.caseOf=function(e){return"_"in e?e._():e.Just(this.__value)},e.prototype.orDefault=function(e){return this.__value},e.prototype.orDefaultLazy=function(e){return this.__value},e.prototype.toList=function(){return[this.__value]},e.prototype.mapOrDefault=function(e,t){return e(this.__value)},e.prototype.extract=function(){return this.__value},e.prototype.extractNullable=function(){return this.__value},e.prototype.toEither=function(e){return r.Right(this.__value)},e.prototype.ifJust=function(e){return e(this.__value),this},e.prototype.ifNothing=function(e){return this},e.prototype.filter=function(e){return e(this.__value)?i(this.__value):l},e.prototype["fantasy-land/equals"]=function(e){return this.equals(e)},e.prototype["fantasy-land/map"]=function(e){return this.map(e)},e.prototype["fantasy-land/ap"]=function(e){return this.ap(e)},e.prototype["fantasy-land/alt"]=function(e){return this.alt(e)},e.prototype["fantasy-land/chain"]=function(e){return this.chain(e)},e.prototype["fantasy-land/reduce"]=function(e,t){return this.reduce(e,t)},e.prototype["fantasy-land/extend"]=function(e){return this.extend(e)},e.prototype["fantasy-land/filter"]=function(e){return this.filter(e)},e}();o.prototype.constructor=t.Maybe;var a=function(){function e(){}return e.prototype.isJust=function(){return!1},e.prototype.isNothing=function(){return!0},e.prototype.inspect=function(){return"Nothing"},e.prototype.toString=function(){return this.inspect()},e.prototype.toJSON=function(){return this.__value},e.prototype.equals=function(e){return this.__value===e.__value},e.prototype.map=function(e){return l},e.prototype.ap=function(e){return l},e.prototype.alt=function(e){return e},e.prototype.chain=function(e){return l},e.prototype.chainNullable=function(e){return l},e.prototype.join=function(){return l},e.prototype.reduce=function(e,t){return t},e.prototype.extend=function(e){return l},e.prototype.unsafeCoerce=function(){throw new Error("Maybe got coerced to a null")},e.prototype.caseOf=function(e){return"_"in e?e._():e.Nothing()},e.prototype.orDefault=function(e){return e},e.prototype.orDefaultLazy=function(e){return e()},e.prototype.toList=function(){return[]},e.prototype.mapOrDefault=function(e,t){return t},e.prototype.extract=function(){},e.prototype.extractNullable=function(){return null},e.prototype.toEither=function(e){return r.Left(e)},e.prototype.ifJust=function(e){return this},e.prototype.ifNothing=function(e){return e(),this},e.prototype.filter=function(e){return l},e.prototype["fantasy-land/equals"]=function(e){return this.equals(e)},e.prototype["fantasy-land/map"]=function(e){return this.map(e)},e.prototype["fantasy-land/ap"]=function(e){return this.ap(e)},e.prototype["fantasy-land/alt"]=function(e){return this.alt(e)},e.prototype["fantasy-land/chain"]=function(e){return this.chain(e)},e.prototype["fantasy-land/reduce"]=function(e,t){return this.reduce(e,t)},e.prototype["fantasy-land/extend"]=function(e){return this.extend(e)},e.prototype["fantasy-land/filter"]=function(e){return this.filter(e)},e}();a.prototype.constructor=t.Maybe;var i=function(e){return new o(e)};t.Just=i;var l=new a;t.Nothing=l},function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(1),o=n.n(r),a=()=>o.a.createElement("div",{className:"sv-loading-screen"},o.a.createElement("div",{className:"sv-loading-screen--spinner"}),o.a.createElement("div",{className:"sv-loading-screen--details"},o.a.createElement("p",null,"Loading...")))},function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(1),o=n(0),a=n(2);function i(e,t,n,i){Object(r.useEffect)(()=>{var r=r=>{var{data:l}=r;(e=>o.Codec.interface({name:Object(o.exactly)(e)}))(e).decode(l).isLeft()||t.decode(l).either(t=>{console.error("Invalid request message `".concat(e,"` - ").concat(t)),n(new a.h("Request message ".concat(e," - ").concat(t)))},e=>i(e))};return window.addEventListener("message",r),()=>window.removeEventListener("message",r)},[e,t,n,i])}},function(e,t,n){"use strict";e.exports=n(24)},function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return l}));var r=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i,o=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,a=e=>{var t=e.userAgent||e.vendor;return!(!r.test(t)&&!o.test(t.substr(0,4)))||"MacIntel"===e.platform&&e.maxTouchPoints>1},i=e=>{a(navigator)?e.dataset.isMobile||(e.dataset.isMobile=""):delete e.dataset.isMobile},l=()=>!!a(navigator)},function(e,t,n){"use strict";
/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var r=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable;function i(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}e.exports=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;if("**********"!==Object.getOwnPropertyNames(t).map((function(e){return t[e]})).join(""))return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach((function(e){r[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},r)).join("")}catch(e){return!1}}()?Object.assign:function(e,t){for(var n,l,u=i(e),s=1;s<arguments.length;s++){for(var c in n=Object(arguments[s]))o.call(n,c)&&(u[c]=n[c]);if(r){l=r(n);for(var d=0;d<l.length;d++)a.call(n,l[d])&&(u[l[d]]=n[l[d]])}}return u}},function(e,t,n){"use strict";var r=function(){},o=n(21),a={},i=Function.call.bind(Object.prototype.hasOwnProperty);function l(e,t,n,l,u){for(var s in e)if(i(e,s)){var c;try{if("function"!=typeof e[s]){var d=Error((l||"React class")+": "+n+" type `"+s+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof e[s]+"`.");throw d.name="Invariant Violation",d}c=e[s](t,s,l,n,null,o)}catch(e){c=e}if(!c||c instanceof Error||r((l||"React class")+": type specification of "+n+" `"+s+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+typeof c+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."),c instanceof Error&&!(c.message in a)){a[c.message]=!0;var f=u?u():"";r("Failed "+n+" type: "+c.message+(null!=f?f:""))}}}r=function(e){var t="Warning: "+e;"undefined"!=typeof console&&console.error(t);try{throw new Error(t)}catch(e){}},l.resetWarningCache=function(){a={}},e.exports=l},function(e,t,n){"use strict";n.d(t,"a",(function(){return l})),n.d(t,"b",(function(){return u})),n.d(t,"c",(function(){return s}));var r=n(12),o=Object.freeze({}),a=null,i=e=>{var t;return null===a&&(a=(()=>{var e,t;return 0===window.location.search.length?o:null!==(t=null===(e=Object(r.a)(window.location.search).get("flags"))||void 0===e?void 0:e.split(",").reduce((e,t)=>(e[t]=!0,e),Object.assign({},o)))&&void 0!==t?t:o})()),null!==(t=a[e])&&void 0!==t&&t},l=()=>i("charts"),u=()=>i("fullPage"),s=()=>i("moreformats")},function(e,t,n){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},function(e,t,n){"use strict";n.p="".concat(window.location.pathname.split("/").slice(0,-1).join("/"),"/")},function(e,t,n){"use strict";
/** @license React v16.14.0
 * react.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(){var e=n(18),r=n(19),o="function"==typeof Symbol&&Symbol.for,a=o?Symbol.for("react.element"):60103,i=o?Symbol.for("react.portal"):60106,l=o?Symbol.for("react.fragment"):60107,u=o?Symbol.for("react.strict_mode"):60108,s=o?Symbol.for("react.profiler"):60114,c=o?Symbol.for("react.provider"):60109,d=o?Symbol.for("react.context"):60110,f=o?Symbol.for("react.concurrent_mode"):60111,p=o?Symbol.for("react.forward_ref"):60112,h=o?Symbol.for("react.suspense"):60113,m=o?Symbol.for("react.suspense_list"):60120,g=o?Symbol.for("react.memo"):60115,y=o?Symbol.for("react.lazy"):60116,v=o?Symbol.for("react.block"):60121,b=o?Symbol.for("react.fundamental"):60117,w=o?Symbol.for("react.responder"):60118,F=o?Symbol.for("react.scope"):60119,C="function"==typeof Symbol&&Symbol.iterator;function T(e){if(null===e||"object"!=typeof e)return null;var t=C&&e[C]||e["@@iterator"];return"function"==typeof t?t:null}var k={current:null},E={current:null},x=/^(.*)[\\\/]/;function R(e){if(null==e)return null;if("number"==typeof e.tag&&A("Received an unexpected object in getComponentName(). This is likely a bug in React. Please file an issue."),"function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case l:return"Fragment";case i:return"Portal";case s:return"Profiler";case u:return"StrictMode";case h:return"Suspense";case m:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case d:return"Context.Consumer";case c:return"Context.Provider";case p:return r=e,o=e.render,a="ForwardRef",f=o.displayName||o.name||"",r.displayName||(""!==f?a+"("+f+")":a);case g:return R(e.type);case v:return R(e.render);case y:var t=1===(n=e)._status?n._result:null;if(t)return R(t)}var n,r,o,a,f;return null}var S={},D=null;function _(e){D=e}S.getCurrentStack=null,S.getStackAddendum=function(){var e="";if(D){var t=R(D.type),n=D._owner;e+=function(e,t,n){var r="";if(t){var o=t.fileName,a=o.replace(x,"");if(/^index\./.test(a)){var i=o.match(x);if(i){var l=i[1];if(l)a=l.replace(x,"")+"/"+a}}r=" (at "+a+":"+t.lineNumber+")"}else n&&(r=" (created by "+n+")");return"\n    in "+(e||"Unknown")+r}(t,D._source,n&&R(n.type))}var r=S.getCurrentStack;return r&&(e+=r()||""),e};var O={ReactCurrentDispatcher:k,ReactCurrentBatchConfig:{suspense:null},ReactCurrentOwner:E,IsSomeRendererActing:{current:!1},assign:e};function P(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];I("warn",e,n)}function A(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];I("error",e,n)}function I(e,t,n){if(!(n.length>0&&"string"==typeof n[n.length-1]&&0===n[n.length-1].indexOf("\n    in"))){var r=O.ReactDebugCurrentFrame.getStackAddendum();""!==r&&(t+="%s",n=n.concat([r]))}var o=n.map((function(e){return""+e}));o.unshift("Warning: "+t),Function.prototype.apply.call(console[e],console,o);try{var a=0,i="Warning: "+t.replace(/%s/g,(function(){return n[a++]}));throw new Error(i)}catch(e){}}e(O,{ReactDebugCurrentFrame:S,ReactComponentTreeHook:{}});var W={};function N(e,t){var n=e.constructor,r=n&&(n.displayName||n.name)||"ReactClass",o=r+"."+t;W[o]||(A("Can't call %s on a component that is not yet mounted. This is a no-op, but it might indicate a bug in your application. Instead, assign to `this.state` directly or define a `state = {};` class property with the desired state in the %s component.",t,r),W[o]=!0)}var L={isMounted:function(e){return!1},enqueueForceUpdate:function(e,t,n){N(e,"forceUpdate")},enqueueReplaceState:function(e,t,n,r){N(e,"replaceState")},enqueueSetState:function(e,t,n,r){N(e,"setState")}},j={};function M(e,t,n){this.props=e,this.context=t,this.refs=j,this.updater=n||L}Object.freeze(j),M.prototype.isReactComponent={},M.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},M.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};var B={isMounted:["isMounted","Instead, make sure to clean up subscriptions and pending requests in componentWillUnmount to prevent memory leaks."],replaceState:["replaceState","Refactor your code to use setState instead (see https://github.com/facebook/react/issues/3236)."]},z=function(e,t){Object.defineProperty(M.prototype,e,{get:function(){P("%s(...) is deprecated in plain JavaScript React classes. %s",t[0],t[1])}})};for(var U in B)B.hasOwnProperty(U)&&z(U,B[U]);function H(){}function V(e,t,n){this.props=e,this.context=t,this.refs=j,this.updater=n||L}H.prototype=M.prototype;var $=V.prototype=new H;$.constructor=V,e($,M.prototype),$.isPureReactComponent=!0;var q,Q,Y,K=Object.prototype.hasOwnProperty,J={key:!0,ref:!0,__self:!0,__source:!0};function X(e){if(K.call(e,"ref")){var t=Object.getOwnPropertyDescriptor(e,"ref").get;if(t&&t.isReactWarning)return!1}return void 0!==e.ref}function G(e){if(K.call(e,"key")){var t=Object.getOwnPropertyDescriptor(e,"key").get;if(t&&t.isReactWarning)return!1}return void 0!==e.key}function Z(e,t){var n=function(){q||(q=!0,A("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://fb.me/react-special-props)",t))};n.isReactWarning=!0,Object.defineProperty(e,"key",{get:n,configurable:!0})}function ee(e,t){var n=function(){Q||(Q=!0,A("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://fb.me/react-special-props)",t))};n.isReactWarning=!0,Object.defineProperty(e,"ref",{get:n,configurable:!0})}function te(e){if("string"==typeof e.ref&&E.current&&e.__self&&E.current.stateNode!==e.__self){var t=R(E.current.type);Y[t]||(A('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://fb.me/react-strict-mode-string-ref',R(E.current.type),e.ref),Y[t]=!0)}}Y={};var ne=function(e,t,n,r,o,i,l){var u={$$typeof:a,type:e,key:t,ref:n,props:l,_owner:i,_store:{}};return Object.defineProperty(u._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(u,"_self",{configurable:!1,enumerable:!1,writable:!1,value:r}),Object.defineProperty(u,"_source",{configurable:!1,enumerable:!1,writable:!1,value:o}),Object.freeze&&(Object.freeze(u.props),Object.freeze(u)),u};function re(e,t,n){var r,o={},a=null,i=null,l=null,u=null;if(null!=t)for(r in X(t)&&(i=t.ref,te(t)),G(t)&&(a=""+t.key),l=void 0===t.__self?null:t.__self,u=void 0===t.__source?null:t.__source,t)K.call(t,r)&&!J.hasOwnProperty(r)&&(o[r]=t[r]);var s=arguments.length-2;if(1===s)o.children=n;else if(s>1){for(var c=Array(s),d=0;d<s;d++)c[d]=arguments[d+2];Object.freeze&&Object.freeze(c),o.children=c}if(e&&e.defaultProps){var f=e.defaultProps;for(r in f)void 0===o[r]&&(o[r]=f[r])}if(a||i){var p="function"==typeof e?e.displayName||e.name||"Unknown":e;a&&Z(o,p),i&&ee(o,p)}return ne(e,a,i,l,u,E.current,o)}function oe(t,n,r){if(null==t)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+t+".");var o,a,i=e({},t.props),l=t.key,u=t.ref,s=t._self,c=t._source,d=t._owner;if(null!=n)for(o in X(n)&&(u=n.ref,d=E.current),G(n)&&(l=""+n.key),t.type&&t.type.defaultProps&&(a=t.type.defaultProps),n)K.call(n,o)&&!J.hasOwnProperty(o)&&(void 0===n[o]&&void 0!==a?i[o]=a[o]:i[o]=n[o]);var f=arguments.length-2;if(1===f)i.children=r;else if(f>1){for(var p=Array(f),h=0;h<f;h++)p[h]=arguments[h+2];i.children=p}return ne(t.type,l,u,s,c,d,i)}function ae(e){return"object"==typeof e&&null!==e&&e.$$typeof===a}var ie=!1,le=/\/+/g;function ue(e){return(""+e).replace(le,"$&/")}var se,ce=[];function de(e,t,n,r){if(ce.length){var o=ce.pop();return o.result=e,o.keyPrefix=t,o.func=n,o.context=r,o.count=0,o}return{result:e,keyPrefix:t,func:n,context:r,count:0}}function fe(e){e.result=null,e.keyPrefix=null,e.func=null,e.context=null,e.count=0,ce.length<10&&ce.push(e)}function pe(e,t,n){return null==e?0:function e(t,n,r,o){var l=typeof t;"undefined"!==l&&"boolean"!==l||(t=null);var u,s=!1;if(null===t)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(t.$$typeof){case a:case i:s=!0}}if(s)return r(o,t,""===n?"."+he(t,0):n),1;var c=0,d=""===n?".":n+":";if(Array.isArray(t))for(var f=0;f<t.length;f++)c+=e(u=t[f],d+he(u,f),r,o);else{var p=T(t);if("function"==typeof p){p===t.entries&&(ie||P("Using Maps as children is deprecated and will be removed in a future major release. Consider converting children to an array of keyed ReactElements instead."),ie=!0);for(var h,m=p.call(t),g=0;!(h=m.next()).done;)c+=e(u=h.value,d+he(u,g++),r,o)}else if("object"===l){var y;y=" If you meant to render a collection of children, use an array instead."+S.getStackAddendum();var v=""+t;throw Error("Objects are not valid as a React child (found: "+("[object Object]"===v?"object with keys {"+Object.keys(t).join(", ")+"}":v)+")."+y)}}return c}(e,"",t,n)}function he(e,t){return"object"==typeof e&&null!==e&&null!=e.key?(n=e.key,r={"=":"=0",":":"=2"},"$"+(""+n).replace(/[=:]/g,(function(e){return r[e]}))):t.toString(36);var n,r}function me(e,t,n){var r=e.func,o=e.context;r.call(o,t,e.count++)}function ge(e,t,n){var r,o,a=e.result,i=e.keyPrefix,l=e.func,u=e.context,s=l.call(u,t,e.count++);Array.isArray(s)?ye(s,a,n,(function(e){return e})):null!=s&&(ae(s)&&(r=s,o=i+(!s.key||t&&t.key===s.key?"":ue(s.key)+"/")+n,s=ne(r.type,o,r.ref,r._self,r._source,r._owner,r.props)),a.push(s))}function ye(e,t,n,r,o){var a="";null!=n&&(a=ue(n)+"/");var i=de(t,a,r,o);pe(e,ge,i),fe(i)}function ve(e){return"string"==typeof e||"function"==typeof e||e===l||e===f||e===s||e===u||e===h||e===m||"object"==typeof e&&null!==e&&(e.$$typeof===y||e.$$typeof===g||e.$$typeof===c||e.$$typeof===d||e.$$typeof===p||e.$$typeof===b||e.$$typeof===w||e.$$typeof===F||e.$$typeof===v)}function be(){var e=k.current;if(null===e)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://fb.me/react-invalid-hook-call for tips about how to debug and fix this problem.");return e}function we(){if(E.current){var e=R(E.current.type);if(e)return"\n\nCheck the render method of `"+e+"`."}return""}function Fe(e){return null!=e&&void 0!==(t=e.__source)?"\n\nCheck your code at "+t.fileName.replace(/^.*[\\\/]/,"")+":"+t.lineNumber+".":"";var t}se=!1;var Ce={};function Te(e,t){if(e._store&&!e._store.validated&&null==e.key){e._store.validated=!0;var n=function(e){var t=we();if(!t){var n="string"==typeof e?e:e.displayName||e.name;n&&(t="\n\nCheck the top-level render call using <"+n+">.")}return t}(t);if(!Ce[n]){Ce[n]=!0;var r="";e&&e._owner&&e._owner!==E.current&&(r=" It was passed a child from "+R(e._owner.type)+"."),_(e),A('Each child in a list should have a unique "key" prop.%s%s See https://fb.me/react-warning-keys for more information.',n,r),_(null)}}}function ke(e,t){if("object"==typeof e)if(Array.isArray(e))for(var n=0;n<e.length;n++){var r=e[n];ae(r)&&Te(r,t)}else if(ae(e))e._store&&(e._store.validated=!0);else if(e){var o=T(e);if("function"==typeof o&&o!==e.entries)for(var a,i=o.call(e);!(a=i.next()).done;)ae(a.value)&&Te(a.value,t)}}function Ee(e){var t=e.type;if(null!=t&&"string"!=typeof t){var n,o=R(t);if("function"==typeof t)n=t.propTypes;else{if("object"!=typeof t||t.$$typeof!==p&&t.$$typeof!==g)return;n=t.propTypes}n?(_(e),r(n,e.props,"prop",o,S.getStackAddendum),_(null)):void 0===t.PropTypes||se||(se=!0,A("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",o||"Unknown")),"function"!=typeof t.getDefaultProps||t.getDefaultProps.isReactClassApproved||A("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function xe(e){_(e);for(var t=Object.keys(e.props),n=0;n<t.length;n++){var r=t[n];if("children"!==r&&"key"!==r){A("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",r);break}}null!==e.ref&&A("Invalid attribute `ref` supplied to `React.Fragment`."),_(null)}function Re(e,t,n){var r=ve(e);if(!r){var o="";(void 0===e||"object"==typeof e&&null!==e&&0===Object.keys(e).length)&&(o+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var i,u=Fe(t);o+=u||we(),null===e?i="null":Array.isArray(e)?i="array":void 0!==e&&e.$$typeof===a?(i="<"+(R(e.type)||"Unknown")+" />",o=" Did you accidentally export a JSX literal instead of a component?"):i=typeof e,A("React.createElement: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",i,o)}var s=re.apply(this,arguments);if(null==s)return s;if(r)for(var c=2;c<arguments.length;c++)ke(arguments[c],e);return e===l?xe(s):Ee(s),s}var Se=!1;try{var De=Object.freeze({}),_e=new Map([[De,null]]),Oe=new Set([De]);_e.set(0,0),Oe.add(0)}catch(e){}var Pe=Re,Ae=function(e,t,n){for(var r=oe.apply(this,arguments),o=2;o<arguments.length;o++)ke(arguments[o],r.type);return Ee(r),r},Ie=function(e){var t=Re.bind(null,e);return t.type=e,Se||(Se=!0,P("React.createFactory() is deprecated and will be removed in a future major release. Consider using JSX or use React.createElement() directly instead.")),Object.defineProperty(t,"type",{enumerable:!1,get:function(){return P("Factory.type is deprecated. Access the class directly before passing it to createFactory."),Object.defineProperty(this,"type",{value:e}),e}}),t},We={map:function(e,t,n){if(null==e)return e;var r=[];return ye(e,r,null,t,n),r},forEach:function(e,t,n){if(null==e)return e;var r=de(null,null,t,n);pe(e,me,r),fe(r)},count:function(e){return pe(e,(function(){return null}),null)},toArray:function(e){var t=[];return ye(e,t,null,(function(e){return e})),t},only:function(e){if(!ae(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};t.Children=We,t.Component=M,t.Fragment=l,t.Profiler=s,t.PureComponent=V,t.StrictMode=u,t.Suspense=h,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=O,t.cloneElement=Ae,t.createContext=function(e,t){void 0===t?t=null:null!==t&&"function"!=typeof t&&A("createContext: Expected the optional second argument to be a function. Instead received: %s",t);var n={$$typeof:d,_calculateChangedBits:t,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null};n.Provider={$$typeof:c,_context:n};var r=!1,o=!1,a={$$typeof:d,_context:n,_calculateChangedBits:n._calculateChangedBits};return Object.defineProperties(a,{Provider:{get:function(){return o||(o=!0,A("Rendering <Context.Consumer.Provider> is not supported and will be removed in a future major release. Did you mean to render <Context.Provider> instead?")),n.Provider},set:function(e){n.Provider=e}},_currentValue:{get:function(){return n._currentValue},set:function(e){n._currentValue=e}},_currentValue2:{get:function(){return n._currentValue2},set:function(e){n._currentValue2=e}},_threadCount:{get:function(){return n._threadCount},set:function(e){n._threadCount=e}},Consumer:{get:function(){return r||(r=!0,A("Rendering <Context.Consumer.Consumer> is not supported and will be removed in a future major release. Did you mean to render <Context.Consumer> instead?")),n.Consumer}}}),n.Consumer=a,n._currentRenderer=null,n._currentRenderer2=null,n},t.createElement=Pe,t.createFactory=Ie,t.createRef=function(){var e={current:null};return Object.seal(e),e},t.forwardRef=function(e){return null!=e&&e.$$typeof===g?A("forwardRef requires a render function but received a `memo` component. Instead of forwardRef(memo(...)), use memo(forwardRef(...))."):"function"!=typeof e?A("forwardRef requires a render function but was given %s.",null===e?"null":typeof e):0!==e.length&&2!==e.length&&A("forwardRef render functions accept exactly two parameters: props and ref. %s",1===e.length?"Did you forget to use the ref parameter?":"Any additional parameter will be undefined."),null!=e&&(null==e.defaultProps&&null==e.propTypes||A("forwardRef render functions do not support propTypes or defaultProps. Did you accidentally pass a React component?")),{$$typeof:p,render:e}},t.isValidElement=ae,t.lazy=function(e){var t,n,r={$$typeof:y,_ctor:e,_status:-1,_result:null};return Object.defineProperties(r,{defaultProps:{configurable:!0,get:function(){return t},set:function(e){A("React.lazy(...): It is not supported to assign `defaultProps` to a lazy component import. Either specify them where the component is defined, or create a wrapping component around it."),t=e,Object.defineProperty(r,"defaultProps",{enumerable:!0})}},propTypes:{configurable:!0,get:function(){return n},set:function(e){A("React.lazy(...): It is not supported to assign `propTypes` to a lazy component import. Either specify them where the component is defined, or create a wrapping component around it."),n=e,Object.defineProperty(r,"propTypes",{enumerable:!0})}}}),r},t.memo=function(e,t){return ve(e)||A("memo: The first argument must be a component. Instead received: %s",null===e?"null":typeof e),{$$typeof:g,type:e,compare:void 0===t?null:t}},t.useCallback=function(e,t){return be().useCallback(e,t)},t.useContext=function(e,t){var n=be();if(void 0!==t&&A("useContext() second argument is reserved for future use in React. Passing it is not supported. You passed: %s.%s",t,"number"==typeof t&&Array.isArray(arguments[2])?"\n\nDid you call array.map(useContext)? Calling Hooks inside a loop is not supported. Learn more at https://fb.me/rules-of-hooks":""),void 0!==e._context){var r=e._context;r.Consumer===e?A("Calling useContext(Context.Consumer) is not supported, may cause bugs, and will be removed in a future major release. Did you mean to call useContext(Context) instead?"):r.Provider===e&&A("Calling useContext(Context.Provider) is not supported. Did you mean to call useContext(Context) instead?")}return n.useContext(e,t)},t.useDebugValue=function(e,t){return be().useDebugValue(e,t)},t.useEffect=function(e,t){return be().useEffect(e,t)},t.useImperativeHandle=function(e,t,n){return be().useImperativeHandle(e,t,n)},t.useLayoutEffect=function(e,t){return be().useLayoutEffect(e,t)},t.useMemo=function(e,t){return be().useMemo(e,t)},t.useReducer=function(e,t,n){return be().useReducer(e,t,n)},t.useRef=function(e){return be().useRef(e)},t.useState=function(e){return be().useState(e)},t.version="16.14.0"})()},function(e,t,n){"use strict";
/** @license React v16.14.0
 * react-dom.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(){var e=n(1),r=n(18),o=n(25),a=n(19),i=n(27),l=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function u(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];c("warn",e,n)}function s(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];c("error",e,n)}function c(e,t,n){if(!(n.length>0&&"string"==typeof n[n.length-1]&&0===n[n.length-1].indexOf("\n    in"))){var r=l.ReactDebugCurrentFrame.getStackAddendum();""!==r&&(t+="%s",n=n.concat([r]))}var o=n.map((function(e){return""+e}));o.unshift("Warning: "+t),Function.prototype.apply.call(console[e],console,o);try{var a=0,i="Warning: "+t.replace(/%s/g,(function(){return n[a++]}));throw new Error(i)}catch(e){}}if(l.hasOwnProperty("ReactCurrentDispatcher")||(l.ReactCurrentDispatcher={current:null}),l.hasOwnProperty("ReactCurrentBatchConfig")||(l.ReactCurrentBatchConfig={suspense:null}),!e)throw Error("ReactDOM was loaded before React. Make sure you load the React package before loading ReactDOM.");var d=function(e,t,n,r,o,a,i,l,u){var s=Array.prototype.slice.call(arguments,3);try{t.apply(n,s)}catch(e){this.onError(e)}};if("undefined"!=typeof window&&"function"==typeof window.dispatchEvent&&"undefined"!=typeof document&&"function"==typeof document.createEvent){var f=document.createElement("react");d=function(e,t,n,r,o,a,i,l,u){if("undefined"==typeof document)throw Error("The `document` global was defined when React was initialized, but is not defined anymore. This can happen in a test environment if a component schedules an update from an asynchronous callback, but the test has already finished running. To solve this, you can either unmount the component at the end of your test (and ensure that any asynchronous operations get canceled in `componentWillUnmount`), or you can change the test itself to be asynchronous.");var s,c=document.createEvent("Event"),d=!0,p=window.event,h=Object.getOwnPropertyDescriptor(window,"event"),m=Array.prototype.slice.call(arguments,3);function g(){f.removeEventListener(w,g,!1),void 0!==window.event&&window.hasOwnProperty("event")&&(window.event=p),t.apply(n,m),d=!1}var y=!1,v=!1;function b(e){if(s=e.error,y=!0,null===s&&0===e.colno&&0===e.lineno&&(v=!0),e.defaultPrevented&&null!=s&&"object"==typeof s)try{s._suppressLogging=!0}catch(e){}}var w="react-"+(e||"invokeguardedcallback");window.addEventListener("error",b),f.addEventListener(w,g,!1),c.initEvent(w,!1,!1),f.dispatchEvent(c),h&&Object.defineProperty(window,"event",h),d&&(y?v&&(s=new Error("A cross-origin error was thrown. React doesn't have access to the actual error object in development. See https://fb.me/react-crossorigin-error for more information.")):s=new Error("An error was thrown inside one of your components, but React doesn't know what it was. This is likely due to browser flakiness. React does its best to preserve the \"Pause on exceptions\" behavior of the DevTools, which requires some DEV-mode only tricks. It's possible that these don't work in your browser. Try triggering the error in production mode, or switching to a modern browser. If you suspect that this is actually an issue with React, please file an issue."),this.onError(s)),window.removeEventListener("error",b)}}var p=d,h=!1,m=null,g=!1,y=null,v={onError:function(e){h=!0,m=e}};function b(e,t,n,r,o,a,i,l,u){h=!1,m=null,p.apply(v,arguments)}function w(){return h}function F(){if(h){var e=m;return h=!1,m=null,e}throw Error("clearCaughtError was called but no error was captured. This error is likely caused by a bug in React. Please file an issue.")}var C,T=null,k=null,E=null;function x(e,t,n){var r=e.type||"unknown-event";e.currentTarget=E(n),function(e,t,n,r,o,a,i,l,u){if(b.apply(this,arguments),h){var s=F();g||(g=!0,y=s)}}(r,t,void 0,e),e.currentTarget=null}C=function(e){var t=e._dispatchListeners,n=e._dispatchInstances,r=Array.isArray(t),o=r?t.length:t?1:0,a=Array.isArray(n),i=a?n.length:n?1:0;a===r&&i===o||s("EventPluginUtils: Invalid `event`.")};var R=null,S={};function D(){if(R)for(var e in S){var t=S[e],n=R.indexOf(e);if(!(n>-1))throw Error("EventPluginRegistry: Cannot inject event plugins that do not exist in the plugin ordering, `"+e+"`.");if(!P[n]){if(!t.extractEvents)throw Error("EventPluginRegistry: Event plugins must implement an `extractEvents` method, but `"+e+"` does not.");P[n]=t;var r=t.eventTypes;for(var o in r)if(!_(r[o],t,o))throw Error("EventPluginRegistry: Failed to publish event `"+o+"` for plugin `"+e+"`.")}}}function _(e,t,n){if(A.hasOwnProperty(n))throw Error("EventPluginRegistry: More than one plugin attempted to publish the same event name, `"+n+"`.");A[n]=e;var r=e.phasedRegistrationNames;if(r){for(var o in r){if(r.hasOwnProperty(o))O(r[o],t,n)}return!0}return!!e.registrationName&&(O(e.registrationName,t,n),!0)}function O(e,t,n){if(I[e])throw Error("EventPluginRegistry: More than one plugin attempted to publish the same registration name, `"+e+"`.");I[e]=t,W[e]=t.eventTypes[n].dependencies;var r=e.toLowerCase();N[r]=e,"onDoubleClick"===e&&(N.ondblclick=e)}var P=[],A={},I={},W={},N={};function L(e){var t=!1;for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];if(!S.hasOwnProperty(n)||S[n]!==r){if(S[n])throw Error("EventPluginRegistry: Cannot inject two different event plugins using the same name, `"+n+"`.");S[n]=r,t=!0}}t&&D()}var j=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),M=null,B=null,z=null;function U(e){var t=k(e);if(t){if("function"!=typeof M)throw Error("setRestoreImplementation() needs to be called to handle a target for controlled events. This error is likely caused by a bug in React. Please file an issue.");var n=t.stateNode;if(n){var r=T(n);M(t.stateNode,t.type,r)}}}function H(e){B?z?z.push(e):z=[e]:B=e}function V(){if(B){var e=B,t=z;if(B=null,z=null,U(e),t)for(var n=0;n<t.length;n++)U(t[n])}}var $=function(e,t){return e(t)},q=function(e,t,n,r,o){return e(t,n,r,o)},Q=function(){},Y=$,K=!1,J=!1;function X(){(null!==B||null!==z)&&(Q(),V())}var G=":A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",Z=G+"\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040",ee=new RegExp("^["+G+"]["+Z+"]*$"),te=Object.prototype.hasOwnProperty,ne={},re={};function oe(e){return!!te.call(re,e)||!te.call(ne,e)&&(ee.test(e)?(re[e]=!0,!0):(ne[e]=!0,s("Invalid attribute name: `%s`",e),!1))}function ae(e,t,n){return null!==t?0===t.type:!n&&(e.length>2&&("o"===e[0]||"O"===e[0])&&("n"===e[1]||"N"===e[1]))}function ie(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":if(r)return!1;if(null!==n)return!n.acceptsBooleans;var o=e.toLowerCase().slice(0,5);return"data-"!==o&&"aria-"!==o;default:return!1}}function le(e,t,n,r){if(null==t)return!0;if(ie(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||t<1}return!1}function ue(e){return ce.hasOwnProperty(e)?ce[e]:null}function se(e,t,n,r,o,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a}var ce={};["children","dangerouslySetInnerHTML","defaultValue","defaultChecked","innerHTML","suppressContentEditableWarning","suppressHydrationWarning","style"].forEach((function(e){ce[e]=new se(e,0,!1,e,null,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0],n=e[1];ce[t]=new se(t,1,!1,n,null,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){ce[e]=new se(e,2,!1,e.toLowerCase(),null,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){ce[e]=new se(e,2,!1,e,null,!1)})),["allowFullScreen","async","autoFocus","autoPlay","controls","default","defer","disabled","disablePictureInPicture","formNoValidate","hidden","loop","noModule","noValidate","open","playsInline","readOnly","required","reversed","scoped","seamless","itemScope"].forEach((function(e){ce[e]=new se(e,3,!1,e.toLowerCase(),null,!1)})),["checked","multiple","muted","selected"].forEach((function(e){ce[e]=new se(e,3,!0,e,null,!1)})),["capture","download"].forEach((function(e){ce[e]=new se(e,4,!1,e,null,!1)})),["cols","rows","size","span"].forEach((function(e){ce[e]=new se(e,6,!1,e,null,!1)})),["rowSpan","start"].forEach((function(e){ce[e]=new se(e,5,!1,e.toLowerCase(),null,!1)}));var de=/[\-\:]([a-z])/g,fe=function(e){return e[1].toUpperCase()};["accent-height","alignment-baseline","arabic-form","baseline-shift","cap-height","clip-path","clip-rule","color-interpolation","color-interpolation-filters","color-profile","color-rendering","dominant-baseline","enable-background","fill-opacity","fill-rule","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","glyph-name","glyph-orientation-horizontal","glyph-orientation-vertical","horiz-adv-x","horiz-origin-x","image-rendering","letter-spacing","lighting-color","marker-end","marker-mid","marker-start","overline-position","overline-thickness","paint-order","panose-1","pointer-events","rendering-intent","shape-rendering","stop-color","stop-opacity","strikethrough-position","strikethrough-thickness","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-anchor","text-decoration","text-rendering","underline-position","underline-thickness","unicode-bidi","unicode-range","units-per-em","v-alphabetic","v-hanging","v-ideographic","v-mathematical","vector-effect","vert-adv-y","vert-origin-x","vert-origin-y","word-spacing","writing-mode","xmlns:xlink","x-height"].forEach((function(e){var t=e.replace(de,fe);ce[t]=new se(t,1,!1,e,null,!1)})),["xlink:actuate","xlink:arcrole","xlink:role","xlink:show","xlink:title","xlink:type"].forEach((function(e){var t=e.replace(de,fe);ce[t]=new se(t,1,!1,e,"http://www.w3.org/1999/xlink",!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(de,fe);ce[t]=new se(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1)})),["tabIndex","crossOrigin"].forEach((function(e){ce[e]=new se(e,1,!1,e.toLowerCase(),null,!1)}));ce.xlinkHref=new se("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0),["src","href","action","formAction"].forEach((function(e){ce[e]=new se(e,1,!1,e.toLowerCase(),null,!0)}));l.ReactDebugCurrentFrame;var pe=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*\:/i,he=!1;function me(e){!he&&pe.test(e)&&(he=!0,s("A future version of React will block javascript: URLs as a security precaution. Use event handlers instead if you can. If you need to generate unsafe HTML try using dangerouslySetInnerHTML instead. React was passed %s.",JSON.stringify(e)))}function ge(e,t,n,r){if(r.mustUseProperty)return e[r.propertyName];r.sanitizeURL&&me(""+n);var o=r.attributeName,a=null;if(4===r.type){if(e.hasAttribute(o)){var i=e.getAttribute(o);return""===i||(le(t,n,r,!1)?i:i===""+n?n:i)}}else if(e.hasAttribute(o)){if(le(t,n,r,!1))return e.getAttribute(o);if(3===r.type)return n;a=e.getAttribute(o)}return le(t,n,r,!1)?null===a?n:a:a===""+n?n:a}function ye(e,t,n){if(oe(t)){if(!e.hasAttribute(t))return void 0===n?void 0:null;var r=e.getAttribute(t);return r===""+n?n:r}}function ve(e,t,n,r){var o=ue(t);if(!ae(t,o,r))if(le(t,n,o,r)&&(n=null),r||null===o){if(oe(t)){var a=t;null===n?e.removeAttribute(a):e.setAttribute(a,""+n)}}else if(o.mustUseProperty){var i=o.propertyName;if(null===n){var l=o.type;e[i]=3!==l&&""}else e[i]=n}else{var u=o.attributeName,s=o.attributeNamespace;if(null===n)e.removeAttribute(u);else{var c,d=o.type;3===d||4===d&&!0===n?c="":(c=""+n,o.sanitizeURL&&me(c.toString())),s?e.setAttributeNS(s,u,c):e.setAttribute(u,c)}}}var be=/^(.*)[\\\/]/;var we="function"==typeof Symbol&&Symbol.for,Fe=we?Symbol.for("react.element"):60103,Ce=we?Symbol.for("react.portal"):60106,Te=we?Symbol.for("react.fragment"):60107,ke=we?Symbol.for("react.strict_mode"):60108,Ee=we?Symbol.for("react.profiler"):60114,xe=we?Symbol.for("react.provider"):60109,Re=we?Symbol.for("react.context"):60110,Se=we?Symbol.for("react.concurrent_mode"):60111,De=we?Symbol.for("react.forward_ref"):60112,_e=we?Symbol.for("react.suspense"):60113,Oe=we?Symbol.for("react.suspense_list"):60120,Pe=we?Symbol.for("react.memo"):60115,Ae=we?Symbol.for("react.lazy"):60116,Ie=we?Symbol.for("react.block"):60121,We="function"==typeof Symbol&&Symbol.iterator;function Ne(e){if(null===e||"object"!=typeof e)return null;var t=We&&e[We]||e["@@iterator"];return"function"==typeof t?t:null}function Le(e){return 1===e._status?e._result:null}function je(e){if(null==e)return null;if("number"==typeof e.tag&&s("Received an unexpected object in getComponentName(). This is likely a bug in React. Please file an issue."),"function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case Te:return"Fragment";case Ce:return"Portal";case Ee:return"Profiler";case ke:return"StrictMode";case _e:return"Suspense";case Oe:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case Re:return"Context.Consumer";case xe:return"Context.Provider";case De:return n=e,r=e.render,o="ForwardRef",a=r.displayName||r.name||"",n.displayName||(""!==a?o+"("+a+")":o);case Pe:return je(e.type);case Ie:return je(e.render);case Ae:var t=Le(e);if(t)return je(t)}var n,r,o,a;return null}var Me=l.ReactDebugCurrentFrame;function Be(e){switch(e.tag){case 3:case 4:case 6:case 7:case 10:case 9:return"";default:var t=e._debugOwner,n=e._debugSource,r=je(e.type),o=null;return t&&(o=je(t.type)),function(e,t,n){var r="";if(t){var o=t.fileName,a=o.replace(be,"");if(/^index\./.test(a)){var i=o.match(be);if(i){var l=i[1];if(l)a=l.replace(be,"")+"/"+a}}r=" (at "+a+":"+t.lineNumber+")"}else n&&(r=" (created by "+n+")");return"\n    in "+(e||"Unknown")+r}(r,n,o)}}function ze(e){var t="",n=e;do{t+=Be(n),n=n.return}while(n);return t}var Ue=null,He=!1;function Ve(){if(null===Ue)return null;var e=Ue._debugOwner;return null!=e?je(e.type):null}function $e(){return null===Ue?"":ze(Ue)}function qe(){Me.getCurrentStack=null,Ue=null,He=!1}function Qe(e){Me.getCurrentStack=$e,Ue=e,He=!1}function Ye(e){He=e}function Ke(e){return""+e}function Je(e){switch(typeof e){case"boolean":case"number":case"object":case"string":case"undefined":return e;default:return""}}var Xe,Ge={checkPropTypes:null};Xe=l.ReactDebugCurrentFrame;var Ze={button:!0,checkbox:!0,image:!0,hidden:!0,radio:!0,reset:!0,submit:!0},et={value:function(e,t,n){return Ze[e.type]||e.onChange||e.readOnly||e.disabled||null==e[t]?null:new Error("You provided a `value` prop to a form field without an `onChange` handler. This will render a read-only field. If the field should be mutable use `defaultValue`. Otherwise, set either `onChange` or `readOnly`.")},checked:function(e,t,n){return e.onChange||e.readOnly||e.disabled||null==e[t]?null:new Error("You provided a `checked` prop to a form field without an `onChange` handler. This will render a read-only field. If the field should be mutable use `defaultChecked`. Otherwise, set either `onChange` or `readOnly`.")}};function tt(e){var t=e.type,n=e.nodeName;return n&&"input"===n.toLowerCase()&&("checkbox"===t||"radio"===t)}function nt(e){return e._valueTracker}function rt(e){nt(e)||(e._valueTracker=function(e){var t=tt(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var o=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){!function(e){e._valueTracker=null}(e),delete e[t]}}}}(e))}function ot(e){if(!e)return!1;var t=nt(e);if(!t)return!0;var n=t.getValue(),r=function(e){var t="";return e?t=tt(e)?e.checked?"true":"false":e.value:t}(e);return r!==n&&(t.setValue(r),!0)}Ge.checkPropTypes=function(e,t){a(et,t,"prop",e,Xe.getStackAddendum)};var at=!1,it=!1,lt=!1,ut=!1;function st(e){return"checkbox"===e.type||"radio"===e.type?null!=e.checked:null!=e.value}function ct(e,t){var n=e,o=t.checked;return r({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=o?o:n._wrapperState.initialChecked})}function dt(e,t){Ge.checkPropTypes("input",t),void 0===t.checked||void 0===t.defaultChecked||it||(s("%s contains an input of type %s with both checked and defaultChecked props. Input elements must be either controlled or uncontrolled (specify either the checked prop, or the defaultChecked prop, but not both). Decide between using a controlled or uncontrolled input element and remove one of these props. More info: https://fb.me/react-controlled-components",Ve()||"A component",t.type),it=!0),void 0===t.value||void 0===t.defaultValue||at||(s("%s contains an input of type %s with both value and defaultValue props. Input elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both). Decide between using a controlled or uncontrolled input element and remove one of these props. More info: https://fb.me/react-controlled-components",Ve()||"A component",t.type),at=!0);var n=e,r=null==t.defaultValue?"":t.defaultValue;n._wrapperState={initialChecked:null!=t.checked?t.checked:t.defaultChecked,initialValue:Je(null!=t.value?t.value:r),controlled:st(t)}}function ft(e,t){var n=e,r=t.checked;null!=r&&ve(n,"checked",r,!1)}function pt(e,t){var n=e,r=st(t);n._wrapperState.controlled||!r||ut||(s("A component is changing an uncontrolled input of type %s to be controlled. Input elements should not switch from uncontrolled to controlled (or vice versa). Decide between using a controlled or uncontrolled input element for the lifetime of the component. More info: https://fb.me/react-controlled-components",t.type),ut=!0),!n._wrapperState.controlled||r||lt||(s("A component is changing a controlled input of type %s to be uncontrolled. Input elements should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled input element for the lifetime of the component. More info: https://fb.me/react-controlled-components",t.type),lt=!0),ft(e,t);var o=Je(t.value),a=t.type;if(null!=o)"number"===a?(0===o&&""===n.value||n.value!=o)&&(n.value=Ke(o)):n.value!==Ke(o)&&(n.value=Ke(o));else if("submit"===a||"reset"===a)return void n.removeAttribute("value");t.hasOwnProperty("value")?gt(n,t.type,o):t.hasOwnProperty("defaultValue")&&gt(n,t.type,Je(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(n.defaultChecked=!!t.defaultChecked)}function ht(e,t,n){var r=e;if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var o=t.type;if(("submit"===o||"reset"===o)&&(void 0===t.value||null===t.value))return;var a=Ke(r._wrapperState.initialValue);n||a!==r.value&&(r.value=a),r.defaultValue=a}var i=r.name;""!==i&&(r.name=""),r.defaultChecked=!r.defaultChecked,r.defaultChecked=!!r._wrapperState.initialChecked,""!==i&&(r.name=i)}function mt(e,t){var n=e;pt(n,t),function(e,t){var n=t.name;if("radio"===t.type&&null!=n){for(var r=e;r.parentNode;)r=r.parentNode;for(var o=r.querySelectorAll("input[name="+JSON.stringify(""+n)+'][type="radio"]'),a=0;a<o.length;a++){var i=o[a];if(i!==e&&i.form===e.form){var l=Fa(i);if(!l)throw Error("ReactDOMInput: Mixing React and non-React radio inputs with the same `name` is not supported.");ot(i),pt(i,l)}}}}(n,t)}function gt(e,t,n){"number"===t&&e.ownerDocument.activeElement===e||(null==n?e.defaultValue=Ke(e._wrapperState.initialValue):e.defaultValue!==Ke(n)&&(e.defaultValue=Ke(n)))}var yt,vt=!1,bt=!1;function wt(t,n){"object"==typeof n.children&&null!==n.children&&e.Children.forEach(n.children,(function(e){null!=e&&"string"!=typeof e&&"number"!=typeof e&&"string"==typeof e.type&&(bt||(bt=!0,s("Only strings and numbers are supported as <option> children.")))})),null==n.selected||vt||(s("Use the `defaultValue` or `value` props on <select> instead of setting `selected` on <option>."),vt=!0)}function Ft(t,n){var o=r({children:void 0},n),a=function(t){var n="";return e.Children.forEach(t,(function(e){null!=e&&(n+=e)})),n}(n.children);return a&&(o.children=a),o}function Ct(){var e=Ve();return e?"\n\nCheck the render method of `"+e+"`.":""}yt=!1;var Tt=["value","defaultValue"];function kt(e,t,n,r){var o=e.options;if(t){for(var a=n,i={},l=0;l<a.length;l++)i["$"+a[l]]=!0;for(var u=0;u<o.length;u++){var s=i.hasOwnProperty("$"+o[u].value);o[u].selected!==s&&(o[u].selected=s),s&&r&&(o[u].defaultSelected=!0)}}else{for(var c=Ke(Je(n)),d=null,f=0;f<o.length;f++){if(o[f].value===c)return o[f].selected=!0,void(r&&(o[f].defaultSelected=!0));null!==d||o[f].disabled||(d=o[f])}null!==d&&(d.selected=!0)}}function Et(e,t){return r({},t,{value:void 0})}function xt(e,t){var n=e;!function(e){Ge.checkPropTypes("select",e);for(var t=0;t<Tt.length;t++){var n=Tt[t];if(null!=e[n]){var r=Array.isArray(e[n]);e.multiple&&!r?s("The `%s` prop supplied to <select> must be an array if `multiple` is true.%s",n,Ct()):!e.multiple&&r&&s("The `%s` prop supplied to <select> must be a scalar value if `multiple` is false.%s",n,Ct())}}}(t),n._wrapperState={wasMultiple:!!t.multiple},void 0===t.value||void 0===t.defaultValue||yt||(s("Select elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both). Decide between using a controlled or uncontrolled select element and remove one of these props. More info: https://fb.me/react-controlled-components"),yt=!0)}var Rt=!1;function St(e,t){var n=e;if(null!=t.dangerouslySetInnerHTML)throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");return r({},t,{value:void 0,defaultValue:void 0,children:Ke(n._wrapperState.initialValue)})}function Dt(e,t){var n=e;Ge.checkPropTypes("textarea",t),void 0===t.value||void 0===t.defaultValue||Rt||(s("%s contains a textarea with both value and defaultValue props. Textarea elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both). Decide between using a controlled or uncontrolled textarea and remove one of these props. More info: https://fb.me/react-controlled-components",Ve()||"A component"),Rt=!0);var r=t.value;if(null==r){var o=t.children,a=t.defaultValue;if(null!=o){if(s("Use the `defaultValue` or `value` props instead of setting children on <textarea>."),null!=a)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(Array.isArray(o)){if(!(o.length<=1))throw Error("<textarea> can only have at most one child.");o=o[0]}a=o}null==a&&(a=""),r=a}n._wrapperState={initialValue:Je(r)}}function _t(e,t){var n=e,r=Je(t.value),o=Je(t.defaultValue);if(null!=r){var a=Ke(r);a!==n.value&&(n.value=a),null==t.defaultValue&&n.defaultValue!==a&&(n.defaultValue=a)}null!=o&&(n.defaultValue=Ke(o))}function Ot(e,t){var n=e,r=n.textContent;r===n._wrapperState.initialValue&&""!==r&&null!==r&&(n.value=r)}var Pt="http://www.w3.org/1999/xhtml",At="http://www.w3.org/2000/svg",It=Pt,Wt=At;function Nt(e){switch(e){case"svg":return At;case"math":return"http://www.w3.org/1998/Math/MathML";default:return Pt}}function Lt(e,t){return null==e||e===Pt?Nt(t):e===At&&"foreignObject"===t?Pt:e}var jt,Mt,Bt=(Mt=function(e,t){if(e.namespaceURI!==Wt||"innerHTML"in e)e.innerHTML=t;else{(jt=jt||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>";for(var n=jt.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;n.firstChild;)e.appendChild(n.firstChild)}},"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return Mt(e,t,n,r)}))}:Mt),zt=function(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t};function Ut(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Ht={animationend:Ut("Animation","AnimationEnd"),animationiteration:Ut("Animation","AnimationIteration"),animationstart:Ut("Animation","AnimationStart"),transitionend:Ut("Transition","TransitionEnd")},Vt={},$t={};function qt(e){if(Vt[e])return Vt[e];if(!Ht[e])return e;var t=Ht[e];for(var n in t)if(t.hasOwnProperty(n)&&n in $t)return Vt[e]=t[n];return e}j&&($t=document.createElement("div").style,"AnimationEvent"in window||(delete Ht.animationend.animation,delete Ht.animationiteration.animation,delete Ht.animationstart.animation),"TransitionEvent"in window||delete Ht.transitionend.transition);var Qt=qt("animationend"),Yt=qt("animationiteration"),Kt=qt("animationstart"),Jt=qt("transitionend"),Xt=["abort","canplay","canplaythrough","durationchange","emptied","encrypted","ended","error","loadeddata","loadedmetadata","loadstart","pause","play","playing","progress","ratechange","seeked","seeking","stalled","suspend","timeupdate","volumechange","waiting"];var Gt=new("function"==typeof WeakMap?WeakMap:Map);function Zt(e){var t=Gt.get(e);return void 0===t&&(t=new Map,Gt.set(e,t)),t}function en(e){return e._reactInternalFiber}var tn=l.ReactCurrentOwner;function nn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{var r=t;do{0!=(1026&(t=r).effectTag)&&(n=t.return),r=t.return}while(r)}return 3===t.tag?n:null}function rn(e){if(13===e.tag){var t=e.memoizedState;if(null===t){var n=e.alternate;null!==n&&(t=n.memoizedState)}if(null!==t)return t.dehydrated}return null}function on(e){return 3===e.tag?e.stateNode.containerInfo:null}function an(e){if(nn(e)!==e)throw Error("Unable to find node on an unmounted component.")}function ln(e){var t=e.alternate;if(!t){var n=nn(e);if(null===n)throw Error("Unable to find node on an unmounted component.");return n!==e?null:e}for(var r=e,o=t;;){var a=r.return;if(null===a)break;var i=a.alternate;if(null===i){var l=a.return;if(null!==l){r=o=l;continue}break}if(a.child===i.child){for(var u=a.child;u;){if(u===r)return an(a),e;if(u===o)return an(a),t;u=u.sibling}throw Error("Unable to find node on an unmounted component.")}if(r.return!==o.return)r=a,o=i;else{for(var s=!1,c=a.child;c;){if(c===r){s=!0,r=a,o=i;break}if(c===o){s=!0,o=a,r=i;break}c=c.sibling}if(!s){for(c=i.child;c;){if(c===r){s=!0,r=i,o=a;break}if(c===o){s=!0,o=i,r=a;break}c=c.sibling}if(!s)throw Error("Child was not found in either parent set. This indicates a bug in React related to the return pointer. Please file an issue.")}}if(r.alternate!==o)throw Error("Return fibers should always be each others' alternates. This error is likely caused by a bug in React. Please file an issue.")}if(3!==r.tag)throw Error("Unable to find node on an unmounted component.");return r.stateNode.current===r?e:t}function un(e){var t=ln(e);if(!t)return null;for(var n=t;;){if(5===n.tag||6===n.tag)return n;if(n.child)n.child.return=n,n=n.child;else{if(n===t)return null;for(;!n.sibling;){if(!n.return||n.return===t)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}}return null}function sn(e,t){if(null==t)throw Error("accumulateInto(...): Accumulated items must not be null or undefined.");return null==e?t:Array.isArray(e)?Array.isArray(t)?(e.push.apply(e,t),e):(e.push(t),e):Array.isArray(t)?[e].concat(t):[e,t]}function cn(e,t,n){Array.isArray(e)?e.forEach(t,n):e&&t.call(n,e)}var dn=null,fn=function(e){e&&(!function(e){var t=e._dispatchListeners,n=e._dispatchInstances;if(C(e),Array.isArray(t))for(var r=0;r<t.length&&!e.isPropagationStopped();r++)x(e,t[r],n[r]);else t&&x(e,t,n);e._dispatchListeners=null,e._dispatchInstances=null}(e),e.isPersistent()||e.constructor.release(e))},pn=function(e){return fn(e)};function hn(e){null!==e&&(dn=sn(dn,e));var t=dn;if(dn=null,t){if(cn(t,pn),dn)throw Error("processEventQueue(): Additional events were enqueued while processing an event queue. Support for this has not yet been implemented.");!function(){if(g){var e=y;throw g=!1,y=null,e}}()}}function mn(e){var t=e.target||e.srcElement||window;return t.correspondingUseElement&&(t=t.correspondingUseElement),3===t.nodeType?t.parentNode:t}
/**
 * Checks if an event is supported in the current execution environment.
 *
 * NOTE: This will not work correctly for non-generic events such as `change`,
 * `reset`, `load`, `error`, and `select`.
 *
 * Borrows from Modernizr.
 *
 * @param {string} eventNameSuffix Event name, e.g. "click".
 * @return {boolean} True if the event is supported.
 * @internal
 * @license Modernizr 3.0.0pre (Custom Build) | MIT
 */function gn(e){if(!j)return!1;var t="on"+e,n=t in document;if(!n){var r=document.createElement("div");r.setAttribute(t,"return;"),n="function"==typeof r[t]}return n}var yn,vn,bn,wn=[];function Fn(e){if(3===e.tag)return e.stateNode.containerInfo;for(;e.return;)e=e.return;return 3!==e.tag?null:e.stateNode.containerInfo}function Cn(e,t,n,r,o){hn(function(e,t,n,r,o){for(var a=null,i=0;i<P.length;i++){var l=P[i];if(l){var u=l.extractEvents(e,t,n,r,o);u&&(a=sn(a,u))}}return a}(e,t,n,r,o))}function Tn(e){var t=e.targetInst,n=t;do{if(!n){e.ancestors.push(n);break}var r=Fn(n);if(!r)break;var o=n.tag;5!==o&&6!==o||e.ancestors.push(n),n=va(r)}while(n);for(var a=0;a<e.ancestors.length;a++){t=e.ancestors[a];var i=mn(e.nativeEvent),l=e.topLevelType,u=e.nativeEvent,s=e.eventSystemFlags;0===a&&(s|=64),Cn(l,t,u,i,s)}}function kn(e,t,n,r){var o,a=function(e,t,n,r){if(wn.length){var o=wn.pop();return o.topLevelType=e,o.eventSystemFlags=r,o.nativeEvent=t,o.targetInst=n,o}return{topLevelType:e,eventSystemFlags:r,nativeEvent:t,targetInst:n,ancestors:[]}}(e,n,r,t);try{!function(e,t,n){if(J)return e(t,n);J=!0;try{Y(e,t,n)}finally{J=!1,X()}}(Tn,a)}finally{(o=a).topLevelType=null,o.nativeEvent=null,o.targetInst=null,o.ancestors.length=0,wn.length<10&&wn.push(o)}}function En(e,t,n){if(!n.has(e)){switch(e){case"scroll":ir("scroll",t);break;case"focus":case"blur":ir("focus",t),ir("blur",t),n.set("blur",null),n.set("focus",null);break;case"cancel":case"close":gn(e)&&ir(e,t);break;case"invalid":case"submit":case"reset":break;default:-1!==Xt.indexOf(e)||ar(e,t)}n.set(e,null)}}var xn=!1,Rn=[],Sn=null,Dn=null,_n=null,On=new Map,Pn=new Map,An=[];var In=["mousedown","mouseup","touchcancel","touchend","touchstart","auxclick","dblclick","pointercancel","pointerdown","pointerup","dragend","dragstart","drop","compositionend","compositionstart","keydown","keypress","keyup","input","textInput","close","cancel","copy","cut","paste","click","change","contextmenu","reset","submit"],Wn=["focus","blur","dragenter","dragleave","mouseover","mouseout","pointerover","pointerout","gotpointercapture","lostpointercapture"];function Nn(e){return In.indexOf(e)>-1}function Ln(e,t,n){En(e,t,n)}function jn(e,t,n,r,o){return{blockedOn:e,topLevelType:t,eventSystemFlags:32|n,nativeEvent:o,container:r}}function Mn(e,t,n,r,o){var a=jn(e,t,n,r,o);Rn.push(a)}function Bn(e,t){switch(e){case"focus":case"blur":Sn=null;break;case"dragenter":case"dragleave":Dn=null;break;case"mouseover":case"mouseout":_n=null;break;case"pointerover":case"pointerout":var n=t.pointerId;On.delete(n);break;case"gotpointercapture":case"lostpointercapture":var r=t.pointerId;Pn.delete(r)}}function zn(e,t,n,r,o,a){if(null===e||e.nativeEvent!==a){var i=jn(t,n,r,o,a);if(null!==t){var l=ba(t);null!==l&&vn(l)}return i}return e.eventSystemFlags|=r,e}function Un(e){var t=va(e.target);if(null!==t){var n=nn(t);if(null!==n){var r=n.tag;if(13===r){var a=rn(n);if(null!==a)return e.blockedOn=a,void o.unstable_runWithPriority(e.priority,(function(){bn(n)}))}else if(3===r){if(n.stateNode.hydrate)return void(e.blockedOn=on(n))}}}e.blockedOn=null}function Hn(e){if(null!==e.blockedOn)return!1;var t=dr(e.topLevelType,e.eventSystemFlags,e.container,e.nativeEvent);if(null!==t){var n=ba(t);return null!==n&&vn(n),e.blockedOn=t,!1}return!0}function Vn(e,t,n){Hn(e)&&n.delete(t)}function $n(){for(xn=!1;Rn.length>0;){var e=Rn[0];if(null!==e.blockedOn){var t=ba(e.blockedOn);null!==t&&yn(t);break}var n=dr(e.topLevelType,e.eventSystemFlags,e.container,e.nativeEvent);null!==n?e.blockedOn=n:Rn.shift()}null!==Sn&&Hn(Sn)&&(Sn=null),null!==Dn&&Hn(Dn)&&(Dn=null),null!==_n&&Hn(_n)&&(_n=null),On.forEach(Vn),Pn.forEach(Vn)}function qn(e,t){e.blockedOn===t&&(e.blockedOn=null,xn||(xn=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,$n)))}function Qn(e){if(Rn.length>0){qn(Rn[0],e);for(var t=1;t<Rn.length;t++){var n=Rn[t];n.blockedOn===e&&(n.blockedOn=null)}}null!==Sn&&qn(Sn,e),null!==Dn&&qn(Dn,e),null!==_n&&qn(_n,e);var r=function(t){return qn(t,e)};On.forEach(r),Pn.forEach(r);for(var o=0;o<An.length;o++){var a=An[o];a.blockedOn===e&&(a.blockedOn=null)}for(;An.length>0;){var i=An[0];if(null!==i.blockedOn)break;Un(i),null===i.blockedOn&&An.shift()}}var Yn={},Kn=new Map,Jn=new Map,Xn=["change","selectionchange","textInput","compositionstart","compositionend","compositionupdate"],Gn=["drag","drag","dragenter","dragEnter","dragexit","dragExit","dragleave","dragLeave","dragover","dragOver","mousemove","mouseMove","mouseout","mouseOut","mouseover","mouseOver","pointermove","pointerMove","pointerout","pointerOut","pointerover","pointerOver","scroll","scroll","toggle","toggle","touchmove","touchMove","wheel","wheel"],Zn=["abort","abort",Qt,"animationEnd",Yt,"animationIteration",Kt,"animationStart","canplay","canPlay","canplaythrough","canPlayThrough","durationchange","durationChange","emptied","emptied","encrypted","encrypted","ended","ended","error","error","gotpointercapture","gotPointerCapture","load","load","loadeddata","loadedData","loadedmetadata","loadedMetadata","loadstart","loadStart","lostpointercapture","lostPointerCapture","playing","playing","progress","progress","seeking","seeking","stalled","stalled","suspend","suspend","timeupdate","timeUpdate",Jt,"transitionEnd","waiting","waiting"];function er(e,t){for(var n=0;n<e.length;n+=2){var r=e[n],o=e[n+1],a="on"+(o[0].toUpperCase()+o.slice(1)),i={phasedRegistrationNames:{bubbled:a,captured:a+"Capture"},dependencies:[r],eventPriority:t};Jn.set(r,t),Kn.set(r,i),Yn[o]=i}}er(["blur","blur","cancel","cancel","click","click","close","close","contextmenu","contextMenu","copy","copy","cut","cut","auxclick","auxClick","dblclick","doubleClick","dragend","dragEnd","dragstart","dragStart","drop","drop","focus","focus","input","input","invalid","invalid","keydown","keyDown","keypress","keyPress","keyup","keyUp","mousedown","mouseDown","mouseup","mouseUp","paste","paste","pause","pause","play","play","pointercancel","pointerCancel","pointerdown","pointerDown","pointerup","pointerUp","ratechange","rateChange","reset","reset","seeked","seeked","submit","submit","touchcancel","touchCancel","touchend","touchEnd","touchstart","touchStart","volumechange","volumeChange"],0),er(Gn,1),er(Zn,2),function(e,t){for(var n=0;n<e.length;n++)Jn.set(e[n],t)}(Xn,0);var tr=o.unstable_UserBlockingPriority,nr=o.unstable_runWithPriority,rr=!0;function or(e){rr=!!e}function ar(e,t){lr(t,e,!1)}function ir(e,t){lr(t,e,!0)}function lr(e,t,n){var r;switch(function(e){var t=Jn.get(e);return void 0===t?2:t}(t)){case 0:r=ur.bind(null,t,1,e);break;case 1:r=sr.bind(null,t,1,e);break;case 2:default:r=cr.bind(null,t,1,e)}var o=t;n?function(e,t,n){e.addEventListener(t,n,!0)}(e,o,r):function(e,t,n){e.addEventListener(t,n,!1)}(e,o,r)}function ur(e,t,n,r){r.timeStamp,K||Q(),function(e,t,n,r,o){var a=K;K=!0;try{q(e,t,n,r,o)}finally{(K=a)||X()}}(cr,e,t,n,r)}function sr(e,t,n,r){nr(tr,cr.bind(null,e,t,n,r))}function cr(e,t,n,r){if(rr)if(Rn.length>0&&Nn(e))Mn(null,e,t,n,r);else{var o=dr(e,t,n,r);null!==o?Nn(e)?Mn(o,e,t,n,r):function(e,t,n,r,o){switch(t){case"focus":return Sn=zn(Sn,e,t,n,r,o),!0;case"dragenter":return Dn=zn(Dn,e,t,n,r,o),!0;case"mouseover":return _n=zn(_n,e,t,n,r,o),!0;case"pointerover":var a=o,i=a.pointerId;return On.set(i,zn(On.get(i)||null,e,t,n,r,a)),!0;case"gotpointercapture":var l=o,u=l.pointerId;return Pn.set(u,zn(Pn.get(u)||null,e,t,n,r,l)),!0}return!1}(o,e,t,n,r)||(Bn(e,r),kn(e,t,r,null)):Bn(e,r)}}function dr(e,t,n,r){var o=va(mn(r));if(null!==o){var a=nn(o);if(null===a)o=null;else{var i=a.tag;if(13===i){var l=rn(a);if(null!==l)return l;o=null}else if(3===i){if(a.stateNode.hydrate)return on(a);o=null}else a!==o&&(o=null)}}return kn(e,t,r,o),null}var fr={animation:["animationDelay","animationDirection","animationDuration","animationFillMode","animationIterationCount","animationName","animationPlayState","animationTimingFunction"],background:["backgroundAttachment","backgroundClip","backgroundColor","backgroundImage","backgroundOrigin","backgroundPositionX","backgroundPositionY","backgroundRepeat","backgroundSize"],backgroundPosition:["backgroundPositionX","backgroundPositionY"],border:["borderBottomColor","borderBottomStyle","borderBottomWidth","borderImageOutset","borderImageRepeat","borderImageSlice","borderImageSource","borderImageWidth","borderLeftColor","borderLeftStyle","borderLeftWidth","borderRightColor","borderRightStyle","borderRightWidth","borderTopColor","borderTopStyle","borderTopWidth"],borderBlockEnd:["borderBlockEndColor","borderBlockEndStyle","borderBlockEndWidth"],borderBlockStart:["borderBlockStartColor","borderBlockStartStyle","borderBlockStartWidth"],borderBottom:["borderBottomColor","borderBottomStyle","borderBottomWidth"],borderColor:["borderBottomColor","borderLeftColor","borderRightColor","borderTopColor"],borderImage:["borderImageOutset","borderImageRepeat","borderImageSlice","borderImageSource","borderImageWidth"],borderInlineEnd:["borderInlineEndColor","borderInlineEndStyle","borderInlineEndWidth"],borderInlineStart:["borderInlineStartColor","borderInlineStartStyle","borderInlineStartWidth"],borderLeft:["borderLeftColor","borderLeftStyle","borderLeftWidth"],borderRadius:["borderBottomLeftRadius","borderBottomRightRadius","borderTopLeftRadius","borderTopRightRadius"],borderRight:["borderRightColor","borderRightStyle","borderRightWidth"],borderStyle:["borderBottomStyle","borderLeftStyle","borderRightStyle","borderTopStyle"],borderTop:["borderTopColor","borderTopStyle","borderTopWidth"],borderWidth:["borderBottomWidth","borderLeftWidth","borderRightWidth","borderTopWidth"],columnRule:["columnRuleColor","columnRuleStyle","columnRuleWidth"],columns:["columnCount","columnWidth"],flex:["flexBasis","flexGrow","flexShrink"],flexFlow:["flexDirection","flexWrap"],font:["fontFamily","fontFeatureSettings","fontKerning","fontLanguageOverride","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontVariantAlternates","fontVariantCaps","fontVariantEastAsian","fontVariantLigatures","fontVariantNumeric","fontVariantPosition","fontWeight","lineHeight"],fontVariant:["fontVariantAlternates","fontVariantCaps","fontVariantEastAsian","fontVariantLigatures","fontVariantNumeric","fontVariantPosition"],gap:["columnGap","rowGap"],grid:["gridAutoColumns","gridAutoFlow","gridAutoRows","gridTemplateAreas","gridTemplateColumns","gridTemplateRows"],gridArea:["gridColumnEnd","gridColumnStart","gridRowEnd","gridRowStart"],gridColumn:["gridColumnEnd","gridColumnStart"],gridColumnGap:["columnGap"],gridGap:["columnGap","rowGap"],gridRow:["gridRowEnd","gridRowStart"],gridRowGap:["rowGap"],gridTemplate:["gridTemplateAreas","gridTemplateColumns","gridTemplateRows"],listStyle:["listStyleImage","listStylePosition","listStyleType"],margin:["marginBottom","marginLeft","marginRight","marginTop"],marker:["markerEnd","markerMid","markerStart"],mask:["maskClip","maskComposite","maskImage","maskMode","maskOrigin","maskPositionX","maskPositionY","maskRepeat","maskSize"],maskPosition:["maskPositionX","maskPositionY"],outline:["outlineColor","outlineStyle","outlineWidth"],overflow:["overflowX","overflowY"],padding:["paddingBottom","paddingLeft","paddingRight","paddingTop"],placeContent:["alignContent","justifyContent"],placeItems:["alignItems","justifyItems"],placeSelf:["alignSelf","justifySelf"],textDecoration:["textDecorationColor","textDecorationLine","textDecorationStyle"],textEmphasis:["textEmphasisColor","textEmphasisStyle"],transition:["transitionDelay","transitionDuration","transitionProperty","transitionTimingFunction"],wordWrap:["overflowWrap"]},pr={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0};var hr=["Webkit","ms","Moz","O"];function mr(e,t,n){return null==t||"boolean"==typeof t||""===t?"":n||"number"!=typeof t||0===t||pr.hasOwnProperty(e)&&pr[e]?(""+t).trim():t+"px"}Object.keys(pr).forEach((function(e){hr.forEach((function(t){pr[function(e,t){return e+t.charAt(0).toUpperCase()+t.substring(1)}(t,e)]=pr[e]}))}));var gr=/([A-Z])/g,yr=/^ms-/;var vr=/^(?:webkit|moz|o)[A-Z]/,br=/^-ms-/,wr=/-(.)/g,Fr=/;\s*$/,Cr={},Tr={},kr=!1,Er=!1,xr=function(e){Cr.hasOwnProperty(e)&&Cr[e]||(Cr[e]=!0,s("Unsupported style property %s. Did you mean %s?",e,e.replace(br,"ms-").replace(wr,(function(e,t){return t.toUpperCase()}))))},Rr=function(e,t){e.indexOf("-")>-1?xr(e):vr.test(e)?function(e){Cr.hasOwnProperty(e)&&Cr[e]||(Cr[e]=!0,s("Unsupported vendor-prefixed style property %s. Did you mean %s?",e,e.charAt(0).toUpperCase()+e.slice(1)))}(e):Fr.test(t)&&function(e,t){Tr.hasOwnProperty(t)&&Tr[t]||(Tr[t]=!0,s('Style property values shouldn\'t contain a semicolon. Try "%s: %s" instead.',e,t.replace(Fr,"")))}(e,t),"number"==typeof t&&(isNaN(t)?function(e,t){kr||(kr=!0,s("`NaN` is an invalid value for the `%s` css style property.",e))}(e):isFinite(t)||function(e,t){Er||(Er=!0,s("`Infinity` is an invalid value for the `%s` css style property.",e))}(e))};function Sr(e){var t="",n="";for(var r in e)if(e.hasOwnProperty(r)){var o=e[r];if(null!=o){var a=0===r.indexOf("--");t+=n+(a?r:r.replace(gr,"-$1").toLowerCase().replace(yr,"-ms-"))+":",t+=mr(r,o,a),n=";"}}return t||null}function Dr(e,t){var n=e.style;for(var r in t)if(t.hasOwnProperty(r)){var o=0===r.indexOf("--");o||Rr(r,t[r]);var a=mr(r,t[r],o);"float"===r&&(r="cssFloat"),o?n.setProperty(r,a):n[r]=a}}function _r(e){var t={};for(var n in e)for(var r=fr[n]||[n],o=0;o<r.length;o++)t[r[o]]=n;return t}var Or=r({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}),Pr=null;function Ar(e,t){if(t){if(Or[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(e+" is a void element tag and must neither have `children` nor use `dangerouslySetInnerHTML`."+Pr.getStackAddendum());if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!=typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://fb.me/react-invariant-dangerously-set-inner-html for more information.")}if(!t.suppressContentEditableWarning&&t.contentEditable&&null!=t.children&&s("A component is `contentEditable` and contains `children` managed by React. It is now your responsibility to guarantee that none of those nodes are unexpectedly modified or duplicated. This is probably not intentional."),null!=t.style&&"object"!=typeof t.style)throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX."+Pr.getStackAddendum())}}function Ir(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}Pr=l.ReactDebugCurrentFrame;var Wr={accept:"accept",acceptcharset:"acceptCharset","accept-charset":"acceptCharset",accesskey:"accessKey",action:"action",allowfullscreen:"allowFullScreen",alt:"alt",as:"as",async:"async",autocapitalize:"autoCapitalize",autocomplete:"autoComplete",autocorrect:"autoCorrect",autofocus:"autoFocus",autoplay:"autoPlay",autosave:"autoSave",capture:"capture",cellpadding:"cellPadding",cellspacing:"cellSpacing",challenge:"challenge",charset:"charSet",checked:"checked",children:"children",cite:"cite",class:"className",classid:"classID",classname:"className",cols:"cols",colspan:"colSpan",content:"content",contenteditable:"contentEditable",contextmenu:"contextMenu",controls:"controls",controlslist:"controlsList",coords:"coords",crossorigin:"crossOrigin",dangerouslysetinnerhtml:"dangerouslySetInnerHTML",data:"data",datetime:"dateTime",default:"default",defaultchecked:"defaultChecked",defaultvalue:"defaultValue",defer:"defer",dir:"dir",disabled:"disabled",disablepictureinpicture:"disablePictureInPicture",download:"download",draggable:"draggable",enctype:"encType",for:"htmlFor",form:"form",formmethod:"formMethod",formaction:"formAction",formenctype:"formEncType",formnovalidate:"formNoValidate",formtarget:"formTarget",frameborder:"frameBorder",headers:"headers",height:"height",hidden:"hidden",high:"high",href:"href",hreflang:"hrefLang",htmlfor:"htmlFor",httpequiv:"httpEquiv","http-equiv":"httpEquiv",icon:"icon",id:"id",innerhtml:"innerHTML",inputmode:"inputMode",integrity:"integrity",is:"is",itemid:"itemID",itemprop:"itemProp",itemref:"itemRef",itemscope:"itemScope",itemtype:"itemType",keyparams:"keyParams",keytype:"keyType",kind:"kind",label:"label",lang:"lang",list:"list",loop:"loop",low:"low",manifest:"manifest",marginwidth:"marginWidth",marginheight:"marginHeight",max:"max",maxlength:"maxLength",media:"media",mediagroup:"mediaGroup",method:"method",min:"min",minlength:"minLength",multiple:"multiple",muted:"muted",name:"name",nomodule:"noModule",nonce:"nonce",novalidate:"noValidate",open:"open",optimum:"optimum",pattern:"pattern",placeholder:"placeholder",playsinline:"playsInline",poster:"poster",preload:"preload",profile:"profile",radiogroup:"radioGroup",readonly:"readOnly",referrerpolicy:"referrerPolicy",rel:"rel",required:"required",reversed:"reversed",role:"role",rows:"rows",rowspan:"rowSpan",sandbox:"sandbox",scope:"scope",scoped:"scoped",scrolling:"scrolling",seamless:"seamless",selected:"selected",shape:"shape",size:"size",sizes:"sizes",span:"span",spellcheck:"spellCheck",src:"src",srcdoc:"srcDoc",srclang:"srcLang",srcset:"srcSet",start:"start",step:"step",style:"style",summary:"summary",tabindex:"tabIndex",target:"target",title:"title",type:"type",usemap:"useMap",value:"value",width:"width",wmode:"wmode",wrap:"wrap",about:"about",accentheight:"accentHeight","accent-height":"accentHeight",accumulate:"accumulate",additive:"additive",alignmentbaseline:"alignmentBaseline","alignment-baseline":"alignmentBaseline",allowreorder:"allowReorder",alphabetic:"alphabetic",amplitude:"amplitude",arabicform:"arabicForm","arabic-form":"arabicForm",ascent:"ascent",attributename:"attributeName",attributetype:"attributeType",autoreverse:"autoReverse",azimuth:"azimuth",basefrequency:"baseFrequency",baselineshift:"baselineShift","baseline-shift":"baselineShift",baseprofile:"baseProfile",bbox:"bbox",begin:"begin",bias:"bias",by:"by",calcmode:"calcMode",capheight:"capHeight","cap-height":"capHeight",clip:"clip",clippath:"clipPath","clip-path":"clipPath",clippathunits:"clipPathUnits",cliprule:"clipRule","clip-rule":"clipRule",color:"color",colorinterpolation:"colorInterpolation","color-interpolation":"colorInterpolation",colorinterpolationfilters:"colorInterpolationFilters","color-interpolation-filters":"colorInterpolationFilters",colorprofile:"colorProfile","color-profile":"colorProfile",colorrendering:"colorRendering","color-rendering":"colorRendering",contentscripttype:"contentScriptType",contentstyletype:"contentStyleType",cursor:"cursor",cx:"cx",cy:"cy",d:"d",datatype:"datatype",decelerate:"decelerate",descent:"descent",diffuseconstant:"diffuseConstant",direction:"direction",display:"display",divisor:"divisor",dominantbaseline:"dominantBaseline","dominant-baseline":"dominantBaseline",dur:"dur",dx:"dx",dy:"dy",edgemode:"edgeMode",elevation:"elevation",enablebackground:"enableBackground","enable-background":"enableBackground",end:"end",exponent:"exponent",externalresourcesrequired:"externalResourcesRequired",fill:"fill",fillopacity:"fillOpacity","fill-opacity":"fillOpacity",fillrule:"fillRule","fill-rule":"fillRule",filter:"filter",filterres:"filterRes",filterunits:"filterUnits",floodopacity:"floodOpacity","flood-opacity":"floodOpacity",floodcolor:"floodColor","flood-color":"floodColor",focusable:"focusable",fontfamily:"fontFamily","font-family":"fontFamily",fontsize:"fontSize","font-size":"fontSize",fontsizeadjust:"fontSizeAdjust","font-size-adjust":"fontSizeAdjust",fontstretch:"fontStretch","font-stretch":"fontStretch",fontstyle:"fontStyle","font-style":"fontStyle",fontvariant:"fontVariant","font-variant":"fontVariant",fontweight:"fontWeight","font-weight":"fontWeight",format:"format",from:"from",fx:"fx",fy:"fy",g1:"g1",g2:"g2",glyphname:"glyphName","glyph-name":"glyphName",glyphorientationhorizontal:"glyphOrientationHorizontal","glyph-orientation-horizontal":"glyphOrientationHorizontal",glyphorientationvertical:"glyphOrientationVertical","glyph-orientation-vertical":"glyphOrientationVertical",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits",hanging:"hanging",horizadvx:"horizAdvX","horiz-adv-x":"horizAdvX",horizoriginx:"horizOriginX","horiz-origin-x":"horizOriginX",ideographic:"ideographic",imagerendering:"imageRendering","image-rendering":"imageRendering",in2:"in2",in:"in",inlist:"inlist",intercept:"intercept",k1:"k1",k2:"k2",k3:"k3",k4:"k4",k:"k",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",kerning:"kerning",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",lengthadjust:"lengthAdjust",letterspacing:"letterSpacing","letter-spacing":"letterSpacing",lightingcolor:"lightingColor","lighting-color":"lightingColor",limitingconeangle:"limitingConeAngle",local:"local",markerend:"markerEnd","marker-end":"markerEnd",markerheight:"markerHeight",markermid:"markerMid","marker-mid":"markerMid",markerstart:"markerStart","marker-start":"markerStart",markerunits:"markerUnits",markerwidth:"markerWidth",mask:"mask",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",mathematical:"mathematical",mode:"mode",numoctaves:"numOctaves",offset:"offset",opacity:"opacity",operator:"operator",order:"order",orient:"orient",orientation:"orientation",origin:"origin",overflow:"overflow",overlineposition:"overlinePosition","overline-position":"overlinePosition",overlinethickness:"overlineThickness","overline-thickness":"overlineThickness",paintorder:"paintOrder","paint-order":"paintOrder",panose1:"panose1","panose-1":"panose1",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",pointerevents:"pointerEvents","pointer-events":"pointerEvents",points:"points",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",prefix:"prefix",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",property:"property",r:"r",radius:"radius",refx:"refX",refy:"refY",renderingintent:"renderingIntent","rendering-intent":"renderingIntent",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",resource:"resource",restart:"restart",result:"result",results:"results",rotate:"rotate",rx:"rx",ry:"ry",scale:"scale",security:"security",seed:"seed",shaperendering:"shapeRendering","shape-rendering":"shapeRendering",slope:"slope",spacing:"spacing",specularconstant:"specularConstant",specularexponent:"specularExponent",speed:"speed",spreadmethod:"spreadMethod",startoffset:"startOffset",stddeviation:"stdDeviation",stemh:"stemh",stemv:"stemv",stitchtiles:"stitchTiles",stopcolor:"stopColor","stop-color":"stopColor",stopopacity:"stopOpacity","stop-opacity":"stopOpacity",strikethroughposition:"strikethroughPosition","strikethrough-position":"strikethroughPosition",strikethroughthickness:"strikethroughThickness","strikethrough-thickness":"strikethroughThickness",string:"string",stroke:"stroke",strokedasharray:"strokeDasharray","stroke-dasharray":"strokeDasharray",strokedashoffset:"strokeDashoffset","stroke-dashoffset":"strokeDashoffset",strokelinecap:"strokeLinecap","stroke-linecap":"strokeLinecap",strokelinejoin:"strokeLinejoin","stroke-linejoin":"strokeLinejoin",strokemiterlimit:"strokeMiterlimit","stroke-miterlimit":"strokeMiterlimit",strokewidth:"strokeWidth","stroke-width":"strokeWidth",strokeopacity:"strokeOpacity","stroke-opacity":"strokeOpacity",suppresscontenteditablewarning:"suppressContentEditableWarning",suppresshydrationwarning:"suppressHydrationWarning",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",textanchor:"textAnchor","text-anchor":"textAnchor",textdecoration:"textDecoration","text-decoration":"textDecoration",textlength:"textLength",textrendering:"textRendering","text-rendering":"textRendering",to:"to",transform:"transform",typeof:"typeof",u1:"u1",u2:"u2",underlineposition:"underlinePosition","underline-position":"underlinePosition",underlinethickness:"underlineThickness","underline-thickness":"underlineThickness",unicode:"unicode",unicodebidi:"unicodeBidi","unicode-bidi":"unicodeBidi",unicoderange:"unicodeRange","unicode-range":"unicodeRange",unitsperem:"unitsPerEm","units-per-em":"unitsPerEm",unselectable:"unselectable",valphabetic:"vAlphabetic","v-alphabetic":"vAlphabetic",values:"values",vectoreffect:"vectorEffect","vector-effect":"vectorEffect",version:"version",vertadvy:"vertAdvY","vert-adv-y":"vertAdvY",vertoriginx:"vertOriginX","vert-origin-x":"vertOriginX",vertoriginy:"vertOriginY","vert-origin-y":"vertOriginY",vhanging:"vHanging","v-hanging":"vHanging",videographic:"vIdeographic","v-ideographic":"vIdeographic",viewbox:"viewBox",viewtarget:"viewTarget",visibility:"visibility",vmathematical:"vMathematical","v-mathematical":"vMathematical",vocab:"vocab",widths:"widths",wordspacing:"wordSpacing","word-spacing":"wordSpacing",writingmode:"writingMode","writing-mode":"writingMode",x1:"x1",x2:"x2",x:"x",xchannelselector:"xChannelSelector",xheight:"xHeight","x-height":"xHeight",xlinkactuate:"xlinkActuate","xlink:actuate":"xlinkActuate",xlinkarcrole:"xlinkArcrole","xlink:arcrole":"xlinkArcrole",xlinkhref:"xlinkHref","xlink:href":"xlinkHref",xlinkrole:"xlinkRole","xlink:role":"xlinkRole",xlinkshow:"xlinkShow","xlink:show":"xlinkShow",xlinktitle:"xlinkTitle","xlink:title":"xlinkTitle",xlinktype:"xlinkType","xlink:type":"xlinkType",xmlbase:"xmlBase","xml:base":"xmlBase",xmllang:"xmlLang","xml:lang":"xmlLang",xmlns:"xmlns","xml:space":"xmlSpace",xmlnsxlink:"xmlnsXlink","xmlns:xlink":"xmlnsXlink",xmlspace:"xmlSpace",y1:"y1",y2:"y2",y:"y",ychannelselector:"yChannelSelector",z:"z",zoomandpan:"zoomAndPan"},Nr={"aria-current":0,"aria-details":0,"aria-disabled":0,"aria-hidden":0,"aria-invalid":0,"aria-keyshortcuts":0,"aria-label":0,"aria-roledescription":0,"aria-autocomplete":0,"aria-checked":0,"aria-expanded":0,"aria-haspopup":0,"aria-level":0,"aria-modal":0,"aria-multiline":0,"aria-multiselectable":0,"aria-orientation":0,"aria-placeholder":0,"aria-pressed":0,"aria-readonly":0,"aria-required":0,"aria-selected":0,"aria-sort":0,"aria-valuemax":0,"aria-valuemin":0,"aria-valuenow":0,"aria-valuetext":0,"aria-atomic":0,"aria-busy":0,"aria-live":0,"aria-relevant":0,"aria-dropeffect":0,"aria-grabbed":0,"aria-activedescendant":0,"aria-colcount":0,"aria-colindex":0,"aria-colspan":0,"aria-controls":0,"aria-describedby":0,"aria-errormessage":0,"aria-flowto":0,"aria-labelledby":0,"aria-owns":0,"aria-posinset":0,"aria-rowcount":0,"aria-rowindex":0,"aria-rowspan":0,"aria-setsize":0},Lr={},jr=new RegExp("^(aria)-["+Z+"]*$"),Mr=new RegExp("^(aria)[A-Z]["+Z+"]*$"),Br=Object.prototype.hasOwnProperty;function zr(e,t){if(Br.call(Lr,t)&&Lr[t])return!0;if(Mr.test(t)){var n="aria-"+t.slice(4).toLowerCase(),r=Nr.hasOwnProperty(n)?n:null;if(null==r)return s("Invalid ARIA attribute `%s`. ARIA attributes follow the pattern aria-* and must be lowercase.",t),Lr[t]=!0,!0;if(t!==r)return s("Invalid ARIA attribute `%s`. Did you mean `%s`?",t,r),Lr[t]=!0,!0}if(jr.test(t)){var o=t.toLowerCase(),a=Nr.hasOwnProperty(o)?o:null;if(null==a)return Lr[t]=!0,!1;if(t!==a)return s("Unknown ARIA attribute `%s`. Did you mean `%s`?",t,a),Lr[t]=!0,!0}return!0}function Ur(e,t){Ir(e,t)||function(e,t){var n=[];for(var r in t){zr(0,r)||n.push(r)}var o=n.map((function(e){return"`"+e+"`"})).join(", ");1===n.length?s("Invalid aria prop %s on <%s> tag. For details, see https://fb.me/invalid-aria-prop",o,e):n.length>1&&s("Invalid aria props %s on <%s> tag. For details, see https://fb.me/invalid-aria-prop",o,e)}(e,t)}var Hr=!1;var Vr,$r={},qr=Object.prototype.hasOwnProperty,Qr=/^on./,Yr=/^on[^A-Z]/,Kr=new RegExp("^(aria)-["+Z+"]*$"),Jr=new RegExp("^(aria)[A-Z]["+Z+"]*$");Vr=function(e,t,n,r){if(qr.call($r,t)&&$r[t])return!0;var o=t.toLowerCase();if("onfocusin"===o||"onfocusout"===o)return s("React uses onFocus and onBlur instead of onFocusIn and onFocusOut. All React events are normalized to bubble, so onFocusIn and onFocusOut are not needed/supported by React."),$r[t]=!0,!0;if(r){if(I.hasOwnProperty(t))return!0;var a=N.hasOwnProperty(o)?N[o]:null;if(null!=a)return s("Invalid event handler property `%s`. Did you mean `%s`?",t,a),$r[t]=!0,!0;if(Qr.test(t))return s("Unknown event handler property `%s`. It will be ignored.",t),$r[t]=!0,!0}else if(Qr.test(t))return Yr.test(t)&&s("Invalid event handler property `%s`. React events use the camelCase naming convention, for example `onClick`.",t),$r[t]=!0,!0;if(Kr.test(t)||Jr.test(t))return!0;if("innerhtml"===o)return s("Directly setting property `innerHTML` is not permitted. For more information, lookup documentation on `dangerouslySetInnerHTML`."),$r[t]=!0,!0;if("aria"===o)return s("The `aria` attribute is reserved for future use in React. Pass individual `aria-` attributes instead."),$r[t]=!0,!0;if("is"===o&&null!=n&&"string"!=typeof n)return s("Received a `%s` for a string attribute `is`. If this is expected, cast the value to a string.",typeof n),$r[t]=!0,!0;if("number"==typeof n&&isNaN(n))return s("Received NaN for the `%s` attribute. If this is expected, cast the value to a string.",t),$r[t]=!0,!0;var i=ue(t),l=null!==i&&0===i.type;if(Wr.hasOwnProperty(o)){var u=Wr[o];if(u!==t)return s("Invalid DOM property `%s`. Did you mean `%s`?",t,u),$r[t]=!0,!0}else if(!l&&t!==o)return s("React does not recognize the `%s` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `%s` instead. If you accidentally passed it from a parent component, remove it from the DOM element.",t,o),$r[t]=!0,!0;return"boolean"==typeof n&&ie(t,n,i,!1)?(n?s('Received `%s` for a non-boolean attribute `%s`.\n\nIf you want to write it to the DOM, pass a string instead: %s="%s" or %s={value.toString()}.',n,t,t,n,t):s('Received `%s` for a non-boolean attribute `%s`.\n\nIf you want to write it to the DOM, pass a string instead: %s="%s" or %s={value.toString()}.\n\nIf you used to conditionally omit it with %s={condition && value}, pass %s={condition ? value : undefined} instead.',n,t,t,n,t,t,t),$r[t]=!0,!0):!!l||(ie(t,n,i,!1)?($r[t]=!0,!1):("false"!==n&&"true"!==n||null===i||3!==i.type||(s("Received the string `%s` for the boolean attribute `%s`. %s Did you mean %s={%s}?",n,t,"false"===n?"The browser will interpret it as a truthy value.":'Although this works, it will not work as expected if you pass the string "false".',t,n),$r[t]=!0),!0))};function Xr(e,t,n){Ir(e,t)||function(e,t,n){var r=[];for(var o in t){Vr(0,o,t[o],n)||r.push(o)}var a=r.map((function(e){return"`"+e+"`"})).join(", ");1===r.length?s("Invalid value for prop %s on <%s> tag. Either remove it from the element, or pass a string or number value to keep it in the DOM. For details, see https://fb.me/react-attribute-behavior",a,e):r.length>1&&s("Invalid values for props %s on <%s> tag. Either remove them from the element, or pass a string or number value to keep them in the DOM. For details, see https://fb.me/react-attribute-behavior",a,e)}(e,t,n)}var Gr,Zr,eo,to,no,ro,oo,ao,io,lo,uo=!1,so=It;Gr={time:!0,dialog:!0,webview:!0},eo=function(e,t){Ur(e,t),function(e,t){"input"!==e&&"textarea"!==e&&"select"!==e||null==t||null!==t.value||Hr||(Hr=!0,"select"===e&&t.multiple?s("`value` prop on `%s` should not be null. Consider using an empty array when `multiple` is set to `true` to clear the component or `undefined` for uncontrolled components.",e):s("`value` prop on `%s` should not be null. Consider using an empty string to clear the component or `undefined` for uncontrolled components.",e))}(e,t),Xr(e,t,!0)},ao=j&&!document.documentMode;var co=/\r\n?/g,fo=/\u0000|\uFFFD/g;function po(e,t){!function(e,t){for(var n=Zt(t),r=W[e],o=0;o<r.length;o++){En(r[o],t,n)}}(t,9===e.nodeType||11===e.nodeType?e:e.ownerDocument)}function ho(e){return 9===e.nodeType?e:e.ownerDocument}function mo(){}function go(e){e.onclick=mo}function yo(e,t,n,r){var o,a=Ir(t,n);switch(eo(t,n),t){case"iframe":case"object":case"embed":ar("load",e),o=n;break;case"video":case"audio":for(var i=0;i<Xt.length;i++)ar(Xt[i],e);o=n;break;case"source":ar("error",e),o=n;break;case"img":case"image":case"link":ar("error",e),ar("load",e),o=n;break;case"form":ar("reset",e),ar("submit",e),o=n;break;case"details":ar("toggle",e),o=n;break;case"input":dt(e,n),o=ct(e,n),ar("invalid",e),po(r,"onChange");break;case"option":wt(0,n),o=Ft(0,n);break;case"select":xt(e,n),o=Et(0,n),ar("invalid",e),po(r,"onChange");break;case"textarea":Dt(e,n),o=St(e,n),ar("invalid",e),po(r,"onChange");break;default:o=n}switch(Ar(t,o),function(e,t,n,r,o){for(var a in r)if(r.hasOwnProperty(a)){var i=r[a];if("style"===a)i&&Object.freeze(i),Dr(t,i);else if("dangerouslySetInnerHTML"===a){var l=i?i.__html:void 0;null!=l&&Bt(t,l)}else if("children"===a){if("string"==typeof i)("textarea"!==e||""!==i)&&zt(t,i);else"number"==typeof i&&zt(t,""+i)}else"suppressContentEditableWarning"===a||"suppressHydrationWarning"===a||"autoFocus"===a||(I.hasOwnProperty(a)?null!=i&&("function"!=typeof i&&oo(a,i),po(n,a)):null!=i&&ve(t,a,i,o))}}(t,e,r,o,a),t){case"input":rt(e),ht(e,n,!1);break;case"textarea":rt(e),Ot(e);break;case"option":!function(e,t){null!=t.value&&e.setAttribute("value",Ke(Je(t.value)))}(e,n);break;case"select":!function(e,t){var n=e;n.multiple=!!t.multiple;var r=t.value;null!=r?kt(n,!!t.multiple,r,!1):null!=t.defaultValue&&kt(n,!!t.multiple,t.defaultValue,!0)}(e,n);break;default:"function"==typeof o.onClick&&go(e)}}function vo(e,t,n,r,o){eo(t,r);var a,i,l,u,c=null;switch(t){case"input":a=ct(e,n),i=ct(e,r),c=[];break;case"option":a=Ft(0,n),i=Ft(0,r),c=[];break;case"select":a=Et(0,n),i=Et(0,r),c=[];break;case"textarea":a=St(e,n),i=St(e,r),c=[];break;default:i=r,"function"!=typeof(a=n).onClick&&"function"==typeof i.onClick&&go(e)}Ar(t,i);var d=null;for(l in a)if(!i.hasOwnProperty(l)&&a.hasOwnProperty(l)&&null!=a[l])if("style"===l){var f=a[l];for(u in f)f.hasOwnProperty(u)&&(d||(d={}),d[u]="")}else"dangerouslySetInnerHTML"===l||"children"===l||"suppressContentEditableWarning"===l||"suppressHydrationWarning"===l||"autoFocus"===l||(I.hasOwnProperty(l)?c||(c=[]):(c=c||[]).push(l,null));for(l in i){var p=i[l],h=null!=a?a[l]:void 0;if(i.hasOwnProperty(l)&&p!==h&&(null!=p||null!=h))if("style"===l)if(p&&Object.freeze(p),h){for(u in h)!h.hasOwnProperty(u)||p&&p.hasOwnProperty(u)||(d||(d={}),d[u]="");for(u in p)p.hasOwnProperty(u)&&h[u]!==p[u]&&(d||(d={}),d[u]=p[u])}else d||(c||(c=[]),c.push(l,d)),d=p;else if("dangerouslySetInnerHTML"===l){var m=p?p.__html:void 0,g=h?h.__html:void 0;null!=m&&g!==m&&(c=c||[]).push(l,m)}else"children"===l?h===p||"string"!=typeof p&&"number"!=typeof p||(c=c||[]).push(l,""+p):"suppressContentEditableWarning"===l||"suppressHydrationWarning"===l||(I.hasOwnProperty(l)?(null!=p&&("function"!=typeof p&&oo(l,p),po(o,l)),c||h===p||(c=[])):(c=c||[]).push(l,p))}return d&&(!function(e,t){if(t){var n,r=_r(e),o=_r(t),a={};for(var i in r){var l=r[i],u=o[i];if(u&&l!==u){var c=l+","+u;if(a[c])continue;a[c]=!0,s("%s a style property during rerender (%s) when a conflicting property is set (%s) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.",null==(n=e[l])||"boolean"==typeof n||""===n?"Removing":"Updating",l,u)}}}}(d,i.style),(c=c||[]).push("style",d)),c}function bo(e,t,n,r,o){"input"===n&&"radio"===o.type&&null!=o.name&&ft(e,o);Ir(n,r);switch(function(e,t,n,r){for(var o=0;o<t.length;o+=2){var a=t[o],i=t[o+1];"style"===a?Dr(e,i):"dangerouslySetInnerHTML"===a?Bt(e,i):"children"===a?zt(e,i):ve(e,a,i,r)}}(e,t,0,Ir(n,o)),n){case"input":pt(e,o);break;case"textarea":_t(e,o);break;case"select":!function(e,t){var n=e,r=n._wrapperState.wasMultiple;n._wrapperState.wasMultiple=!!t.multiple;var o=t.value;null!=o?kt(n,!!t.multiple,o,!1):r!==!!t.multiple&&(null!=t.defaultValue?kt(n,!!t.multiple,t.defaultValue,!0):kt(n,!!t.multiple,t.multiple?[]:"",!1))}(e,o)}}function wo(e,t){to(e.nodeValue,t)}function Fo(e,t){uo||(uo=!0,s("Did not expect server HTML to contain a <%s> in <%s>.",t.nodeName.toLowerCase(),e.nodeName.toLowerCase()))}function Co(e,t){uo||(uo=!0,s('Did not expect server HTML to contain the text node "%s" in <%s>.',t.nodeValue,e.nodeName.toLowerCase()))}function To(e,t,n){uo||(uo=!0,s("Expected server HTML to contain a matching <%s> in <%s>.",t,e.nodeName.toLowerCase()))}function ko(e,t){""!==t&&(uo||(uo=!0,s('Expected server HTML to contain a matching text node for "%s" in <%s>.',t,e.nodeName.toLowerCase())))}function Eo(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function xo(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ro(e){for(;e;){if(e.nextSibling)return e.nextSibling;e=e.parentNode}}function So(e,t){for(var n=xo(e),r=0,o=0;n;){if(3===n.nodeType){if(o=r+n.textContent.length,r<=t&&o>=t)return{node:n,offset:t-r};r=o}n=xo(Ro(n))}}function Do(e){var t=e.ownerDocument,n=t&&t.defaultView||window,r=n.getSelection&&n.getSelection();if(!r||0===r.rangeCount)return null;var o=r.anchorNode,a=r.anchorOffset,i=r.focusNode,l=r.focusOffset;try{o.nodeType,i.nodeType}catch(e){return null}return function(e,t,n,r,o){var a=0,i=-1,l=-1,u=0,s=0,c=e,d=null;e:for(;;){for(var f=null;c!==t||0!==n&&3!==c.nodeType||(i=a+n),c!==r||0!==o&&3!==c.nodeType||(l=a+o),3===c.nodeType&&(a+=c.nodeValue.length),null!==(f=c.firstChild);)d=c,c=f;for(;;){if(c===e)break e;if(d===t&&++u===n&&(i=a),d===r&&++s===o&&(l=a),null!==(f=c.nextSibling))break;d=(c=d).parentNode}c=f}if(-1===i||-1===l)return null;return{start:i,end:l}}(e,o,a,i,l)}function _o(e){return e&&3===e.nodeType}function Oo(e){return e&&e.ownerDocument&&function e(t,n){return!(!t||!n)&&(t===n||!_o(t)&&(_o(n)?e(t,n.parentNode):"contains"in t?t.contains(n):!!t.compareDocumentPosition&&!!(16&t.compareDocumentPosition(n))))}(e.ownerDocument.documentElement,e)}function Po(e){try{return"string"==typeof e.contentWindow.location.href}catch(e){return!1}}function Ao(){for(var e=window,t=Eo();t instanceof e.HTMLIFrameElement;){if(!Po(t))return t;t=Eo((e=t.contentWindow).document)}return t}function Io(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function Wo(e){var t=Ao(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&Oo(n)){null!==r&&Io(n)&&function(e,t){var n=t.start,r=t.end;void 0===r&&(r=n);"selectionStart"in e?(e.selectionStart=n,e.selectionEnd=Math.min(r,e.value.length)):function(e,t){var n=e.ownerDocument||document,r=n&&n.defaultView||window;if(r.getSelection){var o=r.getSelection(),a=e.textContent.length,i=Math.min(t.start,a),l=void 0===t.end?i:Math.min(t.end,a);if(!o.extend&&i>l){var u=l;l=i,i=u}var s=So(e,i),c=So(e,l);if(s&&c){if(1===o.rangeCount&&o.anchorNode===s.node&&o.anchorOffset===s.offset&&o.focusNode===c.node&&o.focusOffset===c.offset)return;var d=n.createRange();d.setStart(s.node,s.offset),o.removeAllRanges(),i>l?(o.addRange(d),o.extend(c.node,c.offset)):(d.setEnd(c.node,c.offset),o.addRange(d))}}}(e,t)}(n,r);for(var o=[],a=n;a=a.parentNode;)1===a.nodeType&&o.push({element:a,left:a.scrollLeft,top:a.scrollTop});"function"==typeof n.focus&&n.focus();for(var i=0;i<o.length;i++){var l=o[i];l.element.scrollLeft=l.left,l.element.scrollTop=l.top}}}io=function(e){return("string"==typeof e?e:""+e).replace(co,"\n").replace(fo,"")},to=function(e,t){if(!uo){var n=io(t),r=io(e);r!==n&&(uo=!0,s('Text content did not match. Server: "%s" Client: "%s"',r,n))}},no=function(e,t,n){if(!uo){var r=io(n),o=io(t);o!==r&&(uo=!0,s("Prop `%s` did not match. Server: %s Client: %s",e,JSON.stringify(o),JSON.stringify(r)))}},ro=function(e){if(!uo){uo=!0;var t=[];e.forEach((function(e){t.push(e)})),s("Extra attributes from the server: %s",t)}},oo=function(e,t){!1===t?s("Expected `%s` listener to be a function, instead got `false`.\n\nIf you used to conditionally omit it with %s={condition && value}, pass %s={condition ? value : undefined} instead.",e,e,e):s("Expected `%s` listener to be a function, instead got a value of `%s` type.",e,typeof t)},lo=function(e,t){var n=e.namespaceURI===so?e.ownerDocument.createElement(e.tagName):e.ownerDocument.createElementNS(e.namespaceURI,e.tagName);return n.innerHTML=t,n.innerHTML};var No,Lo,jo=["address","applet","area","article","aside","base","basefont","bgsound","blockquote","body","br","button","caption","center","col","colgroup","dd","details","dir","div","dl","dt","embed","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","iframe","img","input","isindex","li","link","listing","main","marquee","menu","menuitem","meta","nav","noembed","noframes","noscript","object","ol","p","param","plaintext","pre","script","section","select","source","style","summary","table","tbody","td","template","textarea","tfoot","th","thead","title","tr","track","ul","wbr","xmp"],Mo=["applet","caption","html","table","td","th","marquee","object","template","foreignObject","desc","title"],Bo=Mo.concat(["button"]),zo=["dd","dt","li","option","optgroup","p","rp","rt"],Uo={current:null,formTag:null,aTagInScope:null,buttonTagInScope:null,nobrTagInScope:null,pTagInButtonScope:null,listItemTagAutoclosing:null,dlItemTagAutoclosing:null};Lo=function(e,t){var n=r({},e||Uo),o={tag:t};return-1!==Mo.indexOf(t)&&(n.aTagInScope=null,n.buttonTagInScope=null,n.nobrTagInScope=null),-1!==Bo.indexOf(t)&&(n.pTagInButtonScope=null),-1!==jo.indexOf(t)&&"address"!==t&&"div"!==t&&"p"!==t&&(n.listItemTagAutoclosing=null,n.dlItemTagAutoclosing=null),n.current=o,"form"===t&&(n.formTag=o),"a"===t&&(n.aTagInScope=o),"button"===t&&(n.buttonTagInScope=o),"nobr"===t&&(n.nobrTagInScope=o),"p"===t&&(n.pTagInButtonScope=o),"li"===t&&(n.listItemTagAutoclosing=o),"dd"!==t&&"dt"!==t||(n.dlItemTagAutoclosing=o),n};var Ho={};No=function(e,t,n){var r=(n=n||Uo).current,o=r&&r.tag;null!=t&&(null!=e&&s("validateDOMNesting: when childText is passed, childTag should be null"),e="#text");var a=function(e,t){switch(t){case"select":return"option"===e||"optgroup"===e||"#text"===e;case"optgroup":return"option"===e||"#text"===e;case"option":return"#text"===e;case"tr":return"th"===e||"td"===e||"style"===e||"script"===e||"template"===e;case"tbody":case"thead":case"tfoot":return"tr"===e||"style"===e||"script"===e||"template"===e;case"colgroup":return"col"===e||"template"===e;case"table":return"caption"===e||"colgroup"===e||"tbody"===e||"tfoot"===e||"thead"===e||"style"===e||"script"===e||"template"===e;case"head":return"base"===e||"basefont"===e||"bgsound"===e||"link"===e||"meta"===e||"title"===e||"noscript"===e||"noframes"===e||"style"===e||"script"===e||"template"===e;case"html":return"head"===e||"body"===e||"frameset"===e;case"frameset":return"frame"===e;case"#document":return"html"===e}switch(e){case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":return"h1"!==t&&"h2"!==t&&"h3"!==t&&"h4"!==t&&"h5"!==t&&"h6"!==t;case"rp":case"rt":return-1===zo.indexOf(t);case"body":case"caption":case"col":case"colgroup":case"frameset":case"frame":case"head":case"html":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return null==t}return!0}(e,o)?null:r,i=a?null:function(e,t){switch(e){case"address":case"article":case"aside":case"blockquote":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"main":case"menu":case"nav":case"ol":case"p":case"section":case"summary":case"ul":case"pre":case"listing":case"table":case"hr":case"xmp":case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":return t.pTagInButtonScope;case"form":return t.formTag||t.pTagInButtonScope;case"li":return t.listItemTagAutoclosing;case"dd":case"dt":return t.dlItemTagAutoclosing;case"button":return t.buttonTagInScope;case"a":return t.aTagInScope;case"nobr":return t.nobrTagInScope}return null}(e,n),l=a||i;if(l){var u=l.tag,c=!!a+"|"+e+"|"+u+"|"+$e();if(!Ho[c]){Ho[c]=!0;var d=e,f="";if("#text"===e?/\S/.test(t)?d="Text nodes":(d="Whitespace text nodes",f=" Make sure you don't have any extra whitespace between tags on each line of your source code."):d="<"+e+">",a){var p="";"table"===u&&"tr"===e&&(p+=" Add a <tbody>, <thead> or <tfoot> to your code to match the DOM tree generated by the browser."),s("validateDOMNesting(...): %s cannot appear as a child of <%s>.%s%s",d,u,f,p)}else s("validateDOMNesting(...): %s cannot appear as a descendant of <%s>.",d,u)}}};var Vo=null,$o=null;function qo(e,t){switch(e){case"button":case"input":case"select":case"textarea":return!!t.autoFocus}return!1}function Qo(e){var t,n;Vo=rr,n=Ao(),$o={activeElementDetached:null,focusedElem:n,selectionRange:Io(n)?(t=n,("selectionStart"in t?{start:t.selectionStart,end:t.selectionEnd}:Do(t))||{start:0,end:0}):null},or(!1)}function Yo(e,t,n,r,o){var a=r;if(No(e,null,a.ancestorInfo),"string"==typeof t.children||"number"==typeof t.children){var i=""+t.children,l=Lo(a.ancestorInfo,e);No(null,i,l)}var u=function(e,t,n,r){var o,a,i=ho(n),l=r;if(l===so&&(l=Nt(e)),l===so){if((o=Ir(e,t))||e===e.toLowerCase()||s("<%s /> is using incorrect casing. Use PascalCase for React components, or lowercase for HTML elements.",e),"script"===e){var u=i.createElement("div");u.innerHTML="<script><\/script>";var c=u.firstChild;a=u.removeChild(c)}else if("string"==typeof t.is)a=i.createElement(e,{is:t.is});else if(a=i.createElement(e),"select"===e){var d=a;t.multiple?d.multiple=!0:t.size&&(d.size=t.size)}}else a=i.createElementNS(l,e);return l===so&&(o||"[object HTMLUnknownElement]"!==Object.prototype.toString.call(a)||Object.prototype.hasOwnProperty.call(Gr,e)||(Gr[e]=!0,s("The tag <%s> is unrecognized in this browser. If you meant to render a React component, start its name with an uppercase letter.",e))),a}(e,t,n,a.namespace);return ma(o,u),Ca(u,t),u}function Ko(e,t){return"textarea"===e||"option"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}function Jo(e,t){return!!t.hidden}function Xo(e,t,n,r){No(null,e,n.ancestorInfo);var o=function(e,t){return ho(t).createTextNode(e)}(e,t);return ma(r,o),o}var Go="function"==typeof setTimeout?setTimeout:void 0,Zo="function"==typeof clearTimeout?clearTimeout:void 0;function ea(e){zt(e,"")}function ta(e,t){e.removeChild(t)}function na(e){var t=(e=e).style;"function"==typeof t.setProperty?t.setProperty("display","none","important"):t.display="none"}function ra(e,t){e=e;var n=t.style,r=null!=n&&n.hasOwnProperty("display")?n.display:null;e.style.display=mr("display",r)}function oa(e,t){e.nodeValue=t}function aa(e){return"$!"===e.data}function ia(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break}return e}function la(e){return ia(e.nextSibling)}function ua(e){return ia(e.firstChild)}function sa(e,t,n,r,o,a){return ma(a,e),Ca(e,n),function(e,t,n,r,o){var a,i;switch(Zr=!0===n.suppressHydrationWarning,a=Ir(t,n),eo(t,n),t){case"iframe":case"object":case"embed":ar("load",e);break;case"video":case"audio":for(var l=0;l<Xt.length;l++)ar(Xt[l],e);break;case"source":ar("error",e);break;case"img":case"image":case"link":ar("error",e),ar("load",e);break;case"form":ar("reset",e),ar("submit",e);break;case"details":ar("toggle",e);break;case"input":dt(e,n),ar("invalid",e),po(o,"onChange");break;case"option":wt(0,n);break;case"select":xt(e,n),ar("invalid",e),po(o,"onChange");break;case"textarea":Dt(e,n),ar("invalid",e),po(o,"onChange")}Ar(t,n),i=new Set;for(var u=e.attributes,s=0;s<u.length;s++){switch(u[s].name.toLowerCase()){case"data-reactroot":case"value":case"checked":case"selected":break;default:i.add(u[s].name)}}var c,d=null;for(var f in n)if(n.hasOwnProperty(f)){var p=n[f];if("children"===f)"string"==typeof p?e.textContent!==p&&(Zr||to(e.textContent,p),d=["children",p]):"number"==typeof p&&e.textContent!==""+p&&(Zr||to(e.textContent,p),d=["children",""+p]);else if(I.hasOwnProperty(f))null!=p&&("function"!=typeof p&&oo(f,p),po(o,f));else if("boolean"==typeof a){var h=void 0,m=ue(f);if(Zr);else if("suppressContentEditableWarning"===f||"suppressHydrationWarning"===f||"value"===f||"checked"===f||"selected"===f);else if("dangerouslySetInnerHTML"===f){var g=e.innerHTML,y=p?p.__html:void 0,v=lo(e,null!=y?y:"");v!==g&&no(f,g,v)}else if("style"===f){if(i.delete(f),ao){var b=Sr(p);b!==(h=e.getAttribute("style"))&&no(f,h,b)}}else if(a)i.delete(f.toLowerCase()),p!==(h=ye(e,f,p))&&no(f,h,p);else if(!ae(f,m,a)&&!le(f,p,m,a)){var w=!1;if(null!==m)i.delete(m.attributeName),h=ge(e,f,p,m);else{var F=r;if(F===so&&(F=Nt(t)),F===so)i.delete(f.toLowerCase());else{var C=(c=void 0,c=f.toLowerCase(),Wr.hasOwnProperty(c)&&Wr[c]||null);null!==C&&C!==f&&(w=!0,i.delete(C)),i.delete(f)}h=ye(e,f,p)}p===h||w||no(f,h,p)}}}switch(i.size>0&&!Zr&&ro(i),t){case"input":rt(e),ht(e,n,!0);break;case"textarea":rt(e),Ot(e);break;case"select":case"option":break;default:"function"==typeof n.onClick&&go(e)}return d}(e,t,n,o.namespace,r)}function ca(e){for(var t=e.previousSibling,n=0;t;){if(8===t.nodeType){var r=t.data;if("$"===r||"$!"===r||"$?"===r){if(0===n)return t;n--}else"/$"===r&&n++}t=t.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactInternalInstance$"+da,pa="__reactEventHandlers$"+da,ha="__reactContainere$"+da;function ma(e,t){t[fa]=e}function ga(e){e[ha]=null}function ya(e){return!!e[ha]}function va(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ha]||n[fa]){var r=t.alternate;if(null!==t.child||null!==r&&null!==r.child)for(var o=ca(e);null!==o;){var a=o[fa];if(a)return a;o=ca(o)}return t}n=(e=n).parentNode}return null}function ba(e){var t=e[fa]||e[ha];return t&&(5===t.tag||6===t.tag||13===t.tag||3===t.tag)?t:null}function wa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error("getNodeFromInstance: Invalid argument.")}function Fa(e){return e[pa]||null}function Ca(e,t){e[pa]=t}function Ta(e){do{e=e.return}while(e&&5!==e.tag);return e||null}function ka(e,t,n,r,o){for(var a=e&&t?function(e,t){for(var n=0,r=e;r;r=Ta(r))n++;for(var o=0,a=t;a;a=Ta(a))o++;for(;n-o>0;)e=Ta(e),n--;for(;o-n>0;)t=Ta(t),o--;for(var i=n;i--;){if(e===t||e===t.alternate)return e;e=Ta(e),t=Ta(t)}return null}(e,t):null,i=[];e&&e!==a;){var l=e.alternate;if(null!==l&&l===a)break;i.push(e),e=Ta(e)}for(var u=[];t&&t!==a;){var s=t.alternate;if(null!==s&&s===a)break;u.push(t),t=Ta(t)}for(var c=0;c<i.length;c++)n(i[c],"bubbled",r);for(var d=u.length;d-- >0;)n(u[d],"captured",o)}function Ea(e,t){var n,r=e.stateNode;if(!r)return null;var o=T(r);if(!o)return null;if(n=o[t],function(e,t,n){switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":return!(!n.disabled||(r=t,"button"!==r&&"input"!==r&&"select"!==r&&"textarea"!==r));default:return!1}var r}(t,e.type,o))return null;if(n&&"function"!=typeof n)throw Error("Expected `"+t+"` listener to be a function, instead got a value of `"+typeof n+"` type.");return n}function xa(e,t,n){e||s("Dispatching inst must not be null");var r=function(e,t,n){return Ea(e,t.dispatchConfig.phasedRegistrationNames[n])}(e,n,t);r&&(n._dispatchListeners=sn(n._dispatchListeners,r),n._dispatchInstances=sn(n._dispatchInstances,e))}function Ra(e){e&&e.dispatchConfig.phasedRegistrationNames&&function(e,t,n){for(var r,o=[];e;)o.push(e),e=Ta(e);for(r=o.length;r-- >0;)t(o[r],"captured",n);for(r=0;r<o.length;r++)t(o[r],"bubbled",n)}(e._targetInst,xa,e)}function Sa(e,t,n){if(e&&n&&n.dispatchConfig.registrationName){var r=Ea(e,n.dispatchConfig.registrationName);r&&(n._dispatchListeners=sn(n._dispatchListeners,r),n._dispatchInstances=sn(n._dispatchInstances,e))}}function Da(e){e&&e.dispatchConfig.registrationName&&Sa(e._targetInst,0,e)}function _a(e){cn(e,Ra)}var Oa=null,Pa=null,Aa=null;function Ia(){if(Aa)return Aa;var e,t,n=Pa,r=n.length,o=Wa(),a=o.length;for(e=0;e<r&&n[e]===o[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===o[a-t];t++);var l=t>1?1-t:void 0;return Aa=o.slice(e,l)}function Wa(){return"value"in Oa?Oa.value:Oa.textContent}var Na={type:null,target:null,currentTarget:function(){return null},eventPhase:null,bubbles:null,cancelable:null,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:null,isTrusted:null};function La(){return!0}function ja(){return!1}function Ma(e,t,n,r){delete this.nativeEvent,delete this.preventDefault,delete this.stopPropagation,delete this.isDefaultPrevented,delete this.isPropagationStopped,this.dispatchConfig=e,this._targetInst=t,this.nativeEvent=n;var o=this.constructor.Interface;for(var a in o)if(o.hasOwnProperty(a)){delete this[a];var i=o[a];i?this[a]=i(n):"target"===a?this.target=r:this[a]=n[a]}var l=null!=n.defaultPrevented?n.defaultPrevented:!1===n.returnValue;return this.isDefaultPrevented=l?La:ja,this.isPropagationStopped=ja,this}function Ba(e,t){var n="function"==typeof t;return{configurable:!0,set:function(e){return r(n?"setting the method":"setting the property","This is effectively a no-op"),e},get:function(){return r(n?"accessing the method":"accessing the property",n?"This is a no-op function":"This is set to null"),t}};function r(t,n){s("This synthetic event is reused for performance reasons. If you're seeing this, you're %s `%s` on a released/nullified synthetic event. %s. If you must keep the original synthetic event around, use event.persist(). See https://fb.me/react-event-pooling for more information.",t,e,n)}}function za(e,t,n,r){if(this.eventPool.length){var o=this.eventPool.pop();return this.call(o,e,t,n,r),o}return new this(e,t,n,r)}function Ua(e){if(!(e instanceof this))throw Error("Trying to release an event instance into a pool of a different type.");e.destructor(),this.eventPool.length<10&&this.eventPool.push(e)}function Ha(e){e.eventPool=[],e.getPooled=za,e.release=Ua}r(Ma.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=La)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=La)},persist:function(){this.isPersistent=La},isPersistent:ja,destructor:function(){var e=this.constructor.Interface;for(var t in e)Object.defineProperty(this,t,Ba(t,e[t]));this.dispatchConfig=null,this._targetInst=null,this.nativeEvent=null,this.isDefaultPrevented=ja,this.isPropagationStopped=ja,this._dispatchListeners=null,this._dispatchInstances=null,Object.defineProperty(this,"nativeEvent",Ba("nativeEvent",null)),Object.defineProperty(this,"isDefaultPrevented",Ba("isDefaultPrevented",ja)),Object.defineProperty(this,"isPropagationStopped",Ba("isPropagationStopped",ja)),Object.defineProperty(this,"preventDefault",Ba("preventDefault",(function(){}))),Object.defineProperty(this,"stopPropagation",Ba("stopPropagation",(function(){})))}}),Ma.Interface=Na,Ma.extend=function(e){var t=this,n=function(){};n.prototype=t.prototype;var o=new n;function a(){return t.apply(this,arguments)}return r(o,a.prototype),a.prototype=o,a.prototype.constructor=a,a.Interface=r({},t.Interface,e),a.extend=t.extend,Ha(a),a},Ha(Ma);var Va=Ma.extend({data:null}),$a=Ma.extend({data:null}),qa=[9,13,27,32],Qa=j&&"CompositionEvent"in window,Ya=null;j&&"documentMode"in document&&(Ya=document.documentMode);var Ka=j&&"TextEvent"in window&&!Ya,Ja=j&&(!Qa||Ya&&Ya>8&&Ya<=11),Xa=String.fromCharCode(32),Ga={beforeInput:{phasedRegistrationNames:{bubbled:"onBeforeInput",captured:"onBeforeInputCapture"},dependencies:["compositionend","keypress","textInput","paste"]},compositionEnd:{phasedRegistrationNames:{bubbled:"onCompositionEnd",captured:"onCompositionEndCapture"},dependencies:["blur","compositionend","keydown","keypress","keyup","mousedown"]},compositionStart:{phasedRegistrationNames:{bubbled:"onCompositionStart",captured:"onCompositionStartCapture"},dependencies:["blur","compositionstart","keydown","keypress","keyup","mousedown"]},compositionUpdate:{phasedRegistrationNames:{bubbled:"onCompositionUpdate",captured:"onCompositionUpdateCapture"},dependencies:["blur","compositionupdate","keydown","keypress","keyup","mousedown"]}},Za=!1;function ei(e,t){switch(e){case"keyup":return-1!==qa.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"blur":return!0;default:return!1}}function ti(e){var t=e.detail;return"object"==typeof t&&"data"in t?t.data:null}function ni(e){return"ko"===e.locale}var ri=!1;function oi(e,t,n,r){var o,a;if(Qa?o=function(e){switch(e){case"compositionstart":return Ga.compositionStart;case"compositionend":return Ga.compositionEnd;case"compositionupdate":return Ga.compositionUpdate}}(e):ri?ei(e,n)&&(o=Ga.compositionEnd):function(e,t){return"keydown"===e&&229===t.keyCode}(e,n)&&(o=Ga.compositionStart),!o)return null;Ja&&!ni(n)&&(ri||o!==Ga.compositionStart?o===Ga.compositionEnd&&ri&&(a=Ia()):ri=function(e){return Oa=e,Pa=Wa(),!0}(r));var i=Va.getPooled(o,t,n,r);if(a)i.data=a;else{var l=ti(n);null!==l&&(i.data=l)}return _a(i),i}function ai(e,t){if(ri){if("compositionend"===e||!Qa&&ei(e,t)){var n=Ia();return Oa=null,Pa=null,Aa=null,ri=!1,n}return null}switch(e){case"paste":return null;case"keypress":if(!function(e){return(e.ctrlKey||e.altKey||e.metaKey)&&!(e.ctrlKey&&e.altKey)}(t)){if(t.char&&t.char.length>1)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ja&&!ni(t)?null:t.data;default:return null}}function ii(e,t,n,r){var o;if(!(o=Ka?function(e,t){switch(e){case"compositionend":return ti(t);case"keypress":return 32!==t.which?null:(Za=!0,Xa);case"textInput":var n=t.data;return n===Xa&&Za?null:n;default:return null}}(e,n):ai(e,n)))return null;var a=$a.getPooled(Ga.beforeInput,t,n,r);return a.data=o,_a(a),a}var li={eventTypes:Ga,extractEvents:function(e,t,n,r,o){var a=oi(e,t,n,r),i=ii(e,t,n,r);return null===a?i:null===i?a:[a,i]}},ui={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function si(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!ui[e.type]:"textarea"===t}var ci={change:{phasedRegistrationNames:{bubbled:"onChange",captured:"onChangeCapture"},dependencies:["blur","change","click","focus","input","keydown","keyup","selectionchange"]}};function di(e,t,n){var r=Ma.getPooled(ci.change,e,t,n);return r.type="change",H(n),_a(r),r}var fi=null,pi=null;function hi(e){!function(e,t){if(K)return e(t);K=!0;try{$(e,t)}finally{K=!1,X()}}(mi,di(pi,e,mn(e)))}function mi(e){hn(e)}function gi(e){if(ot(wa(e)))return e}function yi(e,t){if("change"===e)return t}var vi=!1;function bi(){fi&&(fi.detachEvent("onpropertychange",wi),fi=null,pi=null)}function wi(e){"value"===e.propertyName&&gi(pi)&&hi(e)}function Fi(e,t,n){"focus"===e?(bi(),function(e,t){pi=t,(fi=e).attachEvent("onpropertychange",wi)}(t,n)):"blur"===e&&bi()}function Ci(e,t){if("selectionchange"===e||"keyup"===e||"keydown"===e)return gi(pi)}function Ti(e,t){if("click"===e)return gi(t)}function ki(e,t){if("input"===e||"change"===e)return gi(t)}j&&(vi=gn("input")&&(!document.documentMode||document.documentMode>9));var Ei={eventTypes:ci,_isInputEventSupported:vi,extractEvents:function(e,t,n,r,o){var a,i,l,u,s,c,d=t?wa(t):window;if("select"===(u=(l=d).nodeName&&l.nodeName.toLowerCase())||"input"===u&&"file"===l.type?a=yi:si(d)?vi?a=ki:(a=Ci,i=Fi):function(e){var t=e.nodeName;return t&&"input"===t.toLowerCase()&&("checkbox"===e.type||"radio"===e.type)}(d)&&(a=Ti),a){var f=a(e,t);if(f)return di(f,n,r)}i&&i(e,d,t),"blur"===e&&(c=(s=d)._wrapperState)&&c.controlled&&"number"===s.type&&gt(s,"number",s.value)}},xi=Ma.extend({view:null,detail:null}),Ri={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Si(e){var t=this.nativeEvent;if(t.getModifierState)return t.getModifierState(e);var n=Ri[e];return!!n&&!!t[n]}function Di(e){return Si}var _i=0,Oi=0,Pi=!1,Ai=!1,Ii=xi.extend({screenX:null,screenY:null,clientX:null,clientY:null,pageX:null,pageY:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,getModifierState:Di,button:null,buttons:null,relatedTarget:function(e){return e.relatedTarget||(e.fromElement===e.srcElement?e.toElement:e.fromElement)},movementX:function(e){if("movementX"in e)return e.movementX;var t=_i;return _i=e.screenX,Pi?"mousemove"===e.type?e.screenX-t:0:(Pi=!0,0)},movementY:function(e){if("movementY"in e)return e.movementY;var t=Oi;return Oi=e.screenY,Ai?"mousemove"===e.type?e.screenY-t:0:(Ai=!0,0)}}),Wi=Ii.extend({pointerId:null,width:null,height:null,pressure:null,tangentialPressure:null,tiltX:null,tiltY:null,twist:null,pointerType:null,isPrimary:null}),Ni={mouseEnter:{registrationName:"onMouseEnter",dependencies:["mouseout","mouseover"]},mouseLeave:{registrationName:"onMouseLeave",dependencies:["mouseout","mouseover"]},pointerEnter:{registrationName:"onPointerEnter",dependencies:["pointerout","pointerover"]},pointerLeave:{registrationName:"onPointerLeave",dependencies:["pointerout","pointerover"]}},Li={eventTypes:Ni,extractEvents:function(e,t,n,r,o){var a,i,l,u,s,c,d,f="mouseover"===e||"pointerover"===e,p="mouseout"===e||"pointerout"===e;if(f&&0==(32&o)&&(n.relatedTarget||n.fromElement))return null;if(!p&&!f)return null;if(r.window===r)a=r;else{var h=r.ownerDocument;a=h?h.defaultView||h.parentWindow:window}if(p){i=t;var m=n.relatedTarget||n.toElement;if(null!==(l=m?va(m):null))(l!==nn(l)||5!==l.tag&&6!==l.tag)&&(l=null)}else i=null,l=t;if(i===l)return null;"mouseout"===e||"mouseover"===e?(u=Ii,s=Ni.mouseLeave,c=Ni.mouseEnter,d="mouse"):"pointerout"!==e&&"pointerover"!==e||(u=Wi,s=Ni.pointerLeave,c=Ni.pointerEnter,d="pointer");var g=null==i?a:wa(i),y=null==l?a:wa(l),v=u.getPooled(s,i,n,r);v.type=d+"leave",v.target=g,v.relatedTarget=y;var b=u.getPooled(c,l,n,r);return b.type=d+"enter",b.target=y,b.relatedTarget=g,function(e,t,n,r){ka(n,r,Sa,e,t)}(v,b,i,l),0==(64&o)?[v]:[v,b]}};var ji="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},Mi=Object.prototype.hasOwnProperty;function Bi(e,t){if(ji(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(var o=0;o<n.length;o++)if(!Mi.call(t,n[o])||!ji(e[n[o]],t[n[o]]))return!1;return!0}var zi=j&&"documentMode"in document&&document.documentMode<=11,Ui={select:{phasedRegistrationNames:{bubbled:"onSelect",captured:"onSelectCapture"},dependencies:["blur","contextmenu","dragend","focus","keydown","keyup","mousedown","mouseup","selectionchange"]}},Hi=null,Vi=null,$i=null,qi=!1;function Qi(e){return e.window===e?e.document:9===e.nodeType?e:e.ownerDocument}function Yi(e,t){var n=Qi(t);if(qi||null==Hi||Hi!==Eo(n))return null;var r=function(e){if("selectionStart"in e&&Io(e))return{start:e.selectionStart,end:e.selectionEnd};var t=(e.ownerDocument&&e.ownerDocument.defaultView||window).getSelection();return{anchorNode:t.anchorNode,anchorOffset:t.anchorOffset,focusNode:t.focusNode,focusOffset:t.focusOffset}}(Hi);if(!$i||!Bi($i,r)){$i=r;var o=Ma.getPooled(Ui.select,Vi,e,t);return o.type="select",o.target=Hi,_a(o),o}return null}var Ki={eventTypes:Ui,extractEvents:function(e,t,n,r,o,a){var i=a||Qi(r);if(!i||!function(e,t){for(var n=Zt(t),r=W[e],o=0;o<r.length;o++){var a=r[o];if(!n.has(a))return!1}return!0}("onSelect",i))return null;var l=t?wa(t):window;switch(e){case"focus":(si(l)||"true"===l.contentEditable)&&(Hi=l,Vi=t,$i=null);break;case"blur":Hi=null,Vi=null,$i=null;break;case"mousedown":qi=!0;break;case"contextmenu":case"mouseup":case"dragend":return qi=!1,Yi(n,r);case"selectionchange":if(zi)break;case"keydown":case"keyup":return Yi(n,r)}return null}},Ji=Ma.extend({animationName:null,elapsedTime:null,pseudoElement:null}),Xi=Ma.extend({clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Gi=xi.extend({relatedTarget:null});function Zi(e){var t,n=e.keyCode;return"charCode"in e?0===(t=e.charCode)&&13===n&&(t=13):t=n,10===t&&(t=13),t>=32||13===t?t:0}var el={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},tl={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"};var nl=xi.extend({key:function(e){if(e.key){var t=el[e.key]||e.key;if("Unidentified"!==t)return t}if("keypress"===e.type){var n=Zi(e);return 13===n?"Enter":String.fromCharCode(n)}return"keydown"===e.type||"keyup"===e.type?tl[e.keyCode]||"Unidentified":""},location:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,repeat:null,locale:null,getModifierState:Di,charCode:function(e){return"keypress"===e.type?Zi(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Zi(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),rl=Ii.extend({dataTransfer:null}),ol=xi.extend({touches:null,targetTouches:null,changedTouches:null,altKey:null,metaKey:null,ctrlKey:null,shiftKey:null,getModifierState:Di}),al=Ma.extend({propertyName:null,elapsedTime:null,pseudoElement:null}),il=Ii.extend({deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:null,deltaMode:null}),ll=["abort","cancel","canplay","canplaythrough","close","durationchange","emptied","encrypted","ended","error","input","invalid","load","loadeddata","loadedmetadata","loadstart","pause","play","playing","progress","ratechange","reset","seeked","seeking","stalled","submit","suspend","timeupdate","toggle","volumechange","waiting"],ul={eventTypes:Yn,extractEvents:function(e,t,n,r,o){var a,i=Kn.get(e);if(!i)return null;switch(e){case"keypress":if(0===Zi(n))return null;case"keydown":case"keyup":a=nl;break;case"blur":case"focus":a=Gi;break;case"click":if(2===n.button)return null;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":a=Ii;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":a=rl;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":a=ol;break;case Qt:case Yt:case Kt:a=Ji;break;case Jt:a=al;break;case"scroll":a=xi;break;case"wheel":a=il;break;case"copy":case"cut":case"paste":a=Xi;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":a=Wi;break;default:-1===ll.indexOf(e)&&s("SimpleEventPlugin: Unhandled event type, `%s`. This warning is likely caused by a bug in React. Please file an issue.",e),a=Ma}var l=a.getPooled(i,t,n,r);return _a(l),l}};!function(e){if(R)throw Error("EventPluginRegistry: Cannot inject event plugin ordering more than once. You are likely trying to load more than one copy of React.");R=Array.prototype.slice.call(e),D()}(["ResponderEventPlugin","SimpleEventPlugin","EnterLeaveEventPlugin","ChangeEventPlugin","SelectEventPlugin","BeforeInputEventPlugin"]),T=Fa,k=ba,(E=wa)&&k||s("EventPluginUtils.setComponentTree(...): Injected module is missing getNodeFromInstance or getInstanceFromNode."),L({SimpleEventPlugin:ul,EnterLeaveEventPlugin:Li,ChangeEventPlugin:Ei,SelectEventPlugin:Ki,BeforeInputEventPlugin:li});var sl="undefined"!=typeof performance&&"function"==typeof performance.mark&&"function"==typeof performance.clearMarks&&"function"==typeof performance.measure&&"function"==typeof performance.clearMeasures,cl=null,dl=null,fl=null,pl=!1,hl=!1,ml=!1,gl=0,yl=0,vl=new Set,bl=function(e){return"⚛ "+e},wl=function(e){performance.mark(bl(e))},Fl=function(e,t,n){var r=bl(t),o=function(e,t){return""+(t?"⛔ ":"⚛ ")+e+(t?" Warning: "+t:"")}(e,n);try{performance.measure(o,r)}catch(e){}performance.clearMarks(r),performance.clearMeasures(o)},Cl=function(e,t){return e+" (#"+t+")"},Tl=function(e,t,n){return null===n?e+" ["+(t?"update":"mount")+"]":e+"."+n},kl=function(e,t){var n=je(e.type)||"Unknown",r=e._debugID,o=null!==e.alternate,a=Tl(n,o,t);if(pl&&vl.has(a))return!1;vl.add(a);var i=Cl(a,r);return wl(i),!0},El=function(e,t){var n=je(e.type)||"Unknown",r=e._debugID,o=null!==e.alternate,a=Tl(n,o,t);!function(e){performance.clearMarks(bl(e))}(Cl(a,r))},xl=function(e,t,n){var r=je(e.type)||"Unknown",o=e._debugID,a=null!==e.alternate,i=Tl(r,a,t),l=Cl(i,o);Fl(i,l,n)},Rl=function(e){switch(e.tag){case 3:case 5:case 6:case 4:case 7:case 10:case 9:case 8:return!0;default:return!1}},Sl=function(e){null!==e.return&&Sl(e.return),e._debugIsCurrentlyTiming&&kl(e,null)};function Dl(){yl++}function _l(e){sl&&!Rl(e)&&(cl=e,kl(e,null)&&(e._debugIsCurrentlyTiming=!0))}function Ol(e){sl&&!Rl(e)&&(e._debugIsCurrentlyTiming=!1,El(e,null))}function Pl(e){sl&&!Rl(e)&&(cl=e.return,e._debugIsCurrentlyTiming&&(e._debugIsCurrentlyTiming=!1,xl(e,null,null)))}function Al(e){if(sl&&!Rl(e)&&(cl=e.return,e._debugIsCurrentlyTiming)){e._debugIsCurrentlyTiming=!1;var t=13===e.tag?"Rendering was suspended":"An error was thrown inside this error boundary";xl(e,null,t)}}function Il(e,t){sl&&(null!==dl&&null!==fl&&El(fl,dl),fl=null,dl=null,ml=!1,kl(e,t)&&(fl=e,dl=t))}function Wl(){sl&&(null!==dl&&null!==fl&&xl(fl,dl,ml?"Scheduled a cascading update":null),dl=null,fl=null)}function Nl(e){cl=e,sl&&(gl=0,wl("(React Tree Reconciliation)"),null!==cl&&Sl(cl))}function Ll(e,t){if(sl){var n=null;null!==e?n=3===e.tag?"A top-level update interrupted the previous render":"An update to "+(je(e.type)||"Unknown")+" interrupted the previous render":gl>1&&(n="There were cascading updates"),gl=0;var r=t?"(React Tree Reconciliation: Completed Root)":"(React Tree Reconciliation: Yielded)";!function(){for(var e=cl;e;)e._debugIsCurrentlyTiming&&xl(e,null,null),e=e.return}(),Fl(r,"(React Tree Reconciliation)",n)}}function jl(){sl&&(yl=0,wl("(Committing Snapshot Effects)"))}function Ml(){if(sl){var e=yl;yl=0,Fl("(Committing Snapshot Effects: "+e+" Total)","(Committing Snapshot Effects)",null)}}function Bl(){sl&&(yl=0,wl("(Committing Host Effects)"))}function zl(){if(sl){var e=yl;yl=0,Fl("(Committing Host Effects: "+e+" Total)","(Committing Host Effects)",null)}}function Ul(){sl&&(yl=0,wl("(Calling Lifecycle Methods)"))}function Hl(){if(sl){var e=yl;yl=0,Fl("(Calling Lifecycle Methods: "+e+" Total)","(Calling Lifecycle Methods)",null)}}var Vl,$l=[];Vl=[];var ql,Ql=-1;function Yl(e){return{current:e}}function Kl(e,t){Ql<0?s("Unexpected pop."):(t!==Vl[Ql]&&s("Unexpected Fiber popped."),e.current=$l[Ql],$l[Ql]=null,Vl[Ql]=null,Ql--)}function Jl(e,t,n){Ql++,$l[Ql]=e.current,Vl[Ql]=n,e.current=t}ql={};var Xl={};Object.freeze(Xl);var Gl=Yl(Xl),Zl=Yl(!1),eu=Xl;function tu(e,t,n){return n&&au(t)?eu:Gl.current}function nu(e,t,n){var r=e.stateNode;r.__reactInternalMemoizedUnmaskedChildContext=t,r.__reactInternalMemoizedMaskedChildContext=n}function ru(e,t){var n=e.type,r=n.contextTypes;if(!r)return Xl;var o=e.stateNode;if(o&&o.__reactInternalMemoizedUnmaskedChildContext===t)return o.__reactInternalMemoizedMaskedChildContext;var i={};for(var l in r)i[l]=t[l];var u=je(n)||"Unknown";return a(r,i,"context",u,$e),o&&nu(e,t,i),i}function ou(){return Zl.current}function au(e){var t=e.childContextTypes;return null!=t}function iu(e){Kl(Zl,e),Kl(Gl,e)}function lu(e){Kl(Zl,e),Kl(Gl,e)}function uu(e,t,n){if(Gl.current!==Xl)throw Error("Unexpected context found on stack. This error is likely caused by a bug in React. Please file an issue.");Jl(Gl,t,e),Jl(Zl,n,e)}function su(e,t,n){var o,i=e.stateNode,l=t.childContextTypes;if("function"!=typeof i.getChildContext){var u=je(t)||"Unknown";return ql[u]||(ql[u]=!0,s("%s.childContextTypes is specified but there is no getChildContext() method on the instance. You can either define getChildContext() on %s or remove childContextTypes from it.",u,u)),n}for(var c in Il(e,"getChildContext"),o=i.getChildContext(),Wl(),o)if(!(c in l))throw Error((je(t)||"Unknown")+'.getChildContext(): key "'+c+'" is not defined in childContextTypes.');var d=je(t)||"Unknown";return a(l,o,"child context",d,$e),r({},n,{},o)}function cu(e){var t=e.stateNode,n=t&&t.__reactInternalMemoizedMergedChildContext||Xl;return eu=Gl.current,Jl(Gl,n,e),Jl(Zl,Zl.current,e),!0}function du(e,t,n){var r=e.stateNode;if(!r)throw Error("Expected to have an instance by this point. This error is likely caused by a bug in React. Please file an issue.");if(n){var o=su(e,t,eu);r.__reactInternalMemoizedMergedChildContext=o,Kl(Zl,e),Kl(Gl,e),Jl(Gl,o,e),Jl(Zl,n,e)}else Kl(Zl,e),Jl(Zl,n,e)}function fu(e){if(!function(e){return nn(e)===e}(e)||1!==e.tag)throw Error("Expected subtree parent to be a mounted class component. This error is likely caused by a bug in React. Please file an issue.");var t=e;do{switch(t.tag){case 3:return t.stateNode.context;case 1:if(au(t.type))return t.stateNode.__reactInternalMemoizedMergedChildContext}t=t.return}while(null!==t);throw Error("Found unexpected detached subtree parent. This error is likely caused by a bug in React. Please file an issue.")}var pu=o.unstable_runWithPriority,hu=o.unstable_scheduleCallback,mu=o.unstable_cancelCallback,gu=o.unstable_shouldYield,yu=o.unstable_requestPaint,vu=o.unstable_now,bu=o.unstable_getCurrentPriorityLevel,wu=o.unstable_ImmediatePriority,Fu=o.unstable_UserBlockingPriority,Cu=o.unstable_NormalPriority,Tu=o.unstable_LowPriority,ku=o.unstable_IdlePriority;if(null==i.__interactionsRef||null==i.__interactionsRef.current)throw Error("It is not supported to run the profiling version of a renderer (for example, `react-dom/profiling`) without also replacing the `scheduler/tracing` module with `scheduler/tracing-profiling`. Your bundler might have a setting for aliasing both modules. Learn more at http://fb.me/react-profiling");var Eu={},xu=gu,Ru=void 0!==yu?yu:function(){},Su=null,Du=null,_u=!1,Ou=vu(),Pu=Ou<1e4?vu:function(){return vu()-Ou};function Au(){switch(bu()){case wu:return 99;case Fu:return 98;case Cu:return 97;case Tu:return 96;case ku:return 95;default:throw Error("Unknown priority level.")}}function Iu(e){switch(e){case 99:return wu;case 98:return Fu;case 97:return Cu;case 96:return Tu;case 95:return ku;default:throw Error("Unknown priority level.")}}function Wu(e,t){var n=Iu(e);return pu(n,t)}function Nu(e,t,n){var r=Iu(e);return hu(r,t,n)}function Lu(e){return null===Su?(Su=[e],Du=hu(wu,Mu)):Su.push(e),Eu}function ju(){if(null!==Du){var e=Du;Du=null,mu(e)}Mu()}function Mu(){if(!_u&&null!==Su){_u=!0;var e=0;try{var t=Su;Wu(99,(function(){for(;e<t.length;e++){var n=t[e];do{n=n(!0)}while(null!==n)}})),Su=null}catch(t){throw null!==Su&&(Su=Su.slice(e+1)),hu(wu,ju),t}finally{_u=!1}}}var Bu=**********;function zu(e){return 1073741821-(e/10|0)}function Uu(e){return 10*(1073741821-e)}function Hu(e,t,n){return 1073741821-(1+((1073741821-e+t/10)/(r=n/10)|0))*r;var r}function Vu(e){return Hu(e,500,100)}function $u(e,t){if(t===Bu)return 99;if(1===t||2===t)return 95;var n=Uu(t)-Uu(e);return n<=0?99:n<=600?98:n<=5250?97:95}var qu={recordUnsafeLifecycleWarnings:function(e,t){},flushPendingUnsafeLifecycleWarnings:function(){},recordLegacyContextWarning:function(e,t){},flushLegacyContextWarning:function(){},discardPendingWarnings:function(){}},Qu=function(e){var t=[];return e.forEach((function(e){t.push(e)})),t.sort().join(", ")},Yu=[],Ku=[],Ju=[],Xu=[],Gu=[],Zu=[],es=new Set;qu.recordUnsafeLifecycleWarnings=function(e,t){es.has(e.type)||("function"==typeof t.componentWillMount&&!0!==t.componentWillMount.__suppressDeprecationWarning&&Yu.push(e),1&e.mode&&"function"==typeof t.UNSAFE_componentWillMount&&Ku.push(e),"function"==typeof t.componentWillReceiveProps&&!0!==t.componentWillReceiveProps.__suppressDeprecationWarning&&Ju.push(e),1&e.mode&&"function"==typeof t.UNSAFE_componentWillReceiveProps&&Xu.push(e),"function"==typeof t.componentWillUpdate&&!0!==t.componentWillUpdate.__suppressDeprecationWarning&&Gu.push(e),1&e.mode&&"function"==typeof t.UNSAFE_componentWillUpdate&&Zu.push(e))},qu.flushPendingUnsafeLifecycleWarnings=function(){var e=new Set;Yu.length>0&&(Yu.forEach((function(t){e.add(je(t.type)||"Component"),es.add(t.type)})),Yu=[]);var t=new Set;Ku.length>0&&(Ku.forEach((function(e){t.add(je(e.type)||"Component"),es.add(e.type)})),Ku=[]);var n=new Set;Ju.length>0&&(Ju.forEach((function(e){n.add(je(e.type)||"Component"),es.add(e.type)})),Ju=[]);var r=new Set;Xu.length>0&&(Xu.forEach((function(e){r.add(je(e.type)||"Component"),es.add(e.type)})),Xu=[]);var o=new Set;Gu.length>0&&(Gu.forEach((function(e){o.add(je(e.type)||"Component"),es.add(e.type)})),Gu=[]);var a=new Set;(Zu.length>0&&(Zu.forEach((function(e){a.add(je(e.type)||"Component"),es.add(e.type)})),Zu=[]),t.size>0)&&s("Using UNSAFE_componentWillMount in strict mode is not recommended and may indicate bugs in your code. See https://fb.me/react-unsafe-component-lifecycles for details.\n\n* Move code with side effects to componentDidMount, and set initial state in the constructor.\n\nPlease update the following components: %s",Qu(t));r.size>0&&s("Using UNSAFE_componentWillReceiveProps in strict mode is not recommended and may indicate bugs in your code. See https://fb.me/react-unsafe-component-lifecycles for details.\n\n* Move data fetching code or side effects to componentDidUpdate.\n* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://fb.me/react-derived-state\n\nPlease update the following components: %s",Qu(r));a.size>0&&s("Using UNSAFE_componentWillUpdate in strict mode is not recommended and may indicate bugs in your code. See https://fb.me/react-unsafe-component-lifecycles for details.\n\n* Move data fetching code or side effects to componentDidUpdate.\n\nPlease update the following components: %s",Qu(a));e.size>0&&u("componentWillMount has been renamed, and is not recommended for use. See https://fb.me/react-unsafe-component-lifecycles for details.\n\n* Move code with side effects to componentDidMount, and set initial state in the constructor.\n* Rename componentWillMount to UNSAFE_componentWillMount to suppress this warning in non-strict mode. In React 17.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run `npx react-codemod rename-unsafe-lifecycles` in your project source folder.\n\nPlease update the following components: %s",Qu(e));n.size>0&&u("componentWillReceiveProps has been renamed, and is not recommended for use. See https://fb.me/react-unsafe-component-lifecycles for details.\n\n* Move data fetching code or side effects to componentDidUpdate.\n* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://fb.me/react-derived-state\n* Rename componentWillReceiveProps to UNSAFE_componentWillReceiveProps to suppress this warning in non-strict mode. In React 17.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run `npx react-codemod rename-unsafe-lifecycles` in your project source folder.\n\nPlease update the following components: %s",Qu(n));o.size>0&&u("componentWillUpdate has been renamed, and is not recommended for use. See https://fb.me/react-unsafe-component-lifecycles for details.\n\n* Move data fetching code or side effects to componentDidUpdate.\n* Rename componentWillUpdate to UNSAFE_componentWillUpdate to suppress this warning in non-strict mode. In React 17.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run `npx react-codemod rename-unsafe-lifecycles` in your project source folder.\n\nPlease update the following components: %s",Qu(o))};var ts=new Map,ns=new Set;qu.recordLegacyContextWarning=function(e,t){var n=function(e){for(var t=null,n=e;null!==n;)1&n.mode&&(t=n),n=n.return;return t}(e);if(null!==n){if(!ns.has(e.type)){var r=ts.get(n);(null!=e.type.contextTypes||null!=e.type.childContextTypes||null!==t&&"function"==typeof t.getChildContext)&&(void 0===r&&(r=[],ts.set(n,r)),r.push(e))}}else s("Expected to find a StrictMode component in a strict mode tree. This error is likely caused by a bug in React. Please file an issue.")},qu.flushLegacyContextWarning=function(){ts.forEach((function(e,t){if(0!==e.length){var n=e[0],r=new Set;e.forEach((function(e){r.add(je(e.type)||"Component"),ns.add(e.type)})),s("Legacy context API has been detected within a strict-mode tree.\n\nThe old API will be supported in all 16.x releases, but applications using it should migrate to the new version.\n\nPlease update the following components: %s\n\nLearn more about this warning here: https://fb.me/react-legacy-context%s",Qu(r),ze(n))}}))},qu.discardPendingWarnings=function(){Yu=[],Ku=[],Ju=[],Xu=[],Gu=[],Zu=[],ts=new Map};var rs=null,os=null,as=function(e){rs=e};function is(e){if(null===rs)return e;var t=rs(e);return void 0===t?e:t.current}function ls(e){return is(e)}function us(e){if(null===rs)return e;var t=rs(e);if(void 0===t){if(null!=e&&"function"==typeof e.render){var n=is(e.render);if(e.render!==n){var r={$$typeof:De,render:n};return void 0!==e.displayName&&(r.displayName=e.displayName),r}}return e}return t.current}function ss(e,t){if(null===rs)return!1;var n=e.elementType,r=t.type,o=!1,a="object"==typeof r&&null!==r?r.$$typeof:null;switch(e.tag){case 1:"function"==typeof r&&(o=!0);break;case 0:("function"==typeof r||a===Ae)&&(o=!0);break;case 11:(a===De||a===Ae)&&(o=!0);break;case 14:case 15:(a===Pe||a===Ae)&&(o=!0);break;default:return!1}if(o){var i=rs(n);if(void 0!==i&&i===rs(r))return!0}return!1}function cs(e){null!==rs&&"function"==typeof WeakSet&&(null===os&&(os=new WeakSet),os.add(e))}var ds=function(e,t){if(null!==rs){var n=t.staleFamilies,r=t.updatedFamilies;Yh(),Sh((function(){!function e(t,n,r){var o=t.alternate,a=t.child,i=t.sibling,l=t.tag,u=t.type,s=null;switch(l){case 0:case 15:case 1:s=u;break;case 11:s=u.render}if(null===rs)throw new Error("Expected resolveFamily to be set during hot reload.");var c=!1,d=!1;if(null!==s){var f=rs(s);void 0!==f&&(r.has(f)?d=!0:n.has(f)&&(1===l?d=!0:c=!0))}null!==os&&(os.has(t)||null!==o&&os.has(o))&&(d=!0);d&&(t._debugNeedsRemount=!0);(d||c)&&wh(t,Bu);null===a||d||e(a,n,r);null!==i&&e(i,n,r)}(e.current,r,n)}))}},fs=function(e,t){var n,r,o;e.context===Xl&&(Yh(),Wu(99,function(){Km(t,e,null,null)}.bind(null,n,r,o)))};var ps=function(e,t){var n=new Set,r=new Set(t.map((function(e){return e.current})));return function e(t,n,r){var o=t.child,a=t.sibling,i=t.tag,l=t.type,u=null;switch(i){case 0:case 15:case 1:u=l;break;case 11:u=l.render}var s=!1;null!==u&&n.has(u)&&(s=!0);s?function(e,t){if(function(e,t){var n=e,r=!1;for(;;){if(5===n.tag)r=!0,t.add(n.stateNode);else if(null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)return r;for(;null===n.sibling;){if(null===n.return||n.return===e)return r;n=n.return}n.sibling.return=n.return,n=n.sibling}return!1}(e,t))return;var n=e;for(;;){switch(n.tag){case 5:return void t.add(n.stateNode);case 4:case 3:return void t.add(n.stateNode.containerInfo)}if(null===n.return)throw new Error("Expected to reach root first.");n=n.return}}(t,r):null!==o&&e(o,n,r);null!==a&&e(a,n,r)}(e.current,r,n),n};function hs(e,t){if(e&&e.defaultProps){var n=r({},t),o=e.defaultProps;for(var a in o)void 0===n[a]&&(n[a]=o[a]);return n}return t}function ms(e){if(function(e){if(-1===e._status){e._status=0;var t=(0,e._ctor)();e._result=t,t.then((function(t){if(0===e._status){var n=t.default;void 0===n&&s("lazy: Expected the result of a dynamic import() call. Instead received: %s\n\nYour code should look like: \n  const MyComponent = lazy(() => import('./MyComponent'))",t),e._status=1,e._result=n}}),(function(t){0===e._status&&(e._status=2,e._result=t)}))}}(e),1!==e._status)throw e._result;return e._result}var gs,ys=Yl(null);gs={};var vs=null,bs=null,ws=null,Fs=!1;function Cs(){vs=null,bs=null,ws=null,Fs=!1}function Ts(){Fs=!0}function ks(){Fs=!1}function Es(e,t){var n=e.type._context;Jl(ys,n._currentValue,e),n._currentValue=t,void 0!==n._currentRenderer&&null!==n._currentRenderer&&n._currentRenderer!==gs&&s("Detected multiple renderers concurrently rendering the same context provider. This is currently unsupported."),n._currentRenderer=gs}function xs(e){var t=ys.current;Kl(ys,e),e.type._context._currentValue=t}function Rs(e,t){for(var n=e;null!==n;){var r=n.alternate;if(n.childExpirationTime<t)n.childExpirationTime=t,null!==r&&r.childExpirationTime<t&&(r.childExpirationTime=t);else{if(!(null!==r&&r.childExpirationTime<t))break;r.childExpirationTime=t}n=n.return}}function Ss(e,t){vs=e,bs=null,ws=null;var n=e.dependencies;null!==n&&(null!==n.firstContext&&(n.expirationTime>=t&&op(),n.firstContext=null))}function Ds(e,t){if(Fs&&s("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo()."),ws===e);else if(!1===t||0===t);else{var n;"number"!=typeof t||**********===t?(ws=e,n=**********):n=t;var r={context:e,observedBits:n,next:null};if(null===bs){if(null===vs)throw Error("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().");bs=r,vs.dependencies={expirationTime:0,firstContext:r,responders:null}}else bs=bs.next=r}return e._currentValue}var _s,Os,Ps=2,As=!1;function Is(e){var t={baseState:e.memoizedState,baseQueue:null,shared:{pending:null},effects:null};e.updateQueue=t}function Ws(e,t){var n=t.updateQueue,r=e.updateQueue;if(n===r){var o={baseState:r.baseState,baseQueue:r.baseQueue,shared:r.shared,effects:r.effects};t.updateQueue=o}}function Ns(e,t){var n={expirationTime:e,suspenseConfig:t,tag:0,payload:null,callback:null,next:null};return n.next=n,n.priority=Au(),n}function Ls(e,t){var n=e.updateQueue;if(null!==n){var r=n.shared,o=r.pending;null===o?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Os!==r||_s||(s("An update (setState, replaceState, or forceUpdate) was scheduled from inside an update function. Update functions should be pure, with zero side-effects. Consider using componentDidUpdate or a callback."),_s=!0)}}function js(e,t){var n=e.alternate;null!==n&&Ws(n,e);var r=e.updateQueue,o=r.baseQueue;null===o?(r.baseQueue=t.next=t,t.next=t):(t.next=o.next,o.next=t)}function Ms(e,t,n,o,a,i){switch(n.tag){case 1:var l=n.payload;if("function"==typeof l){Ts(),1&e.mode&&l.call(i,o,a);var u=l.call(i,o,a);return ks(),u}return l;case 3:e.effectTag=-4097&e.effectTag|64;case 0:var s,c=n.payload;return"function"==typeof c?(Ts(),1&e.mode&&c.call(i,o,a),s=c.call(i,o,a),ks()):s=c,null==s?o:r({},o,s);case Ps:return As=!0,o}return o}function Bs(e,t,n,r){var o=e.updateQueue;As=!1,Os=o.shared;var a=o.baseQueue,i=o.shared.pending;if(null!==i){if(null!==a){var l=a.next,u=i.next;a.next=u,i.next=l}a=i,o.shared.pending=null;var s=e.alternate;if(null!==s){var c=s.updateQueue;null!==c&&(c.baseQueue=i)}}if(null!==a){var d=a.next,f=o.baseState,p=0,h=null,m=null,g=null;if(null!==d)for(var y=d;;){var v=y.expirationTime;if(v<r){var b={expirationTime:y.expirationTime,suspenseConfig:y.suspenseConfig,tag:y.tag,payload:y.payload,callback:y.callback,next:null};null===g?(m=g=b,h=f):g=g.next=b,v>p&&(p=v)}else{if(null!==g){var w={expirationTime:Bu,suspenseConfig:y.suspenseConfig,tag:y.tag,payload:y.payload,callback:y.callback,next:null};g=g.next=w}if(Wh(v,y.suspenseConfig),f=Ms(e,0,y,f,t,n),null!==y.callback){e.effectTag|=32;var F=o.effects;null===F?o.effects=[y]:F.push(y)}}if(null===(y=y.next)||y===d){if(null===(i=o.shared.pending))break;y=a.next=i.next,i.next=d,o.baseQueue=a=i,o.shared.pending=null}}null===g?h=f:g.next=m,o.baseState=h,o.baseQueue=g,Nh(p),e.expirationTime=p,e.memoizedState=f}Os=null}function zs(e,t){if("function"!=typeof e)throw Error("Invalid argument passed as callback. Expected a function. Instead received: "+e);e.call(t)}function Us(){As=!1}function Hs(){return As}function Vs(e,t,n){var r=t.effects;if(t.effects=null,null!==r)for(var o=0;o<r.length;o++){var a=r[o],i=a.callback;null!==i&&(a.callback=null,zs(i,n))}}_s=!1,Os=null;var $s=l.ReactCurrentBatchConfig;function qs(){return $s.suspense}var Qs,Ys,Ks,Js,Xs,Gs,Zs,ec,tc,nc,rc={},oc=Array.isArray,ac=(new e.Component).refs;Qs=new Set,Ys=new Set,Ks=new Set,Js=new Set,ec=new Set,Xs=new Set,tc=new Set,nc=new Set;var ic=new Set;function lc(e,t,n,o){var a=e.memoizedState;1&e.mode&&n(o,a);var i=n(o,a);Gs(t,i);var l=null==i?a:r({},a,i);(e.memoizedState=l,0===e.expirationTime)&&(e.updateQueue.baseState=l)}Zs=function(e,t){if(null!==e&&"function"!=typeof e){var n=t+"_"+e;ic.has(n)||(ic.add(n),s("%s(...): Expected the last optional `callback` argument to be a function. Instead received: %s.",t,e))}},Gs=function(e,t){if(void 0===t){var n=je(e)||"Component";Xs.has(n)||(Xs.add(n),s("%s.getDerivedStateFromProps(): A valid state object (or null) must be returned. You have returned undefined.",n))}},Object.defineProperty(rc,"_processChildContext",{enumerable:!1,value:function(){throw Error("_processChildContext is not available in React 16+. This likely means you have multiple copies of React and are attempting to nest a React 15 tree inside a React 16 tree using unstable_renderSubtreeIntoContainer, which isn't supported. Try to make sure you have only one copy of React (and ideally, switch to ReactDOM.createPortal).")}}),Object.freeze(rc);var uc,sc,cc,dc,fc,pc={isMounted:function(e){var t=tn.current;if(null!==t&&1===t.tag){var n=t,r=n.stateNode;r._warnedAboutRefsInRender||s("%s is accessing isMounted inside its render() function. render() should be a pure function of props and state. It should never access something that requires stale data from the previous render, such as refs. Move this logic to componentDidMount and componentDidUpdate instead.",je(n.type)||"A component"),r._warnedAboutRefsInRender=!0}var o=en(e);return!!o&&nn(o)===o},enqueueSetState:function(e,t,n){var r=en(e),o=vh(),a=qs(),i=bh(o,r,a),l=Ns(i,a);l.payload=t,null!=n&&(Zs(n,"setState"),l.callback=n),Ls(r,l),wh(r,i)},enqueueReplaceState:function(e,t,n){var r=en(e),o=vh(),a=qs(),i=bh(o,r,a),l=Ns(i,a);l.tag=1,l.payload=t,null!=n&&(Zs(n,"replaceState"),l.callback=n),Ls(r,l),wh(r,i)},enqueueForceUpdate:function(e,t){var n=en(e),r=vh(),o=qs(),a=bh(r,n,o),i=Ns(a,o);i.tag=Ps,null!=t&&(Zs(t,"forceUpdate"),i.callback=t),Ls(n,i),wh(n,a)}};function hc(e,t,n,r,o,a,i){var l=e.stateNode;if("function"==typeof l.shouldComponentUpdate){1&e.mode&&l.shouldComponentUpdate(r,a,i),Il(e,"shouldComponentUpdate");var u=l.shouldComponentUpdate(r,a,i);return Wl(),void 0===u&&s("%s.shouldComponentUpdate(): Returned undefined instead of a boolean value. Make sure to return true or false.",je(t)||"Component"),u}return!t.prototype||!t.prototype.isPureReactComponent||(!Bi(n,r)||!Bi(o,a))}function mc(e,t){var n;t.updater=pc,e.stateNode=t,n=e,t._reactInternalFiber=n,t._reactInternalInstance=rc}function gc(e,t,n){var r=!1,o=Xl,a=Xl,i=t.contextType;if("contextType"in t&&(!(null===i||void 0!==i&&i.$$typeof===Re&&void 0===i._context)&&!nc.has(t))){nc.add(t);var l="";l=void 0===i?" However, it is set to undefined. This can be caused by a typo or by mixing up named and default imports. This can also happen due to a circular dependency, so try moving the createContext() call to a separate file.":"object"!=typeof i?" However, it is set to a "+typeof i+".":i.$$typeof===xe?" Did you accidentally pass the Context.Provider instead?":void 0!==i._context?" Did you accidentally pass the Context.Consumer instead?":" However, it is set to an object with keys {"+Object.keys(i).join(", ")+"}.",s("%s defines an invalid contextType. contextType should point to the Context object returned by React.createContext().%s",je(t)||"Component",l)}if("object"==typeof i&&null!==i)a=Ds(i);else{o=tu(0,t,!0);var u=t.contextTypes;a=(r=null!=u)?ru(e,o):Xl}1&e.mode&&new t(n,a);var c=new t(n,a),d=e.memoizedState=null!==c.state&&void 0!==c.state?c.state:null;if(mc(e,c),"function"==typeof t.getDerivedStateFromProps&&null===d){var f=je(t)||"Component";Ys.has(f)||(Ys.add(f),s("`%s` uses `getDerivedStateFromProps` but its initial state is %s. This is not recommended. Instead, define the initial state by assigning an object to `this.state` in the constructor of `%s`. This ensures that `getDerivedStateFromProps` arguments have a consistent shape.",f,null===c.state?"null":"undefined",f))}if("function"==typeof t.getDerivedStateFromProps||"function"==typeof c.getSnapshotBeforeUpdate){var p=null,h=null,m=null;if("function"==typeof c.componentWillMount&&!0!==c.componentWillMount.__suppressDeprecationWarning?p="componentWillMount":"function"==typeof c.UNSAFE_componentWillMount&&(p="UNSAFE_componentWillMount"),"function"==typeof c.componentWillReceiveProps&&!0!==c.componentWillReceiveProps.__suppressDeprecationWarning?h="componentWillReceiveProps":"function"==typeof c.UNSAFE_componentWillReceiveProps&&(h="UNSAFE_componentWillReceiveProps"),"function"==typeof c.componentWillUpdate&&!0!==c.componentWillUpdate.__suppressDeprecationWarning?m="componentWillUpdate":"function"==typeof c.UNSAFE_componentWillUpdate&&(m="UNSAFE_componentWillUpdate"),null!==p||null!==h||null!==m){var g=je(t)||"Component",y="function"==typeof t.getDerivedStateFromProps?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";Js.has(g)||(Js.add(g),s("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n%s uses %s but also contains the following legacy lifecycles:%s%s%s\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-unsafe-component-lifecycles",g,y,null!==p?"\n  "+p:"",null!==h?"\n  "+h:"",null!==m?"\n  "+m:""))}}return r&&nu(e,o,a),c}function yc(e,t,n,r){var o=t.state;if(Il(e,"componentWillReceiveProps"),"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),Wl(),t.state!==o){var a=je(e.type)||"Component";Qs.has(a)||(Qs.add(a),s("%s.componentWillReceiveProps(): Assigning directly to this.state is deprecated (except inside a component's constructor). Use setState instead.",a)),pc.enqueueReplaceState(t,t.state,null)}}function vc(e,t,n,r){!function(e,t,n){var r=e.stateNode,o=je(t)||"Component";r.render||(t.prototype&&"function"==typeof t.prototype.render?s("%s(...): No `render` method found on the returned component instance: did you accidentally return an object from the constructor?",o):s("%s(...): No `render` method found on the returned component instance: you may have forgotten to define `render`.",o)),!r.getInitialState||r.getInitialState.isReactClassApproved||r.state||s("getInitialState was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Did you mean to define a state property instead?",o),r.getDefaultProps&&!r.getDefaultProps.isReactClassApproved&&s("getDefaultProps was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Use a static property to define defaultProps instead.",o),r.propTypes&&s("propTypes was defined as an instance property on %s. Use a static property to define propTypes instead.",o),r.contextType&&s("contextType was defined as an instance property on %s. Use a static property to define contextType instead.",o),r.contextTypes&&s("contextTypes was defined as an instance property on %s. Use a static property to define contextTypes instead.",o),t.contextType&&t.contextTypes&&!tc.has(t)&&(tc.add(t),s("%s declares both contextTypes and contextType static properties. The legacy contextTypes property will be ignored.",o)),"function"==typeof r.componentShouldUpdate&&s("%s has a method called componentShouldUpdate(). Did you mean shouldComponentUpdate()? The name is phrased as a question because the function is expected to return a value.",o),t.prototype&&t.prototype.isPureReactComponent&&void 0!==r.shouldComponentUpdate&&s("%s has a method called shouldComponentUpdate(). shouldComponentUpdate should not be used when extending React.PureComponent. Please extend React.Component if shouldComponentUpdate is used.",je(t)||"A pure component"),"function"==typeof r.componentDidUnmount&&s("%s has a method called componentDidUnmount(). But there is no such lifecycle method. Did you mean componentWillUnmount()?",o),"function"==typeof r.componentDidReceiveProps&&s("%s has a method called componentDidReceiveProps(). But there is no such lifecycle method. If you meant to update the state in response to changing props, use componentWillReceiveProps(). If you meant to fetch data or run side-effects or mutations after React has updated the UI, use componentDidUpdate().",o),"function"==typeof r.componentWillRecieveProps&&s("%s has a method called componentWillRecieveProps(). Did you mean componentWillReceiveProps()?",o),"function"==typeof r.UNSAFE_componentWillRecieveProps&&s("%s has a method called UNSAFE_componentWillRecieveProps(). Did you mean UNSAFE_componentWillReceiveProps()?",o);var a=r.props!==n;void 0!==r.props&&a&&s("%s(...): When calling super() in `%s`, make sure to pass up the same props that your component's constructor was passed.",o,o),r.defaultProps&&s("Setting defaultProps as an instance property on %s is not supported and will be ignored. Instead, define defaultProps as a static property on %s.",o,o),"function"!=typeof r.getSnapshotBeforeUpdate||"function"==typeof r.componentDidUpdate||Ks.has(t)||(Ks.add(t),s("%s: getSnapshotBeforeUpdate() should be used with componentDidUpdate(). This component defines getSnapshotBeforeUpdate() only.",je(t))),"function"==typeof r.getDerivedStateFromProps&&s("%s: getDerivedStateFromProps() is defined as an instance method and will be ignored. Instead, declare it as a static method.",o),"function"==typeof r.getDerivedStateFromError&&s("%s: getDerivedStateFromError() is defined as an instance method and will be ignored. Instead, declare it as a static method.",o),"function"==typeof t.getSnapshotBeforeUpdate&&s("%s: getSnapshotBeforeUpdate() is defined as a static method and will be ignored. Instead, declare it as an instance method.",o);var i=r.state;i&&("object"!=typeof i||oc(i))&&s("%s.state: must be set to an object or null",o),"function"==typeof r.getChildContext&&"object"!=typeof t.childContextTypes&&s("%s.getChildContext(): childContextTypes must be defined in order to use getChildContext().",o)}(e,t,n);var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs=ac,Is(e);var a=t.contextType;if("object"==typeof a&&null!==a)o.context=Ds(a);else{var i=tu(0,t,!0);o.context=ru(e,i)}if(o.state===n){var l=je(t)||"Component";ec.has(l)||(ec.add(l),s("%s: It is not recommended to assign props directly to state because updates to props won't be reflected in state. In most cases, it is better to use props directly.",l))}1&e.mode&&qu.recordLegacyContextWarning(e,o),qu.recordUnsafeLifecycleWarnings(e,o),Bs(e,n,o,r),o.state=e.memoizedState;var u=t.getDerivedStateFromProps;"function"==typeof u&&(lc(e,t,u,n),o.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof o.getSnapshotBeforeUpdate||"function"!=typeof o.UNSAFE_componentWillMount&&"function"!=typeof o.componentWillMount||(!function(e,t){Il(e,"componentWillMount");var n=t.state;"function"==typeof t.componentWillMount&&t.componentWillMount(),"function"==typeof t.UNSAFE_componentWillMount&&t.UNSAFE_componentWillMount(),Wl(),n!==t.state&&(s("%s.componentWillMount(): Assigning directly to this.state is deprecated (except inside a component's constructor). Use setState instead.",je(e.type)||"Component"),pc.enqueueReplaceState(t,t.state,null))}(e,o),Bs(e,n,o,r),o.state=e.memoizedState),"function"==typeof o.componentDidMount&&(e.effectTag|=4)}var bc;uc=!1,sc=!1,cc={},dc={},fc={},bc=function(e){if(null!==e&&"object"==typeof e&&e._store&&!e._store.validated&&null==e.key){if("object"!=typeof e._store)throw Error("React Component in warnForMissingKey should have a _store. This error is likely caused by a bug in React. Please file an issue.");e._store.validated=!0;var t='Each child in a list should have a unique "key" prop. See https://fb.me/react-warning-keys for more information.'+$e();dc[t]||(dc[t]=!0,s('Each child in a list should have a unique "key" prop. See https://fb.me/react-warning-keys for more information.'))}};var wc=Array.isArray;function Fc(e,t,n){var r=n.ref;if(null!==r&&"function"!=typeof r&&"object"!=typeof r){if(1&e.mode&&(!n._owner||!n._self||n._owner.stateNode===n._self)){var o=je(e.type)||"Component";cc[o]||(s('A string ref, "%s", has been found within a strict mode tree. String refs are a source of potential bugs and should be avoided. We recommend using useRef() or createRef() instead. Learn more about using refs safely here: https://fb.me/react-strict-mode-string-ref%s',r,ze(e)),cc[o]=!0)}if(n._owner){var a,i=n._owner;if(i){var l=i;if(1!==l.tag)throw Error("Function components cannot have string refs. We recommend using useRef() instead. Learn more about using refs safely here: https://fb.me/react-strict-mode-string-ref");a=l.stateNode}if(!a)throw Error("Missing owner for string ref "+r+". This error is likely caused by a bug in React. Please file an issue.");var u=""+r;if(null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===u)return t.ref;var c=function(e){var t=a.refs;t===ac&&(t=a.refs={}),null===e?delete t[u]:t[u]=e};return c._stringRef=u,c}if("string"!=typeof r)throw Error("Expected ref to be a function, a string, an object returned by React.createRef(), or null.");if(!n._owner)throw Error("Element ref was specified as a string ("+r+") but no owner was set. This could happen for one of the following reasons:\n1. You may be adding a ref to a function component\n2. You may be adding a ref to a component that was not created inside a component's render method\n3. You have multiple copies of React loaded\nSee https://fb.me/react-refs-must-have-owner for more information.")}return r}function Cc(e,t){if("textarea"!==e.type){var n;throw n=" If you meant to render a collection of children, use an array instead."+$e(),Error("Objects are not valid as a React child (found: "+("[object Object]"===Object.prototype.toString.call(t)?"object with keys {"+Object.keys(t).join(", ")+"}":t)+")."+n)}}function Tc(){var e="Functions are not valid as a React child. This may happen if you return a Component instead of <Component /> from render. Or maybe you meant to call this function rather than return it."+$e();fc[e]||(fc[e]=!0,s("Functions are not valid as a React child. This may happen if you return a Component instead of <Component /> from render. Or maybe you meant to call this function rather than return it."))}function kc(e){function t(t,n){if(e){var r=t.lastEffect;null!==r?(r.nextEffect=n,t.lastEffect=n):t.firstEffect=t.lastEffect=n,n.nextEffect=null,n.effectTag=8}}function n(n,r){if(!e)return null;for(var o=r;null!==o;)t(n,o),o=o.sibling;return null}function r(e,t){for(var n=new Map,r=t;null!==r;)null!==r.key?n.set(r.key,r):n.set(r.index,r),r=r.sibling;return n}function o(e,t){var n=Wm(e,t);return n.index=0,n.sibling=null,n}function a(t,n,r){if(t.index=r,!e)return n;var o=t.alternate;if(null!==o){var a=o.index;return a<n?(t.effectTag=2,n):a}return t.effectTag=2,n}function i(t){return e&&null===t.alternate&&(t.effectTag=2),t}function l(e,t,n,r){if(null===t||6!==t.tag){var a=Bm(n,e.mode,r);return a.return=e,a}var i=o(t,n);return i.return=e,i}function u(e,t,n,r){if(null!==t&&(t.elementType===n.type||ss(t,n))){var a=o(t,n.props);return a.ref=Fc(e,t,n),a.return=e,a._debugSource=n._source,a._debugOwner=n._owner,a}var i=jm(n,e.mode,r);return i.ref=Fc(e,t,n),i.return=e,i}function c(e,t,n,r){if(null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation){var a=zm(n,e.mode,r);return a.return=e,a}var i=o(t,n.children||[]);return i.return=e,i}function d(e,t,n,r,a){if(null===t||7!==t.tag){var i=Mm(n,e.mode,r,a);return i.return=e,i}var l=o(t,n);return l.return=e,l}function f(e,t,n){if("string"==typeof t||"number"==typeof t){var r=Bm(""+t,e.mode,n);return r.return=e,r}if("object"==typeof t&&null!==t){switch(t.$$typeof){case Fe:var o=jm(t,e.mode,n);return o.ref=Fc(e,null,t),o.return=e,o;case Ce:var a=zm(t,e.mode,n);return a.return=e,a}if(wc(t)||Ne(t)){var i=Mm(t,e.mode,n,null);return i.return=e,i}Cc(e,t)}return"function"==typeof t&&Tc(),null}function p(e,t,n,r){var o=null!==t?t.key:null;if("string"==typeof n||"number"==typeof n)return null!==o?null:l(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case Fe:return n.key===o?n.type===Te?d(e,t,n.props.children,r,o):u(e,t,n,r):null;case Ce:return n.key===o?c(e,t,n,r):null}if(wc(n)||Ne(n))return null!==o?null:d(e,t,n,r,null);Cc(e,n)}return"function"==typeof n&&Tc(),null}function h(e,t,n,r,o){if("string"==typeof r||"number"==typeof r)return l(t,e.get(n)||null,""+r,o);if("object"==typeof r&&null!==r){switch(r.$$typeof){case Fe:var a=e.get(null===r.key?n:r.key)||null;return r.type===Te?d(t,a,r.props.children,o,r.key):u(t,a,r,o);case Ce:return c(t,e.get(null===r.key?n:r.key)||null,r,o)}if(wc(r)||Ne(r))return d(t,e.get(n)||null,r,o,null);Cc(t,r)}return"function"==typeof r&&Tc(),null}function m(e,t){if("object"!=typeof e||null===e)return t;switch(e.$$typeof){case Fe:case Ce:bc(e);var n=e.key;if("string"!=typeof n)break;if(null===t){(t=new Set).add(n);break}if(!t.has(n)){t.add(n);break}s("Encountered two children with the same key, `%s`. Keys should be unique so that components maintain their identity across updates. Non-unique keys may cause children to be duplicated and/or omitted — the behavior is unsupported and could change in a future version.",n)}return t}return function(l,u,c,d){var g="object"==typeof c&&null!==c&&c.type===Te&&null===c.key;g&&(c=c.props.children);var y="object"==typeof c&&null!==c;if(y)switch(c.$$typeof){case Fe:return i(function(e,r,a,i){for(var l=a.key,u=r;null!==u;){if(u.key===l){switch(u.tag){case 7:if(a.type===Te){n(e,u.sibling);var s=o(u,a.props.children);return s.return=e,s._debugSource=a._source,s._debugOwner=a._owner,s}break;case 22:default:if(u.elementType===a.type||ss(u,a)){n(e,u.sibling);var c=o(u,a.props);return c.ref=Fc(e,u,a),c.return=e,c._debugSource=a._source,c._debugOwner=a._owner,c}}n(e,u);break}t(e,u),u=u.sibling}if(a.type===Te){var d=Mm(a.props.children,e.mode,i,a.key);return d.return=e,d}var f=jm(a,e.mode,i);return f.ref=Fc(e,r,a),f.return=e,f}(l,u,c,d));case Ce:return i(function(e,r,a,i){for(var l=a.key,u=r;null!==u;){if(u.key===l){if(4===u.tag&&u.stateNode.containerInfo===a.containerInfo&&u.stateNode.implementation===a.implementation){n(e,u.sibling);var s=o(u,a.children||[]);return s.return=e,s}n(e,u);break}t(e,u),u=u.sibling}var c=zm(a,e.mode,i);return c.return=e,c}(l,u,c,d))}if("string"==typeof c||"number"==typeof c)return i(function(e,t,r,a){if(null!==t&&6===t.tag){n(e,t.sibling);var i=o(t,r);return i.return=e,i}n(e,t);var l=Bm(r,e.mode,a);return l.return=e,l}(l,u,""+c,d));if(wc(c))return function(o,i,l,u){for(var s=null,c=0;c<l.length;c++){s=m(l[c],s)}for(var d=null,g=null,y=i,v=0,b=0,w=null;null!==y&&b<l.length;b++){y.index>b?(w=y,y=null):w=y.sibling;var F=p(o,y,l[b],u);if(null===F){null===y&&(y=w);break}e&&y&&null===F.alternate&&t(o,y),v=a(F,v,b),null===g?d=F:g.sibling=F,g=F,y=w}if(b===l.length)return n(o,y),d;if(null===y){for(;b<l.length;b++){var C=f(o,l[b],u);null!==C&&(v=a(C,v,b),null===g?d=C:g.sibling=C,g=C)}return d}for(var T=r(0,y);b<l.length;b++){var k=h(T,o,b,l[b],u);null!==k&&(e&&null!==k.alternate&&T.delete(null===k.key?b:k.key),v=a(k,v,b),null===g?d=k:g.sibling=k,g=k)}return e&&T.forEach((function(e){return t(o,e)})),d}(l,u,c,d);if(Ne(c))return function(o,i,l,u){var c=Ne(l);if("function"!=typeof c)throw Error("An object is not an iterable. This error is likely caused by a bug in React. Please file an issue.");"function"==typeof Symbol&&"Generator"===l[Symbol.toStringTag]&&(sc||s("Using Generators as children is unsupported and will likely yield unexpected results because enumerating a generator mutates it. You may convert it to an array with `Array.from()` or the `[...spread]` operator before rendering. Keep in mind you might need to polyfill these features for older browsers."),sc=!0),l.entries===c&&(uc||s("Using Maps as children is unsupported and will likely yield unexpected results. Convert it to a sequence/iterable of keyed ReactElements instead."),uc=!0);var d=c.call(l);if(d)for(var g=null,y=d.next();!y.done;y=d.next()){g=m(y.value,g)}var v=c.call(l);if(null==v)throw Error("An iterable object provided no iterator.");for(var b=null,w=null,F=i,C=0,T=0,k=null,E=v.next();null!==F&&!E.done;T++,E=v.next()){F.index>T?(k=F,F=null):k=F.sibling;var x=p(o,F,E.value,u);if(null===x){null===F&&(F=k);break}e&&F&&null===x.alternate&&t(o,F),C=a(x,C,T),null===w?b=x:w.sibling=x,w=x,F=k}if(E.done)return n(o,F),b;if(null===F){for(;!E.done;T++,E=v.next()){var R=f(o,E.value,u);null!==R&&(C=a(R,C,T),null===w?b=R:w.sibling=R,w=R)}return b}for(var S=r(0,F);!E.done;T++,E=v.next()){var D=h(S,o,T,E.value,u);null!==D&&(e&&null!==D.alternate&&S.delete(null===D.key?T:D.key),C=a(D,C,T),null===w?b=D:w.sibling=D,w=D)}return e&&S.forEach((function(e){return t(o,e)})),b}(l,u,c,d);if(y&&Cc(l,c),"function"==typeof c&&Tc(),void 0===c&&!g)switch(l.tag){case 1:if(l.stateNode.render._isMockFunction)break;case 0:var v=l.type;throw Error((v.displayName||v.name||"Component")+"(...): Nothing was returned from render. This usually means a return statement is missing. Or, to render nothing, return null.")}return n(l,u)}}var Ec=kc(!0),xc=kc(!1);function Rc(e,t){for(var n=e.child;null!==n;)Nm(n,t),n=n.sibling}var Sc={},Dc=Yl(Sc),_c=Yl(Sc),Oc=Yl(Sc);function Pc(e){if(e===Sc)throw Error("Expected host context to exist. This error is likely caused by a bug in React. Please file an issue.");return e}function Ac(){return Pc(Oc.current)}function Ic(e,t){Jl(Oc,t,e),Jl(_c,e,e),Jl(Dc,Sc,e);var n=function(e){var t,n,r=e.nodeType;switch(r){case 9:case 11:t=9===r?"#document":"#fragment";var o=e.documentElement;n=o?o.namespaceURI:Lt(null,"");break;default:var a=8===r?e.parentNode:e;n=Lt(a.namespaceURI||null,t=a.tagName)}var i=t.toLowerCase();return{namespace:n,ancestorInfo:Lo(null,i)}}(t);Kl(Dc,e),Jl(Dc,n,e)}function Wc(e){Kl(Dc,e),Kl(_c,e),Kl(Oc,e)}function Nc(){return Pc(Dc.current)}function Lc(e){Pc(Oc.current);var t,n,r,o=Pc(Dc.current),a=(t=o,n=e.type,{namespace:Lt((r=t).namespace,n),ancestorInfo:Lo(r.ancestorInfo,n)});o!==a&&(Jl(_c,e,e),Jl(Dc,a,e))}function jc(e){_c.current===e&&(Kl(Dc,e),Kl(_c,e))}var Mc=Yl(0);function Bc(e,t){return 0!=(e&t)}function zc(e){return 1&e}function Uc(e,t){return 1&e|t}function Hc(e,t){Jl(Mc,t,e)}function Vc(e){Kl(Mc,e)}function $c(e,t){var n=e.memoizedState;if(null!==n)return null!==n.dehydrated;var r=e.memoizedProps;return void 0!==r.fallback&&(!0!==r.unstable_avoidThisFallback||!t)}function qc(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n){var r=n.dehydrated;if(null===r||"$?"===r.data||aa(r))return t}}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!=(64&t.effectTag))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)return null;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Qc(e,t){var n={responder:e,props:t};return Object.freeze(n),n}var Yc,Kc=l.ReactCurrentDispatcher,Jc=l.ReactCurrentBatchConfig;Yc=new Set;var Xc=0,Gc=null,Zc=null,ed=null,td=!1,nd=null,rd=null,od=-1,ad=!1;function id(){var e=nd;null===rd?rd=[e]:rd.push(e)}function ld(){var e=nd;null!==rd&&(od++,rd[od]!==e&&function(e){var t=je(Gc.type);if(!Yc.has(t)&&(Yc.add(t),null!==rd)){for(var n="",r=0;r<=od;r++){for(var o=rd[r],a=r===od?e:o,i=r+1+". "+o;i.length<30;)i+=" ";n+=i+=a+"\n"}s("React has detected a change in the order of Hooks called by %s. This will lead to bugs and errors if not fixed. For more information, read the Rules of Hooks: https://fb.me/rules-of-hooks\n\n   Previous render            Next render\n   ------------------------------------------------------\n%s   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n",t,n)}}(e))}function ud(e){null==e||Array.isArray(e)||s("%s received a final argument that is not an array (instead, received `%s`). When specified, the final argument must be an array.",nd,typeof e)}function sd(){throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://fb.me/react-invalid-hook-call for tips about how to debug and fix this problem.")}function cd(e,t){if(ad)return!1;if(null===t)return s("%s received a final argument during this render, but not during the previous render. Even though the final argument is optional, its type cannot change between renders.",nd),!1;e.length!==t.length&&s("The final argument passed to %s changed size between renders. The order and size of this array must remain constant.\n\nPrevious: %s\nIncoming: %s",nd,"["+t.join(", ")+"]","["+e.join(", ")+"]");for(var n=0;n<t.length&&n<e.length;n++)if(!ji(e[n],t[n]))return!1;return!0}function dd(e,t,n,r,o,a){Xc=a,Gc=t,rd=null!==e?e._debugHookTypes:null,od=-1,ad=null!==e&&e.type!==t.type,t.memoizedState=null,t.updateQueue=null,t.expirationTime=0,null!==e&&null!==e.memoizedState?Kc.current=Gd:Kc.current=null!==rd?Xd:Jd;var i=n(r,o);if(t.expirationTime===Xc){var l=0;do{if(t.expirationTime=0,!(l<25))throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");l+=1,ad=!1,Zc=null,ed=null,t.updateQueue=null,od=-1,Kc.current=Zd,i=n(r,o)}while(t.expirationTime===Xc)}Kc.current=Kd,t._debugHookTypes=rd;var u=null!==Zc&&null!==Zc.next;if(Xc=0,Gc=null,Zc=null,ed=null,nd=null,rd=null,od=-1,td=!1,u)throw Error("Rendered fewer hooks than expected. This may be caused by an accidental early return statement.");return i}function fd(e,t,n){t.updateQueue=e.updateQueue,t.effectTag&=-517,e.expirationTime<=n&&(e.expirationTime=0)}function pd(){if(Kc.current=Kd,td)for(var e=Gc.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}Xc=0,Gc=null,Zc=null,ed=null,rd=null,od=-1,nd=null,td=!1}function hd(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ed?Gc.memoizedState=ed=e:ed=ed.next=e,ed}function md(){var e,t;if(null===Zc){var n=Gc.alternate;e=null!==n?n.memoizedState:null}else e=Zc.next;if(null!==(t=null===ed?Gc.memoizedState:ed.next))t=(ed=t).next,Zc=e;else{if(null===e)throw Error("Rendered more hooks than during the previous render.");var r={memoizedState:(Zc=e).memoizedState,baseState:Zc.baseState,baseQueue:Zc.baseQueue,queue:Zc.queue,next:null};null===ed?Gc.memoizedState=ed=r:ed=ed.next=r}return ed}function gd(e,t){return"function"==typeof t?t(e):t}function yd(e,t,n){var r,o=hd();r=void 0!==n?n(t):t,o.memoizedState=o.baseState=r;var a=o.queue={pending:null,dispatch:null,lastRenderedReducer:e,lastRenderedState:r},i=a.dispatch=Yd.bind(null,Gc,a);return[o.memoizedState,i]}function vd(e,t,n){var r=md(),o=r.queue;if(null===o)throw Error("Should have a queue. This is likely a bug in React. Please file an issue.");o.lastRenderedReducer=e;var a=Zc,i=a.baseQueue,l=o.pending;if(null!==l){if(null!==i){var u=i.next,s=l.next;i.next=s,l.next=u}a.baseQueue=i=l,o.pending=null}if(null!==i){var c=i.next,d=a.baseState,f=null,p=null,h=null,m=c;do{var g=m.expirationTime;if(g<Xc){var y={expirationTime:m.expirationTime,suspenseConfig:m.suspenseConfig,action:m.action,eagerReducer:m.eagerReducer,eagerState:m.eagerState,next:null};null===h?(p=h=y,f=d):h=h.next=y,g>Gc.expirationTime&&(Gc.expirationTime=g,Nh(g))}else{if(null!==h){var v={expirationTime:Bu,suspenseConfig:m.suspenseConfig,action:m.action,eagerReducer:m.eagerReducer,eagerState:m.eagerState,next:null};h=h.next=v}if(Wh(g,m.suspenseConfig),m.eagerReducer===e)d=m.eagerState;else d=e(d,m.action)}m=m.next}while(null!==m&&m!==c);null===h?f=d:h.next=p,ji(d,r.memoizedState)||op(),r.memoizedState=d,r.baseState=f,r.baseQueue=h,o.lastRenderedState=d}var b=o.dispatch;return[r.memoizedState,b]}function bd(e,t,n){var r=md(),o=r.queue;if(null===o)throw Error("Should have a queue. This is likely a bug in React. Please file an issue.");o.lastRenderedReducer=e;var a=o.dispatch,i=o.pending,l=r.memoizedState;if(null!==i){o.pending=null;var u=i.next,s=u;do{l=e(l,s.action),s=s.next}while(s!==u);ji(l,r.memoizedState)||op(),r.memoizedState=l,null===r.baseQueue&&(r.baseState=l),o.lastRenderedState=l}return[l,a]}function wd(e){var t=hd();"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e;var n=t.queue={pending:null,dispatch:null,lastRenderedReducer:gd,lastRenderedState:e},r=n.dispatch=Yd.bind(null,Gc,n);return[t.memoizedState,r]}function Fd(e){return vd(gd)}function Cd(e){return bd(gd)}function Td(e,t,n,r){var o={tag:e,create:t,destroy:n,deps:r,next:null},a=Gc.updateQueue;if(null===a)a={lastEffect:null},Gc.updateQueue=a,a.lastEffect=o.next=o;else{var i=a.lastEffect;if(null===i)a.lastEffect=o.next=o;else{var l=i.next;i.next=o,o.next=l,a.lastEffect=o}}return o}function kd(e){var t=hd(),n={current:e};return Object.seal(n),t.memoizedState=n,n}function Ed(e){return md().memoizedState}function xd(e,t,n,r){var o=hd(),a=void 0===r?null:r;Gc.effectTag|=e,o.memoizedState=Td(1|t,n,void 0,a)}function Rd(e,t,n,r){var o=md(),a=void 0===r?null:r,i=void 0;if(null!==Zc){var l=Zc.memoizedState;if(i=l.destroy,null!==a)if(cd(a,l.deps))return void Td(t,n,i,a)}Gc.effectTag|=e,o.memoizedState=Td(1|t,n,i,a)}function Sd(e,t){return"undefined"!=typeof jest&&cm(Gc),xd(516,4,e,t)}function Dd(e,t){return"undefined"!=typeof jest&&cm(Gc),Rd(516,4,e,t)}function _d(e,t){return xd(4,2,e,t)}function Od(e,t){return Rd(4,2,e,t)}function Pd(e,t){if("function"==typeof t){var n=t,r=e();return n(r),function(){n(null)}}if(null!=t){var o=t;o.hasOwnProperty("current")||s("Expected useImperativeHandle() first argument to either be a ref callback or React.createRef() object. Instead received: %s.","an object with keys {"+Object.keys(o).join(", ")+"}");var a=e();return o.current=a,function(){o.current=null}}}function Ad(e,t,n){"function"!=typeof t&&s("Expected useImperativeHandle() second argument to be a function that creates a handle. Instead received: %s.",null!==t?typeof t:"null");var r=null!=n?n.concat([e]):null;return xd(4,2,Pd.bind(null,t,e),r)}function Id(e,t,n){"function"!=typeof t&&s("Expected useImperativeHandle() second argument to be a function that creates a handle. Instead received: %s.",null!==t?typeof t:"null");var r=null!=n?n.concat([e]):null;return Rd(4,2,Pd.bind(null,t,e),r)}function Wd(e,t){}var Nd=Wd;function Ld(e,t){var n=void 0===t?null:t;return hd().memoizedState=[e,n],e}function jd(e,t){var n=md(),r=void 0===t?null:t,o=n.memoizedState;if(null!==o&&(null!==r&&cd(r,o[1])))return o[0];return n.memoizedState=[e,r],e}function Md(e,t){var n=hd(),r=void 0===t?null:t,o=e();return n.memoizedState=[o,r],o}function Bd(e,t){var n=md(),r=void 0===t?null:t,o=n.memoizedState;if(null!==o&&(null!==r&&cd(r,o[1])))return o[0];var a=e();return n.memoizedState=[a,r],a}function zd(e,t){var n=wd(e),r=n[0],o=n[1];return Sd((function(){var n=Jc.suspense;Jc.suspense=void 0===t?null:t;try{o(e)}finally{Jc.suspense=n}}),[e,t]),r}function Ud(e,t){var n=Fd(),r=n[0],o=n[1];return Dd((function(){var n=Jc.suspense;Jc.suspense=void 0===t?null:t;try{o(e)}finally{Jc.suspense=n}}),[e,t]),r}function Hd(e,t){var n=Cd(),r=n[0],o=n[1];return Dd((function(){var n=Jc.suspense;Jc.suspense=void 0===t?null:t;try{o(e)}finally{Jc.suspense=n}}),[e,t]),r}function Vd(e,t,n){var r=Au();Wu(r<98?98:r,(function(){e(!0)})),Wu(r>97?97:r,(function(){var r=Jc.suspense;Jc.suspense=void 0===t?null:t;try{e(!1),n()}finally{Jc.suspense=r}}))}function $d(e){var t=wd(!1),n=t[0],r=t[1];return[Ld(Vd.bind(null,r,e),[r,e]),n]}function qd(e){var t=Fd(),n=t[0],r=t[1];return[jd(Vd.bind(null,r,e),[r,e]),n]}function Qd(e){var t=Cd(),n=t[0],r=t[1];return[jd(Vd.bind(null,r,e),[r,e]),n]}function Yd(e,t,n){"function"==typeof arguments[3]&&s("State updates from the useState() and useReducer() Hooks don't support the second callback argument. To execute a side effect after rendering, declare it in the component body with useEffect().");var r=vh(),o=qs(),a=bh(r,e,o),i={expirationTime:a,suspenseConfig:o,action:n,eagerReducer:null,eagerState:null,next:null};i.priority=Au();var l=t.pending;null===l?i.next=i:(i.next=l.next,l.next=i),t.pending=i;var u=e.alternate;if(e===Gc||null!==u&&u===Gc)td=!0,i.expirationTime=Xc,Gc.expirationTime=Xc;else{if(0===e.expirationTime&&(null===u||0===u.expirationTime)){var c=t.lastRenderedReducer;if(null!==c){var d;d=Kc.current,Kc.current=tf;try{var f=t.lastRenderedState,p=c(f,n);if(i.eagerReducer=c,i.eagerState=p,ji(p,f))return}catch(e){}finally{Kc.current=d}}}"undefined"!=typeof jest&&(sm(e),dm(e)),wh(e,a)}}var Kd={readContext:Ds,useCallback:sd,useContext:sd,useEffect:sd,useImperativeHandle:sd,useLayoutEffect:sd,useMemo:sd,useReducer:sd,useRef:sd,useState:sd,useDebugValue:sd,useResponder:sd,useDeferredValue:sd,useTransition:sd},Jd=null,Xd=null,Gd=null,Zd=null,ef=null,tf=null,nf=null,rf=function(){s("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().")},of=function(){s("Do not call Hooks inside useEffect(...), useMemo(...), or other built-in Hooks. You can only call Hooks at the top level of your React function. For more information, see https://fb.me/rules-of-hooks")};Jd={readContext:function(e,t){return Ds(e,t)},useCallback:function(e,t){return nd="useCallback",id(),ud(t),Ld(e,t)},useContext:function(e,t){return nd="useContext",id(),Ds(e,t)},useEffect:function(e,t){return nd="useEffect",id(),ud(t),Sd(e,t)},useImperativeHandle:function(e,t,n){return nd="useImperativeHandle",id(),ud(n),Ad(e,t,n)},useLayoutEffect:function(e,t){return nd="useLayoutEffect",id(),ud(t),_d(e,t)},useMemo:function(e,t){nd="useMemo",id(),ud(t);var n=Kc.current;Kc.current=ef;try{return Md(e,t)}finally{Kc.current=n}},useReducer:function(e,t,n){nd="useReducer",id();var r=Kc.current;Kc.current=ef;try{return yd(e,t,n)}finally{Kc.current=r}},useRef:function(e){return nd="useRef",id(),kd(e)},useState:function(e){nd="useState",id();var t=Kc.current;Kc.current=ef;try{return wd(e)}finally{Kc.current=t}},useDebugValue:function(e,t){nd="useDebugValue",id()},useResponder:function(e,t){return nd="useResponder",id(),Qc(e,t)},useDeferredValue:function(e,t){return nd="useDeferredValue",id(),zd(e,t)},useTransition:function(e){return nd="useTransition",id(),$d(e)}},Xd={readContext:function(e,t){return Ds(e,t)},useCallback:function(e,t){return nd="useCallback",ld(),Ld(e,t)},useContext:function(e,t){return nd="useContext",ld(),Ds(e,t)},useEffect:function(e,t){return nd="useEffect",ld(),Sd(e,t)},useImperativeHandle:function(e,t,n){return nd="useImperativeHandle",ld(),Ad(e,t,n)},useLayoutEffect:function(e,t){return nd="useLayoutEffect",ld(),_d(e,t)},useMemo:function(e,t){nd="useMemo",ld();var n=Kc.current;Kc.current=ef;try{return Md(e,t)}finally{Kc.current=n}},useReducer:function(e,t,n){nd="useReducer",ld();var r=Kc.current;Kc.current=ef;try{return yd(e,t,n)}finally{Kc.current=r}},useRef:function(e){return nd="useRef",ld(),kd(e)},useState:function(e){nd="useState",ld();var t=Kc.current;Kc.current=ef;try{return wd(e)}finally{Kc.current=t}},useDebugValue:function(e,t){nd="useDebugValue",ld()},useResponder:function(e,t){return nd="useResponder",ld(),Qc(e,t)},useDeferredValue:function(e,t){return nd="useDeferredValue",ld(),zd(e,t)},useTransition:function(e){return nd="useTransition",ld(),$d(e)}},Gd={readContext:function(e,t){return Ds(e,t)},useCallback:function(e,t){return nd="useCallback",ld(),jd(e,t)},useContext:function(e,t){return nd="useContext",ld(),Ds(e,t)},useEffect:function(e,t){return nd="useEffect",ld(),Dd(e,t)},useImperativeHandle:function(e,t,n){return nd="useImperativeHandle",ld(),Id(e,t,n)},useLayoutEffect:function(e,t){return nd="useLayoutEffect",ld(),Od(e,t)},useMemo:function(e,t){nd="useMemo",ld();var n=Kc.current;Kc.current=tf;try{return Bd(e,t)}finally{Kc.current=n}},useReducer:function(e,t,n){nd="useReducer",ld();var r=Kc.current;Kc.current=tf;try{return vd(e)}finally{Kc.current=r}},useRef:function(e){return nd="useRef",ld(),Ed()},useState:function(e){nd="useState",ld();var t=Kc.current;Kc.current=tf;try{return Fd()}finally{Kc.current=t}},useDebugValue:function(e,t){return nd="useDebugValue",ld(),Nd()},useResponder:function(e,t){return nd="useResponder",ld(),Qc(e,t)},useDeferredValue:function(e,t){return nd="useDeferredValue",ld(),Ud(e,t)},useTransition:function(e){return nd="useTransition",ld(),qd(e)}},Zd={readContext:function(e,t){return Ds(e,t)},useCallback:function(e,t){return nd="useCallback",ld(),jd(e,t)},useContext:function(e,t){return nd="useContext",ld(),Ds(e,t)},useEffect:function(e,t){return nd="useEffect",ld(),Dd(e,t)},useImperativeHandle:function(e,t,n){return nd="useImperativeHandle",ld(),Id(e,t,n)},useLayoutEffect:function(e,t){return nd="useLayoutEffect",ld(),Od(e,t)},useMemo:function(e,t){nd="useMemo",ld();var n=Kc.current;Kc.current=nf;try{return Bd(e,t)}finally{Kc.current=n}},useReducer:function(e,t,n){nd="useReducer",ld();var r=Kc.current;Kc.current=nf;try{return bd(e)}finally{Kc.current=r}},useRef:function(e){return nd="useRef",ld(),Ed()},useState:function(e){nd="useState",ld();var t=Kc.current;Kc.current=nf;try{return Cd()}finally{Kc.current=t}},useDebugValue:function(e,t){return nd="useDebugValue",ld(),Nd()},useResponder:function(e,t){return nd="useResponder",ld(),Qc(e,t)},useDeferredValue:function(e,t){return nd="useDeferredValue",ld(),Hd(e,t)},useTransition:function(e){return nd="useTransition",ld(),Qd(e)}},ef={readContext:function(e,t){return rf(),Ds(e,t)},useCallback:function(e,t){return nd="useCallback",of(),id(),Ld(e,t)},useContext:function(e,t){return nd="useContext",of(),id(),Ds(e,t)},useEffect:function(e,t){return nd="useEffect",of(),id(),Sd(e,t)},useImperativeHandle:function(e,t,n){return nd="useImperativeHandle",of(),id(),Ad(e,t,n)},useLayoutEffect:function(e,t){return nd="useLayoutEffect",of(),id(),_d(e,t)},useMemo:function(e,t){nd="useMemo",of(),id();var n=Kc.current;Kc.current=ef;try{return Md(e,t)}finally{Kc.current=n}},useReducer:function(e,t,n){nd="useReducer",of(),id();var r=Kc.current;Kc.current=ef;try{return yd(e,t,n)}finally{Kc.current=r}},useRef:function(e){return nd="useRef",of(),id(),kd(e)},useState:function(e){nd="useState",of(),id();var t=Kc.current;Kc.current=ef;try{return wd(e)}finally{Kc.current=t}},useDebugValue:function(e,t){nd="useDebugValue",of(),id()},useResponder:function(e,t){return nd="useResponder",of(),id(),Qc(e,t)},useDeferredValue:function(e,t){return nd="useDeferredValue",of(),id(),zd(e,t)},useTransition:function(e){return nd="useTransition",of(),id(),$d(e)}},tf={readContext:function(e,t){return rf(),Ds(e,t)},useCallback:function(e,t){return nd="useCallback",of(),ld(),jd(e,t)},useContext:function(e,t){return nd="useContext",of(),ld(),Ds(e,t)},useEffect:function(e,t){return nd="useEffect",of(),ld(),Dd(e,t)},useImperativeHandle:function(e,t,n){return nd="useImperativeHandle",of(),ld(),Id(e,t,n)},useLayoutEffect:function(e,t){return nd="useLayoutEffect",of(),ld(),Od(e,t)},useMemo:function(e,t){nd="useMemo",of(),ld();var n=Kc.current;Kc.current=tf;try{return Bd(e,t)}finally{Kc.current=n}},useReducer:function(e,t,n){nd="useReducer",of(),ld();var r=Kc.current;Kc.current=tf;try{return vd(e)}finally{Kc.current=r}},useRef:function(e){return nd="useRef",of(),ld(),Ed()},useState:function(e){nd="useState",of(),ld();var t=Kc.current;Kc.current=tf;try{return Fd()}finally{Kc.current=t}},useDebugValue:function(e,t){return nd="useDebugValue",of(),ld(),Nd()},useResponder:function(e,t){return nd="useResponder",of(),ld(),Qc(e,t)},useDeferredValue:function(e,t){return nd="useDeferredValue",of(),ld(),Ud(e,t)},useTransition:function(e){return nd="useTransition",of(),ld(),qd(e)}},nf={readContext:function(e,t){return rf(),Ds(e,t)},useCallback:function(e,t){return nd="useCallback",of(),ld(),jd(e,t)},useContext:function(e,t){return nd="useContext",of(),ld(),Ds(e,t)},useEffect:function(e,t){return nd="useEffect",of(),ld(),Dd(e,t)},useImperativeHandle:function(e,t,n){return nd="useImperativeHandle",of(),ld(),Id(e,t,n)},useLayoutEffect:function(e,t){return nd="useLayoutEffect",of(),ld(),Od(e,t)},useMemo:function(e,t){nd="useMemo",of(),ld();var n=Kc.current;Kc.current=tf;try{return Bd(e,t)}finally{Kc.current=n}},useReducer:function(e,t,n){nd="useReducer",of(),ld();var r=Kc.current;Kc.current=tf;try{return bd(e)}finally{Kc.current=r}},useRef:function(e){return nd="useRef",of(),ld(),Ed()},useState:function(e){nd="useState",of(),ld();var t=Kc.current;Kc.current=tf;try{return Cd()}finally{Kc.current=t}},useDebugValue:function(e,t){return nd="useDebugValue",of(),ld(),Nd()},useResponder:function(e,t){return nd="useResponder",of(),ld(),Qc(e,t)},useDeferredValue:function(e,t){return nd="useDeferredValue",of(),ld(),Hd(e,t)},useTransition:function(e){return nd="useTransition",of(),ld(),Qd(e)}};var af=o.unstable_now,lf=0,uf=-1;function sf(){lf=af()}function cf(e){uf=af(),e.actualStartTime<0&&(e.actualStartTime=af())}function df(e){uf=-1}function ff(e,t){if(uf>=0){var n=af()-uf;e.actualDuration+=n,t&&(e.selfBaseDuration=n),uf=-1}}var pf=null,hf=null,mf=!1;function gf(e,t){switch(e.tag){case 3:!function(e,t){1===t.nodeType?Fo(e,t):8===t.nodeType||Co(e,t)}(e.stateNode.containerInfo,t);break;case 5:!function(e,t,n,r){!0!==t.suppressHydrationWarning&&(1===r.nodeType?Fo(n,r):8===r.nodeType||Co(n,r))}(e.type,e.memoizedProps,e.stateNode,t)}var n,r=((n=Am(5,null,null,0)).elementType="DELETED",n.type="DELETED",n);r.stateNode=t,r.return=e,r.effectTag=8,null!==e.lastEffect?(e.lastEffect.nextEffect=r,e.lastEffect=r):e.firstEffect=e.lastEffect=r}function yf(e,t){switch(t.effectTag=-1025&t.effectTag|2,e.tag){case 3:var n=e.stateNode.containerInfo;switch(t.tag){case 5:var r=t.type;t.pendingProps;!function(e,t,n){To(e,t)}(n,r);break;case 6:!function(e,t){ko(e,t)}(n,t.pendingProps)}break;case 5:e.type;var o=e.memoizedProps,a=e.stateNode;switch(t.tag){case 5:var i=t.type;t.pendingProps;!function(e,t,n,r,o){!0!==t.suppressHydrationWarning&&To(n,r)}(0,o,a,i);break;case 6:!function(e,t,n,r){!0!==t.suppressHydrationWarning&&ko(n,r)}(0,o,a,t.pendingProps);break;case 13:!function(e,t,n){t.suppressHydrationWarning}(0,o)}break;default:return}}function vf(e,t){switch(e.tag){case 5:var n=e.type,r=(e.pendingProps,function(e,t,n){return 1!==e.nodeType||t.toLowerCase()!==e.nodeName.toLowerCase()?null:e}(t,n));return null!==r&&(e.stateNode=r,!0);case 6:var o=function(e,t){return""===t||3!==e.nodeType?null:e}(t,e.pendingProps);return null!==o&&(e.stateNode=o,!0);case 13:default:return!1}}function bf(e){if(mf){var t=hf;if(!t)return yf(pf,e),mf=!1,void(pf=e);var n=t;if(!vf(e,t)){if(!(t=la(n))||!vf(e,t))return yf(pf,e),mf=!1,void(pf=e);gf(pf,n)}pf=e,hf=ua(t)}}function wf(e){var t=e.stateNode,n=e.memoizedProps,r=function(e,t,n){return ma(n,e),function(e,t){return e.nodeValue!==t}(e,t)}(t,n,e);if(r){var o=pf;if(null!==o)switch(o.tag){case 3:o.stateNode.containerInfo;!function(e,t,n){wo(t,n)}(0,t,n);break;case 5:o.type;var a=o.memoizedProps;o.stateNode;!function(e,t,n,r,o){!0!==t.suppressHydrationWarning&&wo(r,o)}(0,a,0,t,n)}}return r}function Ff(e){var t=e.memoizedState,n=null!==t?t.dehydrated:null;if(!n)throw Error("Expected to have a hydrated suspense instance. This error is likely caused by a bug in React. Please file an issue.");return function(e){for(var t=e.nextSibling,n=0;t;){if(8===t.nodeType){var r=t.data;if("/$"===r){if(0===n)return la(t);n--}else"$"!==r&&"$!"!==r&&"$?"!==r||n++}t=t.nextSibling}return null}(n)}function Cf(e){for(var t=e.return;null!==t&&5!==t.tag&&3!==t.tag&&13!==t.tag;)t=t.return;pf=t}function Tf(e){if(e!==pf)return!1;if(!mf)return Cf(e),mf=!0,!1;var t=e.type;if(5!==e.tag||"head"!==t&&"body"!==t&&!Ko(t,e.memoizedProps))for(var n=hf;n;)gf(e,n),n=la(n);return Cf(e),hf=13===e.tag?Ff(e):pf?la(e.stateNode):null,!0}function kf(){pf=null,hf=null,mf=!1}var Ef,xf,Rf,Sf,Df,_f,Of,Pf,Af=l.ReactCurrentOwner,If=!1;function Wf(e,t,n,r){t.child=null===e?xc(t,null,n,r):Ec(t,e.child,n,r)}function Nf(e,t,n,r,o){if(t.type!==t.elementType){var i=n.propTypes;i&&a(i,r,"prop",je(n),$e)}var l,u=n.render,s=t.ref;return Ss(t,o),Af.current=t,Ye(!0),l=dd(e,t,u,r,s,o),1&t.mode&&null!==t.memoizedState&&(l=dd(e,t,u,r,s,o)),Ye(!1),null===e||If?(t.effectTag|=1,Wf(e,t,l,o),t.child):(fd(e,t,o),ap(e,t,o))}function Lf(e,t,n,r,o,i){if(null===e){var l=n.type;if(function(e){return"function"==typeof e&&!Im(e)&&void 0===e.defaultProps}(l)&&null===n.compare&&void 0===n.defaultProps){var u;return u=is(l),t.tag=15,t.type=u,qf(t,l),jf(e,t,u,r,o,i)}var s=l.propTypes;s&&a(s,r,"prop",je(l),$e);var c=Lm(n.type,null,r,null,t.mode,i);return c.ref=t.ref,c.return=t,t.child=c,c}var d=n.type,f=d.propTypes;f&&a(f,r,"prop",je(d),$e);var p=e.child;if(o<i){var h=p.memoizedProps,m=n.compare;if((m=null!==m?m:Bi)(h,r)&&e.ref===t.ref)return ap(e,t,i)}t.effectTag|=1;var g=Wm(p,r);return g.ref=t.ref,g.return=t,t.child=g,g}function jf(e,t,n,r,o,i){if(t.type!==t.elementType){var l=t.elementType;l.$$typeof===Ae&&(l=Le(l));var u=l&&l.propTypes;u&&a(u,r,"prop",je(l),$e)}if(null!==e&&(Bi(e.memoizedProps,r)&&e.ref===t.ref&&t.type===e.type&&(If=!1,o<i)))return t.expirationTime=e.expirationTime,ap(e,t,i);return Bf(e,t,n,r,i)}function Mf(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.effectTag|=128)}function Bf(e,t,n,r,o){if(t.type!==t.elementType){var i=n.propTypes;i&&a(i,r,"prop",je(n),$e)}var l,u;return l=ru(t,tu(0,n,!0)),Ss(t,o),Af.current=t,Ye(!0),u=dd(e,t,n,r,l,o),1&t.mode&&null!==t.memoizedState&&(u=dd(e,t,n,r,l,o)),Ye(!1),null===e||If?(t.effectTag|=1,Wf(e,t,u,o),t.child):(fd(e,t,o),ap(e,t,o))}function zf(e,t,n,r,o){if(t.type!==t.elementType){var i=n.propTypes;i&&a(i,r,"prop",je(n),$e)}var l,u;au(n)?(l=!0,cu(t)):l=!1,Ss(t,o),null===t.stateNode?(null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),gc(t,n,r),vc(t,n,r,o),u=!0):u=null===e?function(e,t,n,r){var o=e.stateNode,a=e.memoizedProps;o.props=a;var i=o.context,l=t.contextType,u=Xl;u="object"==typeof l&&null!==l?Ds(l):ru(e,tu(0,t,!0));var s=t.getDerivedStateFromProps,c="function"==typeof s||"function"==typeof o.getSnapshotBeforeUpdate;c||"function"!=typeof o.UNSAFE_componentWillReceiveProps&&"function"!=typeof o.componentWillReceiveProps||a===n&&i===u||yc(e,o,n,u),Us();var d=e.memoizedState,f=o.state=d;if(Bs(e,n,o,r),f=e.memoizedState,a===n&&d===f&&!ou()&&!Hs())return"function"==typeof o.componentDidMount&&(e.effectTag|=4),!1;"function"==typeof s&&(lc(e,t,s,n),f=e.memoizedState);var p=Hs()||hc(e,t,a,n,d,f,u);return p?(c||"function"!=typeof o.UNSAFE_componentWillMount&&"function"!=typeof o.componentWillMount||(Il(e,"componentWillMount"),"function"==typeof o.componentWillMount&&o.componentWillMount(),"function"==typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),Wl()),"function"==typeof o.componentDidMount&&(e.effectTag|=4)):("function"==typeof o.componentDidMount&&(e.effectTag|=4),e.memoizedProps=n,e.memoizedState=f),o.props=n,o.state=f,o.context=u,p}(t,n,r,o):function(e,t,n,r,o){var a=t.stateNode;Ws(e,t);var i=t.memoizedProps;a.props=t.type===t.elementType?i:hs(t.type,i);var l=a.context,u=n.contextType,s=Xl;s="object"==typeof u&&null!==u?Ds(u):ru(t,tu(0,n,!0));var c=n.getDerivedStateFromProps,d="function"==typeof c||"function"==typeof a.getSnapshotBeforeUpdate;d||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||i===r&&l===s||yc(t,a,r,s),Us();var f=t.memoizedState,p=a.state=f;if(Bs(t,r,a,o),p=t.memoizedState,i===r&&f===p&&!ou()&&!Hs())return"function"==typeof a.componentDidUpdate&&(i===e.memoizedProps&&f===e.memoizedState||(t.effectTag|=4)),"function"==typeof a.getSnapshotBeforeUpdate&&(i===e.memoizedProps&&f===e.memoizedState||(t.effectTag|=256)),!1;"function"==typeof c&&(lc(t,n,c,r),p=t.memoizedState);var h=Hs()||hc(t,n,i,r,f,p,s);return h?(d||"function"!=typeof a.UNSAFE_componentWillUpdate&&"function"!=typeof a.componentWillUpdate||(Il(t,"componentWillUpdate"),"function"==typeof a.componentWillUpdate&&a.componentWillUpdate(r,p,s),"function"==typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,p,s),Wl()),"function"==typeof a.componentDidUpdate&&(t.effectTag|=4),"function"==typeof a.getSnapshotBeforeUpdate&&(t.effectTag|=256)):("function"==typeof a.componentDidUpdate&&(i===e.memoizedProps&&f===e.memoizedState||(t.effectTag|=4)),"function"==typeof a.getSnapshotBeforeUpdate&&(i===e.memoizedProps&&f===e.memoizedState||(t.effectTag|=256)),t.memoizedProps=r,t.memoizedState=p),a.props=r,a.state=p,a.context=s,h}(e,t,n,r,o);var c=Uf(e,t,n,u,l,o);return t.stateNode.props!==r&&(_f||s("It looks like %s is reassigning its own `this.props` while rendering. This is not supported and can lead to confusing bugs.",je(t.type)||"a component"),_f=!0),c}function Uf(e,t,n,r,o,a){Mf(e,t);var i=0!=(64&t.effectTag);if(!r&&!i)return o&&du(t,n,!1),ap(e,t,a);var l,u=t.stateNode;return Af.current=t,i&&"function"!=typeof n.getDerivedStateFromError?(l=null,df()):(Ye(!0),l=u.render(),1&t.mode&&u.render(),Ye(!1)),t.effectTag|=1,null!==e&&i?function(e,t,n,r){t.child=Ec(t,e.child,null,r),t.child=Ec(t,null,n,r)}(e,t,l,a):Wf(e,t,l,a),t.memoizedState=u.state,o&&du(t,n,!0),t.child}function Hf(e){var t=e.stateNode;t.pendingContext?uu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&uu(e,t.context,!1),Ic(e,t.containerInfo)}function Vf(e,t,n){Hf(t);var r=t.updateQueue;if(null===e||null===r)throw Error("If the root does not have an updateQueue, we should have already bailed out. This error is likely caused by a bug in React. Please file an issue.");var o=t.pendingProps,a=t.memoizedState,i=null!==a?a.element:null;Ws(e,t),Bs(t,o,null,n);var l,u,s=t.memoizedState.element;if(s===i)return kf(),ap(e,t,n);if(t.stateNode.hydrate&&(u=(l=t).stateNode.containerInfo,hf=ua(u),pf=l,mf=!0,1)){var c=xc(t,null,s,n);t.child=c;for(var d=c;d;)d.effectTag=-3&d.effectTag|1024,d=d.sibling}else Wf(e,t,s,n),kf();return t.child}function $f(e,t,n,r,o){null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2);var i=t.pendingProps;Ol(t);var l=ms(n);t.type=l;var u=t.tag=function(e){if("function"==typeof e)return Im(e)?1:0;if(null!=e){var t=e.$$typeof;if(t===De)return 11;if(t===Pe)return 14}return 2}(l);_l(t);var s=hs(l,i);switch(u){case 0:return qf(t,l),t.type=l=is(l),Bf(null,t,l,s,o);case 1:return t.type=l=ls(l),zf(null,t,l,s,o);case 11:return t.type=l=us(l),Nf(null,t,l,s,o);case 14:if(t.type!==t.elementType){var c=l.propTypes;c&&a(c,s,"prop",je(l),$e)}return Lf(null,t,l,hs(l.type,s),r,o)}var d="";throw null!==l&&"object"==typeof l&&l.$$typeof===Ae&&(d=" Did you wrap a component in React.lazy() more than once?"),Error("Element type is invalid. Received a promise that resolves to: "+l+". Lazy element type must resolve to a class or function."+d)}function qf(e,t){if(t&&t.childContextTypes&&s("%s(...): childContextTypes cannot be defined on a function component.",t.displayName||t.name||"Component"),null!==e.ref){var n="",r=Ve();r&&(n+="\n\nCheck the render method of `"+r+"`.");var o=r||e._debugID||"",a=e._debugSource;a&&(o=a.fileName+":"+a.lineNumber),Df[o]||(Df[o]=!0,s("Function components cannot be given refs. Attempts to access this ref will fail. Did you mean to use React.forwardRef()?%s",n))}if("function"==typeof t.getDerivedStateFromProps){var i=je(t)||"Unknown";Sf[i]||(s("%s: Function components do not support getDerivedStateFromProps.",i),Sf[i]=!0)}if("object"==typeof t.contextType&&null!==t.contextType){var l=je(t)||"Unknown";Rf[l]||(s("%s: Function components do not support contextType.",l),Rf[l]=!0)}}Ef={},xf={},Rf={},Sf={},Df={},_f=!1,Of={},Pf={};var Qf={dehydrated:null,retryTime:0};function Yf(e,t,n){var r=t.mode,o=t.pendingProps;eg(t)&&(t.effectTag|=64);var a=Mc.current,i=!1;if(0!=(64&t.effectTag)||function(e,t,n){return Bc(e,2)&&(null===t||null!==t.memoizedState)}(a,e)?(i=!0,t.effectTag&=-65):null!==e&&null===e.memoizedState||void 0!==o.fallback&&!0!==o.unstable_avoidThisFallback&&(a=a|1),Hc(t,a=zc(a)),null===e){if(void 0!==o.fallback&&bf(t),i){var l=o.fallback,u=Mm(null,r,0,null);if(u.return=t,0==(2&t.mode)){var s=null!==t.memoizedState?t.child.child:t.child;u.child=s;for(var c=s;null!==c;)c.return=u,c=c.sibling}var d=Mm(l,r,n,null);return d.return=t,u.sibling=d,t.memoizedState=Qf,t.child=u,d}var f=o.children;return t.memoizedState=null,t.child=xc(t,null,f,n)}if(null!==e.memoizedState){var p=e.child,h=p.sibling;if(i){var m=o.fallback,g=Wm(p,p.pendingProps);if(g.return=t,0==(2&t.mode)){var y=null!==t.memoizedState?t.child.child:t.child;if(y!==p.child){g.child=y;for(var v=y;null!==v;)v.return=g,v=v.sibling}}if(8&t.mode){for(var b=0,w=g.child;null!==w;)b+=w.treeBaseDuration,w=w.sibling;g.treeBaseDuration=b}var F=Wm(h,m);return F.return=t,g.sibling=F,g.childExpirationTime=0,t.memoizedState=Qf,t.child=g,F}var C=o.children,T=p.child,k=Ec(t,T,C,n);return t.memoizedState=null,t.child=k}var E=e.child;if(i){var x=o.fallback,R=Mm(null,r,0,null);if(R.return=t,R.child=E,null!==E&&(E.return=R),0==(2&t.mode)){var S=null!==t.memoizedState?t.child.child:t.child;R.child=S;for(var D=S;null!==D;)D.return=R,D=D.sibling}if(8&t.mode){for(var _=0,O=R.child;null!==O;)_+=O.treeBaseDuration,O=O.sibling;R.treeBaseDuration=_}var P=Mm(x,r,n,null);return P.return=t,R.sibling=P,P.effectTag|=2,R.childExpirationTime=0,t.memoizedState=Qf,t.child=R,P}t.memoizedState=null;var A=o.children;return t.child=Ec(t,E,A,n)}function Kf(e,t){e.expirationTime<t&&(e.expirationTime=t);var n=e.alternate;null!==n&&n.expirationTime<t&&(n.expirationTime=t),Rs(e.return,t)}function Jf(e,t){var n=Array.isArray(e),r=!n&&"function"==typeof Ne(e);if(n||r){var o=n?"array":"iterable";return s("A nested %s was passed to row #%s in <SuspenseList />. Wrap it in an additional SuspenseList to configure its revealOrder: <SuspenseList revealOrder=...> ... <SuspenseList revealOrder=...>{%s}</SuspenseList> ... </SuspenseList>",o,t,o),!1}return!0}function Xf(e,t,n,r,o,a){var i=e.memoizedState;null===i?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailExpiration:0,tailMode:o,lastEffect:a}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailExpiration=0,i.tailMode=o,i.lastEffect=a)}function Gf(e,t,n){var r=t.pendingProps,o=r.revealOrder,a=r.tail,i=r.children;!function(e){if(void 0!==e&&"forwards"!==e&&"backwards"!==e&&"together"!==e&&!Of[e])if(Of[e]=!0,"string"==typeof e)switch(e.toLowerCase()){case"together":case"forwards":case"backwards":s('"%s" is not a valid value for revealOrder on <SuspenseList />. Use lowercase "%s" instead.',e,e.toLowerCase());break;case"forward":case"backward":s('"%s" is not a valid value for revealOrder on <SuspenseList />. React uses the -s suffix in the spelling. Use "%ss" instead.',e,e.toLowerCase());break;default:s('"%s" is not a supported revealOrder on <SuspenseList />. Did you mean "together", "forwards" or "backwards"?',e)}else s('%s is not a supported value for revealOrder on <SuspenseList />. Did you mean "together", "forwards" or "backwards"?',e)}(o),function(e,t){void 0===e||Pf[e]||("collapsed"!==e&&"hidden"!==e?(Pf[e]=!0,s('"%s" is not a supported value for tail on <SuspenseList />. Did you mean "collapsed" or "hidden"?',e)):"forwards"!==t&&"backwards"!==t&&(Pf[e]=!0,s('<SuspenseList tail="%s" /> is only valid if revealOrder is "forwards" or "backwards". Did you mean to specify revealOrder="forwards"?',e)))}(a,o),function(e,t){if(("forwards"===t||"backwards"===t)&&null!=e&&!1!==e)if(Array.isArray(e)){for(var n=0;n<e.length;n++)if(!Jf(e[n],n))return}else{var r=Ne(e);if("function"==typeof r){var o=r.call(e);if(o)for(var a=o.next(),i=0;!a.done;a=o.next()){if(!Jf(a.value,i))return;i++}}else s('A single row was passed to a <SuspenseList revealOrder="%s" />. This is not useful since it needs multiple rows. Did you mean to pass multiple children or an array?',t)}}(i,o),Wf(e,t,i,n);var l=Mc.current;Bc(l,2)?(l=Uc(l,2),t.effectTag|=64):(null!==e&&0!=(64&e.effectTag)&&function(e,t,n){for(var r=t;null!==r;){if(13===r.tag)null!==r.memoizedState&&Kf(r,n);else if(19===r.tag)Kf(r,n);else if(null!==r.child){r.child.return=r,r=r.child;continue}if(r===e)return;for(;null===r.sibling;){if(null===r.return||r.return===e)return;r=r.return}r.sibling.return=r.return,r=r.sibling}}(t,t.child,n),l=zc(l));if(Hc(t,l),0==(2&t.mode))t.memoizedState=null;else switch(o){case"forwards":var u,c=function(e){for(var t=e,n=null;null!==t;){var r=t.alternate;null!==r&&null===qc(r)&&(n=t),t=t.sibling}return n}(t.child);null===c?(u=t.child,t.child=null):(u=c.sibling,c.sibling=null),Xf(t,!1,u,c,a,t.lastEffect);break;case"backwards":var d=null,f=t.child;for(t.child=null;null!==f;){var p=f.alternate;if(null!==p&&null===qc(p)){t.child=f;break}var h=f.sibling;f.sibling=d,d=f,f=h}Xf(t,!0,d,null,a,t.lastEffect);break;case"together":Xf(t,!1,null,null,void 0,t.lastEffect);break;default:t.memoizedState=null}return t.child}function Zf(e,t,n){var r=t.type._context,o=t.pendingProps,i=t.memoizedProps,l=o.value,u=t.type.propTypes;if(u&&a(u,o,"prop","Context.Provider",$e),Es(t,l),null!==i){var c=function(e,t,n){if(ji(n,t))return 0;var r="function"==typeof e._calculateChangedBits?e._calculateChangedBits(n,t):**********;return(**********&r)!==r&&s("calculateChangedBits: Expected the return value to be a 31-bit integer. Instead received: %s",r),0|r}(r,l,i.value);if(0===c){if(i.children===o.children&&!ou())return ap(e,t,n)}else!function(e,t,n,r){var o=e.child;for(null!==o&&(o.return=e);null!==o;){var a=void 0,i=o.dependencies;if(null!==i){a=o.child;for(var l=i.firstContext;null!==l;){if(l.context===t&&0!=(l.observedBits&n)){if(1===o.tag){var u=Ns(r,null);u.tag=Ps,Ls(o,u)}o.expirationTime<r&&(o.expirationTime=r);var s=o.alternate;null!==s&&s.expirationTime<r&&(s.expirationTime=r),Rs(o.return,r),i.expirationTime<r&&(i.expirationTime=r);break}l=l.next}}else a=10===o.tag&&o.type===e.type?null:o.child;if(null!==a)a.return=o;else for(a=o;null!==a;){if(a===e){a=null;break}var c=a.sibling;if(null!==c){c.return=a.return,a=c;break}a=a.return}o=a}}(t,r,c,n)}return Wf(e,t,o.children,n),t.child}var ep,tp,np,rp=!1;function op(){If=!0}function ap(e,t,n){Ol(t),null!==e&&(t.dependencies=e.dependencies),df();var r=t.expirationTime;return 0!==r&&Nh(r),t.childExpirationTime<n?null:(function(e,t){if(null!==e&&t.child!==e.child)throw Error("Resuming work not yet implemented.");if(null!==t.child){var n=t.child,r=Wm(n,n.pendingProps);for(t.child=r,r.return=t;null!==n.sibling;)n=n.sibling,(r=r.sibling=Wm(n,n.pendingProps)).return=t;r.sibling=null}}(e,t),t.child)}function ip(e,t,n){var r=t.expirationTime;if(t._debugNeedsRemount&&null!==e)return function(e,t,n){var r=t.return;if(null===r)throw new Error("Cannot swap the root fiber.");if(e.alternate=null,t.alternate=null,n.index=t.index,n.sibling=t.sibling,n.return=t.return,n.ref=t.ref,t===r.child)r.child=n;else{var o=r.child;if(null===o)throw new Error("Expected parent to have a child.");for(;o.sibling!==t;)if(null===(o=o.sibling))throw new Error("Expected to find the previous sibling.");o.sibling=n}var a=r.lastEffect;return null!==a?(a.nextEffect=e,r.lastEffect=e):r.firstEffect=r.lastEffect=e,e.nextEffect=null,e.effectTag=8,n.effectTag|=2,n}(e,t,Lm(t.type,t.key,t.pendingProps,t._debugOwner||null,t.mode,t.expirationTime));if(null!==e){var o=e.memoizedProps,i=t.pendingProps;if(o!==i||ou()||t.type!==e.type)If=!0;else{if(r<n){switch(If=!1,t.tag){case 3:Hf(t),kf();break;case 5:if(Lc(t),4&t.mode&&1!==n&&Jo(t.type,i))return hm(1),t.expirationTime=t.childExpirationTime=1,null;break;case 1:au(t.type)&&cu(t);break;case 4:Ic(t,t.stateNode.containerInfo);break;case 10:Es(t,t.memoizedProps.value);break;case 12:t.childExpirationTime>=n&&(t.effectTag|=4);break;case 13:if(null!==t.memoizedState){var l=t.child.childExpirationTime;if(0!==l&&l>=n)return Yf(e,t,n);Hc(t,zc(Mc.current));var u=ap(e,t,n);return null!==u?u.sibling:null}Hc(t,zc(Mc.current));break;case 19:var c=0!=(64&e.effectTag),d=t.childExpirationTime>=n;if(c){if(d)return Gf(e,t,n);t.effectTag|=64}var f=t.memoizedState;if(null!==f&&(f.rendering=null,f.tail=null),Hc(t,Mc.current),d)break;return null}return ap(e,t,n)}If=!1}}else If=!1;switch(t.expirationTime=0,t.tag){case 2:return function(e,t,n,r){null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2);var o,a,i=t.pendingProps;if(o=ru(t,tu(0,n,!1)),Ss(t,r),n.prototype&&"function"==typeof n.prototype.render){var l=je(n)||"Unknown";Ef[l]||(s("The <%s /> component appears to have a render method, but doesn't extend React.Component. This is likely to cause errors. Change %s to extend React.Component instead.",l,l),Ef[l]=!0)}if(1&t.mode&&qu.recordLegacyContextWarning(t,null),Ye(!0),Af.current=t,a=dd(null,t,n,i,o,r),Ye(!1),t.effectTag|=1,"object"==typeof a&&null!==a&&"function"==typeof a.render&&void 0===a.$$typeof){var u=je(n)||"Unknown";xf[u]||(s("The <%s /> component appears to be a function component that returns a class instance. Change %s to a class that extends React.Component instead. If you can't use a class try assigning the prototype on the function as a workaround. `%s.prototype = React.Component.prototype`. Don't use an arrow function since it cannot be called with `new` by React.",u,u,u),xf[u]=!0),t.tag=1,t.memoizedState=null,t.updateQueue=null;var c=!1;au(n)?(c=!0,cu(t)):c=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Is(t);var d=n.getDerivedStateFromProps;return"function"==typeof d&&lc(t,n,d,i),mc(t,a),vc(t,n,i,r),Uf(null,t,n,!0,c,r)}return t.tag=0,1&t.mode&&null!==t.memoizedState&&(a=dd(null,t,n,i,o,r)),Wf(null,t,a,r),qf(t,n),t.child}(e,t,t.type,n);case 16:return $f(e,t,t.elementType,r,n);case 0:var p=t.type,h=t.pendingProps;return Bf(e,t,p,t.elementType===p?h:hs(p,h),n);case 1:var m=t.type,g=t.pendingProps;return zf(e,t,m,t.elementType===m?g:hs(m,g),n);case 3:return Vf(e,t,n);case 5:return function(e,t,n){Lc(t),null===e&&bf(t);var r=t.type,o=t.pendingProps,a=null!==e?e.memoizedProps:null,i=o.children;return Ko(r,o)?i=null:null!==a&&Ko(r,a)&&(t.effectTag|=16),Mf(e,t),4&t.mode&&1!==n&&Jo(0,o)?(hm(1),t.expirationTime=t.childExpirationTime=1,null):(Wf(e,t,i,n),t.child)}(e,t,n);case 6:return function(e,t){return null===e&&bf(t),null}(e,t);case 13:return Yf(e,t,n);case 4:return function(e,t,n){Ic(t,t.stateNode.containerInfo);var r=t.pendingProps;return null===e?t.child=Ec(t,null,r,n):Wf(e,t,r,n),t.child}(e,t,n);case 11:var y=t.type,v=t.pendingProps;return Nf(e,t,y,t.elementType===y?v:hs(y,v),n);case 7:return function(e,t,n){return Wf(e,t,t.pendingProps,n),t.child}(e,t,n);case 8:return function(e,t,n){return Wf(e,t,t.pendingProps.children,n),t.child}(e,t,n);case 12:return function(e,t,n){return t.effectTag|=4,Wf(e,t,t.pendingProps.children,n),t.child}(e,t,n);case 10:return Zf(e,t,n);case 9:return function(e,t,n){var r=t.type;void 0===r._context?r!==r.Consumer&&(rp||(rp=!0,s("Rendering <Context> directly is not supported and will be removed in a future major release. Did you mean to render <Context.Consumer> instead?"))):r=r._context;var o=t.pendingProps,a=o.children;"function"!=typeof a&&s("A context consumer was rendered with multiple children, or a child that isn't a function. A context consumer expects a single child that is a function. If you did pass a function, make sure there is no trailing or leading whitespace around it."),Ss(t,n);var i,l=Ds(r,o.unstable_observedBits);return Af.current=t,Ye(!0),i=a(l),Ye(!1),t.effectTag|=1,Wf(e,t,i,n),t.child}(e,t,n);case 14:var b=t.type,w=hs(b,t.pendingProps);if(t.type!==t.elementType){var F=b.propTypes;F&&a(F,w,"prop",je(b),$e)}return Lf(e,t,b,w=hs(b.type,w),r,n);case 15:return jf(e,t,t.type,t.pendingProps,r,n);case 17:var C=t.type,T=t.pendingProps;return function(e,t,n,r,o){var a;return null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),t.tag=1,au(n)?(a=!0,cu(t)):a=!1,Ss(t,o),gc(t,n,r),vc(t,n,r,o),Uf(null,t,n,!0,a,o)}(e,t,C,t.elementType===C?T:hs(C,T),n);case 19:return Gf(e,t,n)}throw Error("Unknown unit of work tag ("+t.tag+"). This error is likely caused by a bug in React. Please file an issue.")}function lp(e){e.effectTag|=4}function up(e){e.effectTag|=128}function sp(e,t){switch(e.tailMode){case"hidden":for(var n=e.tail,r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?e.tail=null:r.sibling=null;break;case"collapsed":for(var o=e.tail,a=null;null!==o;)null!==o.alternate&&(a=o),o=o.sibling;null===a?t||null===e.tail?e.tail=null:e.tail.sibling=null:a.sibling=null}}function cp(e,t,n){var r=t.pendingProps;switch(t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return null;case 1:return au(t.type)&&iu(t),null;case 3:Wc(t),lu(t);var o=t.stateNode;if(o.pendingContext&&(o.context=o.pendingContext,o.pendingContext=null),null===e||null===e.child)Tf(t)&&lp(t);return null;case 5:jc(t);var a=Ac(),i=t.type;if(null!==e&&null!=t.stateNode)tp(e,t,i,r,a),e.ref!==t.ref&&up(t);else{if(!r){if(null===t.stateNode)throw Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");return null}var l=Nc();if(Tf(t))(function(e,t,n){var r=sa(e.stateNode,e.type,e.memoizedProps,t,n,e);return e.updateQueue=r,null!==r})(t,a,l)&&lp(t);else{var u=Yo(i,r,a,l,t);ep(u,t),t.stateNode=u,function(e,t,n,r,o){return yo(e,t,n,r),qo(t,n)}(u,i,r,a)&&lp(t)}null!==t.ref&&up(t)}return null;case 6:var s=r;if(e&&null!=t.stateNode){var c=e.memoizedProps;np(0,t,c,s)}else{if("string"!=typeof s&&null===t.stateNode)throw Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");var d=Ac(),f=Nc();Tf(t)?wf(t)&&lp(t):t.stateNode=Xo(s,d,f,t)}return null;case 13:Vc(t);var p=t.memoizedState;if(0!=(64&t.effectTag))return t.expirationTime=n,t;var h=null!==p,m=!1;if(null===e)void 0!==t.memoizedProps.fallback&&Tf(t);else{var g=e.memoizedState;if(m=null!==g,!h&&null!==g){var y=e.child.sibling;if(null!==y){var v=t.firstEffect;null!==v?(t.firstEffect=y,y.nextEffect=v):(t.firstEffect=t.lastEffect=y,y.nextEffect=null),y.effectTag=8}}}if(h&&!m)if(0!=(2&t.mode))null===e&&!0!==t.memoizedProps.unstable_avoidThisFallback||Bc(Mc.current,1)?0===Kp&&(Kp=3):function(){0!==Kp&&3!==Kp||(Kp=4);0!==eh&&null!==qp&&(qm(qp,Yp),Qm(qp,eh))}();return(h||m)&&(t.effectTag|=4),null;case 4:return Wc(t),null;case 10:return xs(t),null;case 17:return au(t.type)&&iu(t),null;case 19:Vc(t);var b=t.memoizedState;if(null===b)return null;var w=0!=(64&t.effectTag),F=b.rendering;if(null===F){if(w)sp(b,!1);else if(!(0===Kp&&(null===e||0==(64&e.effectTag))))for(var C=t.child;null!==C;){var T=qc(C);if(null!==T){w=!0,t.effectTag|=64,sp(b,!1);var k=T.updateQueue;return null!==k&&(t.updateQueue=k,t.effectTag|=4),null===b.lastEffect&&(t.firstEffect=null),t.lastEffect=b.lastEffect,Rc(t,n),Hc(t,Uc(Mc.current,2)),t.child}C=C.sibling}}else{if(!w){var E=qc(F);if(null!==E){t.effectTag|=64,w=!0;var x=E.updateQueue;if(null!==x&&(t.updateQueue=x,t.effectTag|=4),sp(b,!0),null===b.tail&&"hidden"===b.tailMode&&!F.alternate){var R=t.lastEffect=b.lastEffect;return null!==R&&(R.nextEffect=null),null}}else if(2*Pu()-b.renderingStartTime>b.tailExpiration&&n>1){t.effectTag|=64,w=!0,sp(b,!1);var S=n-1;t.expirationTime=t.childExpirationTime=S,hm(S)}}if(b.isBackwards)F.sibling=t.child,t.child=F;else{var D=b.last;null!==D?D.sibling=F:t.child=F,b.last=F}}if(null!==b.tail){if(0===b.tailExpiration){b.tailExpiration=Pu()+500}var _=b.tail;b.rendering=_,b.tail=_.sibling,b.lastEffect=t.lastEffect,b.renderingStartTime=Pu(),_.sibling=null;var O=Mc.current;return Hc(t,O=w?Uc(O,2):zc(O)),_}return null}throw Error("Unknown unit of work tag ("+t.tag+"). This error is likely caused by a bug in React. Please file an issue.")}function dp(e,t){switch(e.tag){case 1:au(e.type)&&iu(e);var n=e.effectTag;return 4096&n?(e.effectTag=-4097&n|64,e):null;case 3:Wc(e),lu(e);var r=e.effectTag;if(0!=(64&r))throw Error("The root failed to unmount after an error. This is likely a bug in React. Please file an issue.");return e.effectTag=-4097&r|64,e;case 5:return jc(e),null;case 13:Vc(e);var o=e.effectTag;return 4096&o?(e.effectTag=-4097&o|64,e):null;case 19:return Vc(e),null;case 4:return Wc(e),null;case 10:return xs(e),null;default:return null}}function fp(e){switch(e.tag){case 1:var t=e.type.childContextTypes;null!=t&&iu(e);break;case 3:Wc(e),lu(e);break;case 5:jc(e);break;case 4:Wc(e);break;case 13:case 19:Vc(e);break;case 10:xs(e)}}function pp(e,t){return{value:e,source:t,stack:ze(t)}}ep=function(e,t,n,r){for(var o,a,i=t.child;null!==i;){if(5===i.tag||6===i.tag)o=e,a=i.stateNode,o.appendChild(a);else if(4===i.tag);else if(null!==i.child){i.child.return=i,i=i.child;continue}if(i===t)return;for(;null===i.sibling;){if(null===i.return||i.return===t)return;i=i.return}i.sibling.return=i.return,i=i.sibling}},tp=function(e,t,n,r,o){var a=e.memoizedProps;if(a!==r){var i=function(e,t,n,r,o,a){var i=a;if(typeof r.children!=typeof n.children&&("string"==typeof r.children||"number"==typeof r.children)){var l=""+r.children,u=Lo(i.ancestorInfo,t);No(null,l,u)}return vo(e,t,n,r,o)}(t.stateNode,n,a,r,o,Nc());t.updateQueue=i,i&&lp(t)}},np=function(e,t,n,r){n!==r&&lp(t)};var hp;hp=new Set;var mp="function"==typeof WeakSet?WeakSet:Set;function gp(e,t){var n=t.source,r=t.stack;null===r&&null!==n&&(r=ze(n));var o={componentName:null!==n?je(n.type):null,componentStack:null!==r?r:"",error:t.value,errorBoundary:null,errorBoundaryName:null,errorBoundaryFound:!1,willRetry:!1};null!==e&&1===e.tag&&(o.errorBoundary=e.stateNode,o.errorBoundaryName=je(e.type),o.errorBoundaryFound=!0,o.willRetry=!0);try{!function(e){var t=e.error,n=e.componentName,r=e.componentStack,o=e.errorBoundaryName,a=e.errorBoundaryFound,i=e.willRetry;if(null!=t&&t._suppressLogging){if(a&&i)return;console.error(t)}var l=""+(n?"The above error occurred in the <"+n+"> component:":"The above error occurred in one of your React components:")+r+"\n\n"+(a&&o?i?"React will try to recreate this component tree from scratch using the error boundary you provided, "+o+".":"This error was initially handled by the error boundary "+o+".\nRecreating the tree from scratch failed so React will unmount the tree.":"Consider adding an error boundary to your tree to customize error handling behavior.\nVisit https://fb.me/react-error-boundaries to learn more about error boundaries.");console.error(l)}(o)}catch(e){setTimeout((function(){throw e}))}}var yp=function(e,t){Il(e,"componentWillUnmount"),t.props=e.memoizedProps,t.state=e.memoizedState,t.componentWillUnmount(),Wl()};function vp(e){var t=e.ref;null!==t&&("function"==typeof t?(b(null,t,null,null),w()&&Zh(e,F())):t.current=null)}function bp(e,t){(b(null,t,null),w())&&Zh(e,F())}function wp(e,t){switch(t.tag){case 0:case 11:case 15:case 22:return;case 1:if(256&t.effectTag&&null!==e){var n=e.memoizedProps,r=e.memoizedState;Il(t,"getSnapshotBeforeUpdate");var o=t.stateNode;t.type!==t.elementType||_f||(o.props!==t.memoizedProps&&s("Expected %s props to match memoized props before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",je(t.type)||"instance"),o.state!==t.memoizedState&&s("Expected %s state to match memoized state before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",je(t.type)||"instance"));var a=o.getSnapshotBeforeUpdate(t.elementType===t.type?n:hs(t.type,n),r),i=hp;void 0!==a||i.has(t.type)||(i.add(t.type),s("%s.getSnapshotBeforeUpdate(): A snapshot value (or null) must be returned. You have returned undefined.",je(t.type))),o.__reactInternalSnapshotBeforeUpdate=a,Wl()}return;case 3:case 5:case 6:case 4:case 17:return}throw Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.")}function Fp(e,t){var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var o=r.next,a=o;do{if((a.tag&e)===e){var i=a.destroy;a.destroy=void 0,void 0!==i&&i()}a=a.next}while(a!==o)}}function Cp(e,t){var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var o=r.next,a=o;do{if((a.tag&e)===e){var i=a.create;a.destroy=i();var l=a.destroy;if(void 0!==l&&"function"!=typeof l){s("An effect function must not return anything besides a function, which is used for clean-up.%s%s",null===l?" You returned null. If your effect does not require clean up, return undefined (or nothing).":"function"==typeof l.then?"\n\nIt looks like you wrote useEffect(async () => ...) or returned a Promise. Instead, write the async function inside your effect and call it immediately:\n\nuseEffect(() => {\n  async function fetchData() {\n    // You can await here\n    const response = await MyAPI.getData(someId);\n    // ...\n  }\n  fetchData();\n}, [someId]); // Or [] if effect doesn't need props or state\n\nLearn more about data fetching with Hooks: https://fb.me/react-hooks-data-fetching":" You returned: "+l,ze(t))}}a=a.next}while(a!==o)}}function Tp(e){if(0!=(512&e.effectTag))switch(e.tag){case 0:case 11:case 15:case 22:Fp(5,e),Cp(5,e)}}function kp(e,t,n,r){switch(n.tag){case 0:case 11:case 15:case 22:return void Cp(3,n);case 1:var o=n.stateNode;if(4&n.effectTag)if(null===t)Il(n,"componentDidMount"),n.type!==n.elementType||_f||(o.props!==n.memoizedProps&&s("Expected %s props to match memoized props before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",je(n.type)||"instance"),o.state!==n.memoizedState&&s("Expected %s state to match memoized state before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",je(n.type)||"instance")),o.componentDidMount(),Wl();else{var a=n.elementType===n.type?t.memoizedProps:hs(n.type,t.memoizedProps),i=t.memoizedState;Il(n,"componentDidUpdate"),n.type!==n.elementType||_f||(o.props!==n.memoizedProps&&s("Expected %s props to match memoized props before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",je(n.type)||"instance"),o.state!==n.memoizedState&&s("Expected %s state to match memoized state before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",je(n.type)||"instance")),o.componentDidUpdate(a,i,o.__reactInternalSnapshotBeforeUpdate),Wl()}var l=n.updateQueue;return void(null!==l&&(n.type!==n.elementType||_f||(o.props!==n.memoizedProps&&s("Expected %s props to match memoized props before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",je(n.type)||"instance"),o.state!==n.memoizedState&&s("Expected %s state to match memoized state before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",je(n.type)||"instance")),Vs(0,l,o)));case 3:var u=n.updateQueue;if(null!==u){var c=null;if(null!==n.child)switch(n.child.tag){case 5:case 1:c=n.child.stateNode}Vs(0,u,c)}return;case 5:var d=n.stateNode;if(null===t&&4&n.effectTag)!function(e,t,n,r){qo(t,n)&&e.focus()}(d,n.type,n.memoizedProps);return;case 6:case 4:return;case 12:var f=n.memoizedProps.onRender;return void("function"==typeof f&&f(n.memoizedProps.id,null===t?"mount":"update",n.actualDuration,n.treeBaseDuration,n.actualStartTime,lf,e.memoizedInteractions));case 13:return void function(e,t){if(null===t.memoizedState){var n=t.alternate;if(null!==n){var r=n.memoizedState;if(null!==r){var o=r.dehydrated;null!==o&&function(e){Qn(e)}(o)}}}}(0,n);case 19:case 17:case 20:case 21:return}throw Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.")}function Ep(e){var t=e.ref;if(null!==t){var n,r=e.stateNode;switch(e.tag){case 5:n=r;break;default:n=r}"function"==typeof t?t(n):(t.hasOwnProperty("current")||s("Unexpected ref object provided for %s. Use either a ref-setter function or React.createRef().%s",je(e.type),ze(e)),t.current=n)}}function xp(e){var t=e.ref;null!==t&&("function"==typeof t?t(null):t.current=null)}function Rp(e,t,n){var r;switch(r=t,"function"==typeof Cm&&Cm(r),t.tag){case 0:case 11:case 14:case 15:case 22:var o=t.updateQueue;if(null!==o){var a=o.lastEffect;if(null!==a){var i=a.next;Wu(n>97?97:n,(function(){var e=i;do{var n=e.destroy;void 0!==n&&bp(t,n),e=e.next}while(e!==i)}))}}return;case 1:vp(t);var l=t.stateNode;return void("function"==typeof l.componentWillUnmount&&function(e,t){b(null,yp,null,e,t),w()&&Zh(e,F())}(t,l));case 5:return void vp(t);case 4:return void Op(e,t,n);case 20:case 18:case 21:return}}function Sp(e,t,n){for(var r=t;;)if(Rp(e,r,n),null===r.child||4===r.tag){if(r===t)return;for(;null===r.sibling;){if(null===r.return||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}else r.child.return=r,r=r.child}function Dp(e){return 5===e.tag||3===e.tag||4===e.tag}function _p(e){var t,n,r=function(e){for(var t=e.return;null!==t;){if(Dp(t))return t;t=t.return}throw Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.")}(e),o=r.stateNode;switch(r.tag){case 5:t=o,n=!1;break;case 3:case 4:t=o.containerInfo,n=!0;break;case 20:default:throw Error("Invalid host parent fiber. This error is likely caused by a bug in React. Please file an issue.")}16&r.effectTag&&(ea(t),r.effectTag&=-17);var a=function(e){var t=e;e:for(;;){for(;null===t.sibling;){if(null===t.return||Dp(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;5!==t.tag&&6!==t.tag&&18!==t.tag;){if(2&t.effectTag)continue e;if(null===t.child||4===t.tag)continue e;t.child.return=t,t=t.child}if(!(2&t.effectTag))return t.stateNode}}(e);n?function e(t,n,r){var o=t.tag,a=5===o||6===o;if(a){var i=a?t.stateNode:t.stateNode.instance;n?function(e,t,n){8===e.nodeType?e.parentNode.insertBefore(t,n):e.insertBefore(t,n)}(r,i,n):function(e,t){var n;8===e.nodeType?(n=e.parentNode).insertBefore(t,e):(n=e).appendChild(t);var r=e._reactRootContainer;null==r&&null===n.onclick&&go(n)}(r,i)}else if(4===o);else{var l=t.child;if(null!==l){e(l,n,r);for(var u=l.sibling;null!==u;)e(u,n,r),u=u.sibling}}}(e,a,t):function e(t,n,r){var o=t.tag,a=5===o||6===o;if(a){var i=a?t.stateNode:t.stateNode.instance;n?function(e,t,n){e.insertBefore(t,n)}(r,i,n):function(e,t){e.appendChild(t)}(r,i)}else if(4===o);else{var l=t.child;if(null!==l){e(l,n,r);for(var u=l.sibling;null!==u;)e(u,n,r),u=u.sibling}}}(e,a,t)}function Op(e,t,n){for(var r,o,a,i,l=t,u=!1;;){if(!u){var s=l.return;e:for(;;){if(null===s)throw Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.");var c=s.stateNode;switch(s.tag){case 5:r=c,o=!1;break e;case 3:case 4:r=c.containerInfo,o=!0;break e}s=s.return}u=!0}if(5===l.tag||6===l.tag)Sp(e,l,n),o?(a=r,i=l.stateNode,8===a.nodeType?a.parentNode.removeChild(i):a.removeChild(i)):ta(r,l.stateNode);else if(4===l.tag){if(null!==l.child){r=l.stateNode.containerInfo,o=!0,l.child.return=l,l=l.child;continue}}else if(Rp(e,l,n),null!==l.child){l.child.return=l,l=l.child;continue}if(l===t)return;for(;null===l.sibling;){if(null===l.return||l.return===t)return;4===(l=l.return).tag&&(u=!1)}l.sibling.return=l.return,l=l.sibling}}function Pp(e,t,n){Op(e,t,n),function e(t){var n=t.alternate;t.return=null,t.child=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.alternate=null,t.firstEffect=null,t.lastEffect=null,t.pendingProps=null,t.memoizedProps=null,t.stateNode=null,null!==n&&e(n)}(t)}function Ap(e,t){switch(t.tag){case 0:case 11:case 14:case 15:case 22:return void Fp(3,t);case 1:return;case 5:var n=t.stateNode;if(null!=n){var r=t.memoizedProps,o=null!==e?e.memoizedProps:r,a=t.type,i=t.updateQueue;t.updateQueue=null,null!==i&&function(e,t,n,r,o,a){Ca(e,o),bo(e,t,n,r,o)}(n,i,a,o,r)}return;case 6:if(null===t.stateNode)throw Error("This should have a text node initialized. This error is likely caused by a bug in React. Please file an issue.");var l=t.stateNode,u=t.memoizedProps;null!==e&&e.memoizedProps;return void function(e,t,n){e.nodeValue=n}(l,0,u);case 3:var s=t.stateNode;return void(s.hydrate&&(s.hydrate=!1,Qn(s.containerInfo)));case 12:return;case 13:return function(e){var t,n=e.memoizedState,r=e;null===n?t=!1:(t=!0,r=e.child,nh=Pu());null!==r&&function(e,t){for(var n=e;;){if(5===n.tag){var r=n.stateNode;t?na(r):ra(n.stateNode,n.memoizedProps)}else if(6===n.tag){var o=n.stateNode;t?o.nodeValue="":oa(o,n.memoizedProps)}else{if(13===n.tag&&null!==n.memoizedState&&null===n.memoizedState.dehydrated){var a=n.child.sibling;a.return=n,n=a;continue}if(null!==n.child){n.child.return=n,n=n.child;continue}}if(n===e)return;for(;null===n.sibling;){if(null===n.return||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}}(r,t)}(t),void Ip(t);case 19:return void Ip(t);case 17:return}throw Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.")}function Ip(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new mp),t.forEach((function(t){var r=tm.bind(null,e,t);n.has(t)||(!0!==t.__reactDoNotTraceInteractions&&(r=i.unstable_wrap(r)),n.add(t),t.then(r,r))}))}}function Wp(e){ea(e.stateNode)}var Np="function"==typeof WeakMap?WeakMap:Map;function Lp(e,t,n){var r=Ns(n,null);r.tag=3,r.payload={element:null};var o=t.value;return r.callback=function(){Xh(o),gp(e,t)},r}function jp(e,t,n){var r=Ns(n,null);r.tag=3;var o=e.type.getDerivedStateFromError;if("function"==typeof o){var a=t.value;r.payload=function(){return gp(e,t),o(a)}}var i=e.stateNode;return null!==i&&"function"==typeof i.componentDidCatch?r.callback=function(){var n;cs(e),"function"!=typeof o&&(n=this,null===ih?ih=new Set([n]):ih.add(n),gp(e,t));var r=t.value,a=t.stack;this.componentDidCatch(r,{componentStack:null!==a?a:""}),"function"!=typeof o&&e.expirationTime!==Bu&&s("%s: Error boundaries should implement getDerivedStateFromError(). In that method, return a state update to display an error message or fallback UI.",je(e.type)||"Unknown")}:r.callback=function(){cs(e)},r}function Mp(e,t,n){var r,o=e.pingCache;if(null===o?(o=e.pingCache=new Np,r=new Set,o.set(n,r)):void 0===(r=o.get(n))&&(r=new Set,o.set(n,r)),!r.has(t)){r.add(t);var a=em.bind(null,e,n,t);n.then(a,a)}}function Bp(e,t,n,r,o){if(n.effectTag|=2048,n.firstEffect=n.lastEffect=null,null!==r&&"object"==typeof r&&"function"==typeof r.then){var a=r;if(0==(2&n.mode)){var i=n.alternate;i?(n.updateQueue=i.updateQueue,n.memoizedState=i.memoizedState,n.expirationTime=i.expirationTime):(n.updateQueue=null,n.memoizedState=null)}var l=Bc(Mc.current,1),u=t;do{if(13===u.tag&&$c(u,l)){var s=u.updateQueue;if(null===s){var c=new Set;c.add(a),u.updateQueue=c}else s.add(a);if(0==(2&u.mode)){if(u.effectTag|=64,n.effectTag&=-2981,1===n.tag)if(null===n.alternate)n.tag=17;else{var d=Ns(Bu,null);d.tag=Ps,Ls(n,d)}return void(n.expirationTime=Bu)}return Mp(e,o,a),u.effectTag|=4096,void(u.expirationTime=o)}u=u.return}while(null!==u);r=new Error((je(n.type)||"A React component")+" suspended while rendering, but no fallback UI was specified.\n\nAdd a <Suspense fallback=...> component higher in the tree to provide a loading indicator or placeholder to display."+ze(n))}5!==Kp&&(Kp=2),r=pp(r,n);var f=t;do{switch(f.tag){case 3:var p=r;return f.effectTag|=4096,f.expirationTime=o,void js(f,Lp(f,p,o));case 1:var h=r,m=f.type,g=f.stateNode;if(0==(64&f.effectTag)&&("function"==typeof m.getDerivedStateFromError||null!==g&&"function"==typeof g.componentDidCatch&&!Jh(g)))return f.effectTag|=4096,f.expirationTime=o,void js(f,jp(f,h,o))}f=f.return}while(null!==f)}var zp=Math.ceil,Up=l.ReactCurrentDispatcher,Hp=l.ReactCurrentOwner,Vp=l.IsSomeRendererActing,$p=0,qp=null,Qp=null,Yp=0,Kp=0,Jp=null,Xp=Bu,Gp=Bu,Zp=null,eh=0,th=!1,nh=0,rh=null,oh=!1,ah=null,ih=null,lh=!1,uh=null,sh=90,ch=0,dh=null,fh=0,ph=null,hh=0,mh=null,gh=null,yh=0;function vh(){return 0!=(48&$p)?zu(Pu()):0!==yh?yh:yh=zu(Pu())}function bh(e,t,n){var r=t.mode;if(0==(2&r))return Bu;var o,a=Au();if(0==(4&r))return 99===a?Bu:1073741822;if(0!=(16&$p))return Yp;if(null!==n)o=function(e,t){return Hu(e,t,250)}(e,0|n.timeoutMs||5e3);else switch(a){case 99:o=Bu;break;case 98:o=Vu(e);break;case 97:case 96:o=function(e){return Hu(e,5e3,250)}(e);break;case 95:o=2;break;default:throw Error("Expected a valid priority level")}return null!==qp&&o===Yp&&(o-=1),o}var wh=function(e,t){!function(){if(fh>50)throw fh=0,ph=null,Error("Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.");hh>50&&(hh=0,s("Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render."))}(),function(e){if(He&&0!=(16&$p))switch(e.tag){case 0:case 11:case 15:var t=Qp&&je(Qp.type)||"Unknown",n=t;if(!im.has(n))im.add(n),s("Cannot update a component (`%s`) while rendering a different component (`%s`). To locate the bad setState() call inside `%s`, follow the stack trace as described in https://fb.me/setstate-in-render",je(e.type)||"Unknown",t,t);break;case 1:lm||(s("Cannot update during an existing state transition (such as within `render`). Render methods should be a pure function of props and state."),lm=!0)}}(e);var n=Fh(e,t);if(null!==n){var r,o;r=e,o=t,null!==qp&&o>Yp&&(mh=r),pl&&(hl=!0),null!==dl&&"componentWillMount"!==dl&&"componentWillReceiveProps"!==dl&&(ml=!0);var a=Au();if(t===Bu?0!=(8&$p)&&0==(48&$p)?(gm(n,t),Eh(n)):(Th(n),gm(n,t),0===$p&&ju()):(Th(n),gm(n,t)),0!=(4&$p)&&(98===a||99===a))if(null===dh)dh=new Map([[n,t]]);else{var i=dh.get(n);(void 0===i||i>t)&&dh.set(n,t)}}else!function(e){var t=e.tag;if(3!==t&&1!==t&&0!==t&&11!==t&&14!==t&&15!==t&&22!==t)return;var n=je(e.type)||"ReactComponent";if(null!==am){if(am.has(n))return;am.add(n)}else am=new Set([n]);s("Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in %s.%s",1===t?"the componentWillUnmount method":"a useEffect cleanup function",ze(e))}(e)};function Fh(e,t){e.expirationTime<t&&(e.expirationTime=t);var n=e.alternate;null!==n&&n.expirationTime<t&&(n.expirationTime=t);var r=e.return,o=null;if(null===r&&3===e.tag)o=e.stateNode;else for(;null!==r;){if(n=r.alternate,r.childExpirationTime<t?(r.childExpirationTime=t,null!==n&&n.childExpirationTime<t&&(n.childExpirationTime=t)):null!==n&&n.childExpirationTime<t&&(n.childExpirationTime=t),null===r.return&&3===r.tag){o=r.stateNode;break}r=r.return}return null!==o&&(qp===o&&(Nh(t),4===Kp&&qm(o,Yp)),Qm(o,t)),o}function Ch(e){var t=e.lastExpiredTime;if(0!==t)return t;var n=e.firstPendingTime;if(!$m(e,n))return n;var r=e.lastPingedTime,o=e.nextKnownPendingLevel,a=r>o?r:o;return a<=2&&n!==a?0:a}function Th(e){if(0!==e.lastExpiredTime)return e.callbackExpirationTime=Bu,e.callbackPriority=99,void(e.callbackNode=Lu(Eh.bind(null,e)));var t=Ch(e),n=e.callbackNode;if(0!==t){var r,o=$u(vh(),t);if(null!==n){var a=e.callbackPriority;if(e.callbackExpirationTime===t&&a>=o)return;!function(e){e!==Eu&&mu(e)}(n)}e.callbackExpirationTime=t,e.callbackPriority=o,r=t===Bu?Lu(Eh.bind(null,e)):Nu(o,kh.bind(null,e),{timeout:Uu(t)-Pu()}),e.callbackNode=r}else null!==n&&(e.callbackNode=null,e.callbackExpirationTime=0,e.callbackPriority=90)}function kh(e,t){if(yh=0,t)return Ym(e,vh()),Th(e),null;var n=Ch(e);if(0!==n){var r=e.callbackNode;if(0!=(48&$p))throw Error("Should not already be working.");if(Yh(),e===qp&&n===Yp||(Dh(e,n),ym(e,n)),null!==Qp){var o=$p;$p|=16;var a=Oh(),i=Ah(e);for(Nl(Qp);;)try{jh();break}catch(t){_h(e,t)}if(Cs(),$p=o,Ph(a),Ih(i),1===Kp){var l=Jp;throw rm(),Dh(e,n),qm(e,n),Th(e),l}if(null!==Qp)rm();else{nm();var u=e.finishedWork=e.current.alternate;e.finishedExpirationTime=n,function(e,t,n,r){switch(qp=null,n){case 0:case 1:throw Error("Root did not complete. This is a bug in React.");case 2:Ym(e,r>2?2:r);break;case 3:qm(e,r);var o=e.lastSuspendedTime;if(r===o&&(e.nextKnownPendingLevel=zh(t)),Xp===Bu&&!um.current){var a=nh+500-Pu();if(a>10){if(th){var i=e.lastPingedTime;if(0===i||i>=r){e.lastPingedTime=r,Dh(e,r);break}}var l=Ch(e);if(0!==l&&l!==r)break;if(0!==o&&o!==r){e.lastPingedTime=o;break}e.timeoutHandle=Go(Hh.bind(null,e),a);break}}Hh(e);break;case 4:qm(e,r);var u=e.lastSuspendedTime;if(r===u&&(e.nextKnownPendingLevel=zh(t)),!um.current){if(th){var s=e.lastPingedTime;if(0===s||s>=r){e.lastPingedTime=r,Dh(e,r);break}}var c,d=Ch(e);if(0!==d&&d!==r)break;if(0!==u&&u!==r){e.lastPingedTime=u;break}if(Gp!==Bu)c=Uu(Gp)-Pu();else if(Xp===Bu)c=0;else{var f=function(e){return Uu(e)-5e3}(Xp),p=Pu(),h=Uu(r)-p,m=p-f;m<0&&(m=0),c=function(e){return e<120?120:e<480?480:e<1080?1080:e<1920?1920:e<3e3?3e3:e<4320?4320:1960*zp(e/1960)}(m)-m,h<c&&(c=h)}if(c>10){e.timeoutHandle=Go(Hh.bind(null,e),c);break}}Hh(e);break;case 5:if(!um.current&&Xp!==Bu&&null!==Zp){var g=function(e,t,n){var r=0|n.busyMinDurationMs;if(r<=0)return 0;var o=0|n.busyDelayMs,a=Pu(),i=function(e,t){return Uu(e)-(0|t.timeoutMs||5e3)}(e,n),l=a-i;if(l<=o)return 0;return o+r-l}(Xp,0,Zp);if(g>10){qm(e,r),e.timeoutHandle=Go(Hh.bind(null,e),g);break}}Hh(e);break;default:throw Error("Unknown root exit status.")}}(e,u,Kp,n)}if(Th(e),e.callbackNode===r)return kh.bind(null,e)}}return null}function Eh(e){var t=e.lastExpiredTime,n=0!==t?t:Bu;if(0!=(48&$p))throw Error("Should not already be working.");if(Yh(),e===qp&&n===Yp||(Dh(e,n),ym(e,n)),null!==Qp){var r=$p;$p|=16;var o=Oh(),a=Ah(e);for(Nl(Qp);;)try{Lh();break}catch(t){_h(e,t)}if(Cs(),$p=r,Ph(o),Ih(a),1===Kp){var i=Jp;throw rm(),Dh(e,n),qm(e,n),Th(e),i}if(null!==Qp)throw Error("Cannot commit an incomplete root. This error is likely caused by a bug in React. Please file an issue.");nm(),e.finishedWork=e.current.alternate,e.finishedExpirationTime=n,function(e){qp=null,Hh(e)}(e),Th(e)}return null}function xh(e,t){var n=$p;$p|=1;try{return e(t)}finally{0===($p=n)&&ju()}}function Rh(e,t){var n=$p;$p&=-2,$p|=8;try{return e(t)}finally{0===($p=n)&&ju()}}function Sh(e,t){if(0!=(48&$p))throw Error("flushSync was called from inside a lifecycle method. It cannot be called when React is already rendering.");var n=$p;$p|=1;try{return Wu(99,e.bind(null,t))}finally{$p=n,ju()}}function Dh(e,t){e.finishedWork=null,e.finishedExpirationTime=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,Zo(n)),null!==Qp)for(var r=Qp.return;null!==r;)fp(r),r=r.return;qp=e,Qp=Wm(e.current,null),Yp=t,Kp=0,Jp=null,Xp=Bu,Gp=Bu,Zp=null,eh=0,th=!1,gh=null,qu.discardPendingWarnings()}function _h(e,t){for(;;){try{if(Cs(),pd(),qe(),null===Qp||null===Qp.return)return Kp=1,Jp=t,Qp=null,null;8&Qp.mode&&ff(Qp,!0),Bp(e,Qp.return,Qp,t,Yp),Qp=Bh(Qp)}catch(e){t=e;continue}return}}function Oh(e){var t=Up.current;return Up.current=Kd,null===t?Kd:t}function Ph(e){Up.current=e}function Ah(e){var t=i.__interactionsRef.current;return i.__interactionsRef.current=e.memoizedInteractions,t}function Ih(e){i.__interactionsRef.current=e}function Wh(e,t){e<Xp&&e>2&&(Xp=e),null!==t&&e<Gp&&e>2&&(Gp=e,Zp=t)}function Nh(e){e>eh&&(eh=e)}function Lh(){for(;null!==Qp;)Qp=Mh(Qp)}function jh(){for(;null!==Qp&&!xu();)Qp=Mh(Qp)}function Mh(e){var t,n=e.alternate;return _l(e),Qe(e),0!=(8&e.mode)?(cf(e),t=om(n,e,Yp),ff(e,!0)):t=om(n,e,Yp),qe(),e.memoizedProps=e.pendingProps,null===t&&(t=Bh(e)),Hp.current=null,t}function Bh(e){Qp=e;do{var t=Qp.alternate,n=Qp.return;if(0==(2048&Qp.effectTag)){Qe(Qp);var r=void 0;if(0==(8&Qp.mode)?r=cp(t,Qp,Yp):(cf(Qp),r=cp(t,Qp,Yp),ff(Qp,!1)),Pl(Qp),qe(),Uh(Qp),null!==r)return r;if(null!==n&&0==(2048&n.effectTag))null===n.firstEffect&&(n.firstEffect=Qp.firstEffect),null!==Qp.lastEffect&&(null!==n.lastEffect&&(n.lastEffect.nextEffect=Qp.firstEffect),n.lastEffect=Qp.lastEffect),Qp.effectTag>1&&(null!==n.lastEffect?n.lastEffect.nextEffect=Qp:n.firstEffect=Qp,n.lastEffect=Qp)}else{var o=dp(Qp);if(0!=(8&Qp.mode)){ff(Qp,!1);for(var a=Qp.actualDuration,i=Qp.child;null!==i;)a+=i.actualDuration,i=i.sibling;Qp.actualDuration=a}if(null!==o)return Al(Qp),o.effectTag&=2047,o;Pl(Qp),null!==n&&(n.firstEffect=n.lastEffect=null,n.effectTag|=2048)}var l=Qp.sibling;if(null!==l)return l;Qp=n}while(null!==Qp);return 0===Kp&&(Kp=5),null}function zh(e){var t=e.expirationTime,n=e.childExpirationTime;return t>n?t:n}function Uh(e){if(1===Yp||1!==e.childExpirationTime){var t=0;if(0!=(8&e.mode)){for(var n=e.actualDuration,r=e.selfBaseDuration,o=null===e.alternate||e.child!==e.alternate.child,a=e.child;null!==a;){var i=a.expirationTime,l=a.childExpirationTime;i>t&&(t=i),l>t&&(t=l),o&&(n+=a.actualDuration),r+=a.treeBaseDuration,a=a.sibling}e.actualDuration=n,e.treeBaseDuration=r}else for(var u=e.child;null!==u;){var s=u.expirationTime,c=u.childExpirationTime;s>t&&(t=s),c>t&&(t=c),u=u.sibling}e.childExpirationTime=t}}function Hh(e){var t=Au();return Wu(99,Vh.bind(null,e,t)),null}function Vh(e,t){do{Yh()}while(null!==uh);if(qu.flushLegacyContextWarning(),qu.flushPendingUnsafeLifecycleWarnings(),0!=(48&$p))throw Error("Should not already be working.");var n,r=e.finishedWork,o=e.finishedExpirationTime;if(null===r)return null;if(e.finishedWork=null,e.finishedExpirationTime=0,r===e.current)throw Error("Cannot commit the same tree as before. This error is likely caused by a bug in React. Please file an issue.");if(e.callbackNode=null,e.callbackExpirationTime=0,e.callbackPriority=90,e.nextKnownPendingLevel=0,sl&&(pl=!0,hl=!1,vl.clear(),wl("(Committing Changes)")),function(e,t,n){e.firstPendingTime=n,t<=e.lastSuspendedTime?e.firstSuspendedTime=e.lastSuspendedTime=e.nextKnownPendingLevel=0:t<=e.firstSuspendedTime&&(e.firstSuspendedTime=t-1);t<=e.lastPingedTime&&(e.lastPingedTime=0);t<=e.lastExpiredTime&&(e.lastExpiredTime=0)}(e,o,zh(r)),e===qp&&(qp=null,Qp=null,Yp=0),r.effectTag>1?null!==r.lastEffect?(r.lastEffect.nextEffect=r,n=r.firstEffect):n=r:n=r.firstEffect,null!==n){var a=$p;$p|=32;var i=Ah(e);Hp.current=null,jl(),Qo(e.containerInfo),rh=n;do{if(b(null,$h,null),w()){if(null===rh)throw Error("Should be working on an effect.");var l=F();Zh(rh,l),rh=rh.nextEffect}}while(null!==rh);Ml(),sf(),Bl(),rh=n;do{if(b(null,qh,null,e,t),w()){if(null===rh)throw Error("Should be working on an effect.");var u=F();Zh(rh,u),rh=rh.nextEffect}}while(null!==rh);zl(),e.containerInfo,Wo($o),or(Vo),Vo=null,$o=null,e.current=r,Ul(),rh=n;do{if(b(null,Qh,null,e,o),w()){if(null===rh)throw Error("Should be working on an effect.");var s=F();Zh(rh,s),rh=rh.nextEffect}}while(null!==rh);Hl(),rh=null,Ru(),Ih(i),$p=a}else e.current=r,jl(),Ml(),sf(),Bl(),zl(),Ul(),Hl();!function(){if(sl){var e=null;hl?e="Lifecycle hook scheduled a cascading update":gl>0&&(e="Caused by a cascading update in earlier commit"),hl=!1,gl++,pl=!1,vl.clear(),Fl("(Committing Changes)","(Committing Changes)",e)}}();var c=lh;if(lh)lh=!1,uh=e,ch=o,sh=t;else for(rh=n;null!==rh;){var d=rh.nextEffect;rh.nextEffect=null,rh=d}var f=e.firstPendingTime;if(0!==f){if(null!==gh){var p=gh;gh=null;for(var h=0;h<p.length;h++)mm(e,p[h],e.memoizedInteractions)}gm(e,f)}else ih=null;if(c||vm(e,o),f===Bu?e===ph?fh++:(fh=0,ph=e):fh=0,function(e,t){"function"==typeof Fm&&Fm(e,t)}(r.stateNode,o),Th(e),oh){oh=!1;var m=ah;throw ah=null,m}return 0!=(8&$p)||ju(),null}function $h(){for(;null!==rh;){var e=rh.effectTag;if(0!=(256&e))Qe(rh),Dl(),wp(rh.alternate,rh),qe();0!=(512&e)&&(lh||(lh=!0,Nu(97,(function(){return Yh(),null})))),rh=rh.nextEffect}}function qh(e,t){for(;null!==rh;){Qe(rh);var n=rh.effectTag;if(16&n&&Wp(rh),128&n){var r=rh.alternate;null!==r&&xp(r)}switch(1038&n){case 2:_p(rh),rh.effectTag&=-3;break;case 6:_p(rh),rh.effectTag&=-3,Ap(rh.alternate,rh);break;case 1024:rh.effectTag&=-1025;break;case 1028:rh.effectTag&=-1025,Ap(rh.alternate,rh);break;case 4:Ap(rh.alternate,rh);break;case 8:Pp(e,rh,t)}Dl(),qe(),rh=rh.nextEffect}}function Qh(e,t){for(;null!==rh;){Qe(rh);var n=rh.effectTag;if(36&n)Dl(),kp(e,rh.alternate,rh);128&n&&(Dl(),Ep(rh)),qe(),rh=rh.nextEffect}}function Yh(){if(90!==sh){var e=sh>97?97:sh;return sh=90,Wu(e,Kh)}}function Kh(){if(null===uh)return!1;var e=uh,t=ch;if(uh=null,ch=0,0!=(48&$p))throw Error("Cannot flush passive effects while already rendering.");var n=$p;$p|=32;for(var r=Ah(e),o=e.current.firstEffect;null!==o;){if(Qe(o),b(null,Tp,null,o),w()){if(null===o)throw Error("Should be working on an effect.");Zh(o,F())}qe();var a=o.nextEffect;o.nextEffect=null,o=a}return Ih(r),vm(e,t),$p=n,ju(),hh=null===uh?0:hh+1,!0}function Jh(e){return null!==ih&&ih.has(e)}var Xh=function(e){oh||(oh=!0,ah=e)};function Gh(e,t,n){Ls(e,Lp(e,pp(n,t),Bu));var r=Fh(e,Bu);null!==r&&(Th(r),gm(r,Bu))}function Zh(e,t){if(3!==e.tag)for(var n=e.return;null!==n;){if(3===n.tag)return void Gh(n,e,t);if(1===n.tag){var r=n.type,o=n.stateNode;if("function"==typeof r.getDerivedStateFromError||"function"==typeof o.componentDidCatch&&!Jh(o)){Ls(n,jp(n,pp(t,e),Bu));var a=Fh(n,Bu);return void(null!==a&&(Th(a),gm(a,Bu)))}}n=n.return}else Gh(e,e,t)}function em(e,t,n){var r=e.pingCache;if(null!==r&&r.delete(t),qp!==e||Yp!==n){if($m(e,n)){var o=e.lastPingedTime;0!==o&&o<n||(e.lastPingedTime=n,Th(e),gm(e,n))}}else 4===Kp||3===Kp&&Xp===Bu&&Pu()-nh<500?Dh(e,Yp):th=!0}function tm(e,t){var n;null!==(n=e.stateNode)&&n.delete(t),function(e,t){if(0===t){t=bh(vh(),e,null)}var n=Fh(e,t);null!==n&&(Th(n),gm(n,t))}(e,0)}function nm(){Ll(mh,!0),mh=null}function rm(){Ll(mh,!1),mh=null}var om,am=null;om=function(e,t,n){var r=Um(null,t);try{return ip(e,t,n)}catch(o){if(null!==o&&"object"==typeof o&&"function"==typeof o.then)throw o;if(Cs(),pd(),fp(t),Um(t,r),8&t.mode&&cf(t),b(null,ip,null,e,t,n),w())throw F();throw o}};var im,lm=!1;im=new Set;var um={current:!1};function sm(e){!0===Vp.current&&!0!==um.current&&s("It looks like you're using the wrong act() around your test interactions.\nBe sure to use the matching version of act() corresponding to your renderer:\n\n// for react-dom:\nimport {act} from 'react-dom/test-utils';\n// ...\nact(() => ...);\n\n// for react-test-renderer:\nimport TestRenderer from 'react-test-renderer';\nconst {act} = TestRenderer;\n// ...\nact(() => ...);%s",ze(e))}function cm(e){0!=(1&e.mode)&&!1===Vp.current&&!1===um.current&&s("An update to %s ran an effect, but was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://fb.me/react-wrap-tests-with-act%s",je(e.type),ze(e))}var dm=function(e){0===$p&&!1===Vp.current&&!1===um.current&&s("An update to %s inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://fb.me/react-wrap-tests-with-act%s",je(e.type),ze(e))},fm=!1;function pm(e,t){return 1e3*t+e.interactionThreadID}function hm(e){null===gh?gh=[e]:gh.push(e)}function mm(e,t,n){if(n.size>0){var r=e.pendingInteractionMap,o=r.get(t);null!=o?n.forEach((function(e){o.has(e)||e.__count++,o.add(e)})):(r.set(t,new Set(n)),n.forEach((function(e){e.__count++})));var a=i.__subscriberRef.current;if(null!==a){var l=pm(e,t);a.onWorkScheduled(n,l)}}}function gm(e,t){mm(e,t,i.__interactionsRef.current)}function ym(e,t){var n=new Set;if(e.pendingInteractionMap.forEach((function(e,r){r>=t&&e.forEach((function(e){return n.add(e)}))})),e.memoizedInteractions=n,n.size>0){var r=i.__subscriberRef.current;if(null!==r){var o=pm(e,t);try{r.onWorkStarted(n,o)}catch(e){Nu(99,(function(){throw e}))}}}}function vm(e,t){var n,r=e.firstPendingTime;try{if(null!==(n=i.__subscriberRef.current)&&e.memoizedInteractions.size>0){var o=pm(e,t);n.onWorkStopped(e.memoizedInteractions,o)}}catch(e){Nu(99,(function(){throw e}))}finally{var a=e.pendingInteractionMap;a.forEach((function(e,t){t>r&&(a.delete(t),e.forEach((function(e){if(e.__count--,null!==n&&0===e.__count)try{n.onInteractionScheduledWorkCompleted(e)}catch(e){Nu(99,(function(){throw e}))}})))}))}}var bm,wm=null,Fm=null,Cm=null,Tm=!1,km="undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__;function Em(e){if("undefined"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)return!1;var t=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(t.isDisabled)return!0;if(!t.supportsFiber)return s("The installed version of React DevTools is too old and will not work with the current version of React. Please update React DevTools. https://fb.me/react-devtools"),!0;try{var n=t.inject(e);"function"==typeof t.onScheduleFiberRoot&&(wm=function(e,r){try{t.onScheduleFiberRoot(n,e,r)}catch(e){Tm||(Tm=!0,s("React instrumentation encountered an error: %s",e))}}),Fm=function(e,r){try{var o=64==(64&e.current.effectTag),a=$u(zu(Pu()),r);t.onCommitFiberRoot(n,e,a,o)}catch(e){Tm||(Tm=!0,s("React instrumentation encountered an error: %s",e))}},Cm=function(e){try{t.onCommitFiberUnmount(n,e)}catch(e){Tm||(Tm=!0,s("React instrumentation encountered an error: %s",e))}}}catch(e){s("React instrumentation encountered an error: %s.",e)}return!0}bm=!1;try{var xm=Object.preventExtensions({}),Rm=new Map([[xm,null]]),Sm=new Set([xm]);Rm.set(0,0),Sm.add(0)}catch(e){bm=!0}var Dm=1;function _m(e,t,n,r){this.tag=e,this.key=n,this.elementType=null,this.type=null,this.stateNode=null,this.return=null,this.child=null,this.sibling=null,this.index=0,this.ref=null,this.pendingProps=t,this.memoizedProps=null,this.updateQueue=null,this.memoizedState=null,this.dependencies=null,this.mode=r,this.effectTag=0,this.nextEffect=null,this.firstEffect=null,this.lastEffect=null,this.expirationTime=0,this.childExpirationTime=0,this.alternate=null,this.actualDuration=Number.NaN,this.actualStartTime=Number.NaN,this.selfBaseDuration=Number.NaN,this.treeBaseDuration=Number.NaN,this.actualDuration=0,this.actualStartTime=-1,this.selfBaseDuration=0,this.treeBaseDuration=0,this._debugID=Dm++,this._debugIsCurrentlyTiming=!1,this._debugSource=null,this._debugOwner=null,this._debugNeedsRemount=!1,this._debugHookTypes=null,bm||"function"!=typeof Object.preventExtensions||Object.preventExtensions(this)}var Om,Pm,Am=function(e,t,n,r){return new _m(e,t,n,r)};function Im(e){var t=e.prototype;return!(!t||!t.isReactComponent)}function Wm(e,t){var n=e.alternate;null===n?((n=Am(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n._debugID=e._debugID,n._debugSource=e._debugSource,n._debugOwner=e._debugOwner,n._debugHookTypes=e._debugHookTypes,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.effectTag=0,n.nextEffect=null,n.firstEffect=null,n.lastEffect=null,n.actualDuration=0,n.actualStartTime=-1),n.childExpirationTime=e.childExpirationTime,n.expirationTime=e.expirationTime,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue;var r=e.dependencies;switch(n.dependencies=null===r?null:{expirationTime:r.expirationTime,firstContext:r.firstContext,responders:r.responders},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.selfBaseDuration=e.selfBaseDuration,n.treeBaseDuration=e.treeBaseDuration,n._debugNeedsRemount=e._debugNeedsRemount,n.tag){case 2:case 0:case 15:n.type=is(e.type);break;case 1:n.type=ls(e.type);break;case 11:n.type=us(e.type)}return n}function Nm(e,t){e.effectTag&=2,e.nextEffect=null,e.firstEffect=null,e.lastEffect=null;var n=e.alternate;if(null===n)e.childExpirationTime=0,e.expirationTime=t,e.child=null,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.selfBaseDuration=0,e.treeBaseDuration=0;else{e.childExpirationTime=n.childExpirationTime,e.expirationTime=n.expirationTime,e.child=n.child,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue;var r=n.dependencies;e.dependencies=null===r?null:{expirationTime:r.expirationTime,firstContext:r.firstContext,responders:r.responders},e.selfBaseDuration=n.selfBaseDuration,e.treeBaseDuration=n.treeBaseDuration}return e}function Lm(e,t,n,r,o,a){var i,l=2,u=e;if("function"==typeof e)Im(e)?(l=1,u=ls(u)):u=is(u);else if("string"==typeof e)l=5;else e:switch(e){case Te:return Mm(n.children,o,a,t);case Se:l=8,o|=7;break;case ke:l=8,o|=1;break;case Ee:return function(e,t,n,r){"string"==typeof e.id&&"function"==typeof e.onRender||s('Profiler must specify an "id" string and "onRender" function as props');var o=Am(12,e,r,8|t);return o.elementType=Ee,o.type=Ee,o.expirationTime=n,o}(n,o,a,t);case _e:return function(e,t,n,r){var o=Am(13,e,r,t);return o.type=_e,o.elementType=_e,o.expirationTime=n,o}(n,o,a,t);case Oe:return function(e,t,n,r){var o=Am(19,e,r,t);return o.type=Oe,o.elementType=Oe,o.expirationTime=n,o}(n,o,a,t);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case xe:l=10;break e;case Re:l=9;break e;case De:l=11,u=us(u);break e;case Pe:l=14;break e;case Ae:l=16,u=null;break e;case Ie:l=22;break e}var c="";(void 0===e||"object"==typeof e&&null!==e&&0===Object.keys(e).length)&&(c+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var d=r?je(r.type):null;throw d&&(c+="\n\nCheck the render method of `"+d+"`."),Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+(null==e?e:typeof e)+"."+c)}return(i=Am(l,n,t,o)).elementType=e,i.type=u,i.expirationTime=a,i}function jm(e,t,n){var r;r=e._owner;var o=Lm(e.type,e.key,e.props,r,t,n);return o._debugSource=e._source,o._debugOwner=e._owner,o}function Mm(e,t,n,r){var o=Am(7,e,r,t);return o.expirationTime=n,o}function Bm(e,t,n){var r=Am(6,e,null,t);return r.expirationTime=n,r}function zm(e,t,n){var r=null!==e.children?e.children:[],o=Am(4,r,e.key,t);return o.expirationTime=n,o.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},o}function Um(e,t){return null===e&&(e=Am(2,null,null,0)),e.tag=t.tag,e.key=t.key,e.elementType=t.elementType,e.type=t.type,e.stateNode=t.stateNode,e.return=t.return,e.child=t.child,e.sibling=t.sibling,e.index=t.index,e.ref=t.ref,e.pendingProps=t.pendingProps,e.memoizedProps=t.memoizedProps,e.updateQueue=t.updateQueue,e.memoizedState=t.memoizedState,e.dependencies=t.dependencies,e.mode=t.mode,e.effectTag=t.effectTag,e.nextEffect=t.nextEffect,e.firstEffect=t.firstEffect,e.lastEffect=t.lastEffect,e.expirationTime=t.expirationTime,e.childExpirationTime=t.childExpirationTime,e.alternate=t.alternate,e.actualDuration=t.actualDuration,e.actualStartTime=t.actualStartTime,e.selfBaseDuration=t.selfBaseDuration,e.treeBaseDuration=t.treeBaseDuration,e._debugID=t._debugID,e._debugSource=t._debugSource,e._debugOwner=t._debugOwner,e._debugIsCurrentlyTiming=t._debugIsCurrentlyTiming,e._debugNeedsRemount=t._debugNeedsRemount,e._debugHookTypes=t._debugHookTypes,e}function Hm(e,t,n){this.tag=t,this.current=null,this.containerInfo=e,this.pendingChildren=null,this.pingCache=null,this.finishedExpirationTime=0,this.finishedWork=null,this.timeoutHandle=-1,this.context=null,this.pendingContext=null,this.hydrate=n,this.callbackNode=null,this.callbackPriority=90,this.firstPendingTime=0,this.firstSuspendedTime=0,this.lastSuspendedTime=0,this.nextKnownPendingLevel=0,this.lastPingedTime=0,this.lastExpiredTime=0,this.interactionThreadID=i.unstable_getThreadID(),this.memoizedInteractions=new Set,this.pendingInteractionMap=new Map}function Vm(e,t,n,r){var o=new Hm(e,t,n),a=function(e){var t;return t=2===e?7:1===e?3:0,km&&(t|=8),Am(3,null,null,t)}(t);return o.current=a,a.stateNode=o,Is(a),o}function $m(e,t){var n=e.firstSuspendedTime,r=e.lastSuspendedTime;return 0!==n&&n>=t&&r<=t}function qm(e,t){var n=e.firstSuspendedTime,r=e.lastSuspendedTime;n<t&&(e.firstSuspendedTime=t),(r>t||0===n)&&(e.lastSuspendedTime=t),t<=e.lastPingedTime&&(e.lastPingedTime=0),t<=e.lastExpiredTime&&(e.lastExpiredTime=0)}function Qm(e,t){t>e.firstPendingTime&&(e.firstPendingTime=t);var n=e.firstSuspendedTime;0!==n&&(t>=n?e.firstSuspendedTime=e.lastSuspendedTime=e.nextKnownPendingLevel=0:t>=e.lastSuspendedTime&&(e.lastSuspendedTime=t+1),t>e.nextKnownPendingLevel&&(e.nextKnownPendingLevel=t))}function Ym(e,t){var n=e.lastExpiredTime;(0===n||n>t)&&(e.lastExpiredTime=t)}function Km(e,t,n,r){!function(e,t){"function"==typeof wm&&wm(e,t)}(t,e);var a,i=t.current,l=vh();"undefined"!=typeof jest&&(a=i,!1===fm&&void 0===o.unstable_flushAllWithoutAsserting&&(2&a.mode||4&a.mode)&&(fm=!0,s("In Concurrent or Sync modes, the \"scheduler\" module needs to be mocked to guarantee consistent behaviour across tests and browsers. For example, with jest: \njest.mock('scheduler', () => require('scheduler/unstable_mock'));\n\nFor more info, visit https://fb.me/react-mock-scheduler")),sm(i));var u=qs(),c=bh(l,i,u),d=function(e){if(!e)return Xl;var t=en(e),n=fu(t);if(1===t.tag){var r=t.type;if(au(r))return su(t,r,n)}return n}(n);null===t.context?t.context=d:t.pendingContext=d,He&&null!==Ue&&!Om&&(Om=!0,s("Render methods should be a pure function of props and state; triggering nested component updates from render is not allowed. If necessary, trigger nested updates in componentDidUpdate.\n\nCheck the render method of %s.",je(Ue.type)||"Unknown"));var f=Ns(c,u);return f.payload={element:e},null!==(r=void 0===r?null:r)&&("function"!=typeof r&&s("render(...): Expected the last optional `callback` argument to be a function. Instead received: %s.",r),f.callback=r),Ls(i,f),wh(i,c),c}function Jm(e){var t=e.current;if(!t.child)return null;switch(t.child.tag){case 5:default:return t.child.stateNode}}function Xm(e,t){var n=e.memoizedState;null!==n&&null!==n.dehydrated&&n.retryTime<t&&(n.retryTime=t)}function Gm(e,t){Xm(e,t);var n=e.alternate;n&&Xm(n,t)}function Zm(e){var t=function(e){var t=ln(e);if(!t)return null;for(var n=t;;){if(5===n.tag||6===n.tag)return n;if(n.child&&4!==n.tag)n.child.return=n,n=n.child;else{if(n===t)return null;for(;!n.sibling;){if(!n.return||n.return===t)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}}return null}(e);return null===t?null:20===t.tag?t.stateNode.instance:t.stateNode}Om=!1,Pm={};var eg=function(e){return!1};var tg,ng,rg,og,ag=function(e,t,n,o){if(n>=t.length)return o;var a=t[n],i=Array.isArray(e)?e.slice():r({},e);return i[a]=ag(e[a],t,n+1,o),i},ig=function(e,t,n){return ag(e,t,0,n)};tg=function(e,t,n,o){for(var a=e.memoizedState;null!==a&&t>0;)a=a.next,t--;if(null!==a){var i=ig(a.memoizedState,n,o);a.memoizedState=i,a.baseState=i,e.memoizedProps=r({},e.memoizedProps),wh(e,Bu)}},ng=function(e,t,n){e.pendingProps=ig(e.memoizedProps,t,n),e.alternate&&(e.alternate.pendingProps=e.pendingProps),wh(e,Bu)},rg=function(e){wh(e,Bu)},og=function(e){eg=e};l.IsSomeRendererActing;function lg(e,t){this._internalRoot=sg(e,2,t)}function ug(e,t,n){this._internalRoot=sg(e,t,n)}function sg(e,t,n){var r,o=null!=n&&!0===n.hydrate,a=(null!=n&&n.hydrationOptions,function(e,t,n,r){return Vm(e,t,n)}(e,t,o));(r=a.current,e[ha]=r,o&&0!==t)&&function(e,t){var n=Zt(t);In.forEach((function(e){Ln(e,t,n)})),Wn.forEach((function(e){Ln(e,t,n)}))}(0,9===e.nodeType?e:e.ownerDocument);return a}function cg(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}lg.prototype.render=ug.prototype.render=function(e){var t=this._internalRoot;"function"==typeof arguments[1]&&s("render(...): does not support the second callback argument. To execute a side effect after rendering, declare it in a component body with useEffect().");var n=t.containerInfo;if(8!==n.nodeType){var r=Zm(t.current);r&&r.parentNode!==n&&s("render(...): It looks like the React-rendered content of the root container was removed without using React. This is not supported and will cause errors. Instead, call root.unmount() to empty a root's container.")}Km(e,t,null,null)},lg.prototype.unmount=ug.prototype.unmount=function(){"function"==typeof arguments[0]&&s("unmount(...): does not support a callback argument. To execute a side effect after rendering, declare it in a component body with useEffect().");var e=this._internalRoot,t=e.containerInfo;Km(null,e,null,(function(){ga(t)}))};var dg,fg=l.ReactCurrentOwner,pg=!1;function hg(e){return e?9===e.nodeType?e.documentElement:e.firstChild:null}function mg(e,t){var n=t||function(e){var t=hg(e);return!(!t||1!==t.nodeType||!t.hasAttribute("data-reactroot"))}(e);if(!n)for(var r,o=!1;r=e.lastChild;)!o&&1===r.nodeType&&r.hasAttribute("data-reactroot")&&(o=!0,s("render(): Target node has markup rendered by React, but there are unrelated nodes as well. This is most commonly caused by white-space inserted around server-rendered markup.")),e.removeChild(r);return!n||t||pg||(pg=!0,u("render(): Calling ReactDOM.render() to hydrate server-rendered markup will stop working in React v17. Replace the ReactDOM.render() call with ReactDOM.hydrate() if you want React to attach to the server HTML.")),function(e,t){return new ug(e,0,t)}(e,n?{hydrate:!0}:void 0)}function gg(e,t,n,r,o){dg(n),function(e,t){null!==e&&"function"!=typeof e&&s("%s(...): Expected the last optional `callback` argument to be a function. Instead received: %s.",t,e)}(void 0===o?null:o,"render");var a,i=n._reactRootContainer;if(i){if(a=i._internalRoot,"function"==typeof o){var l=o;o=function(){var e=Jm(a);l.call(e)}}Km(t,a,e,o)}else{if(i=n._reactRootContainer=mg(n,r),a=i._internalRoot,"function"==typeof o){var u=o;o=function(){var e=Jm(a);u.call(e)}}Rh((function(){Km(t,a,e,o)}))}return Jm(a)}function yg(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:Ce,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}dg=function(e){if(e._reactRootContainer&&8!==e.nodeType){var t=Zm(e._reactRootContainer._internalRoot.current);t&&t.parentNode!==e&&s("render(...): It looks like the React-rendered content of this container was removed without using React. This is not supported and will cause errors. Instead, call ReactDOM.unmountComponentAtNode to empty a container.")}var n=!!e._reactRootContainer,r=hg(e);!(!r||!ba(r))&&!n&&s("render(...): Replacing React-rendered children with a new root component. If you intended to update the children of this node, you should instead have the existing children update their state and render the new components instead of calling ReactDOM.render."),1===e.nodeType&&e.tagName&&"BODY"===e.tagName.toUpperCase()&&s("render(): Rendering components directly into document.body is discouraged, since its children are often manipulated by third-party scripts and browser extensions. This may lead to subtle reconciliation issues. Try rendering into a container element created for your app.")};yn=function(e){if(13===e.tag){var t=Vu(vh());wh(e,t),Gm(e,t)}},function(e){vn=e}((function(e){13===e.tag&&(wh(e,3),Gm(e,3))})),function(e){bn=e}((function(e){if(13===e.tag){var t=bh(vh(),e,null);wh(e,t),Gm(e,t)}}));var vg=!1;function bg(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!cg(t))throw Error("Target container is not a DOM element.");return yg(e,t,null,n)}"function"==typeof Map&&null!=Map.prototype&&"function"==typeof Map.prototype.forEach&&"function"==typeof Set&&null!=Set.prototype&&"function"==typeof Set.prototype.clear&&"function"==typeof Set.prototype.forEach||s("React depends on Map and Set built-in types. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills"),M=function(e,t,n){switch(t){case"input":return void mt(e,n);case"textarea":return void function(e,t){_t(e,t)}(e,n);case"select":return void function(e,t){var n=e,r=t.value;null!=r&&kt(n,!!t.multiple,r,!1)}(e,n)}},$=xh,q=function(e,t,n,r,o){var a=$p;$p|=4;try{return Wu(98,e.bind(null,t,n,r,o))}finally{0===($p=a)&&ju()}},Q=function(){0==(49&$p)?(function(){if(null!==dh){var e=dh;dh=null,e.forEach((function(e,t){Ym(t,e),Th(t)})),ju()}}(),Yh()):0!=(16&$p)&&s("unstable_flushDiscreteUpdates: Cannot flush updates when React is already rendering.")},Y=function(e,t){var n=$p;$p|=2;try{return e(t)}finally{0===($p=n)&&ju()}};var wg={Events:[ba,wa,Fa,L,A,_a,function(e){cn(e,Da)},H,V,cr,hn,Yh,um]};if(!function(e){var t=e.findFiberByHostInstance,n=l.ReactCurrentDispatcher;return Em(r({},e,{overrideHookState:tg,overrideProps:ng,setSuspenseHandler:og,scheduleUpdate:rg,currentDispatcherRef:n,findHostInstanceByFiber:function(e){var t=un(e);return null===t?null:t.stateNode},findFiberByHostInstance:function(e){return t?t(e):null},findHostInstancesForRefresh:ps,scheduleRefresh:ds,scheduleRoot:fs,setRefreshHandler:as,getCurrentFiber:function(){return Ue}}))}({findFiberByHostInstance:va,bundleType:1,version:"16.14.0",rendererPackageName:"react-dom"})&&j&&window.top===window.self&&(navigator.userAgent.indexOf("Chrome")>-1&&-1===navigator.userAgent.indexOf("Edge")||navigator.userAgent.indexOf("Firefox")>-1)){var Fg=window.location.protocol;/^(https?|file):$/.test(Fg)&&console.info("%cDownload the React DevTools for a better development experience: https://fb.me/react-devtools"+("file:"===Fg?"\nYou might need to use a local HTTP server (instead of file://): https://fb.me/react-devtools-faq":""),"font-weight:bold")}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=wg,t.createPortal=bg,t.findDOMNode=function(e){var t=fg.current;return null!==t&&null!==t.stateNode&&(t.stateNode._warnedAboutRefsInRender||s("%s is accessing findDOMNode inside its render(). render() should be a pure function of props and state. It should never access something that requires stale data from the previous render, such as refs. Move this logic to componentDidMount and componentDidUpdate instead.",je(t.type)||"A component"),t.stateNode._warnedAboutRefsInRender=!0),null==e?null:1===e.nodeType?e:function(e,t){var n=en(e);if(void 0===n)throw"function"==typeof e.render?Error("Unable to find node on an unmounted component."):Error("Argument appears to not be a ReactComponent. Keys: "+Object.keys(e));var r=un(n);if(null===r)return null;if(1&r.mode){var o=je(n.type)||"Component";Pm[o]||(Pm[o]=!0,1&n.mode?s("%s is deprecated in StrictMode. %s was passed an instance of %s which is inside StrictMode. Instead, add a ref directly to the element you want to reference. Learn more about using refs safely here: https://fb.me/react-strict-mode-find-node%s",t,t,o,ze(r)):s("%s is deprecated in StrictMode. %s was passed an instance of %s which renders StrictMode children. Instead, add a ref directly to the element you want to reference. Learn more about using refs safely here: https://fb.me/react-strict-mode-find-node%s",t,t,o,ze(r)))}return r.stateNode}(e,"findDOMNode")},t.flushSync=Sh,t.hydrate=function(e,t,n){if(!cg(t))throw Error("Target container is not a DOM element.");return ya(t)&&void 0===t._reactRootContainer&&s("You are calling ReactDOM.hydrate() on a container that was previously passed to ReactDOM.createRoot(). This is not supported. Did you mean to call createRoot(container, {hydrate: true}).render(element)?"),gg(null,e,t,!0,n)},t.render=function(e,t,n){if(!cg(t))throw Error("Target container is not a DOM element.");return ya(t)&&void 0===t._reactRootContainer&&s("You are calling ReactDOM.render() on a container that was previously passed to ReactDOM.createRoot(). This is not supported. Did you mean to call root.render(element)?"),gg(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!cg(e))throw Error("unmountComponentAtNode(...): Target container is not a DOM element.");if(ya(e)&&void 0===e._reactRootContainer&&s("You are calling ReactDOM.unmountComponentAtNode() on a container that was previously passed to ReactDOM.createRoot(). This is not supported. Did you mean to call root.unmount()?"),e._reactRootContainer){var t=hg(e);return t&&!ba(t)&&s("unmountComponentAtNode(): The node you're attempting to unmount was rendered by another copy of React."),Rh((function(){gg(null,null,e,!1,(function(){e._reactRootContainer=null,ga(e)}))})),!0}var n=hg(e),r=!(!n||!ba(n)),o=1===e.nodeType&&cg(e.parentNode)&&!!e.parentNode._reactRootContainer;return r&&s("unmountComponentAtNode(): The node you're attempting to unmount was rendered by React and is not a top-level container. %s",o?"You may have accidentally passed in a React root node instead of its container.":"Instead, have the parent component update its state and rerender in order to remove this component."),!1},t.unstable_batchedUpdates=xh,t.unstable_createPortal=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return vg||(vg=!0,u('The ReactDOM.unstable_createPortal() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactDOM.createPortal() instead. It has the exact same API, but without the "unstable_" prefix.')),bg(e,t,n)},t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){return function(e,t,n,r){if(!cg(n))throw Error("Target container is not a DOM element.");if(null==e||void 0===e._reactInternalFiber)throw Error("parentComponent must be a valid React Component");return gg(e,t,n,!1,r)}(e,t,n,r)},t.version="16.14.0"})()},function(e,t,n){"use strict";e.exports=n(26)},function(e,t,n){"use strict";
/** @license React v0.19.1
 * scheduler.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(){var e,n,r,o,a;if("undefined"==typeof window||"function"!=typeof MessageChannel){var i=null,l=null,u=function(){if(null!==i)try{var e=t.unstable_now();i(!0,e),i=null}catch(e){throw setTimeout(u,0),e}},s=Date.now();t.unstable_now=function(){return Date.now()-s},e=function(t){null!==i?setTimeout(e,0,t):(i=t,setTimeout(u,0))},n=function(e,t){l=setTimeout(e,t)},r=function(){clearTimeout(l)},o=function(){return!1},a=t.unstable_forceFrameRate=function(){}}else{var c=window.performance,d=window.Date,f=window.setTimeout,p=window.clearTimeout;if("undefined"!=typeof console){var h=window.requestAnimationFrame,m=window.cancelAnimationFrame;"function"!=typeof h&&console.error("This browser doesn't support requestAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills"),"function"!=typeof m&&console.error("This browser doesn't support cancelAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills")}if("object"==typeof c&&"function"==typeof c.now)t.unstable_now=function(){return c.now()};else{var g=d.now();t.unstable_now=function(){return d.now()-g}}var y=!1,v=null,b=-1,w=5,F=0;o=function(){return t.unstable_now()>=F},a=function(){},t.unstable_forceFrameRate=function(e){e<0||e>125?console.error("forceFrameRate takes a positive int between 0 and 125, forcing framerates higher than 125 fps is not unsupported"):w=e>0?Math.floor(1e3/e):5};var C=new MessageChannel,T=C.port2;C.port1.onmessage=function(){if(null!==v){var e=t.unstable_now();F=e+w;try{v(!0,e)?T.postMessage(null):(y=!1,v=null)}catch(e){throw T.postMessage(null),e}}else y=!1},e=function(e){v=e,y||(y=!0,T.postMessage(null))},n=function(e,n){b=f((function(){e(t.unstable_now())}),n)},r=function(){p(b),b=-1}}function k(e,t){var n=e.length;e.push(t),function(e,t,n){var r=n;for(;;){var o=r-1>>>1,a=e[o];if(!(void 0!==a&&R(a,t)>0))return;e[o]=t,e[r]=a,r=o}}(e,t,n)}function E(e){var t=e[0];return void 0===t?null:t}function x(e){var t=e[0];if(void 0!==t){var n=e.pop();return n!==t&&(e[0]=n,function(e,t,n){var r=n,o=e.length;for(;r<o;){var a=2*(r+1)-1,i=e[a],l=a+1,u=e[l];if(void 0!==i&&R(i,t)<0)void 0!==u&&R(u,i)<0?(e[r]=u,e[l]=t,r=l):(e[r]=i,e[a]=t,r=a);else{if(!(void 0!==u&&R(u,t)<0))return;e[r]=u,e[l]=t,r=l}}}(e,n,0)),t}return null}function R(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}var S=0,D=0,_="function"==typeof SharedArrayBuffer?new SharedArrayBuffer(4*Int32Array.BYTES_PER_ELEMENT):"function"==typeof ArrayBuffer?new ArrayBuffer(4*Int32Array.BYTES_PER_ELEMENT):null,O=null!==_?new Int32Array(_):[];O[0]=0,O[3]=0,O[1]=0;var P=0,A=null,I=null,W=0;function N(e){if(null!==I){var t=W;if((W+=e.length)+1>P){if((P*=2)>524288)return console.error("Scheduler Profiling: Event log exceeded maximum size. Don't forget to call `stopLoggingProfilingEvents()`."),void L();var n=new Int32Array(4*P);n.set(I),A=n.buffer,I=n}I.set(e,t)}}function L(){var e=A;return P=0,A=null,I=null,W=0,e}function j(e,t){O[3]++,null!==I&&N([1,1e3*t,e.id,e.priorityLevel])}function M(e,t){O[0]=0,O[1]=0,O[3]--,null!==I&&N([2,1e3*t,e.id])}function B(e,t){O[0]=0,O[1]=0,O[2]=0,null!==I&&N([6,1e3*t,e.id,S])}var z=[],U=[],H=1,V=null,$=3,q=!1,Q=!1,Y=!1;function K(e){for(var t=E(U);null!==t;){if(null===t.callback)x(U);else{if(!(t.startTime<=e))return;x(U),t.sortIndex=t.expirationTime,k(z,t),j(t,e),t.isQueued=!0}t=E(U)}}function J(t){if(Y=!1,K(t),!Q)if(null!==E(z))Q=!0,e(X);else{var r=E(U);null!==r&&n(J,r.startTime-t)}}function X(e,n){var o;o=n,null!==I&&N([8,1e3*o,D]),Q=!1,Y&&(Y=!1,r()),q=!0;var a=$;try{try{return G(e,n)}catch(e){if(null!==V){var i=t.unstable_now();!function(e,t){O[0]=0,O[1]=0,O[3]--,null!==I&&N([3,1e3*t,e.id])}(V,i),V.isQueued=!1}throw e}}finally{V=null,$=a,q=!1,function(e){D++,null!==I&&N([7,1e3*e,D])}(t.unstable_now())}}function G(e,r){var a,i,l=r;for(K(l),V=E(z);null!==V&&(!(V.expirationTime>l)||e&&!o());){var u=V.callback;if(null!==u){V.callback=null,$=V.priorityLevel;var s=V.expirationTime<=l;a=V,i=l,S++,O[0]=a.priorityLevel,O[1]=a.id,O[2]=S,null!==I&&N([5,1e3*i,a.id,S]);var c=u(s);l=t.unstable_now(),"function"==typeof c?(V.callback=c,B(V,l)):(M(V,l),V.isQueued=!1,V===E(z)&&x(z)),K(l)}else x(z);V=E(z)}if(null!==V)return!0;var d=E(U);return null!==d&&n(J,d.startTime-l),!1}function Z(e){switch(e){case 1:return-1;case 2:return 250;case 5:return **********;case 4:return 1e4;case 3:default:return 5e3}}var ee=a,te={startLoggingProfilingEvents:function(){P=131072,A=new ArrayBuffer(4*P),I=new Int32Array(A),W=0},stopLoggingProfilingEvents:L,sharedProfilingBuffer:_};t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=te,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.isQueued&&(!function(e,t){O[3]--,null!==I&&N([4,1e3*t,e.id])}(e,t.unstable_now()),e.isQueued=!1),e.callback=null},t.unstable_continueExecution=function(){Q||q||(Q=!0,e(X))},t.unstable_getCurrentPriorityLevel=function(){return $},t.unstable_getFirstCallbackNode=function(){return E(z)},t.unstable_next=function(e){var t;switch($){case 1:case 2:case 3:t=3;break;default:t=$}var n=$;$=t;try{return e()}finally{$=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=ee,t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=$;$=e;try{return t()}finally{$=n}},t.unstable_scheduleCallback=function(o,a,i){var l,u,s=t.unstable_now();if("object"==typeof i&&null!==i){var c=i.delay;l="number"==typeof c&&c>0?s+c:s,u="number"==typeof i.timeout?i.timeout:Z(o)}else u=Z(o),l=s;var d=l+u,f={id:H++,callback:a,priorityLevel:o,startTime:l,expirationTime:d,sortIndex:-1,isQueued:!1};return l>s?(f.sortIndex=l,k(U,f),null===E(z)&&f===E(U)&&(Y?r():Y=!0,n(J,l-s))):(f.sortIndex=d,k(z,f),j(f,s),f.isQueued=!0,Q||q||(Q=!0,e(X))),f},t.unstable_shouldYield=function(){var e=t.unstable_now();K(e);var n=E(z);return n!==V&&null!==V&&null!==n&&null!==n.callback&&n.startTime<=e&&n.expirationTime<V.expirationTime||o()},t.unstable_wrapCallback=function(e){var t=$;return function(){var n=$;$=t;try{return e.apply(this,arguments)}finally{$=n}}}})()},function(e,t,n){"use strict";e.exports=n(28)},function(e,t,n){"use strict";
/** @license React v0.19.1
 * scheduler-tracing.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(){var e=0,n=0;t.__interactionsRef=null,t.__subscriberRef=null,t.__interactionsRef={current:new Set},t.__subscriberRef={current:null};var r=null;function o(e){var t=!1,n=null;if(r.forEach((function(r){try{r.onInteractionTraced(e)}catch(e){t||(t=!0,n=e)}})),t)throw n}function a(e){var t=!1,n=null;if(r.forEach((function(r){try{r.onInteractionScheduledWorkCompleted(e)}catch(e){t||(t=!0,n=e)}})),t)throw n}function i(e,t){var n=!1,o=null;if(r.forEach((function(r){try{r.onWorkScheduled(e,t)}catch(e){n||(n=!0,o=e)}})),n)throw o}function l(e,t){var n=!1,o=null;if(r.forEach((function(r){try{r.onWorkStarted(e,t)}catch(e){n||(n=!0,o=e)}})),n)throw o}function u(e,t){var n=!1,o=null;if(r.forEach((function(r){try{r.onWorkStopped(e,t)}catch(e){n||(n=!0,o=e)}})),n)throw o}function s(e,t){var n=!1,o=null;if(r.forEach((function(r){try{r.onWorkCanceled(e,t)}catch(e){n||(n=!0,o=e)}})),n)throw o}r=new Set,t.unstable_clear=function(e){var n=t.__interactionsRef.current;t.__interactionsRef.current=new Set;try{return e()}finally{t.__interactionsRef.current=n}},t.unstable_getCurrent=function(){return t.__interactionsRef.current},t.unstable_getThreadID=function(){return++n},t.unstable_subscribe=function(e){r.add(e),1===r.size&&(t.__subscriberRef.current={onInteractionScheduledWorkCompleted:a,onInteractionTraced:o,onWorkCanceled:s,onWorkScheduled:i,onWorkStarted:l,onWorkStopped:u})},t.unstable_trace=function(n,r,o){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i={__count:1,id:e++,name:n,timestamp:r},l=t.__interactionsRef.current,u=new Set(l);u.add(i),t.__interactionsRef.current=u;var s,c=t.__subscriberRef.current;try{null!==c&&c.onInteractionTraced(i)}finally{try{null!==c&&c.onWorkStarted(u,a)}finally{try{s=o()}finally{t.__interactionsRef.current=l;try{null!==c&&c.onWorkStopped(u,a)}finally{i.__count--,null!==c&&0===i.__count&&c.onInteractionScheduledWorkCompleted(i)}}}}return s},t.unstable_unsubscribe=function(e){r.delete(e),0===r.size&&(t.__subscriberRef.current=null)},t.unstable_wrap=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=t.__interactionsRef.current,o=t.__subscriberRef.current;null!==o&&o.onWorkScheduled(r,n),r.forEach((function(e){e.__count++}));var a=!1;function i(){var i=t.__interactionsRef.current;t.__interactionsRef.current=r,o=t.__subscriberRef.current;try{var l;try{null!==o&&o.onWorkStarted(r,n)}finally{try{l=e.apply(void 0,arguments)}finally{t.__interactionsRef.current=i,null!==o&&o.onWorkStopped(r,n)}}return l}finally{a||(a=!0,r.forEach((function(e){e.__count--,null!==o&&0===e.__count&&o.onInteractionScheduledWorkCompleted(e)})))}}return i.cancel=function(){o=t.__subscriberRef.current;try{null!==o&&o.onWorkCanceled(r,n)}finally{r.forEach((function(e){e.__count--,o&&0===e.__count&&o.onInteractionScheduledWorkCompleted(e)}))}},i}})()},function(e,t,n){"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.identity=function(e){return e},t.always=function(e){return function(){return e}},function(e){e.LT="LT",e.EQ="EQ",e.GT="GT"}(r=t.Order||(t.Order={})),t.compare=function(e,t){return e>t?r.GT:e<t?r.LT:r.EQ},t.orderToNumber=function(e){switch(e){case r.LT:return-1;case r.EQ:return 0;case r.GT:return 1}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(13);t.NonEmptyList=Object.assign((function(e){return e}),{fromArray:function(e){return t.NonEmptyList.isNonEmpty(e)?r.Just(e):r.Nothing},unsafeCoerce:function(e){return t.NonEmptyList.isNonEmpty(e)?e:function(){throw new Error("NonEmptyList#unsafeCoerce passed an empty array")}()},fromTuple:function(e){return t.NonEmptyList(e.toArray())},head:function(e){return e[0]},last:function(e){return e[e.length-1]},isNonEmpty:function(e){return e.length>0}})},function(e,t,n){"use strict";n.r(t);n(22);var r=n(1),o=n.n(r),a=n(16),i=n.n(a),l=n(3);class u extends r.PureComponent{componentDidCatch(e){var{onError:t}=this.props;t(e)}render(){var{children:e}=this.props;return e}}var s=n(14),c={light:n.p+"light.03ddf3efe02cef7120efa35f2a51850c.css",dark:n.p+"dark.8f9746538022b0f2fc5f9c97ee20197d.css"},d=e=>{var{themeStylesheet:t}=e;return o.a.createElement("link",{rel:"stylesheet",href:c[t]})},f=n(8),p=n(4),h=e=>({rgb:e}),m="left",g="right",y="bottom",v="thin",b="medium",w="thick",F=(e,t,n)=>{var r={};return e.forEach(e=>{r[e]=((e,t)=>({width:e,color:t}))(t,n)}),r},C={TableStyleDark1:{header:{fgColor:h("000000"),border:Object.assign(F(["top",m,g],v,"000000"),F([y],b,"FFFFFF")),patternType:"solid",fontWeight:"bold"},averageRow:{fgColor:h("262626"),border:Object.assign(F([y,m,g],v,"262626"),F(["top"],b,"FFFFFF")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold",fgColor:h("404040"),border:Object.assign(F([g],b,"FFFFFF"),F([m],v,"404040"))},showLastColumn:{fontWeight:"bold",fgColor:h("404040"),border:Object.assign(F([m],b,"FFFFFF"),F([g],v,"404040"))},oddColumn:{fgColor:h("404040"),patternType:"solid"},evenRow:{fgColor:h("737373"),border:F(["top",y,m,g],v,"737373"),patternType:"solid"},oddRow:{fgColor:h("404040"),border:F(["top",y,m,g],v,"404040"),patternType:"solid"},table:{color:"#FFFFFF"}},TableStyleDark2:{header:{fgColor:h("000000"),border:Object.assign(F(["top",m,g],v,"000000"),F([y],b,"FFFFFF")),patternType:"solid",fontWeight:"bold"},averageRow:{fgColor:h("203764"),border:Object.assign(F([y,m,g],v,"203764"),F(["top"],b,"FFFFFF")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold",fgColor:h("305496"),border:Object.assign(F([g],b,"FFFFFF"),F([m],v,"305496"))},showLastColumn:{fontWeight:"bold",fgColor:h("305496"),border:Object.assign(F([m],b,"FFFFFF"),F([g],v,"305496"))},oddColumn:{fgColor:h("305496"),patternType:"solid"},evenRow:{fgColor:h("4472C4"),border:F(["top",y,m,g],v,"4472C4"),patternType:"solid"},oddRow:{fgColor:h("305496"),border:F(["top",y,m,g],v,"305496"),patternType:"solid"},table:{color:"#FFFFFF"}},TableStyleDark3:{header:{fgColor:h("000000"),border:Object.assign(F(["top",m,g],v,"000000"),F([y],b,"FFFFFF")),patternType:"solid",fontWeight:"bold"},averageRow:{fgColor:h("833C0C"),border:Object.assign(F([y,m,g],v,"833C0C"),F(["top"],b,"FFFFFF")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold",fgColor:h("C65911"),border:Object.assign(F([g],b,"FFFFFF"),F([m],v,"C65911"))},showLastColumn:{fontWeight:"bold",fgColor:h("C65911"),border:Object.assign(F([m],b,"FFFFFF"),F([g],v,"C65911"))},oddColumn:{fgColor:h("C65911"),patternType:"solid"},evenRow:{fgColor:h("ED7D31"),border:F(["top",y,m,g],v,"ED7D31"),patternType:"solid"},oddRow:{fgColor:h("C65911"),border:F(["top",y,m,g],v,"C65911"),patternType:"solid"},table:{color:"#FFFFFF"}},TableStyleDark4:{header:{fgColor:h("000000"),border:Object.assign(F(["top",m,g],v,"000000"),F([y],b,"FFFFFF")),patternType:"solid",fontWeight:"bold"},averageRow:{fgColor:h("525252"),border:Object.assign(F([y,m,g],v,"525252"),F(["top"],b,"FFFFFF")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold",fgColor:h("7B7B7B"),border:Object.assign(F([g],b,"FFFFFF"),F([m],v,"7B7B7B"))},showLastColumn:{fontWeight:"bold",fgColor:h("7B7B7B"),border:Object.assign(F([m],b,"FFFFFF"),F([g],v,"7B7B7B"))},oddColumn:{fgColor:h("7B7B7B"),patternType:"solid"},evenRow:{fgColor:h("A5A5A5"),border:F(["top",y,m,g],v,"A5A5A5"),patternType:"solid"},oddRow:{fgColor:h("7B7B7B"),border:F(["top",y,m,g],v,"7B7B7B"),patternType:"solid"},table:{color:"#FFFFFF"}},TableStyleDark5:{header:{fgColor:h("000000"),border:Object.assign(F(["top",m,g],v,"000000"),F([y],b,"FFFFFF")),patternType:"solid",fontWeight:"bold"},averageRow:{fgColor:h("816100"),border:Object.assign(F([y,m,g],v,"816100"),F(["top"],b,"FFFFFF")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold",fgColor:h("C09000"),border:Object.assign(F([g],b,"FFFFFF"),F([m],v,"C09000"))},showLastColumn:{fontWeight:"bold",fgColor:h("C09000"),border:Object.assign(F([m],b,"FFFFFF"),F([g],v,"C09000"))},oddColumn:{fgColor:h("C09000"),patternType:"solid"},evenRow:{fgColor:h("FFC101"),border:F(["top",y,m,g],v,"FFC101"),patternType:"solid"},oddRow:{fgColor:h("C09000"),border:F(["top",y,m,g],v,"C09000"),patternType:"solid"},table:{color:"#FFFFFF"}},TableStyleDark6:{header:{fgColor:h("000000"),border:Object.assign(F(["top",m,g],v,"000000"),F([y],b,"FFFFFF")),patternType:"solid",fontWeight:"bold"},averageRow:{fgColor:h("1C4D7A"),border:Object.assign(F([y,m,g],v,"1C4D7A"),F(["top"],b,"FFFFFF")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold",fgColor:h("2A74B7"),border:Object.assign(F([g],b,"FFFFFF"),F([m],v,"2A74B7"))},showLastColumn:{fontWeight:"bold",fgColor:h("2A74B7"),border:Object.assign(F([m],b,"FFFFFF"),F([g],v,"2A74B7"))},oddColumn:{fgColor:h("2A74B7"),patternType:"solid"},evenRow:{fgColor:h("589AD7"),border:F(["top",y,m,g],v,"589AD7"),patternType:"solid"},oddRow:{fgColor:h("2A74B7"),border:F(["top",y,m,g],v,"2A74B7"),patternType:"solid"},table:{color:"#FFFFFF"}},TableStyleDark7:{header:{fgColor:h("000000"),border:Object.assign(F(["top",m,g],v,"000000"),F([y],b,"FFFFFF")),patternType:"solid",fontWeight:"bold"},averageRow:{fgColor:h("365720"),border:Object.assign(F([y,m,g],v,"365720"),F(["top"],b,"FFFFFF")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold",fgColor:h("538330"),border:Object.assign(F([g],b,"FFFFFF"),F([m],v,"538330"))},showLastColumn:{fontWeight:"bold",fgColor:h("538330"),border:Object.assign(F([m],b,"FFFFFF"),F([g],v,"538330"))},oddColumn:{fgColor:h("538330"),patternType:"solid"},evenRow:{fgColor:h("6EAE40"),border:F(["top",y,m,g],v,"6EAE40"),patternType:"solid"},oddRow:{fgColor:h("538330"),border:F(["top",y,m,g],v,"538330"),patternType:"solid"},table:{color:"#FFFFFF"}},TableStyleDark8:{header:{fgColor:h("000000"),border:F(["top",y,m,g],v,"000000"),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},averageRow:{fgColor:h("D9D9D9"),border:Object.assign(F([y,m,g],v,"D9D9D9"),F(["top"],w,"000000")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("A6A6A6"),patternType:"solid"},evenRow:{fgColor:h("D9D9D9"),border:F(["top",y,m,g],v,"D9D9D9"),patternType:"solid"},oddRow:{fgColor:h("A6A6A6"),border:F(["top",y,m,g],v,"A6A6A6"),patternType:"solid"},table:{color:"#000000"}},TableStyleDark9:{header:{fgColor:h("EF7D22"),border:F(["top",y,m,g],v,"EF7D22"),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},averageRow:{fgColor:h("D9E1F3"),border:Object.assign(F(["top"],w,"000000"),F([y,m,g],v,"D9E1F3")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("B3C5E8"),patternType:"solid"},evenRow:{fgColor:h("D9E1F3"),border:F(["top",y,m,g],v,"D9E1F3"),patternType:"solid"},oddRow:{fgColor:h("B3C5E8"),border:F(["top",y,m,g],v,"B3C5E8"),patternType:"solid"},table:{color:"#000000"}},TableStyleDark10:{header:{fgColor:h("FFC101"),border:F(["top",y,m,g],v,"FFC101"),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},averageRow:{fgColor:h("EDEDED"),border:Object.assign(F(["top"],w,"000000"),F([y,m,g],v,"EDEDED")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold",fgColor:h("DBDBDB")},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("DBDBDB"),patternType:"solid"},evenRow:{fgColor:h("EDEDED"),border:F(["top",y,m,g],v,"EDEDED"),patternType:"solid"},oddRow:{fgColor:h("DBDBDB"),border:F(["top",y,m,g],v,"DBDBDB"),patternType:"solid"},table:{color:"#000000"}},TableStyleDark11:{header:{fgColor:h("6EAE40"),border:F(["top",y,m,g],v,"6EAE40"),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},averageRow:{fgColor:h("DDEBF8"),border:Object.assign(F(["top"],w,"000000"),F([y,m,g],v,"DDEBF8")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold",fgColor:h("BCD7EF")},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("BCD7EF"),patternType:"solid"},evenRow:{fgColor:h("DDEBF8"),border:F(["top",y,m,g],v,"DDEBF8"),patternType:"solid"},oddRow:{fgColor:h("BCD7EF"),border:F(["top",y,m,g],v,"BCD7EF"),patternType:"solid"},table:{color:"#000000"}}},T={TableStyleLight1:{header:{border:F(["top",y],v,"000000"),patternType:"solid",fontWeight:"bold"},averageRow:{border:F(["top",y],v,"000000"),patternType:"solid",fontWeight:"bold"},firstRow:{border:F(["top"],v,"000000")},lastRow:{border:F([y],v,"000000")},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("D9D9D9"),border:F(["top",y,m,g],v,"D9D9D9"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("D9D9D9"),border:F(["top",y,m,g],v,"D9D9D9"),patternType:"solid"},table:{color:"#000000"}},TableStyleLight2:{header:{border:F(["top",y],v,"4472C4"),patternType:"solid",fontWeight:"bold"},averageRow:{border:F(["top",y],v,"4472C4"),patternType:"solid",fontWeight:"bold"},firstRow:{border:F(["top"],v,"4472C4")},lastRow:{border:F([y],v,"4472C4")},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("D9E1F2"),border:F(["top",y,m,g],v,"D9E1F2"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("D9E1F2"),border:F(["top",y,m,g],v,"D9E1F2"),patternType:"solid"},table:{color:"#305496"}},TableStyleLight3:{header:{border:F(["top",y],v,"ED7D31"),patternType:"solid",fontWeight:"bold"},averageRow:{border:F(["top",y],v,"ED7D31"),patternType:"solid",fontWeight:"bold"},firstRow:{border:F(["top"],v,"ED7D31")},lastRow:{border:F([y],v,"ED7D31")},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("FCE4D6"),border:F(["top",y,m,g],v,"FCE4D6"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("FCE4D6"),border:F(["top",y,m,g],v,"FCE4D6"),patternType:"solid"},table:{color:"#C65911"}},TableStyleLight4:{header:{border:F(["top",y],v,"A5A5A5"),patternType:"solid",fontWeight:"bold"},averageRow:{border:F(["top",y],v,"A5A5A5"),patternType:"solid",fontWeight:"bold"},firstRow:{border:F(["top"],v,"A5A5A5")},lastRow:{border:F([y],v,"A5A5A5")},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("EDEDED"),border:F(["top",y,m,g],v,"EDEDED"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("EDEDED"),border:F(["top",y,m,g],v,"EDEDED"),patternType:"solid"},table:{color:"#7B7B7B"}},TableStyleLight5:{header:{border:F(["top",y],v,"FFC001"),patternType:"solid",fontWeight:"bold"},averageRow:{border:F(["top",y],v,"FFC001"),patternType:"solid",fontWeight:"bold"},firstRow:{border:F(["top"],v,"FFC001")},lastRow:{border:F([y],v,"FFC001")},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("FFF2CC"),border:F(["top",y,m,g],v,"FFF2CC"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("FFF2CC"),border:F(["top",y,m,g],v,"FFF2CC"),patternType:"solid"},table:{color:"#BF8F01"}},TableStyleLight6:{header:{border:F(["top",y],"thin","5B9BD5"),patternType:"solid",fontWeight:"bold"},averageRow:{border:F(["top",y],"thin","5B9BD5"),patternType:"solid",fontWeight:"bold"},firstRow:{border:F(["top"],"thin","5B9BD5")},lastRow:{border:F([y],"thin","5B9BD5")},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("DDEBF7"),border:F(["top",y,m,g],v,"DDEBF7"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("DDEBF7"),border:F(["top",y,m,g],v,"DDEBF7"),patternType:"solid"},table:{color:"#2F75B5"}},TableStyleLight7:{header:{border:F(["top",y],v,"70AD47"),patternType:"solid",fontWeight:"bold"},averageRow:{border:F(["top",y],v,"70AD47"),patternType:"solid",fontWeight:"bold"},firstRow:{border:F(["top"],v,"70AD47")},lastRow:{border:F([y],v,"70AD47")},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("E2EFDA"),border:F(["top",y,m,g],v,"E2EFDA"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("E2EFDA"),border:F(["top",y,m,g],v,"E2EFDA"),patternType:"solid"},table:{color:"#548235"}},TableStyleLight8:{header:{fgColor:h("000000"),border:F(["top",y,m,g],v,"000000"),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},averageRow:{border:Object.assign(F([y],v,"000000"),F(["top"],w,"000000")),patternType:"solid",fontWeight:"bold"},firstColumn:{border:F([m],v,"000000")},lastColumn:{border:F([g],v,"000000")},firstRow:{border:F(["top"],v,"000000")},lastRow:{border:F([y],v,"000000")},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{border:F([m,g],v,"000000"),patternType:"solid"},evenRow:{border:F(["top"],v,"000000"),patternType:"solid"},oddRow:{border:F(["top"],v,"000000"),patternType:"solid"},table:{color:"#000000"}},TableStyleLight9:{header:{fgColor:h("4472C4"),border:F(["top",y,m,g],v,"4472C4"),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},firstColumn:{border:F([m],v,"4472C4")},lastColumn:{border:F([g],v,"4472C4")},firstRow:{border:F(["top"],v,"4472C4")},lastRow:{border:F([y],v,"4472C4")},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{border:F([m,g],v,"4472C4"),patternType:"solid"},evenRow:{border:F(["top"],v,"4472C4"),patternType:"solid"},oddRow:{border:F(["top"],v,"4472C4"),patternType:"solid"},averageRow:{border:Object.assign(F([y],v,"4472C4"),F(["top"],w,"4472C4")),patternType:"solid",fontWeight:"bold",color:"#000000"},table:{color:"#000000"}},TableStyleLight10:{header:{fgColor:h("ED7D31"),border:F(["top",y,m,g],v,"ED7D31"),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},firstColumn:{border:F([m],v,"ED7D31")},lastColumn:{border:F([g],v,"ED7D31")},firstRow:{border:F(["top"],v,"ED7D31")},lastRow:{border:F([y],v,"ED7D31")},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{border:F([m,g],v,"ED7D31"),patternType:"solid"},evenRow:{border:F(["top"],v,"ED7D31"),patternType:"solid"},oddRow:{border:F(["top"],v,"ED7D31"),patternType:"solid"},averageRow:{border:Object.assign(F([y],v,"ED7D31"),F(["top"],w,"ED7D31")),patternType:"solid",fontWeight:"bold",color:"#000000"},table:{color:"#000000"}},TableStyleLight11:{header:{fgColor:h("A5A5A5"),border:F(["top",y,m,g],v,"A5A5A5"),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},firstColumn:{border:F([m],v,"A5A5A5")},lastColumn:{border:F([g],v,"A5A5A5")},firstRow:{border:F(["top"],v,"A5A5A5")},lastRow:{border:F([y],v,"A5A5A5")},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{border:F([m,g],v,"A5A5A5"),patternType:"solid"},evenRow:{border:F(["top"],v,"A5A5A5"),patternType:"solid"},oddRow:{border:F(["top"],v,"A5A5A5"),patternType:"solid"},averageRow:{border:Object.assign(F([y],v,"A5A5A5"),F(["top"],w,"A5A5A5")),patternType:"solid",fontWeight:"bold",color:"#000000"},table:{color:"#000000"}},TableStyleLight12:{header:{fgColor:h("FFC001"),border:F(["top",y,m,g],v,"FFC001"),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},firstColumn:{border:F([m],v,"FFC001")},lastColumn:{border:F([g],v,"FFC001")},firstRow:{border:F(["top"],v,"FFC001")},lastRow:{border:F([y],v,"FFC001")},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{border:F([m,g],v,"FFC001"),patternType:"solid"},evenRow:{border:F(["top"],v,"FFC001"),patternType:"solid"},oddRow:{border:F(["top"],v,"FFC001"),patternType:"solid"},averageRow:{border:Object.assign(F([y],v,"FFC001"),F(["top"],w,"FFC001")),patternType:"solid",fontWeight:"bold",color:"#000000"},table:{color:"#000000"}},TableStyleLight13:{header:{fgColor:h("5B9BD5"),border:F(["top",y,m,g],v,"5B9BD5"),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},firstColumn:{border:F([m],v,"5B9BD5")},lastColumn:{border:F([g],v,"5B9BD5")},firstRow:{border:F(["top"],v,"5B9BD5")},lastRow:{border:F([y],v,"5B9BD5")},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{border:F([m,g],v,"5B9BD5"),patternType:"solid"},evenRow:{border:F(["top"],v,"5B9BD5"),patternType:"solid"},oddRow:{border:F(["top"],v,"5B9BD5"),patternType:"solid"},averageRow:{border:Object.assign(F([y],v,"5B9BD5"),F(["top"],w,"5B9BD5")),patternType:"solid",fontWeight:"bold",color:"#000000"},table:{color:"#000000"}},TableStyleLight14:{header:{fgColor:h("70AD47"),border:F(["top",y,m,g],v,"70AD47"),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},firstColumn:{border:F([m],v,"70AD47")},lastColumn:{border:F([g],v,"70AD47")},firstRow:{border:F(["top"],v,"70AD47")},lastRow:{border:F([y],v,"70AD47")},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{border:F([m,g],v,"70AD47"),patternType:"solid"},evenRow:{border:F(["top"],v,"70AD47"),patternType:"solid"},oddRow:{border:F(["top"],v,"70AD47"),patternType:"solid"},averageRow:{border:Object.assign(F([y],v,"70AD47"),F(["top"],w,"70AD47")),patternType:"solid",fontWeight:"bold",color:"#000000"},table:{color:"#000000"}},TableStyleLight15:{header:{border:F(["top",y,m,g],v,"000000"),patternType:"solid",fontWeight:"bold"},averageRow:{border:Object.assign(F([m,g,y],v,"000000"),F(["top"],w,"000000")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("D9D9D9"),patternType:"solid"},evenRow:{border:F(["top",y,m,g],v,"000000"),patternType:"solid"},oddRow:{fgColor:h("D9D9D9"),border:F(["top",y,m,g],v,"000000"),patternType:"solid"},table:{color:"#000000"}},TableStyleLight16:{header:{border:F(["top",y,m,g],v,"4472C4"),patternType:"solid",fontWeight:"bold"},averageRow:{border:Object.assign(F([m,g,y],v,"4472C4"),F(["top"],w,"4472C4")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("D9E1F2"),patternType:"solid"},evenRow:{border:F(["top",y,m,g],v,"4472C4"),patternType:"solid"},oddRow:{fgColor:h("D9E1F2"),border:F(["top",y,m,g],v,"4472C4"),patternType:"solid"},table:{color:"#000000"}},TableStyleLight17:{header:{border:F(["top",y,m,g],v,"ED7D31"),patternType:"solid",fontWeight:"bold",color:"#000000"},averageRow:{border:Object.assign(F([m,g,y],v,"ED7D31"),F(["top"],w,"ED7D31")),patternType:"solid",fontWeight:"bold",color:"#000000"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("FCE4D6"),patternType:"solid"},evenRow:{border:F(["top",y,m,g],v,"ED7D31"),patternType:"solid"},oddRow:{fgColor:h("FCE4D6"),border:F(["top",y,m,g],v,"ED7D31"),patternType:"solid"},table:{color:"#000000"}},TableStyleLight18:{header:{border:F(["top",y,m,g],v,"A5A5A5"),patternType:"solid",fontWeight:"bold"},averageRow:{border:Object.assign(F([m,g,y],v,"A5A5A5"),F(["top"],w,"A5A5A5")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("EDEDED"),patternType:"solid"},evenRow:{border:F(["top",y,m,g],v,"A5A5A5"),patternType:"solid"},oddRow:{fgColor:h("EDEDED"),border:F(["top",y,m,g],v,"A5A5A5"),patternType:"solid"},table:{color:"#000000"}},TableStyleLight19:{header:{border:F(["top",y,m,g],v,"FFC001"),patternType:"solid",fontWeight:"bold"},averageRow:{border:Object.assign(F([m,g,y],v,"FFC001"),F(["top"],w,"FFC001")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("FFF2CC"),patternType:"solid"},evenRow:{border:F(["top",y,m,g],v,"FFC001"),patternType:"solid"},oddRow:{fgColor:h("FFF2CC"),border:F(["top",y,m,g],v,"FFC001"),patternType:"solid"},table:{color:"#000000"}},TableStyleLight20:{header:{border:F(["top",y,m,g],v,"5B9BD5"),patternType:"solid",fontWeight:"bold"},averageRow:{border:Object.assign(F([m,g,y],v,"5B9BD5"),F(["top"],w,"5B9BD5")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("DDEBF7"),patternType:"solid"},evenRow:{border:F(["top",y,m,g],v,"5B9BD5"),patternType:"solid"},oddRow:{fgColor:h("DDEBF7"),border:F(["top",y,m,g],v,"5B9BD5"),patternType:"solid"},table:{color:"#000000"}},TableStyleLight21:{header:{border:F(["top",y,m,g],v,"70AD47"),patternType:"solid",fontWeight:"bold"},averageRow:{border:Object.assign(F([m,g,y],v,"70AD47"),F(["top"],w,"70AD47")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("E2EFDA"),patternType:"solid"},evenRow:{border:F(["top",y,m,g],v,"70AD47"),patternType:"solid"},oddRow:{fgColor:h("E2EFDA"),border:F(["top",y,m,g],v,"70AD47"),patternType:"solid"},table:{color:"#000000"}}},k={TableStyleMedium1:{firstColumn:{border:F([m],v,"000000")},lastColumn:{border:F([g],v,"000000")},header:{fgColor:h("000000"),color:"#FFFFFF",border:F(["top",y,m,g],v,"000000"),patternType:"solid",fontWeight:"bold"},averageRow:{border:Object.assign(F([y],v,"000000"),F(["top"],w,"000000")),fontWeight:"bold",patternType:"solid"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("D9D9D9"),border:F([m,g],v,"D9D9D9"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("D9D9D9"),border:Object.assign(F(["top",y],v,"000000"),F([m,g],v,"D9D9D9")),patternType:"solid"}},TableStyleMedium2:{firstColumn:{border:F([m],v,"8DA8DD")},lastColumn:{border:F([g],v,"8DA8DD")},header:{fgColor:h("4270C7"),border:F(["top",y,m,g],v,"4270C7"),color:"#FFFFFF",fontWeight:"bold",patternType:"solid"},averageRow:{border:Object.assign(F([y],v,"8DA8DD"),F(["top"],w,"4270C7")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("D9E1F2"),border:F([m,g],v,"D9E1F2"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("D9E1F2"),border:Object.assign(F(["top",y],v,"8EA9DB"),F([m,g],v,"D9E1F2")),patternType:"solid"}},TableStyleMedium3:{firstColumn:{border:F([m],v,"F6B080")},lastColumn:{border:F([g],v,"F6B080")},header:{fgColor:h("F37D22"),border:F(["top",y,m,g],v,"F37D22"),color:"#FFFFFF",fontWeight:"bold",patternType:"solid"},averageRow:{border:Object.assign(F(["top"],w,"EF7D22"),F([y],v,"F6B080")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("FDE4D5"),border:F([m,g],v,"FDE4D5"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("FDE4D5"),border:Object.assign(F(["top",y],v,"F4B084"),F([m,g],v,"FDE4D5")),patternType:"solid"}},TableStyleMedium4:{firstColumn:{border:F([m],v,"C9C9C9")},lastColumn:{border:F([g],v,"C9C9C9")},header:{fgColor:h("A5A5A5"),border:F(["top",y,m,g],v,"A5A5A5"),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},averageRow:{border:Object.assign(F(["top"],w,"A5A5A5"),F([y],v,"C9C9C9")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("EDEDED"),border:F([m,g],v,"EDEDED"),patternType:"solid"},oddRow:{fgColor:h("EDEDED"),border:Object.assign(F(["top",y],v,"C9C9C9"),F([m,g],v,"EDEDED")),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"}},TableStyleMedium5:{firstColumn:{border:F([m],v,"FFDA5C")},lastColumn:{border:F([g],v,"FFDA5C")},header:{fgColor:h("FFC100"),border:F(["top",y,m,g],v,"FFC100"),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},averageRow:{border:Object.assign(F(["top"],w,"FFC100"),F([y],v,"FFDA5C")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("FFF3CA"),border:F([m,g],v,"FFF3CA"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("FFF3CA"),border:Object.assign(F(["top",y],v,"FFDA5C"),F([m,g],v,"FFF3CA")),patternType:"solid"}},TableStyleMedium6:{firstColumn:{border:F([m],v,"9BC2E6")},lastColumn:{border:F([g],v,"9BC2E6")},header:{fgColor:h("589AD7"),border:F(["top",y,m,g],v,"589AD7"),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},averageRow:{border:Object.assign(F(["top"],w,"589AD7"),F([y],v,"9BC2E6")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("DDEBF8"),border:F([m,g],v,"DDEBF8"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("DDEBF8"),border:Object.assign(F(["top",y],v,"9BC2E6"),F([m,g],v,"DDEBF8")),patternType:"solid"}},TableStyleMedium7:{firstColumn:{border:F([m],v,"A8D18B")},lastColumn:{border:F([g],v,"A8D18B")},header:{fgColor:h("6EAE40"),border:F(["top",y,m,g],v,"6EAE40"),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},averageRow:{border:Object.assign(F(["top"],w,"6EAE40"),F([y],v,"A8D18B")),patternType:"solid",fontWeight:"bold"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("E2EFD9"),border:F([m,g],v,"E2EFD9"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("E2EFD9"),border:Object.assign(F(["top",y],v,"A8D18B"),F([m,g],v,"E2EFD9")),patternType:"solid"}},TableStyleMedium8:{header:{fgColor:h("000000"),border:Object.assign(F(["top",m,g],v,"FFFFFF"),F([y],w,"FFFFFF")),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},averageRow:{fgColor:h("000000"),border:Object.assign(F([y,m,g],v,"FFFFFF"),F(["top"],w,"FFFFFF")),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},showFirstColumn:{fgColor:h("000000"),color:"#FFFFFF",fontWeight:"bold"},showLastColumn:{fgColor:h("000000"),color:"#FFFFFF",fontWeight:"bold"},oddColumn:{fgColor:h("A6A6A6"),border:F([m,g],v,"FFFFFF"),patternType:"solid"},evenRow:{fgColor:h("D9D9D9"),border:F(["top",y,m,g],v,"FFFFFF"),patternType:"solid"},oddRow:{fgColor:h("A6A6A6"),border:F(["top",y,m,g],v,"FFFFFF"),patternType:"solid"}},TableStyleMedium9:{header:{fgColor:h("4472C4"),border:Object.assign(F(["top",m,g],v,"FFFFFF"),F([y],w,"FFFFFF")),color:"#FFFFFF",fontWeight:"bold",patternType:"solid"},averageRow:{fgColor:h("4472C4"),border:Object.assign(F([y,m,g],v,"FFFFFF"),F(["top"],w,"FFFFFF")),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},showFirstColumn:{fgColor:h("4472C4"),color:"#FFFFFF",fontWeight:"bold"},showLastColumn:{fgColor:h("4472C4"),color:"#FFFFFF",fontWeight:"bold"},oddColumn:{fgColor:h("B4C6E7"),border:F([m,g],v,"FFFFFF"),patternType:"solid"},evenRow:{fgColor:h("D9E1F2"),border:F(["top",y,m,g],v,"FFFFFF"),patternType:"solid"},oddRow:{fgColor:h("B4C6E7"),border:F(["top",y,m,g],v,"FFFFFF"),patternType:"solid"}},TableStyleMedium10:{header:{fgColor:h("ED7D31"),border:Object.assign(F(["top",m,g],v,"FFFFFF"),F([y],w,"FFFFFF")),color:"#FFFFFF",fontWeight:"bold",patternType:"solid"},averageRow:{fgColor:h("ED7D31"),border:Object.assign(F([y,m,g],v,"FFFFFF"),F(["top"],w,"FFFFFF")),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},showFirstColumn:{fgColor:h("ED7D31"),color:"#FFFFFF",fontWeight:"bold"},showLastColumn:{fgColor:h("ED7D31"),color:"#FFFFFF",fontWeight:"bold"},oddColumn:{fgColor:h("F8CBAD"),border:F([m,g],v,"FFFFFF"),patternType:"solid"},evenRow:{fgColor:h("FCE4D6"),border:F(["top",y,m,g],v,"FFFFFF"),patternType:"solid"},oddRow:{fgColor:h("F8CBAD"),border:F(["top",y,m,g],v,"FFFFFF"),patternType:"solid"}},TableStyleMedium11:{header:{fgColor:h("A5A5A5"),border:Object.assign(F(["top",m,g],v,"FFFFFF"),F([y],w,"FFFFFF")),color:"#FFFFFF",fontWeight:"bold",patternType:"solid"},averageRow:{fgColor:h("A5A5A5"),border:Object.assign(F([y,m,g],v,"FFFFFF"),F(["top"],w,"FFFFFF")),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},showFirstColumn:{fgColor:h("A5A5A5"),color:"#FFFFFF",fontWeight:"bold"},showLastColumn:{fgColor:h("A5A5A5"),color:"#FFFFFF",fontWeight:"bold"},oddColumn:{fgColor:h("DBDBDB"),border:F([m,g],v,"FFFFFF"),patternType:"solid"},evenRow:{fgColor:h("EDEDED"),border:F(["top",y,m,g],v,"FFFFFF"),patternType:"solid"},oddRow:{fgColor:h("DBDBDB"),border:F(["top",y,m,g],v,"FFFFFF"),patternType:"solid"}},TableStyleMedium12:{header:{fgColor:h("FFC001"),border:Object.assign(F(["top",m,g],v,"FFFFFF"),F([y],w,"FFFFFF")),color:"#FFFFFF",fontWeight:"bold",patternType:"solid"},averageRow:{fgColor:h("FFC001"),border:Object.assign(F([y,m,g],v,"FFFFFF"),F(["top"],w,"FFFFFF")),fontWeight:"bold",color:"#FFFFFF",patternType:"solid"},showFirstColumn:{fgColor:h("FFC001"),color:"#FFFFFF",fontWeight:"bold"},showLastColumn:{fgColor:h("FFC001"),color:"#FFFFFF",fontWeight:"bold"},oddColumn:{fgColor:h("FFE698"),border:F([m,g],v,"FFFFFF"),patternType:"solid"},evenRow:{fgColor:h("FFF2CC"),border:F(["top",y,m,g],v,"FFFFFF"),patternType:"solid"},oddRow:{fgColor:h("FFE698"),border:F(["top",y,m,g],v,"FFFFFF"),patternType:"solid"}},TableStyleMedium13:{header:{fgColor:h("5B9BD5"),border:Object.assign(F(["top",m,g],v,"FFFFFF"),F([y],w,"FFFFFF")),color:"#FFFFFF",fontWeight:"bold",patternType:"solid"},averageRow:{fgColor:h("5B9BD5"),border:Object.assign(F([y,m,g],v,"FFFFFF"),F(["top"],w,"FFFFFF")),fontWeight:"bold",color:"#FFFFFF",patternType:"solid"},showFirstColumn:{fgColor:h("5B9BD5"),color:"#FFFFFF",fontWeight:"bold"},showLastColumn:{fgColor:h("5B9BD5"),color:"#FFFFFF",fontWeight:"bold"},oddColumn:{fgColor:h("BDD7EE"),border:F([m,g],v,"FFFFFF"),patternType:"solid"},evenRow:{fgColor:h("DDEBF7"),border:F(["top",y,m,g],v,"FFFFFF"),patternType:"solid"},oddRow:{fgColor:h("BDD7EE"),border:F(["top",y,m,g],v,"FFFFFF"),patternType:"solid"}},TableStyleMedium14:{header:{fgColor:h("6EAE40"),border:Object.assign(F(["top",m,g],v,"FFFFFF"),F([y],w,"FFFFFF")),color:"#FFFFFF",fontWeight:"bold",patternType:"solid"},averageRow:{fgColor:h("6EAE40"),border:Object.assign(F([y,m,g],v,"FFFFFF"),F(["top"],w,"FFFFFF")),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},showFirstColumn:{fgColor:h("6EAE40"),color:"#FFFFFF",fontWeight:"bold"},showLastColumn:{fgColor:h("6EAE40"),color:"#FFFFFF",fontWeight:"bold"},oddColumn:{fgColor:h("C5E1B2"),border:F([m,g],v,"FFFFFF"),patternType:"solid"},evenRow:{fgColor:h("E2EFD9"),border:F(["top",y,m,g],v,"FFFFFF"),patternType:"solid"},oddRow:{fgColor:h("C5E1B2"),border:F(["top",y,m,g],v,"FFFFFF"),patternType:"solid"}},TableStyleMedium15:{header:{fgColor:h("000000"),border:F(["top",y,m,g],v,"000000"),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},showFirstColumn:{fgColor:h("000000"),color:"#FFFFFF",fontWeight:"bold"},showLastColumn:{fgColor:h("000000"),color:"#FFFFFF",fontWeight:"bold"},oddColumn:{fgColor:h("A6A6A6"),border:F([m,g],v,"000000"),patternType:"solid"},averageRow:{border:Object.assign(F([m,g],v,"000000"),F(["top"],w,"000000"),F([y],b,"000000")),patternType:"solid"},evenRow:{border:F(["top",y,m,g],v,"000000"),patternType:"solid"},oddRow:{fgColor:h("D9D9D9"),border:F(["top",y,m,g],v,"000000"),patternType:"solid"}},TableStyleMedium16:{header:{fgColor:h("4472C4"),border:Object.assign(F(["top",y],v,"000000"),F([m,g],v,"4472C4")),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},averageRow:{border:Object.assign(F(["top"],w,"000000"),F([y],b,"000000")),patternType:"solid"},showFirstColumn:{fgColor:h("4472C4"),border:F(["top",y,m,g],v,"4472C4"),color:"#FFFFFF",fontWeight:"bold"},showLastColumn:{fgColor:h("4472C4"),border:F(["top",y,m,g],v,"4472C4"),color:"#FFFFFF",fontWeight:"bold"},oddColumn:{fgColor:h("D9D9D9"),border:F([m,g],v,"D9D9D9"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("D9D9D9"),border:F([m,g],v,"D9D9D9"),patternType:"solid"}},TableStyleMedium17:{header:{fgColor:h("ED7D31"),border:Object.assign(F(["top",y],v,"000000"),F([m,g],v,"ED7D31")),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},averageRow:{border:Object.assign(F(["top"],w,"000000"),F([y],b,"000000")),patternType:"solid"},showFirstColumn:{fgColor:h("ED7D31"),border:F(["top",y,m,g],v,"ED7D31"),color:"#FFFFFF",fontWeight:"bold"},showLastColumn:{fgColor:h("ED7D31"),border:F(["top",y,m,g],v,"ED7D31"),color:"#FFFFFF",fontWeight:"bold"},oddColumn:{fgColor:h("D9D9D9"),border:F([m,g],v,"D9D9D9"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("D9D9D9"),border:F([m,g],v,"D9D9D9"),patternType:"solid"}},TableStyleMedium18:{header:{fgColor:h("A5A5A5"),border:Object.assign(F(["top",y],v,"000000"),F([m,g],v,"A5A5A5")),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},averageRow:{border:Object.assign(F(["top"],w,"000000"),F([y],b,"000000")),patternType:"solid"},showFirstColumn:{fgColor:h("A5A5A5"),border:F(["top",y,m,g],v,"A5A5A5"),color:"#FFFFFF",fontWeight:"bold"},showLastColumn:{fgColor:h("A5A5A5"),border:F(["top",y,m,g],v,"A5A5A5"),color:"#FFFFFF",fontWeight:"bold"},oddColumn:{fgColor:h("D9D9D9"),border:F([m,g],v,"D9D9D9"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("D9D9D9"),border:F([m,g],v,"D9D9D9"),patternType:"solid"}},TableStyleMedium19:{header:{fgColor:h("FFC001"),border:Object.assign(F(["top",y],v,"000000"),F([m,g],v,"FFC001")),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},averageRow:{border:Object.assign(F(["top"],w,"000000"),F([y],b,"000000")),patternType:"solid"},showFirstColumn:{fgColor:h("FFC001"),border:F(["top",y,m,g],v,"FFC001"),color:"#FFFFFF",fontWeight:"bold"},showLastColumn:{fgColor:h("FFC001"),border:F(["top",y,m,g],v,"FFC001"),color:"#FFFFFF",fontWeight:"bold"},oddColumn:{fgColor:h("D9D9D9"),border:F([m,g],v,"D9D9D9"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("D9D9D9"),border:F([m,g],v,"D9D9D9"),patternType:"solid"}},TableStyleMedium20:{header:{fgColor:h("5B9BD5"),border:Object.assign(F(["top",y],v,"000000"),F([m,g],v,"5B9BD5")),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},averageRow:{border:Object.assign(F(["top"],w,"000000"),F([y],b,"000000")),patternType:"solid"},showFirstColumn:{fgColor:h("5B9BD5"),border:F(["top",y,m,g],v,"5B9BD5"),color:"#FFFFFF",fontWeight:"bold"},showLastColumn:{fgColor:h("5B9BD5"),border:F(["top",y,m,g],v,"5B9BD5"),color:"#FFFFFF",fontWeight:"bold"},oddColumn:{fgColor:h("D9D9D9"),border:F([m,g],v,"D9D9D9"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("D9D9D9"),border:F([m,g],v,"D9D9D9"),patternType:"solid"}},TableStyleMedium21:{header:{fgColor:h("6EAE40"),border:Object.assign(F(["top",y],v,"000000"),F([m,g],v,"6EAE40")),patternType:"solid",fontWeight:"bold",color:"#FFFFFF"},averageRow:{border:Object.assign(F(["top"],w,"000000"),F([y],b,"000000")),patternType:"solid"},showFirstColumn:{fgColor:h("6EAE40"),border:F(["top",y,m,g],v,"6EAE40"),color:"#FFFFFF",fontWeight:"bold"},showLastColumn:{fgColor:h("6EAE40"),border:F(["top",y,m,g],v,"6EAE40"),color:"#FFFFFF",fontWeight:"bold"},oddColumn:{fgColor:h("D9D9D9"),border:F([m,g],v,"D9D9D9"),patternType:"solid"},evenRow:{border:void 0,patternType:"solid"},oddRow:{fgColor:h("D9D9D9"),border:F([m,g],v,"D9D9D9"),patternType:"solid"}},TableStyleMedium22:{header:{fgColor:h("D9D9D9"),border:F(["top",y,m,g],v,"000000"),patternType:"solid",fontWeight:"bold",color:"#000000"},averageRow:{fgColor:h("D9D9D9"),border:Object.assign(F([y,m,g],v,"000000"),F(["top"],b,"000000")),patternType:"solid",fontWeight:"bold",color:"#000000"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("A6A6A6"),patternType:"solid"},evenRow:{fgColor:h("D9D9D9"),border:F(["top",y,m,g],v,"000000"),patternType:"solid"},oddRow:{fgColor:h("A6A6A6"),border:F(["top",y,m,g],v,"000000"),patternType:"solid"}},TableStyleMedium23:{header:{fgColor:h("D9E1F2"),border:F(["top",y,m,g],v,"8EA9DB"),patternType:"solid",fontWeight:"bold",color:"#000000"},averageRow:{fgColor:h("D9E1F2"),border:Object.assign(F([y,m,g],v,"8EA9DB"),F(["top"],b,"4472C4")),patternType:"solid",fontWeight:"bold",color:"#000000"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("B4C6E7"),patternType:"solid"},evenRow:{fgColor:h("D9E1F2"),border:F(["top",y,m,g],v,"8EA9DB"),patternType:"solid"},oddRow:{fgColor:h("B4C6E7"),border:F(["top",y,m,g],v,"8EA9DB"),patternType:"solid"}},TableStyleMedium24:{header:{fgColor:h("FCE4D6"),border:F(["top",y,m,g],v,"F3B084"),patternType:"solid",fontWeight:"bold",color:"#000000"},averageRow:{fgColor:h("FCE4D6"),border:Object.assign(F([y,m,g],v,"F3B084"),F(["top"],b,"ED7D31")),patternType:"solid",fontWeight:"bold",color:"#000000"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("FCE4D6"),patternType:"solid"},evenRow:{fgColor:h("FCE4D6"),border:F(["top",y,m,g],v,"F3B084"),patternType:"solid"},oddRow:{fgColor:h("F8CBAD"),border:F(["top",y,m,g],v,"F3B084"),patternType:"solid"}},TableStyleMedium25:{header:{fgColor:h("EDEDED"),border:F(["top",y,m,g],v,"C9C9C9"),patternType:"solid",fontWeight:"bold",color:"#000000"},averageRow:{fgColor:h("EDEDED"),border:Object.assign(F([y,m,g],v,"C9C9C9"),F(["top"],b,"A5A5A5")),patternType:"solid",fontWeight:"bold",color:"#000000"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("DBDBDB"),patternType:"solid"},evenRow:{fgColor:h("EDEDED"),border:F(["top",y,m,g],v,"C9C9C9"),patternType:"solid"},oddRow:{fgColor:h("DBDBDB"),border:F(["top",y,m,g],v,"C9C9C9"),patternType:"solid"}},TableStyleMedium26:{header:{fgColor:h("FFF2CC"),border:F(["top",y,m,g],v,"FFD966"),patternType:"solid",fontWeight:"bold",color:"#000000"},averageRow:{fgColor:h("FFF2CC"),border:Object.assign(F([y,m,g],v,"FFD966"),F(["top"],b,"FFC001")),patternType:"solid",fontWeight:"bold",color:"#000000"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("FFE698"),patternType:"solid"},evenRow:{fgColor:h("FFF2CC"),border:F(["top",y,m,g],v,"FFD966"),patternType:"solid"},oddRow:{fgColor:h("FFE698"),border:F(["top",y,m,g],v,"FFD966"),patternType:"solid"}},TableStyleMedium27:{header:{fgColor:h("DDEBF7"),border:F(["top",y,m,g],v,"9BC2E6"),patternType:"solid",fontWeight:"bold",color:"#000000"},averageRow:{fgColor:h("DDEBF7"),border:Object.assign(F([y,m,g],v,"9BC2E6"),F(["top"],b,"5B9BD5")),patternType:"solid",fontWeight:"bold",color:"#000000"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("BDD7EE"),patternType:"solid"},evenRow:{fgColor:h("DDEBF7"),border:F(["top",y,m,g],v,"9BC2E6"),patternType:"solid"},oddRow:{fgColor:h("BDD7EE"),border:F(["top",y,m,g],v,"9BC2E6"),patternType:"solid"}},TableStyleMedium28:{header:{fgColor:h("E2EFD9"),border:F(["top",y,m,g],v,"A8D18B"),patternType:"solid",fontWeight:"bold",color:"#000000"},averageRow:{fgColor:h("E2EFD9"),border:Object.assign(F([y,m,g],v,"A8D18B"),F(["top"],b,"6EAE40")),patternType:"solid",fontWeight:"bold",color:"#000000"},showFirstColumn:{fontWeight:"bold"},showLastColumn:{fontWeight:"bold"},oddColumn:{fgColor:h("C5E1B2"),patternType:"solid"},evenRow:{fgColor:h("E2EFD9"),border:F(["top",y,m,g],v,"A8D18B"),patternType:"solid"},oddRow:{fgColor:h("C5E1B2"),border:F(["top",y,m,g],v,"A8D18B"),patternType:"solid"}}},E=n(10),x=Object.assign(Object.assign(Object.assign({},T),k),C),R=n(6),S={evenRow:(e,t)=>!(t.showRowStripes&&(S.header(e,t)||S.averageRow(e,t)||(e.row+(t.address.startCell.row+1)%2+t.headerRowCount)%2)),oddRow:(e,t)=>t.showRowStripes&&!S.header(e,t)&&!S.averageRow(e,t)&&!S.evenRow(e,t),header:(e,t)=>{var{address:n,headerRowCount:r}=t;return!!r&&e.row<n.startCell.row+r},firstColumn:(e,t)=>{var{address:n}=t;return e.column===n.startCell.column},lastColumn:(e,t)=>{var{address:n}=t;return e.column===n.endCell.column},averageRow:(e,t)=>{var{address:n,totalsRowCount:r}=t;return!!r&&e.row>n.endCell.row-r},showFirstColumn:(e,t)=>t.showFirstColumn&&S.firstColumn(e,t),showLastColumn:(e,t)=>t.showLastColumn&&S.lastColumn(e,t),oddColumn:(e,t)=>t.showColumnStripes&&!S.header(e,t)&&!S.averageRow(e,t)&&!!((Object(R.b)(e.column)-Object(R.b)(t.address.endCell.column))%2),firstRow:(e,t)=>{var{address:n,headerRowCount:r}=t;return r?e.row===n.startCell.row+r:e.row===n.startCell.row},lastRow:(e,t)=>{var{address:n,totalsRowCount:r}=t;return r?e.row===n.endCell.row-r:e.row===n.endCell.row},table:()=>!0},D=e=>{var t=e.match(/([A-Z]+)([0-9]+)/);if(null===t)return{row:0,column:"0"};var[n,r]=t.slice(-2);return{row:Number(r),column:n}},_=(e,t,n,r)=>{n.forEach(n=>{var o=(e=>"".concat(e.column).concat(e.row))(n),a=e[o];a||(a={s:{},isTextContent:!1,t:"n"},e[o]=a);var i=((e,t,n)=>Object.entries(n).reduce((n,r)=>{var[o,a]=r,i=Object(E.a)(S,o);if(i&&i(t,e)){var l=Object.assign(Object.assign({},a),n);return l.border&&(l.border=Object.assign(Object.assign({},(null==a?void 0:a.border)?a.border:{}),(null==n?void 0:n.border)?n.border:{})),l}return n},{}))(t,n,r),l="#000000"===a.s.color?i.color:a.s.color;a.s=Object.assign(Object.assign(Object.assign({},i),a.s),{color:l})})},O=e=>{var{ref:t,headerRowCount:n,totalsRowCount:r,style:o}=e,a=(e=>{var[t,n]=e.split(":");return{startCell:D(t),endCell:D(n)}})(t);return{showFirstColumn:Boolean(null==o?void 0:o.showFirstColumn),showLastColumn:Boolean(null==o?void 0:o.showLastColumn),showRowStripes:Boolean(null==o?void 0:o.showRowStripes),showColumnStripes:Boolean(null==o?void 0:o.showColumnStripes),address:a,headerRowCount:n,totalsRowCount:r}},P=e=>{e[p.g].forEach(t=>{if(t.style){var n=O(t),r=(e=>{for(var t=Object(R.b)(e.startCell.column),n=Object(R.b)(e.endCell.column),r=e.startCell.row,o=e.endCell.row,a=[],i=t;i<=n;i++)for(var l=r;l<=o;l++)a.push({column:Object(R.c)(i),row:l});return a})(n.address),o=(e=>{if(!Object(E.c)(x,e))return console.warn('Table style "'.concat(e,'" is not implemented!')),{};var t=x[e];return t||{}})(t.style.name);_(e,n,r,o)}})},A=0,I=e=>e*(96/72),W=e=>Math.ceil(I(e)),N=(e,t)=>{if(e.fontSize){var n=parseInt(e.fontSize,10);e.fontSize="".concat((r=n,Math.round(1.2*r)),"px"),e.lineHeight="".concat(1.2*I(n),"px")}var r;e.verticalAlign||(e.verticalAlign="bottom"),e.direction&&(e.direction=void 0),t||e.textAlign||(e.textAlign="right")};function L(e,t,n,r,o){var a,i,l;if(void 0!==t){var u=null===(a=n.CellXf)||void 0===a?void 0:a[Number(t)];if(u&&(u.applyBorder||u.applyFill)){var s={};if(!0===u.applyBorder&&(s.border=null===(i=n.Borders)||void 0===i?void 0:i[u.borderId]),!0===u.applyFill){var c=null===(l=n.Fills)||void 0===l?void 0:l[u.fillId];Object.assign(s,c)}((e,t)=>{var n,r,{fgColor:o}=e;void 0!==o&&void 0!==o.theme&&(null===(n=t[o.theme])||void 0===n?void 0:n.rgb)&&(o.rgb=o.rgb||t[o.theme].rgb);var{bgColor:a}=e;void 0!==a&&void 0!==a.theme&&(null===(r=t[a.theme])||void 0===r?void 0:r.rgb)&&(a.rgb=a.rgb||t[a.theme].rgb)})(s,r);var d=JSON.stringify(s),f=o.get(d);return f||(f="_sv-col-".concat(A),A+=1,o.set(d,f),e[f]=s),f}}}var j=e=>{var t=new Map,n=[],r={};return e.SheetNames.forEach(o=>{var a=e.Sheets[o];if(a){a[p.g]&&P(a);var i=((e,t)=>{var n={},r=/^[A-Z]/;return Object.keys(e).filter(e=>e.match(r)).forEach(r=>{var o=((e,t)=>e[t])(e,r),a=o.s;if(a){var i="s"===o.t,l=JSON.stringify(a)+i,u=t.get(l);u||(u="_sv-cell-".concat(A),A+=1,t.set(l,u),N(a,i),n[u]=a),o.svStyleId=u,o.s=null,o.isTextContent=i}}),n})(a,t);(e=>{var t=e[p.a];t&&t.length>256&&(t.length=256)})(a);var l=((e,t,n,r)=>{if(!t)return{};var o={},a=e[p.a];a&&a.forEach(e=>{if(!e)return e;e.svStyleId=L(o,e.style,t,n,r)});var i=e[p.e];return i&&i.forEach(e=>{e&&(e.svStyleId=L(o,e.style,t,n,r))}),o})(a,e.Styles,(e=>{var t,n;return(null===(n=null===(t=e.Themes)||void 0===t?void 0:t.themeElements)||void 0===n?void 0:n.clrScheme)||[]})(e),t);(e=>{var t,n,r=e[p.e];if(r){var o=r.map(e=>e?Object.assign(Object.assign({},e),{hpx:void 0!==e.hpt?W(e.hpt):NaN}):e);e[p.e]=o}(null===(n=null===(t=e[p.b])||void 0===t?void 0:t.rows)||void 0===n?void 0:n.hpx)&&(e[p.b].rows.hpx=W(e[p.b].rows.hpx))})(a),n.push({title:o,data:a}),r=Object.assign(Object.assign(Object.assign({},r),l),i)}else n.push({title:o,data:null})}),{styleMap:r,sheetsData:n}},M=n(7),B=n(2),z=Symbol("non empty array"),U=(e,t)=>({$symbol:z,last:H(e,t),findLast:V(e,t),append:$(e,t)}),H=(e,t)=>()=>t.length>0?t[t.length-1]:e,V=(e,t)=>n=>[e,...t].reverse().find(n),$=(e,t)=>n=>U(e,[...t,n]),q=Symbol("sheet history"),Q=e=>t=>e.findLast(e=>t.includes(e)),Y=e=>e.last(),K=e=>t=>{return n=e.append(t),{$symbol:q,latestSheet:Y(n),getLatestLoadedSheet:Q(n),selectSheet:K(n)};var n},J=e=>{var t=U(e,[]);return{$symbol:q,latestSheet:Y(t),getLatestLoadedSheet:Q(t),selectSheet:K(t)}},X=(e,t)=>{var n,r,o;switch(t.type){case"initialize":return{type:"initialising",workbookId:t.workbookId,input:t.workbookInput,desiredSheet:t.sheet};case"initialized":if((null==e?void 0:e.workbookId)!==t.workbookId)return e;if("initialising"!==e.type)return e;return{type:"ready",workbookId:e.workbookId,input:e.input,sheetHistory:J((r=t.sheetNames.length-1,o=e.desiredSheet,o<0?(console.warn("Desired sheet (".concat(o,") was lower than 0")),0):o>r?(console.warn("Desired sheet (".concat(o,") was larger than the amount of sheets present in the current workbook (").concat(r,")")),r):o)),fileSize:t.fileSize,sheets:new Map,sheetNames:t.sheetNames};case"updateWorkbook":return"ready"!==(null==e?void 0:e.type)||"loading"!==(null===(n=e.sheets.get(t.sheet))||void 0===n?void 0:n.type)?e:Object.assign(Object.assign({},e),{sheets:e.sheets.set(t.sheet,{type:"ready",parsedData:t.parsedData})});case"startLoadingSheet":return"ready"!==(null==e?void 0:e.type)||void 0!==e.sheets.get(t.sheet)?e:Object.assign(Object.assign({},e),{sheets:e.sheets.set(t.sheet,{type:"loading"})});case"changeCurrentSheet":return"ready"!==(null==e?void 0:e.type)?e:Object.assign(Object.assign({},e),{sheetHistory:e.sheetHistory.selectSheet(t.sheet)});default:throw new TypeError("useWorkbook: Invalid action (".concat(t,")"))}},G=n(15),Z=n(12),ee=n(20),te=n(17),ne=n(11),re=e=>{var t=Math.random().toString(36).substring(2);return t.length<e?t+re(e-t.length):t.substring(0,e)},oe=o.a.lazy(()=>Promise.all([n.e(0),n.e(1)]).then(n.bind(null,188)));(e=>{Object(l.c)(l.a.presentation);var{queryParameters:t,error:a}=Object(Z.b)(window.location.href),c=Object(ne.a)(t.svId||"random-".concat(re(25)));Object(M.d)(c,window),Object(te.a)(document.body);var p=o.a.lazy(()=>Promise.all([n.e(0),n.e(5),n.e(2)]).then(n.bind(null,182))),h=document.getElementById("root");i.a.render(window.Worker?o.a.createElement(()=>{var[n,i]=Object(r.useState)(()=>t.themeStylesheet||"dark"),[h,m]=Object(r.useState)(()=>t.licenseKey),[g,y]=Object(r.useState)(!1),{workbookState:v,latestLoadedSheet:b,loadWorkbook:w,changeCurrentSheet:F}=(e=>{var{moreformats:t,parseWorker:n,onError:o,svId:a}=e,[i,u]=Object(r.useReducer)(X,void 0),s="ready"===(null==i?void 0:i.type)&&i.sheets,c="ready"===(null==i?void 0:i.type)?i.sheetHistory.latestSheet:void 0;Object(r.useEffect)(()=>{var e=e=>{var t,n,{data:r}=e;if("initialized"===r.type)u({type:"initialized",workbookId:r.workbookId,fileSize:r.fileSize,sheetNames:r.sheetNames});else if("error"===r.type){var a={"download-status":new B.b,"download-timeout":new B.c,"download-network":new B.a,"download-filesize":new B.e,"unsupported non-workbook file":new B.m,"unsupported workbook file":new B.n,filesize:new B.e,parser:new B.i,"parser-sheet-limit":new B.j,"parser-password-protected":new B.d,"cache-integrity":new B.o}[r.code];o(a)}else if("parsedWorkbook"===r.type){var{workbook:i,sheet:s,workbookId:c}=r;Object(l.b)(l.a.parserReading),Object(l.c)(l.a.parserFixing),console.log("File authored with:","".concat(null===(t=i.Props)||void 0===t?void 0:t.Application," (").concat(null===(n=i.Props)||void 0===n?void 0:n.AppVersion,")"));var d=j(i);Object(l.b)(l.a.parserFixing),Object(l.b)(l.a.parser),u({type:"updateWorkbook",workbookId:c,sheet:s,parsedData:d})}};return n.addEventListener("message",e),()=>n.removeEventListener("message",e)},[n,o]),Object(r.useEffect)(()=>{if("ready"===(null==i?void 0:i.type)&&s&&void 0!==c){var e=c;void 0===s.get(e)&&(u({type:"startLoadingSheet",sheet:e}),n.postMessage({type:"loadSheet",sheet:e,workbookId:i.workbookId}))}},[null==i?void 0:i.workbookId,c,s,null==i?void 0:i.type,n]),Object(r.useEffect)(()=>{void 0!==c&&Object(M.a)(a,c)},[c,a]);var d=Object(r.useCallback)(e=>u({type:"changeCurrentSheet",sheet:e}),[]),f=Object(r.useCallback)((function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=Math.random().toString(32).substring(2);u({type:"initialize",workbookId:o,workbookInput:e,sheet:r}),Object(l.c)(l.a.parser),Object(l.c)(l.a.parserReading),n.postMessage({type:"initialize",workbookId:o,workbookInput:e,moreformats:t})}),[n,t]),p=(()=>{if("ready"===(null==i?void 0:i.type)){var e=[...i.sheets.entries()].filter(e=>{var[t,n]=e;return"ready"===n.type}).map(e=>{var[t]=e;return t});return i.sheetHistory.getLatestLoadedSheet(e)}})();return{workbookState:i,changeCurrentSheet:d,loadWorkbook:f,latestLoadedSheet:p}})({parseWorker:e,onError:y,moreformats:Object(ee.c)(),svId:c});Object(r.useEffect)(()=>{if(a)return console.error("Query String API:",a),y(new B.g(a));var{workbookUrl:e,sheet:n}=t;e&&w({type:"url",url:e,fileName:t.fileName},n)},[w]);var C=Object(r.useCallback)(e=>y(e),[]);Object(G.a)(f.e,f.b,C,e=>{var t;y(!1),"string"==typeof e.workbook?w({type:"url",url:e.workbook,fileName:e.fileName},e.sheet):w({type:"arraybuffer",arrayBuffer:e.workbook,fileName:null!==(t=e.fileName)&&void 0!==t?t:""},e.sheet)}),Object(G.a)(f.d,f.a,C,e=>{e.themeStylesheet&&i(e.themeStylesheet),e.licenseKey&&m(e.licenseKey)}),Object(r.useEffect)(()=>Object(M.b)(c),[]);var T,k=Object(r.useCallback)(e=>y(e),[]);return o.a.createElement(o.a.Fragment,null,o.a.createElement(d,{themeStylesheet:n}),g?o.a.createElement(o.a.Suspense,{fallback:o.a.createElement(s.a,null)},o.a.createElement(oe,{workbookState:v,error:g,loadWorkbook:w,resetError:()=>y(!1)})):o.a.createElement(u,{onError:k},o.a.createElement(o.a.Suspense,{fallback:o.a.createElement(s.a,null)},o.a.createElement(p,{simulateError:t.simulateError,loadWorkbook:w,workbookState:v,latestLoadedSheet:b,onSheetChange:F,requestMessageOnError:C,hasValidLicenseKey:(T=h,"string"==typeof T&&T.length>0),svId:c}))))},null):o.a.createElement("div",null,"Web Worker unavailable"),h)})(new function(){return new Worker(n.p+"modern.modern.worker.0ab37b52ef495ce6d89c.bundle.worker.js")})}]);
    </script>
    <!-- <script type="module" src="%modernBundlePath%"></script> -->

    <!-- Insert a `nomodule` script with a proper public path link for legacy browsers-->
    <script nomodule>
      // Same as __webpack_public_path__ in src/sv/public-path.ts
      const publicPath = window.location.pathname.split('/').slice(0, -1).join('/') + '/';

      const $script = document.createElement('script');

      $script.src = publicPath + 'legacy.main.1a43a1d3f28cfd0cd223.bundle.js';
      $script.setAttribute('nomodule', true);

      document.body.appendChild($script)
    </script>
  </body>
</html>
