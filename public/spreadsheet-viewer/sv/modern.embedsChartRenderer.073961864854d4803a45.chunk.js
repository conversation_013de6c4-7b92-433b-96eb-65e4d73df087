/*!
 * Spreadsheet Viewer
 * 
 * Version: 1.0.1
 * Code version: 06577ad
 * Build date: Thu, October 21, 2021, 12:29 PM GMT+2
 */
(window.webpackJsonp=window.webpackJsonp||[]).push([[3],{184:function(e,a,t){"use strict";t.r(a),t.d(a,"getChartRenderer",(function(){return R}));var r,n=t(16),l=t(1),o=t.n(l),i=t(76),s=t.n(i),c=t(65),u={appear:{animation:"fadeIn",easing:"easeQuadIn",delay:0,duration:0}},d=(e,a)=>{var t,r=null===(t=null==e?void 0:e.label)||void 0===t?void 0:t.value;return r||"Series".concat(a+1)},v=e=>{var a,t={},r=(e=>{var a=new Set,t=JSON.parse(JSON.stringify(e));return t.forEach((e,t)=>{var r=d(e,t);a.has(r)&&(r=d(void 0,t)),a.add(r),e.label&&(e.label.value=r)}),t})(e),n={values:(null===(a=r[0].values.values)||void 0===a?void 0:a.slice())||[]};r.forEach((e,a)=>{var r,l=d(e,a);e.categories||(e.categories=n),null===(r=e.categories.values)||void 0===r||r.forEach((a,r)=>{t[a]=t[a]||{},t[a][l]=(()=>{var a,t=null===(a=e.values.values)||void 0===a?void 0:a[r];if(null==t)return 0;var n=Number(t);return Number.isNaN(n)?0:n})()})});var l=[];return{data:Object.entries(t).map(e=>{var[a,t]=e;return l=[...l,...Object.keys(t)],Object.assign(Object.assign({},t),{key:a})}),series:[...new Set(l)]}},p={1:{back:{shape:"square"},front:{shape:"hollowSquare"}},2:{back:{shape:"diamond"},front:{shape:"hollowDiamond"}},3:{back:{shape:"triangle"},front:{shape:"hollowTriangle"}},4:{front:{shape:"cross"},back:{shape:"square"}},5:{front:{shape:"hexagon"},back:{shape:"square"}},6:{front:{shape:"hyphen"},back:{shape:"hyphen"}},7:{front:{shape:"hyphen"},back:{shape:"hyphen"}},8:{back:{shape:"circle"},front:{shape:"hollowCircle"}},9:{front:{shape:"plus"},back:{shape:"square"}}},h=(e,a)=>{var t;return null===(t=e.find(e=>{var t;return(null===(t=e.label)||void 0===t?void 0:t.value)===a}))||void 0===t?void 0:t.markerFormat};!function(e){e.MARKER_COMPONENT_FRONT="fNotShowBrd",e.MARKER_COMPONENT_BACK="fNotShowInt"}(r||(r={}));var m,f;!function(e){e.FIELD_FRONT="front",e.FIELD_BACK="back"}(m||(m={})),function(e){e.FIELD_FRONT="rgbFore",e.FIELD_BACK="rgbBack"}(f||(f={}));var y,g=e=>{var{chartSeries:a,componentFlag:t,adjust:r,imkComponent:n,colorField:l}=e;return((e,a)=>{try{return e.find(e=>{try{return!(e.markerFormat.imk<=0)&&!e.markerFormat[a]}catch(e){return!1}})}catch(e){return!1}})(a,t)?o.a.createElement(c.Geom,{type:"point",position:"key*value",adjust:r,color:["type",e=>{var r=h(a,e);return"number"==typeof(null==r?void 0:r.imk)&&r.imk<=0||(null==r?void 0:r[t])?"transparent":(null==r?void 0:r[l])||"transparent"}],shape:["type",e=>{var t,r=h(a,e);return void 0===r?"":(t=r.imk)>0&&t<10?p[r.imk][n].shape:""}],size:["type",e=>{var t=h(a,e);return(null==t?void 0:t.size)||0}],style:{fill:"transparent"},animate:u}):null},E=e=>{var{chartSeries:a,chartOptions:t,name:n,width:l,height:i}=e,d=v(a),p=(new s.a.View).source(d.data);p.transform({type:"fold",fields:d.series,key:"type",value:"value"}),t.flags.f100&&p.transform({type:"percent",dimension:"type",field:"value",groupBy:["key"],as:"value"});var h=t.flags.fStacked?"stack":"",y=a.map(e=>{var a,t;return null===(t=null===(a=e.format)||void 0===a?void 0:a.line)||void 0===t?void 0:t.rgb}).filter(e=>"string"==typeof e),E=y.length>0?["type",y]:["type",void 0];return o.a.createElement(c.Chart,{height:i,width:l,data:p,padding:"auto",animate:!1},o.a.createElement("span",{className:"sv-chart-title"},n),o.a.createElement(c.Tooltip,{crosshairs:!0,"g2-tooltip":{display:"inline-flex"}}),o.a.createElement(c.Axis,null),o.a.createElement(c.Legend,null),o.a.createElement(c.Geom,{type:"line",adjust:h,position:"key*value",color:E,shape:"smooth",size:2,animate:u}),o.a.createElement(g,{chartSeries:a,componentFlag:r.MARKER_COMPONENT_FRONT,imkComponent:m.FIELD_FRONT,adjust:h,colorField:f.FIELD_FRONT}),o.a.createElement(g,{chartSeries:a,componentFlag:r.MARKER_COMPONENT_BACK,imkComponent:m.FIELD_BACK,adjust:h,colorField:f.FIELD_BACK}))},k=e=>{var{chartSeries:a,chartOptions:t,name:r,width:n,height:l}=e,i=v(a),d=(new s.a.View).source(i.data);d.transform({type:"fold",fields:i.series,key:"type",value:"value"}),t.flags.f100&&d.transform({type:"percent",dimension:"type",field:"value",groupBy:["key"],as:"value"});var p=t.flags.fStacked?"stack":"",h=a.map(e=>{var a,t;return null===(t=null===(a=e.format)||void 0===a?void 0:a.area)||void 0===t?void 0:t.rgbFore}).filter(e=>"string"==typeof e),m=h.length>0?["type",h]:["type",void 0];return o.a.createElement(c.Chart,{height:l,width:n,data:d,padding:"auto"},o.a.createElement("span",{className:"sv-chart-title"},r),o.a.createElement(c.Tooltip,{crosshairs:!0}),o.a.createElement(c.Axis,null),o.a.createElement(c.Legend,null),o.a.createElement(c.Geom,{type:"area",adjust:p,position:"key*value",color:m,shape:"smooth",size:2,animate:u}))},b=e=>{var{chartSeries:a,chartOptions:t,name:r,width:n,height:l}=e,i=v(a),d=(new s.a.View).source(i.data);d.transform({type:"fold",fields:i.series,key:"type",value:"value"}),t.flags.f100&&d.transform({type:"percent",dimension:"type",field:"value",groupBy:["key"],as:"value"});var p=t.flags.fStacked?"intervalStack":"interval",h=t.flags.fTranspose?o.a.createElement(c.Coord,{transpose:!0,scale:[1,-1]}):o.a.createElement(c.Coord,{scale:[1,1]}),m=a.map(e=>{var a,t;return null===(t=null===(a=e.format)||void 0===a?void 0:a.area)||void 0===t?void 0:t.rgbFore}).filter(e=>"string"==typeof e),f=m.length>0?["type",m]:["type",void 0];return o.a.createElement(c.Chart,{height:l,width:n,data:d,padding:"auto"},o.a.createElement("span",{className:"sv-chart-title"},r),o.a.createElement(c.Tooltip,{crosshairs:!0}),h,o.a.createElement(c.Axis,null),o.a.createElement(c.Legend,null),o.a.createElement(c.Geom,{type:p,position:"key*value",color:f,shape:"smooth",adjust:[{type:"dodge",marginRatio:4/32}],animate:u}))},O=e=>{var{chartSeries:a,name:t,width:r,height:n}=e,l=v(a),i=(new s.a.View).source(l.data);i.transform({type:"fold",fields:l.series,key:"type",value:"value"}),i.transform({type:"percent",dimension:"type",field:"value",groupBy:["key"],as:"value"});var d="type",p=Object.keys(l.data[0])[0];return i.transform({type:"filter",callback:e=>e.type===p}),d="key",o.a.createElement(c.Chart,{height:n,width:r,data:i,padding:"auto"},o.a.createElement("span",{className:"sv-chart-title"},t),o.a.createElement(c.Tooltip,{crosshairs:!0}),o.a.createElement(c.Axis,null),o.a.createElement(c.Legend,null),o.a.createElement(c.Coord,{type:"theta",radius:.65}),o.a.createElement(c.Geom,{type:"intervalStack",position:"value",color:d,shape:"smooth",tooltip:["key*value*type",(e,a,t)=>({name:"".concat(t,":").concat(e),value:"".concat((1e4*a|0)/100,"%")})],animate:u}))},w={Line:E,Bar:b,Bar3D:b,Line3D:E,Area:k,Area3D:k,Stock:void 0,Surface3D:void 0,Radar:void 0,Pie:O,OfPie:void 0,Pie3D:O,Doughnut:void 0,Scatter:void 0,Bubble:void 0,treemap:void 0,sunburst:void 0,regionMap:void 0,clusteredColumn:void 0,boxWhisker:void 0,waterfall:void 0,funnel:void 0};!function(e){e.RANGE="PtgArea3d",e.POINT="PtgRef3d"}(y||(y={}));var N=t(6),S=t(128),F=t(94),C=(e,a)=>{var t=Object(N.c)(e.c),r=e.r+1,n=a["".concat(t).concat(r)];return n?n.v:null},A=(e,a)=>e&&Array.isArray(e)?e.map(e=>{if(e[0]===y.POINT){var t=e[1][2];return C(t,a)}if(e[0]===y.RANGE)return((e,a)=>{for(var t=[],r=Math.min(e.s.r,e.e.r),n=Math.max(e.s.r,e.e.r),l=Math.min(e.s.c,e.e.c),o=Math.min(e.s.c,e.e.c),i=r;i<=n;i++)for(var s=l;s<=o;s++){var c=C({r:i,c:s},a);t.push(c)}return t})(e[1][2],a)}).reduce((e,a)=>e.concat(a),[]):[];class T extends l.Component{componentDidMount(){setTimeout(()=>{Object(F.a)()},0)}render(){var{children:e}=this.props;return e}}var R=(e,a)=>{var{chartSeries:t,chartOptions:r,chartType:o}=e,{chartTitle:i}=e;i||(i="");var s=((e,a)=>e&&Array.isArray(e)?e.map(e=>{var t,r,n,l;e=JSON.parse(JSON.stringify(e));try{!e.values.values&&e.values.formula&&(e.values.values=A(e.values.formula,a),delete e.values.formula)}catch(e){}try{if(!(null===(t=e.label)||void 0===t?void 0:t.value)&&(null===(r=e.label)||void 0===r?void 0:r.formula)){var[o]=A(e.label.formula,a);e.label.value=o,delete e.label.formula}}catch(e){}try{!(null===(n=e.categories)||void 0===n?void 0:n.values)&&(null===(l=e.categories)||void 0===l?void 0:l.formula)&&(e.categories.values=A(e.categories.formula,a),delete e.categories.formula)}catch(e){}return e}):e)(t,a);return function(e,a,t){e.classList.add("sv-handsontable-chart");var c=w[o]||S.a;"function"==typeof c?Object(n.render)(l.createElement(T,null,l.createElement(c,{width:a,height:t,chartSeries:s,chartOptions:r,name:i||""})),e):Object(F.a)()}}}}]);