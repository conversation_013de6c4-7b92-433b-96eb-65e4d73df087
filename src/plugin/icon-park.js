import { install } from '@icon-park/vue-next/es/all';
import {
  ApplicationMenu,
  Api,
  User,
  Lock,
  Alipay,
  Wechat,
  Github,
  Twitter,
  Google,
  IdCardV,
  MenuUnfoldOne,
  MenuFoldOne,
  FullScreen,
  OffScreen,
  Refresh,
  Remind,
  AllApplication,
  Close,
  ToLeft,
  ToRight,
  Minus,
  Mail,
  Home,
  Code,
  ChartLine,
  Like,
  Xigua,
  Permissions,
  Performance,
  Pic,
  MoveOne,
  Search,
  Tailoring,
  TailoringTwo,
  AddText,
  ScanCode,
  Play,
  PauseOne,
  VolumeNotice,
  VolumeMute,
  PlayCycle,
  PlayOnce,
  GoStart,
  GoEnd,
  MusicList,
  LinkCloudFaild,
  LinkInterrupt,
  Copy,
  ChartHistogram,
  MultiPictureCarousel,
  Theme,
  Translate,
} from '@icon-park/vue-next';
import '@icon-park/vue-next/styles/index.css';

export const components = [
  ApplicationMenu,
  Api,
  User,
  Lock,
  Alipay,
  Wechat,
  Github,
  Twitter,
  Google,
  IdCardV,
  MenuUnfoldOne,
  MenuFoldOne,
  FullScreen,
  OffScreen,
  Refresh,
  Remind,
  AllApplication,
  Close,
  ToLeft,
  ToRight,
  Minus,
  Mail,
  Home,
  Code,
  ChartLine,
  Like,
  Xigua,
  Permissions,
  Performance,
  Pic,
  MoveOne,
  Search,
  Tailoring,
  TailoringTwo,
  AddText,
  ScanCode,
  Play,
  PauseOne,
  VolumeNotice,
  VolumeMute,
  PlayCycle,
  PlayOnce,
  GoStart,
  GoEnd,
  MusicList,
  LinkCloudFaild,
  LinkInterrupt,
  Copy,
  ChartHistogram,
  MultiPictureCarousel,
  Theme,
  Translate,
];
import SvgIcon from '@/components/SvgIcon/index.vue';

export default (app) => {
  app.component('svg-icon', SvgIcon);
  install(app);
};
