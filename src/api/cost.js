import{ Geturl,formatDate } from '@/views/cost/components/my-module'
const IPPort=import.meta.env.VITE_BACKEND_URL

export const GetCostReportData=(params)=>{
  const data=Geturl(IPPort+'api/v1/cost/costreport',params)
  let result=null
  data.then((data1)=>{
      if(typeof(data1.error) !=='undefined'){
          console.log(data1.error)
          return null
      }else{
        console.log(data1)
        result=data1
      }     
  });
  return result
}