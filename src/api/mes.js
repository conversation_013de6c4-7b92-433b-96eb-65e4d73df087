import request from '@/utils/request.js';

export const getDashboardDataAPI = (params) => {
  return request({
    url: '/api/v1/mes/dashboard_data',
    method: 'get',
    params,
  });
};

export const saveOrganizationalDataAPI = (data) => {
  return request({
    url: '/api/v1/mes/organizational',
    method: 'post',
    data,
  });
};

export const UploadScheduleAPI = (data) => {
  return request({
    url: '/api/v1/mes/schedule',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const getThermometerDataAPI = (params) => {
  return request({
    url: '/api/v1/mes/thermometer',
    method: 'get',
    params,
  });
};
