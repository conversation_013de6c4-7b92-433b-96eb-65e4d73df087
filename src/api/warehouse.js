import request from '@/utils/request.js';

export const getRackSpaceAPI = (params) => {
  return request({
    url: '/api/v1/warehouse/space',
    method: 'get',
    params,
  });
};

export const getRackInformationAPI = (params) => {
  return request({
    url: '/api/v1/warehouse/rack',
    method: 'get',
    params,
  });
};

export const getOrderInformationAPI = (params) => {
  return request({
    url: '/api/v1/warehouse/order',
    method: 'get',
    params,
  });
};

export const getMaterialInformationAPI = (params) => {
  return request({
    url: '/api/v1/warehouse/material',
    method: 'get',
    params,
  });
};

export const getSupplierInformationAPI = (params) => {
  return request({
    url: '/api/v1/warehouse/supplier',
    method: 'get',
    params,
  });
};

export const getWarehouseTemperatureAndHumidityAPI = (params) => {
  return request({
    url: '/api/v1/warehouse/TemperatureAndHumidity',
    method: 'get',
    params,
  });
};
