import request from '@/utils/request.js';

export const getOrderExistenceStatusAPI = (params) => {
  return request({
    url: '/api/v1/trace/check_order',
    method: 'get',
    params,
  });
};

export const getProductionInformationAPI = (params) => {
  return request({
    url: '/api/v1/trace/production_information',
    method: 'get',
    params,
  });
};

export const getBOMtionInformationAPI = (params) => {
  return request({
    url: '/api/v1/trace/bom_information',
    method: 'get',
    params,
  });
};

export const getMaterialInformationAPI = (params) => {
  return request({
    url: '/api/v1/trace/material_information',
    method: 'get',
    params,
  });
};
