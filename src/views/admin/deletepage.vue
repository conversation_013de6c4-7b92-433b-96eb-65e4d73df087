<template>
  <header class="header">
    <LangChange class="lang" color="black" />
    <el-button class="back-button" @click="logout" type="danger" plain> Log out </el-button>
  </header>
  <div class="container" style="margin-top: 20px">
    <el-row justify="center" :size="20" style="margin-bottom: 2%">
      <el-space wrap :size="20" style="align-items: center">
        <div style="display: flex; align-items: center">
          <span style="margin-right: 5px">{{ t('qc.start') }}:</span>
          <el-input v-model="st1" type="date" style="width: 150px" />
        </div>

        <div style="display: flex; align-items: center">
          <span style="margin-right: 5px">{{ t('qc.end') }}:</span>
          <el-input v-model="ed1" type="date" style="width: 150px" />
        </div>

        <el-button type="primary" @click="getdatadetails">{{ t('qc.search') }}</el-button>

        <el-select
          v-if="upid1 == 1 || upid1 == 4"
          v-model="subData"
          :placeholder="t('qc.lean')"
          @change="getdatadetails"
          clearable
          style="width: 200px"
        >
          <el-option v-for="item in dataToDisplay" :key="item.k" :label="item.v" :value="item.k" />
        </el-select>

        <el-input
          clearable
          v-model="searchString"
          @keyup="searchByKeyword"
          :placeholder="t('details.search')"
          style="width: 200px"
        />
      </el-space>
    </el-row>
    <el-row>
      <p></p>
      <el-table
        ref="table"
        stripe
        border
        :data="filteredTableData"
        style="width: 100%; height: 42rem"
        v-loading="isLoading"
      >
        <el-table-column
          prop="VI_CATEGORY"
          v-if="$i18n.locale === 'vi'"
          width="100"
          :label="t('details.category')"
        >
          <template #default="scope">
            {{ scope.row.VI_CATEGORY }}
          </template>
        </el-table-column>
        <el-table-column prop="EN_CATEGORY" v-else width="100" :label="t('details.category')">
          <template #default="scope">
            {{ scope.row.EN_CATEGORY }}
          </template>
        </el-table-column>
        <el-table-column prop="RY" width="150" :label="t('details.ry')"></el-table-column>
        <el-table-column prop="SKU" label="SKU" width="150"></el-table-column>
        <el-table-column prop="SUBCATEGORY" width="150" :label="t('qc.lean')"></el-table-column>

        <el-table-column prop="CREATEDATE" :label="t('details.date')" width="100"></el-table-column>
        <el-table-column prop="TIMES" :label="t('details.times')" width="100"></el-table-column>

        <el-table-column
          prop="OUTQTY"
          width="100"
          :label="` ${t('details.output')} (${totalOutput})`"
        ></el-table-column>
        <el-table-column
          prop="INSQTY"
          width="100"
          :label="`${t('details.insQty')} &nbsp; (${totalInspection})`"
        ></el-table-column>
        <el-table-column
          prop="DEFECT_QTY"
          width="100"
          :label="`${t('details.dfQty')} (${totalDefects})`"
        ></el-table-column>
        <el-table-column
          prop="INSPECTION_RATE"
          width="135"
          :label="`${t('details.insPassRate')} (${totalPassRate}%)`"
        ></el-table-column>
        <el-table-column
          v-if="$i18n.locale === 'vi'"
          prop="VI_DEFECT_NAMES"
          width="300"
          :label="t('details.dfName')"
        >
          <template #default="scope">
            {{ scope.row.VI_DEFECT_NAMES }}
          </template>
        </el-table-column>
        <el-table-column v-else prop="EN_DEFECT_NAMES" width="300" :label="t('details.dfName')">
          <template #default="scope">
            {{ scope.row.EN_DEFECT_NAMES }}
          </template>
        </el-table-column>

        <el-table-column prop="RESULTS" width="95" :label="t('details.results')">
          <template #default="{ row }">
            <div>
              {{
                locale === 'en'
                  ? row.Status == 0
                    ? 'Not Passed'
                    : 'Passed'
                  : row.Status == 0
                  ? 'Không đạt'
                  : 'Đạt'
              }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="Actions" width="100">
          <template #default="scope">
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">
              Delete
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
  </div>
</template>
<script>
  export default {
    name: 'report2',
  };
</script>
<script setup>
  import axios from 'axios';
  import { ref, reactive } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { writeFile, utils } from 'xlsx-js-style';
  import { ElLoading } from 'element-plus';
  import { useRouter } from 'vue-router';
  const router = useRouter();
  const { t, locale } = useI18n();
  const dialogVisible = ref(false);
  const urlIp = `${import.meta.env.VITE_BACKEND_URL}`;
  const emit = defineEmits(['setshow']);
  const sno = defineProps(['sno']);
  const sku1 = ref('');
  const ry1 = ref([]);
  const upid1 = ref('');
  const st1 = ref();
  const ed1 = ref();
  const searchString = ref('');
  var totalOutput = ref(0);
  var totalInspection = ref(0);
  var totalDefects = ref(0);
  var totalPassRate = ref(0);

  const state = reactive({
    data: [],
    data_details: [],
    in: [
      {
        k: 1,
        v: t('subCategory.logoBody'),
      },
      {
        k: 2,
        v: t('subCategory.muGiayIn'),
      },
      {
        k: 3,
        v: t('subCategory.ctKhac'),
      },
    ],
    ep: [
      {
        k: 4,
        v: t('subCategory.logoDa'),
      },
      {
        k: 5,
        v: t('subCategory.logoVai'),
      },
      {
        k: 6,
        v: t('subCategory.epNhiet'),
      },
      {
        k: 7,
        v: t('subCategory.ctKhac'),
      },
    ],
    excelValue: computed(() => [
      {
        k: 1,
        v: t('subCategory.logoBody'),
      },
      {
        k: 2,
        v: t('subCategory.muGiayIn'),
      },
      {
        k: 3,
        v: t('subCategory.ctKhac'),
      },
      {
        k: 4,
        v: t('subCategory.logoDa'),
      },
      {
        k: 5,
        v: t('subCategory.logoVai'),
      },
      {
        k: 6,
        v: t('subCategory.epNhiet'),
      },
      {
        k: 7,
        v: t('subCategory.ctKhac'),
      },
    ]),
  });
  const subData = ref('');

  let filteredTableData = computed(() => {
    const searchStr = searchString.value.trim().toLowerCase();
    return state.data_details.filter((row) => {
      return Object.values(row).some((val) =>
        val.toString().trim().toLowerCase().includes(searchStr)
      );
    });
  });

  const searchByKeyword = () => {
    // Reset values
    totalOutput.value = 0;
    totalInspection.value = 0;
    totalDefects.value = 0;
    totalPassRate.value = 0;
    const a = ref([]);

    a.value = filteredTableData.value.map((item) => {
      // Update values
      totalOutput.value += Number(item.OUTQTY);
      totalInspection.value += Number(item.INSQTY);
      totalDefects.value += Number(item.DEFECT_QTY);
      totalPassRate.value =
        Number.parseFloat(
          ((totalInspection.value - totalDefects.value) / totalInspection.value) * 100
        ).toFixed(2) || 0.0;

      return item;
    });
  };

  const dataToDisplay = computed(() => {
    return upid1.value == 1 ? state.in : state.ep;
  });

  /* getdatadetails */
  const timeMapping = {
    1: '07:30-09:30',
    2: '09:31-11:00',
    3: '12:00-14:00',
    4: '14:01-16:30',
    5: 'TC',
  };

  const formatDate = (date) => new Date(date).toLocaleDateString('en-GB');

  const calculateTotals = (item) => {
    totalOutput.value += Number(item.OUTQTY);
    totalInspection.value += Number(item.INSQTY);
    totalDefects.value += Number(item.DEFECT_QTY);
    totalPassRate.value =
      Number.parseFloat(
        ((totalInspection.value - totalDefects.value) / totalInspection.value) * 100
      ).toFixed(2) || 0.0;
  };

  const formatItem = (item) => {
    // const correspondingValue = state.excelValue.find((v) => v.k == item.SUBCATEGORY)?.v || '';
    return {
      ...item,
      // SUBCATEGORY: correspondingValue,
      CREATEDATE: formatDate(item.CREATEDATE),
      TIMES: timeMapping[item.TIMES] || 'Unknown',
      INSPECTION_RATE: `${item.INSPECTION_RATE}%`,
      RESULTS: item.FAIL_QTY == 0 ? 'PASS' : 'FAIL',
    };
  };

  const getdatadetails = async () => {
    isLoading.value = true;

    try {
      [totalOutput, totalInspection, totalDefects, totalPassRate].forEach((ref) => (ref.value = 0));
      state.data_details = ref([]);

      if (isNaN(new Date(st1.value)) || isNaN(new Date(ed1.value))) {
        throw new Error('Date is error !!!');
      }

      const params = {
        ry: ry1.value.length > 1 ? ry1.value : 'A',
        sku: sku1.value.length > 1 ? sku1.value : 'A',
        st: st1.value,
        ed: ed1.value,
        upid: upid1.value.length > 0 ? upid1.value : 'A',
        sub: subData.value !== 'A' && subData.value !== '' ? subData.value : 'A',
      };

      const url = `${urlIp}api/v1/qc/getreportdetails`;
      const { data } = await axios.get(url, { params });

      if (data.data != null) {
        state.data_details = data.data.map((item) => {
          calculateTotals(item);
          return formatItem(item);
        });
        console.log('data_details', state.data_details);
      } else {
        throw new Error('No data !!!');
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      alert(error.message || 'Error loading data.');
    } finally {
      isLoading.value = false;
    }
  };
  const isLoading = ref(false);

  async function handleDelete(row) {
    try {
      // Hiển thị hộp thoại xác nhận
      await ElMessageBox.confirm('Bạn có chắc chắn muốn xóa dữ liệu này?', 'Xác nhận', {
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy',
        type: 'warning',
      });

      const parts = row.CREATEDATE.split(/[-/]/); // tách ngày, tháng, năm
      const formattedDate = `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;

      // Gọi API POST để xóa dữ liệu
      const url = `${urlIp}api/v1/qc/deleteqc`;

      const response = await axios.post(url, {
        CREATEDATE: formattedDate,
        RY: row.RY.trim(),
        TIMES1: row.TIMES1,
      });
      // Kiểm tra phản hồi nếu cần
      if (response.data.code === 200) {
        ElMessage.success('Xóa thành công!');
        getdatadetails();
      } else {
        ElMessage.error(response.data.msg || 'Xóa thất bại!');
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error(error);
        ElMessage.error('Xóa thất bại!');
      }
    }
  }

  function logout() {
    localStorage.removeItem('token');
    router.push('/login');
  }
</script>

<style scoped>
  .subCategory {
    margin-left: 20px;
  }
  .el-select {
    top: 0;
  }

  .filter-group {
    display: flex;
    flex-wrap: wrap;
  }
  .details-search {
    margin-left: 20px;
    width: 12vw;
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px;
    background-color: #be83f1;
    color: white;
  }

  .back-button {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    color: white;
    width: 3rem;
    height: 3rem;
  }
  .arrow-icon {
    width: 24px;
    height: 24px;
  }

  .title {
    flex: 1;
    margin-left: 16px;
    font-size: 1.5rem;
  }

  .lang {
    margin-left: auto;
    padding: 10px;
  }
</style>
