<template>
  <div class="login-wrapper">
    <div class="login-box">
      <el-card shadow="hover" class="login-card">
        <div class="login-header">
          <h2><PERSON><PERSON><PERSON> nhập hệ thống</h2>
        </div>

        <el-form :model="form" :rules="rules" ref="formRef" label-position="top">
          <el-form-item label="Tài khoản" prop="username">
            <el-input
              v-model="form.username"
              placeholder="Nhập tài khoản"
              prefix-icon="el-icon-user"
              clearable
            />
          </el-form-item>

          <el-form-item label="<PERSON>ậ<PERSON> khẩu" prop="password">
            <el-input
              v-model="form.password"
              placeholder="Nhập mật khẩu"
              :type="passwordVisible ? 'text' : 'password'"
              :suffix-icon="passwordVisible ? 'el-icon-view' : 'el-icon-view-off'"
              @suffix-icon-click="togglePasswordVisible"
              show-password
              clearable
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleLogin" class="btn-login" round>
              Đ<PERSON>ng nhập
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue';
  import { ElMessage } from 'element-plus';
  import { useRouter } from 'vue-router';
  const router = useRouter();
  const formRef = ref(null);
  const form = ref({
    username: '',
    password: '',
  });

  const rules = {
    username: [{ required: true, message: 'Vui lòng nhập tài khoản', trigger: 'blur' }],
    password: [{ required: true, message: 'Vui lòng nhập mật khẩu', trigger: 'blur' }],
  };

  // Dữ liệu người dùng giả (có thể thêm nhiều tài khoản nếu cần)
  const mockUsers = [{ username: 'admin', password: '123456' }];

  const passwordVisible = ref(false);
  const togglePasswordVisible = () => {
    passwordVisible.value = !passwordVisible.value;
  };

  const handleLogin = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        const foundUser = mockUsers.find(
          (user) => user.username === form.value.username && user.password === form.value.password
        );
        localStorage.setItem('token', 'your-token');
        if (foundUser) {
          ElMessage.success(`Xin chào, ${form.value.username}`);

          router.push({ name: 'deletepage' });

          console.log('✅ Đã chuyển đến:', router.currentRoute.value.fullPath);
        } else {
          ElMessage.error('Sai tài khoản hoặc mật khẩu.');
        }
      } else {
        ElMessage.error('Vui lòng điền đầy đủ thông tin.');
      }
    });
  };

  const resetForm = () => {
    formRef.value.resetFields();
  };
</script>

<style scoped>
  .login-wrapper {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(to right, #e0eafc, #cfdef3);
  }

  .login-box {
    width: 100%;
    max-width: 420px;
    padding: 20px;
  }

  .login-card {
    padding: 30px 40px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    background-color: white;
  }

  .login-header {
    text-align: center;
    margin-bottom: 25px;
  }

  .logo {
    width: 60px;
    margin-bottom: 10px;
  }

  .el-input {
    height: 40px;
  }

  .btn-login {
    width: 100%;
  }
</style>
