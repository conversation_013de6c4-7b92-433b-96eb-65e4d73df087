<template>
  <div class="container">
    <el-row>
      <div style="width: 100%" v-if="state.data">
        <!-- <PERSON><PERSON>t Menu chỉ xuất hiện trên màn hình nhỏ -->
        <button v-if="isMobile" class="menu-button" @click="toggleMenu">
          {{ itemTranslatedName(selectedTab) || '☰ Menu' }}
        </button>

        <!-- <PERSON>h sách các nút -->
        <div class="tab-buttons" :class="{ 'show-menu': showMenu }">
          <button
            :class="active == index + 1 ? 'active' : 'button'"
            v-for="(item, index) in state.data"
            :key="item.SNO"
            @click="
              setSNO(item.SNO, index);
              closeMenu();
            "
          >
            {{ itemTranslatedName(item.NAME) }}
          </button>
        </div>
      </div>
    </el-row>
  </div>

  <div>
    <inputpage :sno="sno" @setshow="setshow"></inputpage>
  </div>
</template>
<script setup>
  import inputpage from './inputpage.vue';
  import axios from 'axios';
  import { ref, reactive, onMounted, watch } from 'vue';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();
  const sno = ref(1);
  const urlIp = `${import.meta.env.VITE_BACKEND_URL}`;
  const emit = defineEmits(['setshow']);
  const state = reactive({
    titles: [],
    data: [],
  });
  const active = ref(1);
  const isMobile = ref(window.innerWidth <= 1023);
  const showMenu = ref(false);
  const selectedTab = ref(''); // Biến để lưu tên tab được chọn

  const toggleMenu = () => {
    showMenu.value = !showMenu.value;
  };

  const closeMenu = () => {
    showMenu.value = false;
  };

  window.addEventListener('resize', () => {
    isMobile.value = window.innerWidth <= 1023;
    if (!isMobile.value) {
      showMenu.value = false;
    }
  });

  async function readFile() {
    const url = urlIp + 'api/v1/qc/titleindex4?sno=0';
    try {
      const response = await axios.get(url);
      state.data = response.data.data;
    } catch (error) {
      console.error('Lỗi khi đọc dữ liệu:', error);
    }
  }

  const setSNO = (data, index) => {
    sno.value = data;
    active.value = index + 1;
    selectedTab.value = state.data[index].NAME;
  };

  function itemTranslatedName(name) {
    const vietnameseToEnglish = {
      'Lỗi chặt': t('qc.cut'),
      'Lỗi may': t('qc.stitch'),
      'Lỗi gò': t('qc.asseml'),
    };
    return vietnameseToEnglish[name] || name;
  }

  onMounted(() => {
    readFile();
  });
</script>
