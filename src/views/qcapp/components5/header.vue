<template>
  <header class="header">
    <button v-if="title[1]" class="back-button" @click="goBack">
      <el-icon>
        <ArrowLeft />
      </el-icon>
    </button>
    <h1 class="title">{{ getTitle(title[0]) }}</h1>
    <LangChange class="lang" color="black" />
  </header>
</template>

<script setup>
  import { ArrowLeft } from '@element-plus/icons-vue';
  import LangChange from '@/components/LangChange/index.vue';
  import { useI18n } from 'vue-i18n';
  const { t } = useI18n();
  const emit = defineEmits(['setshow']);
  const props = defineProps(['title']);
  const { title } = toRefs(props);
  const goBack = () => {
    emit('setshow', 0, true, '1');
  };

  const getTitle = () => {
    switch (title.value[0]) {
      case '5':
        return t('qc.Input');
      case '7':
        return t('qc.Input1');
      case '4':
        return t('qc.Report');
      case '3':
        return t('qc.Todays');
      case '8':
        return t('qc.exportovertime');

      default:
        return 'HOME';
    }
  };
</script>

<style scoped>
 
</style>
