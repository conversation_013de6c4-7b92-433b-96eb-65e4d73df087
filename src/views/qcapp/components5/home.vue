<template>
  <div class="home-page">
    <div class="home-header">HOME</div>
    <el-row :gutter="24" class="home-row">
      <el-col :xs="24" :sm="8">
        <el-card class="home-card" @click="onInput">
          <el-icon class="home-card-icon">
            <Edit />
          </el-icon>
          <div class="home-card-title">Nhập báo cáo</div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="8">
        <el-card class="home-card" @click="onToday">
          <el-icon class="home-card-icon">
            <Clock />
          </el-icon>
          <div class="home-card-title">Kết quả kiểm hàng hôm nay</div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="8">
        <el-card class="home-card" @click="onExport">
          <el-icon class="home-card-icon">
            <ChatLineSquare />
          </el-icon>
          <div class="home-card-title">Xuất báo cáo theo giờ</div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'home',
};
</script>

<script setup>
import { Edit, Clock, ChatLineSquare } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const emit = defineEmits(['setshow'])

function onInput() {
  emit('setshow', 5, false, '5')
}
function onToday() {
  emit('setshow', 5, false, '3')
}
function onExport() {
  emit('setshow', 8, false, '8')
}
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  background: #f8f9fb;
  padding: 0;
}

.home-header {
  width: 100%;
  background: linear-gradient(90deg, #a084e8 0%, #7c3aed 100%);
  color: #fff;
  font-size: 2.2rem;
  font-weight: bold;
  text-align: center;
  padding: 32px 0 24px 0;
  letter-spacing: 2px;
  border-radius: 0 0 18px 18px;
  box-shadow: 0 2px 8px rgba(120, 80, 180, 0.08);
}

.home-row {
  margin: 0 auto;
  max-width: 1100px;
  padding: 40px 0 0 0;
}

.home-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 180px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(120, 80, 180, 0.08);
  background: #fff;
  cursor: pointer;
  transition: box-shadow 0.2s, transform 0.2s;
  margin-bottom: 32px;
  text-align: center;
}

.home-card:hover {
  box-shadow: 0 8px 24px rgba(120, 80, 180, 0.18);
  transform: translateY(-6px) scale(1.03);
}

.home-card-icon {
  font-size: 2.8rem;
  color: #7c3aed;
  margin-bottom: 18px;
}

.home-card-title {
  font-size: 1.18rem;
  font-weight: 600;
  color: #303133;
}

@media (max-width: 900px) {
  .home-row {
    padding: 24px 0 0 0;
  }

  .home-card {
    min-height: 140px;
    font-size: 1rem;
  }
}

@media (max-width: 600px) {
  .home-header {
    font-size: 1.3rem;
    padding: 18px 0 12px 0;
    border-radius: 0 0 10px 10px;
  }

  .home-row {
    padding: 12px 0 0 0;
  }

  .home-card {
    min-height: 100px;
    font-size: 0.95rem;
    margin-bottom: 18px;
  }

  .home-card-icon {
    font-size: 2rem;
    margin-bottom: 10px;
  }
}
</style>
