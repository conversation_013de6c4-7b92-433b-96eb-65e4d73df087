<template>
  <div class="container">
    <div v-if="state.titles">
      <inputpage_head ref="inputpage" :sno="sno.sno" @change-ok="change_ok"></inputpage_head>
      <hr />
      <el-row justify="center">
        <el-radio-group v-model="Checked">
          <el-radio label="1">07:30-09:30</el-radio>
          <el-radio label="2">09:30-11:30</el-radio>
          <el-radio label="3">12:30-14:30</el-radio>
          <el-radio label="4">14:30-16:30</el-radio>
          <el-radio label="5">16:30-18:30</el-radio>
        </el-radio-group>
      </el-row>
      <p>
        <el-row justify="center">
          <el-space :size="20" :wrap="true">
            <el-card class="input-card" shadow="always" v-for="(title, index) in state.titles" :key="title.SNO"
              :header="locale === 'en' ? title.ENAME : title.NAME" style="
                width: 200px;
                min-height: 150px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
              ">
              <!-- Phần input number trước -->
              <el-input-number v-model="state.num[index]" size="small" class="input-number" style="width: 110px"
                placeholder="Nhập số lượng" @change="OnChangeData()" @click="
                  () => {
                    if (state.num[index] < 0) {
                      state.num[index] = '';
                    }
                    onInputClick(index);
                  }
                "></el-input-number>
              <span class="suffix"><b>{{ getSuffix(title.SNO) }}</b></span>

              <!-- Thêm textarea vào phần tử cuối cùng, căn chỉnh bên dưới -->
              <div v-if="state.num[index] > 0 && index === state.titles.length - 2 && sno.sno === 2"
                style="margin-top: 10px">
                <textarea v-model="state.additionalInput" placeholder="Nhập thêm dữ liệu" rows="3"
                  style="width: 100%; padding: 5px; box-sizing: border-box"></textarea>
              </div>

              <div v-else-if="
                state.num[index] > 0 &&
                index === state.titles.length - 1 &&
                (sno.sno === 1 || sno.sno === 3)
              " style="margin-top: 10px">
                <textarea v-model="state.additionalInput" placeholder="Nhập thêm dữ liệu" rows="3"
                  style="width: 100%; padding: 5px; box-sizing: border-box"></textarea>
              </div>
            </el-card>
          </el-space>
        </el-row>
      </p>
      <hr />
      <el-row justify="center">
        <el-space :size="10" :wrap="true">
          <div v-if="sno.sno !== 1 && sno.sno !== 3">
            <el-card :header="t('qc.output')">
              <el-input-number :min="0" v-model="TQuty" class="input-number" size="small" style="width: 100px"
                @change="OnChangeData" @click="OnChangeData"></el-input-number>
            </el-card>
          </div>

          <el-card :header="t('qc.inspectionqty')">
            <el-input-number :min="0" v-model="InQuty" size="small" style="width: 190px" @change="OnChangeData"
              :readonly="true"></el-input-number>
          </el-card>
          <el-card>
            <div style="width: 190px; min-height: 100px">
              {{ t('qc.passrate') }} : {{ Sum }} %
              <br />
              {{
                status == 'ACCEPT'
                  ? t('qc.status1')
                  : status == 'REJECT'
                    ? t('qc.status0')
                    : t('qc.status')
              }}
            </div>
          </el-card>
        </el-space>
      </el-row>
      <hr />
      <el-row justify="center">
        <el-button type="primary" @click="insertDialogVisible = true">INSERT</el-button>
        <el-dialog v-model="insertDialogVisible" title="Cảnh báo" width="500" center>
          <div style="text-align: center"> {{ t('notification.shutdownAlert') }} </div>
          <template #footer>
            <div class="dialog-footer">
              <el-button @click="insertDialogVisible = false">{{ t('btn.no') }}</el-button>
              <el-button type="primary" @click="InsertClick">{{ t('btn.yes') }}</el-button>
            </div>
          </template>
        </el-dialog>
      </el-row>
    </div>
  </div>
</template>
<script>
export default {
  name: 'inputpage',
};
</script>

<script setup>
import axios from 'axios';
import { ref, reactive, onMounted, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import inputpage_head from './inputpage_head.vue';
const inputpage = ref(null);

// Define a reactive variable
const count = ref(0);
const { t, locale } = useI18n();

const urlIp = `${import.meta.env.VITE_BACKEND_URL}`;
const emit = defineEmits(['setshow']);
const sno = defineProps(['sno']);
const RY = ref('');
const SKU = ref('');
var times1 = ref(1);
const Checked = ref('');
const TQuty = ref(0); //生產量
const InQuty = ref(0); //品檢量
const Sum = ref(0);
const Ispass = ref('0');
const status = ref('N/A');
const isok = ref('');
const insertDialogVisible = ref(false);
const state = reactive({
  titles: [],
  data: [],
  num: [],
  additionalInput: ref(''),
});
const ngCount = ref('');
const getSuffix = (sno) => {
  if (sno < 10 || sno == 29) return '/10';
  if (sno == 22) return '/2';
  if (sno >= 23 && sno <= 28) return '/6';
  return '';
};

const selectedData = ref('');

const rys = ref(['']);

async function readFile() {
  const url = urlIp + 'api/v1/qc/titleindex4';
  axios.get(url, { params: { sno: sno.sno } }).then((result) => {
    state.titles = result.data.data;
    let tmp = [];
    for (var i = 0; i < result.data.data.length; i++) {
      tmp.push(0);
    }
    state.data = tmp;
  });
}

const change_ok = (ok, ry, sku_CP, select) => {
  isok.value = ok.value;
  rys.value = ry.value;
  SKU.value = sku_CP.value;
  selectedData.value = select.value;
};

onMounted(() => {
  readFile();
  OnChangeData();
  status.value = 'N/A';
});

const clickedInputs = new Set();
const temp = ref(0);
const a = ref(0);
const b = ref(0);
// Khi nhập input number thì InQuty tự tăng
const onInputClick = (index) => {
  let statesno = 0;
  if (sno.sno == 1) {
    if (!clickedInputs.has(index) && index >= 0 && index <= 5) {
      let errCount = ref(0);
      for (let i = 0; i < state.num.length; i++) {
        statesno = state.titles[i]?.SNO;
        if (state.num[i] < 0) {
          state.num[i] = '';
        }

        if (state.num[i] !== '' && state.num[i] >= 0 && statesno != 10) {
          errCount.value += 1;
          InQuty.value = errCount.value * 10;
        } else if (errCount.value == 0) {
          InQuty.value = 0;
        }
      }
    }
  } else if (sno.sno == 3 && index != 7) {
    if (!clickedInputs.has(index) && index >= 0 && index <= 6 && statesno != 30) {
      let errCount = ref(0);
      for (let i = 0; i < state.num.length; i++) {
        if (state.num[i] < 0) {
          state.num[i] = '';
        }

        if (state.num[i] >= 0 && i != 6 && i != 7 && state.num[i] !== '') {
          errCount.value += 1;
        } else if (errCount.value == 0) {
          InQuty.value = 0;
        }
      }

      if (index <= 5) {
        a.value = errCount.value * 6;
      }

      if (state.num[6] >= 0 && state.num[6] !== '') {
        InQuty.value = a.value + 10;
      } else {
        InQuty.value = a.value;
      }
    }
  }
};
function OnChangeData() {
  ngCount.value = 0;
  let statesno = 0;

  if (state.value && state.value.titles && state.value.num) {
    for (let i = 0; i < state.value.num.length; i++) {
      const sno = Number(state.value.titles[i]?.SNO);

      if (sno !== 10 && sno !== 22 && sno !== 30) {
        ngCount.value += state.value.num[i] || 0;
      }
    }
  }

  let backupNgCountValue = ngCount.value;

  if (InQuty.value > 0 && ngCount.value <= InQuty.value) {
    status.value = ngCount.value === 0 ? 'ACCEPT' : 'REJECT';
    Ispass.value = ngCount.value === 0 ? '1' : '0';
  } else {
    status.value = 'N/A';
    Ispass.value = '0';
  }

  updateSum();

  if (sno.sno == 2) {
    for (let i = 0; i < state.num.length; i++) {
      statesno = state.titles[i]?.SNO;
      if (statesno >= 11 && statesno <= 21 && statesno !== 22) {
        if (TQuty.value >= 2 && TQuty.value <= 8) {
          InQuty.value = 4;
          if (ngCount.value > 0) {
            status.value = 'REJECT';
            Ispass.value = '0';
          } else {
            status.value = 'ACCEPT';
            Ispass.value = '1';
          }
        } else if (TQuty.value >= 9 && TQuty.value <= 15) {
          InQuty.value = 5;
          if (ngCount.value > 0) {
            status.value = 'REJECT';
            Ispass.value = '0';
          } else {
            status.value = 'ACCEPT';
            Ispass.value = '1';
          }
        } else if (TQuty.value >= 16 && TQuty.value <= 25) {
          InQuty.value = 7;
          if (ngCount.value > 0) {
            status.value = 'REJECT';
            Ispass.value = '0';
          } else {
            status.value = 'ACCEPT';
            Ispass.value = '1';
          }
        } else if (TQuty.value >= 26 && TQuty.value <= 50) {
          InQuty.value = 10;
          if (ngCount.value > 0) {
            status.value = 'REJECT';
            Ispass.value = '0';
          } else {
            status.value = 'ACCEPT';
            Ispass.value = '1';
          }
        } else if (TQuty.value >= 51 && TQuty.value <= 90) {
          InQuty.value = 15;
          if (ngCount.value >= 2) {
            status.value = 'REJECT';
            Ispass.value = '0';
          } else {
            status.value = 'ACCEPT';
            Ispass.value = '1';
          }
        } else if (TQuty.value >= 91 && TQuty.value <= 150) {
          InQuty.value = 22;
          if (ngCount.value >= 2) {
            status.value = 'REJECT';
            Ispass.value = '0';
          } else {
            status.value = 'ACCEPT';
            Ispass.value = '1';
          }
        } else if (TQuty.value >= 151 && TQuty.value <= 280) {
          InQuty.value = 34;
          if (ngCount.value >= 3) {
            status.value = 'REJECT';
            Ispass.value = '0';
          } else {
            status.value = 'ACCEPT';
            Ispass.value = '1';
          }
        } else if (TQuty.value >= 281 && TQuty.value <= 500) {
          InQuty.value = 52;
          if (ngCount.value >= 4) {
            status.value = 'REJECT';
            Ispass.value = '0';
          } else {
            status.value = 'ACCEPT';
            Ispass.value = '1';
          }
        } else if (TQuty.value >= 501 && TQuty.value <= 1200) {
          InQuty.value = 82;
          if (ngCount.value >= 6) {
            status.value = 'REJECT';
            Ispass.value = '0';
          } else {
            status.value = 'ACCEPT';
            Ispass.value = '1';
          }
        } else if (TQuty.value >= 1201 && TQuty.value <= 3200) {
          InQuty.value = 127;
          if (ngCount.value >= 8) {
            status.value = 'REJECT';
            Ispass.value = '0';
          } else {
            status.value = 'ACCEPT';
            Ispass.value = '1';
          }
        } else if (TQuty.value >= 3201 && TQuty.value <= 10000) {
          InQuty.value = 202;
          if (ngCount.value >= 11) {
            status.value = 'REJECT';
            Ispass.value = '0';
          } else {
            status.value = 'ACCEPT';
            Ispass.value = '1';
          }
        } else if (TQuty.value >= 10001 && TQuty.value <= 35000) {
          InQuty.value = 317;
          if (ngCount.value >= 15) {
            status.value = 'REJECT';
            Ispass.value = '0';
          } else {
            status.value = 'ACCEPT';
            Ispass.value = '1';
          }
        } else {
          InQuty.value = 0;

          status.value = 'N/A';
          Ispass.value = '0';
        }
      } else if (statesno === 22) {
        if (state.num[i] > 1) {
          status.value = 'REJECT';
          Ispass.value = '0';
        }
      }
    }
  } else {
    // Các trường hợp khác
    for (let i = 0; i < state.num.length; i++) {
      const sno = state.titles[i]?.SNO;
      if (sno === 10 || sno === 30 || state.num[i] == 1 || state.num[i] == 0) {
        status.value = 'ACCEPT';
        Ispass.value = '1';
      } else if (state.num[i] >= 2 && sno !== 10 && sno !== 30) {
        status.value = 'REJECT';
        Ispass.value = '0';
        break;
      } else {
        status.value = 'N/A';
        Ispass.value = '0';
      }
    }
  }
}

watch(
  () => sno.sno,
  (newSno) => {
    InQuty.value = 0;
    Sum.value = 0.0;
    state.num = [];
    selectedData.value = [];
    clickedInputs.clear();
    updateSum();
  }
);

watch(sno, () => {
  readFile();
  InQuty.value = 0;
  selectedData.value = '';
  TQuty.value = 0;
  status.value = 'N/A';
});

watch(
  () => state.num,
  () => {
    updateSum();
  },
  { deep: true }
);
watch(InQuty, () => {
  updateSum();
});

function updateSum() {
  ngCount.value = 0;

  // Tính tổng số lỗi
  for (let i = 0; i < state.num.length; i++) {
    ngCount.value += state.num[i] || 0;
  }

  // Tính toán lại tỷ lệ pass (Sum)
  if (InQuty.value > 0 && ngCount.value <= InQuty.value) {
    Sum.value = 100 - parseFloat(((ngCount.value / InQuty.value) * 100).toFixed(1));
  } else {
    Sum.value = 0;
  }
}

function generateShortSub(base) {
  const rand = Math.floor(100 + Math.random() * 900); // 3 số random
  const suffix = Date.now().toString().slice(-3); // 3 số cuối timestamp
  return `${base}_${suffix}${rand}`;
}

async function InsertClick() {
  insertDialogVisible.value = false;

  // Kiểm tra dữ liệu có tồn tại không
  // const dataisexist = urlIp + 'api/v1/qc/dataisexistcsa';
  // await axios.get(dataisexist).then((res) => {
  //   if (res.data.data == 0) {
  //     times1.value = 1;
  //   } else {
  //     times1.value = res.data.data + 1;
  //   }
  // });
  times1.value = generateShortSub(selectedData.value);

  if (isok.value.length > 5) {
    if (selectedData.value == '') {
      alert(t('notification.selectline'));
      return;
    } else if (Checked.value == '') {
      alert(t('notification.selecttime'));
      return;
    } else if (InQuty.value == 0 || InQuty.value == null) {
      alert(t('notification.inspectionqty'));
      return;
    } else if (InQuty.value > TQuty.value && sno.sno == 2) {
      alert(t('notification.IQProdoutput'));
      return;
    } else if (ngCount.value > InQuty.value) {
      alert(t('notification.numbererr'));
      return;
    } else if (state.num.length == 0) {
      alert(t('notification.errorinput'));
      return;
    } else {
      let firstRyInQuty = InQuty.value;
      let firstRyTQuty = TQuty.value;
      let firstRyIsPass = Ispass.value;
      let error = 0;
      SKU.value.forEach((sku, skuIndex) => {
        if (sku.rys && sku.rys.length > 0) {
          sku.rys.forEach((ry, ryIndex) => {
            const formdata = new FormData();
            let id = [];

            // Lấy ID từ state.titles
            for (let i = 0; i < state.titles.length; i++) {
              id.push(state.titles[i].SNO);
            }

            // Thêm dữ liệu vào formdata
            formdata.append('upid', sno.sno);
            formdata.append('ry', ry);
            formdata.append('sku', sku.sku);
            formdata.append('times', Checked.value);
            formdata.append('id', id.toString());
            formdata.append('times1', times1.value);
            formdata.append('sub', selectedData.value);
            formdata.append('others', state.additionalInput || '');
            formdata.append('status', Ispass.value);

            // Chỉ thêm dữ liệu đầu tiên một lần
            if (ryIndex === 0 && skuIndex === 0) {
              formdata.append('data', state.num.toString());
            } else {
              formdata.append('data', 0);
            }

            // Sử dụng các giá trị đặc biệt cho SKU và ry đầu tiên
            if (ryIndex === 0 && skuIndex === 0) {
              formdata.append('inquty', firstRyInQuty);
              formdata.append('tquty', firstRyTQuty);
              formdata.append('ispass', firstRyIsPass);
              formdata.append('passrate', Sum.value);
            } else {
              formdata.append('inquty', 0);
              formdata.append('tquty', 0);
              formdata.append('ispass', 0);
              formdata.append('ngtimes', 0);
              formdata.append('passrate', 0);
            }

            try {
              axios({
                method: 'post',
                url: urlIp + 'api/v1/qc/titlecsa',
                data: formdata,
                headers: { 'Content-Type': 'multipart/form-data' },
              });
              if (skuIndex === SKU.value.length - 1 && ryIndex === sku.rys.length - 1) {
                // Reset trạng thái sau khi xử lý xong
                rys.value = [''];
                SKU.value = [''];
                isok.value = '';
                readFile();
                InQuty.value = 0;
                TQuty.value = 0;
                selectedData.value = ref('');
                inputpage.value.Reset_Data();

                InQuty.value = 0; // Đặt lại InQuty về 0
                Sum.value = 0.0; // Đặt lại Sum về 0
                state.num = [];
              }
            } catch (error) {
              console.log(state.data, 'ERR');
              alert('Thêm dữ liệu thất bại !!!!');
              error++;
            }
          });
        }
      });
      if (error < 1) {
        alert(t('notification.successfully'));
      } else {
        alert(t('notification.datafailed'));
      }
    }
  } else {
    alert('Not find RY-SKU:' + RY.value + '-' + SKU.value);
  }
}
</script>

<style scropt>
.box-1 {
  width: 80px;
  height: 70px;
  max-width: 100%;
  background-color: #dcdcdc;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  word-wrap: break-word;
}

.box-2 {
  width: 80px;
  height: 70px;
  max-width: 100%;
  background-color: #ffdd99;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  word-wrap: break-word;
}

.btn-Check {
  margin-top: 1rem;
}

.el-select {
  position: relative;
  top: 8px;
}

.groupBtnRy {
  margin: 10px;
}

html body {
  position: relative;
  box-sizing: border-box;
  height: 100vh;
  padding: 0;
  overflow: auto !important;
}

html body::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

html body::-webkit-scrollbar-thumb {
  background-color: #ddd;
  background-clip: padding-box;
  border: 3px solid transparent;
  border-radius: 7px;
}

html body::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.5);
}

.el-card__header {
  min-height: 80px;
}

@media (max-width: 768px) {
  .el-radio__label {
    font-size: 20px;
    padding-left: 8px;
  }

  .input-card {
    font-size: 20px;
    width: 350px !important;
    margin-left: 20px;
  }

  .input-number {
    width: 250px !important;
    height: 30px;
  }

  .el-input-number .el-input__inner,
  .el-card__header,
  .el-card__body,
  .el-input__inner {
    font-size: 18px !important;
  }

  .el-input-number__increase,
  .el-input-number__decrease {
    width: 39px !important;
  }

  .el-space .el-space--horizontal {
    margin-left: 42px !important;
  }
}
</style>
