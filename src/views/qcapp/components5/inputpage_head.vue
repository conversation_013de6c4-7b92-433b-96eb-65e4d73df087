<template>
  <el-space :wrap="true" class="main-form">
    <el-select
      clearable
      v-model="selectedID"
      :placeholder="t('qc.lean')"
      @change="subSelectedData"
      style="width: 200px"
      class="input-select"
      :value-key="'ID'"
    >
      <el-option v-for="item in data" :key="item.ID" :label="item.Val" :value="item" />
    </el-select>

    <div
      v-for="(item, skuIndex) in skus"
      :key="skuIndex"
      class="sku-ry-group"
      style="display: flex; align-items: center; background-color: #f5f5f5; padding-left: 5px"
    >
      <div>
        <div v-for="(ry, ryIndex) in item.rys" :key="ryIndex" class="ry-group">
          <el-form-item class="ry" style="margin-right: 20px">
            <el-select
              v-model="item.rys[ryIndex]"
              filterable
              allow-create
              default-first-option
              placeholder="Chọn hoặc nhập RY"
              clearable
              style="width: 170px; height: 45px"
              @change="() => searchSkufromRy(skuIndex, ryIndex)"
            >
              <el-option
                v-for="ryOption in dataRY"
                :key="ryOption.YSBH"
                :label="ryOption.YSBH"
                :value="ryOption.YSBH"
              />
            </el-select>

            <span class="groupBtnRys">
              <el-button style="font-size: 25px" type="text" @click="addNewRYField(skuIndex)"
                >+</el-button
              >
              <el-button
                v-if="item.rys.length > 1"
                style="font-size: 25px"
                type="text"
                @click="removeNewRYField(skuIndex, ryIndex)"
                >-</el-button
              >
            </span>
          </el-form-item>
        </div>
      </div>
      <el-form-item class="sku" style="margin: 0px">
        <el-input
          v-model="item.sku"
          placeholder="SKU"
          style="width: 150px; height: 30px"
          @change="handleChange"
        />
        <span class="groupBtnSku">
          <el-button style="font-size: 25px" type="text" @click="addNewSKUField(skuIndex)"
            >+</el-button
          >
          <el-button
            v-if="skus.length > 1"
            type="text"
            style="font-size: 25px"
            @click="removeNewSKUField(skuIndex)"
            >-</el-button
          >
        </span>
      </el-form-item>
    </div>

    <el-form-item>
      <el-button class="btn-Check" type="primary" @click="searchRYSKU">
        {{ t('qc.checkrysku') }}
      </el-button>
    </el-form-item>
  </el-space>

  <p
    style="
      color: blue;
      text-align: center;
      font-weight: bolder;
      font-size: larger;
      margin-bottom: 0;
    "
  >
    {{ isok }}
  </p>
</template>
<script setup>
  import axios from 'axios';

  import { reactive, ref, onMounted, watch, defineExpose, computed } from 'vue';
  import { useI18n } from 'vue-i18n';
  const urlIp = `${import.meta.env.VITE_BACKEND_URL}`;
  const props = defineProps(['sno']);
  const { sno } = toRefs(props);
  const emit = defineEmits(['change-ok']);
  const { t } = useI18n();
  const sku = ref('');

  const selectedData = ref('');
  const selectDepNo = ref('');
  const isok = ref('');

  console.log(sno.value);
  watch(sno, () => {
    Reset_Data();
    Lean(sno);
  });

  // Dữ liệu mẫu với SKU và danh sách các RY tương ứng
  const skus = ref([{ sku: '', rys: [''] }]);
  const rys = ref(['']);

  const handleChange = () => {
    isok.value = '';
    console.log(isok.value);
  };

  const data = ref([]); // Khai báo một ref để lưu trữ dữ liệu

  const Lean = async (sno) => {
    let GXLB;
    // Gán giá trị GXLB dựa trên sno
    if (sno.value == 1) {
      GXLB = 'C';
    } else if (sno.value == 2 || sno.value == 31) {
      GXLB = 'S';
    } else if (sno.value == 3 || sno.value == 32) {
      GXLB = 'A';
    } else {
      console.error('Invalid sno value. It must be 1 or 2.');
      return;
    }

    try {
      const response = await axios.post(urlIp + 'api/v1/qc/leans', { GXLB });
      data.value = response.data.data;
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  // lấy ID và lean
  const selectedID = ref('');

  const subSelectedData = async () => {
    selectedData.value = selectedID.value.Val;
    selectDepNo.value = selectedID.value.ID;
    await getRY();
  };

  // Laays RY
  const dataRY = ref([]);
  const getRY = async () => {
    let GXLB;
    // Gán giá trị GXLB dựa trên sno
    if (sno.value == 1) {
      GXLB = 'C';
    } else if (sno.value == 2 || sno.value == 31) {
      GXLB = 'S';
    } else if (sno.value == 3 || sno.value == 32) {
      GXLB = 'A';
    } else {
      console.error('Invalid sno value. It must be 1 or 2.');
      return;
    }

    try {
      const response = await axios.post(urlIp + 'api/v1/qc/getry', {
        GXLB,
        depNo: selectDepNo.value,
      });
      dataRY.value = response.data.data;
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };
  // Hàm thêm trường SKU mới
  const addNewSKUField = (index) => {
    skus.value.splice(index + 1, 0, { sku: '', rys: [''] });
  };

  // Hàm xóa trường SKU
  const removeNewSKUField = (index) => {
    skus.value.splice(index, 1);
  };

  const addNewRYField = (skuIndex) => {
    skus.value[skuIndex].rys.push('');
  };

  // Hàm xóa trường RY
  const removeNewRYField = (skuIndex, ryIndex) => {
    skus.value[skuIndex].rys.splice(ryIndex, 1);
  };

  const searchSkufromRy = (skuIndex, ryIndex) => {
    const url = urlIp + 'api/v1/qc/skufromry';

    // Lấy giá trị của RY hiện tại
    const ry = skus.value[skuIndex].rys[ryIndex];

    // Gọi API để lấy sku tương ứng với RY
    axios
      .get(url, { params: { ry: ry } })
      .then((response) => {
        const fetchedSku = response.data.data.ARTICLE;

        if (fetchedSku) {
          const currentSku = skus.value[skuIndex].sku;

          if (currentSku && currentSku !== fetchedSku) {
            alert(`Lỗi: RY ${ry} không khớp với SKU hiện tại (${currentSku})`);
            skus.value[skuIndex].sku = '';
            skus.value[skuIndex].rys[ryIndex] = '';
          } else {
            // Cập nhật SKU nếu hợp lệ
            skus.value[skuIndex].sku = fetchedSku;
          }
        } else {
          skus.value[skuIndex].sku = ''; // Nếu không có SKU phù hợp
        }
      })
      .catch((error) => {
        console.error('Error fetching SKU:', error);
        skus.value[skuIndex].sku = ''; // Nếu có lỗi, reset trường SKU
      });
  };

  const searchRYSKU = () => {
    console.log('RY:', rys.value, 'SKU:', skus.value);

    // Kiểm tra xem RY hoặc SKU có để trống không
    if (
      skus.value.some(
        (skuItem) => skuItem.sku.trim() === '' || skuItem.rys.some((ry) => ry.trim() === '')
      )
    ) {
      alert('RY hoặc SKU không được để trống!');
      return;
    }

    let rySkuPairs = ref([]);
    let skuMap = new Map();

    // Duyệt qua từng SKU
    skus.value.forEach((skuItem) => {
      const sku = skuItem.sku; // Lấy SKU
      const rysArray = skuItem.rys; // Lấy mảng RYs
      const urlSearchSku = `${urlIp}api/v1/qc/searchsku`;

      if (skuMap.has(sku)) {
        return;
      }

      // Gọi API để kiểm tra SKU
      axios
        .get(urlSearchSku, { params: { sku } })
        .then((res) => {
          const dataSku = res.data.data;
          if (dataSku) {
            // Tìm thấy SKU
            alert('Tìm thấy SKU: ' + dataSku);

            // Thêm SKU và RYs vào Map
            skuMap.set(sku, rysArray); // Lưu SKU với các RYs tương ứng

            // Cập nhật isok
            isok.value = Array.from(skuMap.entries())
              .map(([skuKey, rys]) => `${rys.join(', ')} - ${skuKey}`)
              .join(' | ');
          } else {
            // Nếu không tìm thấy SKU, kiểm tra từng RY
            rysArray.forEach((ryValue) => {
              axios
                .get(`${urlIp}api/v1/qc/search`, { params: { ry: ryValue, sku } })
                .then((response) => {
                  if (response.data.data) {
                    // Nếu tìm thấy RY-SKU
                    alert('Không tìm thấy RY-SKU: ' + response.data.data);
                  } else {
                    // Thêm RY vào Map
                    if (!skuMap.has(sku)) {
                      skuMap.set(sku, []);
                    }
                    skuMap.get(sku).push(ryValue);

                    // Cập nhật isok
                    isok.value = Array.from(skuMap.entries())
                      .map(([skuKey, rys]) => `${rys.join(', ')} - ${skuKey}`)
                      .join(' | ');
                  }
                });
            });
          }
        })
        .catch((error) => {
          console.error('Error fetching SKU:', error);
        });
    });

    // Theo dõi thay đổi
    watch([isok, rys, skus, selectedData], () => {
      emit('change-ok', isok, rys, skus, selectedData);
    });
  };

  const Reset_Data = async () => {
    console.log(1);
    selectedData.value = '';
    rys.value = [''];
    isok.value = '';
    emit('change-ok', isok, rys, skus, selectedData);
  };
  defineExpose({ Reset_Data });
  onMounted(() => {
    Lean(sno);
  });
</script>
<style scoped>
  .btn-Check {
    margin-top: 1rem;
  }
  .main-form {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px;
  }
  @media (max-width: 768px) {
    .main-form,
    button {
      font-size: 16px;
    }
    .input-select {
      margin-bottom: 20px;
    }
    .btn-Check {
      margin-top: 1rem;
      font-size: 20px;
    }
    .main-form {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0px;
    }
  }
</style>
