<template>
  <div class="inspection-dashboard">
    <!-- Header Section with Process Selection -->
    <div class="dashboard-header">
      <el-card class="header-card" shadow="hover">
        <template #header>
          <div class="header-title">
            <el-icon class="title-icon">
              <DataAnalysis />
            </el-icon>
            <h2>{{ t('qc.todaysInspection') }}</h2>
            <el-tag type="info" size="small" class="live-indicator">
              <el-icon><Timer /></el-icon>
              {{ t('qc.liveData') }}
            </el-tag>
          </div>
        </template>

        <!-- Process Selection Tabs -->
        <div class="process-selection">
          <!-- Mobile Menu Button -->
          <el-button
            v-if="isMobile"
            class="mobile-menu-btn"
            type="primary"
            @click="toggleMenu"
            :icon="Menu"
          >
            {{ itemTranslatedName(selectedTab) || t('qc.selectProcess') }}
            <el-icon class="dropdown-icon" :class="{ 'rotated': showMenu }">
              <ArrowDown />
            </el-icon>
          </el-button>

          <!-- Process Tabs -->
          <el-collapse-transition>
            <div v-show="!isMobile || showMenu" class="process-tabs">
              <el-button-group class="tab-group">
                <el-button
                  v-for="(item, index) in title"
                  :key="index"
                  :type="css[index] ? 'primary' : 'default'"
                  :class="{ 'active-tab': css[index] }"
                  @click="handleProcessSelect(item.SNO, index)"
                  class="process-tab"
                >
                  <el-icon class="tab-icon">
                    <component :is="getProcessIcon(item.SNO)" />
                  </el-icon>
                  {{ itemTranslatedName(item.NAME) }}
                </el-button>
              </el-button-group>
            </div>
          </el-collapse-transition>

          <!-- Department Selection -->
          <div class="department-selection">
            <el-select
              v-model="upid1"
              :placeholder="t('qc.selectDepartment')"
              size="large"
              clearable
              filterable
              class="department-select"
              @change="readFile3(upid1)"
              :loading="departmentLoading"
            >
              <template #prefix>
                <el-icon><OfficeBuilding /></el-icon>
              </template>
              <el-option
                v-for="item in data"
                :key="item.Val"
                :label="item.Val"
                :value="item.Val"
              >
                <div class="department-option">
                  <span class="department-name">{{ item.Val }}</span>
                </div>
              </el-option>
            </el-select>
          </div>
        </div>
      </el-card>
    </div>

    <!-- Inspection Results Section -->
    <div class="inspection-results">
      <!-- Loading State -->
      <div v-if="loading" class="loading-section">
        <el-skeleton :rows="3" animated />
        <el-skeleton :rows="3" animated />
        <el-skeleton :rows="3" animated />
      </div>

      <!-- Results Grid -->
      <div v-else-if="state.data_details && state.data_details.length > 0" class="results-grid">
        <el-card
          v-for="(data, index) in state.data_details"
          :key="`${data.TIMES}-${index}`"
          class="inspection-card"
          :class="getCardStatusClass(data.Status)"
          shadow="hover"
        >
          <!-- Time Slot Header -->
          <template #header>
            <div class="card-header">
              <div class="time-slot">
                <el-icon class="time-icon">
                  <Clock />
                </el-icon>
                <span class="time-text">{{ timeMapping[data.TIMES] || 'N/A' }}</span>
              </div>
              <el-tag
                :type="getStatusTagType(data.Status)"
                size="small"
                class="status-tag"
              >
                <el-icon class="status-icon">
                  <component :is="getStatusIcon(data.Status)" />
                </el-icon>
                {{ getStatusText(data.Status) }}
              </el-tag>
            </div>
          </template>

          <!-- Inspection Metrics -->
          <div class="metrics-grid">
            <!-- Pass Rate -->
            <div class="metric-item pass-rate">
              <div class="metric-header">
                <el-icon class="metric-icon success">
                  <TrendCharts />
                </el-icon>
                <span class="metric-label">{{ t('qc.inspectionpassrate') }}</span>
              </div>
              <div class="metric-value">
                <span class="value-number">{{ data.INSPECTION_RATE }}</span>
                <span class="value-unit">%</span>
              </div>
              <el-progress
                :percentage="parseFloat(data.INSPECTION_RATE)"
                :color="getProgressColor(data.INSPECTION_RATE)"
                :stroke-width="6"
                class="progress-bar"
              />
            </div>

            <!-- Inspection Quantity -->
            <div class="metric-item">
              <div class="metric-header">
                <el-icon class="metric-icon primary">
                  <Search />
                </el-icon>
                <span class="metric-label">{{ t('qc.inspestionqty2') }}</span>
              </div>
              <div class="metric-value">
                <span class="value-number">{{ data.INSQTY }}</span>
                <span class="value-unit">{{ t('qc.units') }}</span>
              </div>
            </div>

            <!-- Defect Quantity -->
            <div class="metric-item">
              <div class="metric-header">
                <el-icon class="metric-icon danger">
                  <WarningFilled />
                </el-icon>
                <span class="metric-label">{{ t('qc.ngqty') }}</span>
              </div>
              <div class="metric-value">
                <span class="value-number">{{ data.DEFECT_QTY }}</span>
                <span class="value-unit">{{ t('qc.defects') }}</span>
              </div>
            </div>

            <!-- Output Quantity -->
            <div class="metric-item">
              <div class="metric-header">
                <el-icon class="metric-icon info">
                  <Box />
                </el-icon>
                <span class="metric-label">{{ t('qc.output') }}</span>
              </div>
              <div class="metric-value">
                <span class="value-number">
                  {{ shouldShowOutput(data.OUTQTY) ? data.OUTQTY : 'N/A' }}
                </span>
                <span v-if="shouldShowOutput(data.OUTQTY)" class="value-unit">{{ t('qc.units') }}</span>
              </div>
            </div>
          </div>

          <!-- Additional Details -->
          <div class="card-footer">
            <el-button
              type="primary"
              link
              @click="showDetails(data)"
              class="details-btn"
            >
              {{ t('qc.viewDetails') }}
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- No Data State -->
      <div v-else class="no-data-section">
        <el-empty
          :description="t('qc.noInspectionData')"
          :image-size="120"
        >
          <template #image>
            <el-icon class="empty-icon">
              <DocumentRemove />
            </el-icon>
          </template>
          <el-button
            type="primary"
            @click="refreshData"
            :loading="loading"
          >
            {{ t('qc.refreshData') }}
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- Details Dialog -->
    <el-dialog
      v-model="detailsDialogVisible"
      :title="t('qc.inspectionDetails')"
      width="600px"
      center
      class="details-dialog"
    >
      <div v-if="selectedInspection" class="details-content">
        <!-- Details content will be added here -->
        <el-descriptions :column="2" border>
          <el-descriptions-item :label="t('qc.timeSlot')">
            {{ timeMapping[selectedInspection.TIMES] }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('qc.department')">
            {{ selectedInspection.DEPARTMENT }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('qc.inspectionpassrate')">
            {{ selectedInspection.INSPECTION_RATE }}%
          </el-descriptions-item>
          <el-descriptions-item :label="t('qc.status')">
            <el-tag :type="getStatusTagType(selectedInspection.Status)">
              {{ getStatusText(selectedInspection.Status) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import axios from 'axios';
import { ref, reactive, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  DataAnalysis,
  Timer,
  Menu,
  ArrowDown,
  Clock,
  TrendCharts,
  Search,
  WarningFilled,
  Box,
  ArrowRight,
  DocumentRemove,
  OfficeBuilding,
  CircleCheckFilled,
  CircleCloseFilled,
  QuestionFilled,
  Setting,
  Tools,
  Cpu
} from '@element-plus/icons-vue';
import { ElMessage, ElNotification } from 'element-plus';

const { t, locale } = useI18n();

// Environment and API
const urlIp = `${import.meta.env.VITE_BACKEND_URL}`;
const emit = defineEmits(['setshow']);

// Reactive state
const state = reactive({
  data: [],
  data_details: [],
  sno: ''
});

const loading = ref(false);
const departmentLoading = ref(false);
const css = ref([]);
const title = ref([]);
const isMobile = ref(window.innerWidth <= 1023);
const showMenu = ref(false);
const selectedTab = ref('');
const data = ref([]);
const upid1 = ref('');
const detailsDialogVisible = ref(false);
const selectedInspection = ref(null);



// UI Methods
function toggleMenu() {
  showMenu.value = !showMenu.value;
}

function closeMenu() {
  showMenu.value = false;
}

// Enhanced process selection handler
async function handleProcessSelect(sno, index) {
  try {
    departmentLoading.value = true;
    await Lean(sno, index);
    closeMenu();

    ElMessage({
      message: t('qc.processSelected', { process: itemTranslatedName(title.value[index]?.NAME) }),
      type: 'success',
      duration: 2000
    });
  } catch (error) {
    ElMessage.error(t('qc.processSelectionError'));
  } finally {
    departmentLoading.value = false;
  }
}

// Helper functions for UI
function getProcessIcon(sno) {
  const iconMap = {
    1: Tools,      // Cutting
    2: Setting,    // Stitching
    3: Cpu,        // Assembly
    31: Setting,   // Stitching packaging
    32: Cpu        // Before steaming
  };
  return iconMap[sno] || Setting;
}

function getCardStatusClass(status) {
  if (status === 1) return 'status-passed';
  if (status === 0) return 'status-failed';
  return 'status-unknown';
}

function getStatusTagType(status) {
  if (status === 1) return 'success';
  if (status === 0) return 'danger';
  return 'info';
}

function getStatusIcon(status) {
  if (status === 1) return CircleCheckFilled;
  if (status === 0) return CircleCloseFilled;
  return QuestionFilled;
}

function getStatusText(status) {
  if (status === 1) return locale.value === 'en' ? 'Passed' : 'Đạt';
  if (status === 0) return locale.value === 'en' ? 'Not Passed' : 'Không đạt';
  return 'N/A';
}

function getProgressColor(rate) {
  const numRate = parseFloat(rate);
  if (numRate >= 95) return '#67c23a';
  if (numRate >= 85) return '#e6a23c';
  return '#f56c6c';
}

function shouldShowOutput(outQty) {
  return !(
    state.sno == 1 ||
    state.sno == 3 ||
    state.sno == 31 ||
    state.sno == 32 ||
    outQty == '0' ||
    outQty === 0
  );
}

function showDetails(inspection) {
  selectedInspection.value = inspection;
  detailsDialogVisible.value = true;
}

function refreshData() {
  if (upid1.value) {
    readFile3(upid1.value);
  } else {
    ElMessage.warning(t('qc.selectDepartmentFirst'));
  }
}

// Responsive handling
window.addEventListener('resize', () => {
  isMobile.value = window.innerWidth <= 1023;
  if (!isMobile.value) {
    showMenu.value = false;
  }
});
  async function Lean(sno, index) {
    let GXLB;

    css.value = css.value.map((_, i) => i === index);
    if (title.value[index] && title.value[index].NAME) {
      selectedTab.value = title.value[index].NAME;
    } else {
      selectedTab.value = '';
    }
    if (sno == 1) {
      GXLB = 'C';
    } else if (sno == 2 || sno == 31) {
      GXLB = 'S';
    } else if (sno == 3 || sno == 32) {
      GXLB = 'A';
    } else {
      console.error('Invalid sno value. It must be 1 or 2.');
      return;
    }

    try {
      const response = await axios.post(urlIp + 'api/v1/qc/leans', { GXLB });
      data.value = response.data.data;
    } catch (error) {
      console.error('Error fetching data:', error);
    }
    state.sno = selectedTab.value;
  }

  const timeMapping = {
    1: '07:30-09:30',
    2: '09:30-11:30',
    3: '12:30-14:30',
    4: '14:30-16:30',
    5: '16:30-18:30',
  };

  async function readFile3(upid1) {
    state.data = [];
    loading.value = true;

    // Kiểm tra và format tên
    let formattedName = state.sno !== 'A' ? `N'${state.sno}'` : state.sno;

    const url = urlIp + 'api/v1/qc/rp2detailcsa';
    try {
      const today = new Date();
      const st = today.toISOString().split('T')[0];
      const ed = today.toISOString().split('T')[0];
      const result = await axios.get(url, {
        params: {
          ry: 'A',
          st: st,
          ed: ed,
          sub: upid1,
          name: formattedName,
        },
      });

      // Kiểm tra dữ liệu trả về
      if (result.data && Array.isArray(result.data.data) && result.data.data.length > 0) {
        const mergedData = mergeDataByDate(result.data.data);
        state.data_details = Object.values(mergedData);
      } else {
        state.data_details = [];
      }
      console.log('state.data_details', state.data_details);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      // Set trạng thái loading là false sau khi kết thúc
      loading.value = false;
    }
  }
  function ngCountCheck(DEFECT_QTY, threshold = 1) {
    if (DEFECT_QTY >= threshold) {
      acc[key].Status = 0;
    } else {
      acc[key].Status = 1;
    }
  }
  function mergeDataByDate(data) {
    function mergeDefectNames(existingValue, newValue) {
      if (!existingValue) return newValue;
      if (!newValue) return existingValue;

      const defectCounts = {};

      function processDefectString(defectString) {
        const groups = defectString.split('|');
        groups.forEach((group) => {
          const defects = group.split(' - ');
          defects.forEach((defect) => {
            defect = defect.trim();
            const matches = defect.match(/(.*?)\s*\((\d+)\)\s*$/);
            if (matches) {
              const [, defectName, count] = matches;
              const cleanDefectName = defectName.trim();
              const defectCount = parseInt(count, 10);

              defectCounts[cleanDefectName] = (defectCounts[cleanDefectName] || 0) + defectCount;
            }
          });
        });
      }

      processDefectString(existingValue);
      processDefectString(newValue);

      return Object.entries(defectCounts)
        .map(([name, count]) => `${name} (${count})`)
        .join(' - ');
    }

    function uniqueJoin(existingValue, newValue) {
      const combined = existingValue ? existingValue.split('|') : [];
      const newValues = newValue ? newValue.split('|') : [];
      const allValues = [...new Set(combined.concat(newValues))];
      return allValues.join('|');
    }

    return data.reduce((acc, item) => {
      const parts = item.CREATEDATE.split('/');
      const createdDate = new Date(parts[2], parts[1] - 1, parts[0]);
      const dateKey = createdDate.toLocaleDateString('en-GB');
      const timeKey = item.TIMES;
      const key = `${dateKey}-${timeKey}-${item.DEPARTMENT}`;

      if (!acc[key]) {
        acc[key] = {
          CREATEDATE: dateKey,
          TIMES: timeKey,
          Country: item.Country,
          DEFECT_NAME: '',
          DEFECT_QTY: 0,
          DEPARTMENT: item.DEPARTMENT,
          EN_DEFECT_NAMES: item.EN_DEFECT_NAMES,
          INSPECTION_RATE: item.INSPECTION_RATE,
          INSQTY: 0,
          OUTQTY: 0,
          Pairs: item.Pairs,
          RY: item.RY,
          SKU: item.SKU,
          Status: item.Status,
          VI_CATEGORY: item.VI_CATEGORY,
          VI_DEFECT_NAMES: '',
          XieMing: item.XieMing,
        };
      }

      acc[key].DEFECT_QTY += parseInt(item.DEFECT_QTY, 10) || 0;
      acc[key].INSQTY += parseInt(item.INSQTY, 10) || 0;
      acc[key].INSPECTION_RATE = parseFloat(
        ((acc[key].INSQTY - acc[key].DEFECT_QTY) / acc[key].INSQTY) * 100
      ).toFixed(1);

      acc[key].DEFECT_NAME = mergeDefectNames(acc[key].DEFECT_NAME, item.DEFECT_NAME);
      acc[key].VI_DEFECT_NAMES = mergeDefectNames(acc[key].VI_DEFECT_NAMES, item.VI_DEFECT_NAMES);

      acc[key].OUTQTY = uniqueJoin(acc[key].OUTQTY, item.OUTQTY);
      const outQtyArray = acc[key].OUTQTY.split('|');
      const lastElement = outQtyArray.pop();
      acc[key].OUTQTY = lastElement;

      acc[key].Country = uniqueJoin(acc[key].Country, item.Country);
      acc[key].DEPARTMENT = uniqueJoin(acc[key].DEPARTMENT, item.DEPARTMENT);
      acc[key].EN_DEFECT_NAMES = mergeDefectNames(acc[key].EN_DEFECT_NAMES, item.EN_DEFECT_NAMES);
      acc[key].Pairs = uniqueJoin(acc[key].Pairs, item.Pairs);
      acc[key].RY = uniqueJoin(acc[key].RY, item.RY);
      acc[key].SKU = uniqueJoin(acc[key].SKU, item.SKU);
      acc[key].VI_CATEGORY = uniqueJoin(acc[key].VI_CATEGORY, item.VI_CATEGORY);
      acc[key].XieMing = uniqueJoin(acc[key].XieMing, item.XieMing);

      const departmentParts = item.DEPARTMENT.split('_');
      const lastPart = departmentParts[departmentParts.length - 1];

      function ngCountCheck(DEFECT_QTY, threshold = 1) {
        if (DEFECT_QTY >= threshold) {
          acc[key].Status = 0;
        } else {
          acc[key].Status = 1;
        }
      }
      if ((lastPart === 'C' || lastPart === 'G') && acc[key].VI_DEFECT_NAMES) {
        const defects = acc[key].VI_DEFECT_NAMES.split(' - ');

        const hasCriticalDefect = defects.some((defect) => {
          const match = defect.match(/(.*?)(?: \((\d+)\))?$/);
          if (match) {
            const [, defectName, count] = match;
            return defectName.trim() !== 'Lỗi khác' && parseInt(count, 10) > 1;
          }
          return false;
        });
        if (hasCriticalDefect) {
          acc[key].Status = 0;
        }
      } else if (lastPart === 'M' && acc[key].VI_DEFECT_NAMES) {
        const defects = acc[key].VI_DEFECT_NAMES.split(' - ');

        const targetDefect = 'Kiểm tra các chi tiết trên mũ giày có đúng tiêu chuẩn chất lượng';
        let foundTargetDefect = false;

        for (const defect of defects) {
          const match = defect.trim().match(/^(.*?)(?:\s*\((\d+)\))?$/);

          if (match) {
            const [, defectName, count] = match;
            if (
              defectName.trim() ===
                'Kiểm tra các chi tiết trên mũ  giày có đúng tiêu chuẩn chất lượng:' &&
              !foundTargetDefect
            ) {
              foundTargetDefect = true;

              const countMatch = defect.match(/\((\d+)\)$/);
              const count = countMatch ? parseInt(countMatch[1], 10) : 0;
              if (count >= 2) {
                acc[key].Status = 0;
              }
            }
          }
        }
        if (acc[key].Status !== 0) {
          let totalDefectQty = 0;

          for (const defect of defects) {
            const match = defect.trim().match(/^(.*?)(?:\s*\((\d+)\))?$/);

            if (match) {
              const [, defectName, count] = match;
              const defectQty = parseInt(count, 10) || 0;

              // Chuẩn hóa tên lỗi để so sánh chính xác
              const cleanedDefectName = defectName.trim().replace(/[:：]/g, '');
              const cleanedTargetDefect = targetDefect.trim().replace(/[:：]/g, '');

              console.log(`Defect found: "${cleanedDefectName}" (count: ${defectQty})`);
              console.log(`Comparing with target: "${cleanedTargetDefect}"`);

              // Loại bỏ targetDefect
              if (cleanedDefectName !== cleanedTargetDefect) {
                totalDefectQty += defectQty;
              }
            }
          }
          if (acc[key].INSQTY >= 2 && acc[key].INSQTY <= 12) {
            if (totalDefectQty > 0) {
              acc[key].Status = 0;
            } else {
              acc[key].Status = 1;
            }
          } else if (acc[key].INSQTY >= 13 && acc[key].INSQTY <= 31) {
            if (totalDefectQty > 1) {
              acc[key].Status = 0;
            } else {
              acc[key].Status = 1;
            }
          } else if (acc[key].INSQTY >= 32 && acc[key].INSQTY <= 49) {
            if (totalDefectQty > 2) {
              acc[key].Status = 0;
            } else {
              acc[key].Status = 1;
            }
          } else if (acc[key].INSQTY >= 50 && acc[key].INSQTY <= 79) {
            if (totalDefectQty > 3) {
              acc[key].Status = 0;
            } else {
              acc[key].Status = 1;
            }
          } else if (acc[key].INSQTY >= 80 && acc[key].INSQTY <= 124) {
            if (totalDefectQty > 5) {
              acc[key].Status = 0;
            } else {
              acc[key].Status = 1;
            }
          } else if (acc[key].INSQTY >= 125 && acc[key].INSQTY <= 199) {
            if (totalDefectQty > 7) {
              acc[key].Status = 0;
            } else {
              acc[key].Status = 1;
            }
          } else if (acc[key].INSQTY >= 200 && acc[key].INSQTY <= 314) {
            if (totalDefectQty > 10) {
              acc[key].Status = 0;
            } else {
              acc[key].Status = 1;
            }
          } else if (acc[key].INSQTY >= 315) {
            if (totalDefectQty > 14) {
              acc[key].Status = 0;
            } else {
              acc[key].Status = 1;
            }
          } else {
            acc[key].Status = 'N/A';
          }
        }
      }

      return acc;
    }, {});
  }

  async function readFile() {
    const url = urlIp + 'api/v1/qc/process';
    try {
      const response = await axios.get(url);
      title.value = response.data.data;
      css.value = Array(title.value.length).fill(false);
    } catch (error) {
      console.error('Lỗi khi đọc dữ liệu:', error);
    }
  }

  function itemTranslatedName(name) {
    const vietnameseToEnglish = {
      'Lỗi chặt': t('qc.cut'),
      'Lỗi may': t('qc.stitch'),
      'Lỗi gò': t('qc.asseml'),
      'Bao bì may': t('qc.Stitchingpackaging'),
      'Trước khi hấp': t('qc.Beforesteaming'),
    };
    return vietnameseToEnglish[name] || name;
  }

  onMounted(() => {
    readFile();
  });
</script>

<style scoped>
/* Main Container */
.inspection-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px;
  overflow-x: hidden;
}

/* Header Section */
.dashboard-header {
  margin-bottom: 32px;
}

.header-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  background: white;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.title-icon {
  font-size: 1.5rem;
  color: var(--el-color-primary);
}

.header-title h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.live-indicator {
  margin-left: auto;
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
  color: white;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Process Selection */
.process-selection {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 24px;
}

.mobile-menu-btn {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.dropdown-icon {
  transition: transform 0.3s ease;
}

.dropdown-icon.rotated {
  transform: rotate(180deg);
}

.process-tabs {
  width: 100%;
}

.tab-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  width: 100%;
}

.process-tab {
  border-radius: 12px;
  padding: 12px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-height: 48px;
  flex: 1;
  min-width: 140px;
}

.process-tab:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.active-tab {
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
}

.tab-icon {
  font-size: 1.1rem;
}

/* Department Selection */
.department-selection {
  display: flex;
  justify-content: flex-end;
}

.department-select {
  min-width: 280px;
}

:deep(.department-select .el-input__wrapper) {
  border-radius: 12px;
  height: 48px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.department-option {
  display: flex;
  align-items: center;
  gap: 12px;
}

.department-name {
  font-weight: 500;
}

/* Loading Section */
.loading-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

/* Results Grid */
.inspection-results {
  margin-top: 32px;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.inspection-card {
  border-radius: 16px;
  border: none;
  transition: all 0.3s ease;
  background: white;
  overflow: hidden;
}

.inspection-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
}

.inspection-card.status-passed {
  border-left: 4px solid var(--el-color-success);
}

.inspection-card.status-failed {
  border-left: 4px solid var(--el-color-danger);
}

.inspection-card.status-unknown {
  border-left: 4px solid var(--el-color-info);
}

/* Card Header */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.time-slot {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.time-icon {
  font-size: 1.2rem;
  color: var(--el-color-primary);
}

.time-text {
  font-size: 1.1rem;
}

.status-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 12px;
}

.status-icon {
  font-size: 1rem;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.metric-item {
  background: var(--el-fill-color-extra-light);
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
}

.metric-item:hover {
  background: var(--el-fill-color-light);
  transform: translateY(-2px);
}

.metric-item.pass-rate {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.metric-icon {
  font-size: 1.1rem;
}

.metric-icon.success {
  color: var(--el-color-success);
}

.metric-icon.primary {
  color: var(--el-color-primary);
}

.metric-icon.danger {
  color: var(--el-color-danger);
}

.metric-icon.info {
  color: var(--el-color-info);
}

.metric-label {
  font-size: 0.9rem;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

.metric-value {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 8px;
}

.value-number {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--el-text-color-primary);
  line-height: 1;
}

.value-unit {
  font-size: 0.9rem;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

.progress-bar {
  margin-top: 8px;
}

:deep(.progress-bar .el-progress-bar__outer) {
  border-radius: 6px;
  background: rgba(0, 0, 0, 0.05);
}

:deep(.progress-bar .el-progress-bar__inner) {
  border-radius: 6px;
}

/* Card Footer */
.card-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.details-btn {
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* No Data Section */
.no-data-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.empty-icon {
  font-size: 4rem;
  color: var(--el-text-color-placeholder);
}

/* Details Dialog */
.details-dialog {
  border-radius: 16px;
}

:deep(.details-dialog .el-dialog) {
  border-radius: 16px;
  overflow: hidden;
}

:deep(.details-dialog .el-dialog__header) {
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
  color: white;
  padding: 20px 24px;
}

:deep(.details-dialog .el-dialog__title) {
  color: white;
  font-weight: 600;
}

.details-content {
  padding: 20px 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .inspection-dashboard {
    padding: 16px;
  }

  .results-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .process-tabs {
    display: block;
  }

  .tab-group {
    flex-direction: column;
    gap: 8px;
  }

  .process-tab {
    width: 100%;
    justify-content: center;
    min-width: unset;
  }

  .department-selection {
    justify-content: stretch;
  }

  .department-select {
    min-width: 100%;
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .header-title {
    flex-wrap: wrap;
    gap: 12px;
  }

  .header-title h2 {
    font-size: 1.3rem;
  }

  .live-indicator {
    margin-left: 0;
    order: 3;
    flex-basis: 100%;
  }
}

@media (max-width: 768px) {
  .inspection-dashboard {
    padding: 12px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .time-slot {
    font-size: 0.95rem;
  }

  .value-number {
    font-size: 1.5rem;
  }

  .metric-item {
    padding: 12px;
  }

  .process-selection {
    gap: 16px;
  }

  .header-title {
    justify-content: center;
    text-align: center;
  }

  .header-title h2 {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .inspection-dashboard {
    padding: 8px;
  }

  .dashboard-header {
    margin-bottom: 20px;
  }

  .header-card {
    border-radius: 12px;
  }

  .inspection-card {
    border-radius: 12px;
  }

  .time-text {
    font-size: 1rem;
  }

  .value-number {
    font-size: 1.3rem;
  }

  .metric-label {
    font-size: 0.85rem;
  }

  .process-tab {
    padding: 10px 16px;
    min-height: 44px;
  }

  .mobile-menu-btn {
    height: 44px;
  }

  :deep(.department-select .el-input__wrapper) {
    height: 44px;
  }
}

/* Animation Classes */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(20px);
  opacity: 0;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-color-primary-light-5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-color-primary-light-3);
}

/* Focus States */
.process-tab:focus-visible {
  outline: 2px solid var(--el-color-primary);
  outline-offset: 2px;
}

.details-btn:focus-visible {
  outline: 2px solid var(--el-color-primary);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Loading Animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}
</style>
