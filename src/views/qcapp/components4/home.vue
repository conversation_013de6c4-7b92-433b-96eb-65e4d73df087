<template>
  <div class="dashboard-home">
    <!-- Welcome Section -->
    <div class="welcome-section">
      <el-card class="welcome-card" shadow="never">
        <div class="welcome-content">
          <div class="welcome-text">
            <h1 class="welcome-title">
              <el-icon class="welcome-icon">
                <Trophy />
              </el-icon>
              {{ t('qc.welcomeBack') }}
            </h1>
            <p class="welcome-subtitle">{{ t('qc.dashboardSubtitle') }}</p>
          </div>
          <div class="welcome-stats">
            <div class="stat-item">
              <div class="stat-value">{{ todayStats.passed }}</div>
              <div class="stat-label">{{ t('qc.passedToday') }}</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-value">{{ todayStats.pending }}</div>
              <div class="stat-label">{{ t('qc.pendingReview') }}</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-value">{{ todayStats.total }}</div>
              <div class="stat-label">{{ t('qc.totalInspections') }}</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- Quick Stats Cards -->
    <div class="stats-grid">
      <el-card
        v-for="(stat, index) in quickStats"
        :key="index"
        class="stat-card"
        :class="`stat-card-${stat.type}`"
        shadow="hover"
      >
        <div class="stat-card-content">
          <div class="stat-icon-wrapper" :class="`icon-${stat.type}`">
            <el-icon class="stat-icon">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-details">
            <div class="stat-number">{{ stat.value }}</div>
            <div class="stat-title">{{ stat.title }}</div>
            <div class="stat-change" :class="stat.changeType">
              <el-icon>
                <component :is="stat.changeIcon" />
              </el-icon>
              {{ stat.change }}
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- Main Action Cards -->
    <div class="action-section">
      <h2 class="section-title">
        <el-icon class="section-icon">
          <Operation />
        </el-icon>
        {{ t('qc.quickActions') }}
      </h2>

      <div class="action-grid">
        <el-card
          v-for="(action, index) in mainActions"
          :key="index"
          class="action-card"
          :class="`action-card-${action.id}`"
          shadow="hover"
          @click="handleActionClick(action)"
        >
          <div class="action-content">
            <div class="action-header">
              <div class="action-icon-wrapper" :style="{ background: action.gradient }">
                <el-icon class="action-icon">
                  <component :is="action.icon" />
                </el-icon>
              </div>
              <div class="action-badge" v-if="action.badge">
                <el-tag :type="action.badgeType" size="small">{{ action.badge }}</el-tag>
              </div>
            </div>

            <div class="action-body">
              <h3 class="action-title">{{ action.title }}</h3>
              <p class="action-description">{{ action.description }}</p>

              <div class="action-stats" v-if="action.stats">
                <div class="action-stat" v-for="(stat, statIndex) in action.stats" :key="statIndex">
                  <span class="stat-label">{{ stat.label }}:</span>
                  <span class="stat-value">{{ stat.value }}</span>
                </div>
              </div>
            </div>

            <div class="action-footer">
              <el-button
                type="primary"
                :icon="ArrowRight"
                class="action-button"
                size="large"
              >
                {{ t('qc.getStarted') }}
              </el-button>
              <div class="action-shortcut" v-if="action.shortcut">
                <el-tag size="small" type="info">{{ action.shortcut }}</el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="activity-section">
      <el-card class="activity-card" shadow="hover">
        <template #header>
          <div class="activity-header">
            <h3 class="activity-title">
              <el-icon class="activity-icon">
                <Clock />
              </el-icon>
              {{ t('qc.recentActivity') }}
            </h3>
            <el-button type="primary" link @click="viewAllActivity">
              {{ t('qc.viewAll') }}
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
        </template>

        <div class="activity-content">
          <el-timeline class="activity-timeline">
            <el-timeline-item
              v-for="(activity, index) in recentActivities"
              :key="index"
              :timestamp="activity.timestamp"
              :type="activity.type"
              :icon="activity.icon"
              :color="activity.color"
              placement="top"
            >
              <div class="activity-item">
                <div class="activity-main">
                  <span class="activity-text">{{ activity.text }}</span>
                  <el-tag :type="activity.tagType" size="small" class="activity-tag">
                    {{ activity.status }}
                  </el-tag>
                </div>
                <div class="activity-meta">
                  <span class="activity-user">{{ activity.user }}</span>
                  <span class="activity-department">{{ activity.department }}</span>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-card>
    </div>

    <!-- Quick Links -->
    <div class="quick-links-section">
      <h2 class="section-title">
        <el-icon class="section-icon">
          <Link />
        </el-icon>
        {{ t('qc.quickLinks') }}
      </h2>

      <div class="quick-links-grid">
        <el-button
          v-for="(link, index) in quickLinks"
          :key="index"
          class="quick-link-btn"
          :type="link.type"
          :icon="link.icon"
          @click="handleLinkClick(link)"
          size="large"
        >
          {{ link.title }}
        </el-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'home',
};
</script>

<script setup>
import axios from 'axios';
import { ref, reactive, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage, ElNotification } from 'element-plus';
import {
  Trophy,
  TrendCharts,
  DataAnalysis,
  Edit,
  Clock,
  Timer,
  ArrowRight,
  Operation,
  Link,
  CircleCheckFilled,
  WarningFilled,
  DocumentCopy,
  Setting,
  ArrowUp,
  ArrowDown,
  Minus
} from '@element-plus/icons-vue';

const { t } = useI18n();

// Environment and API
const urlIp = `${import.meta.env.VITE_BACKEND_URL}`;
const emit = defineEmits(['setshow']);

// Reactive state
const Checked = ref('1');
const state = reactive({
  titles: [],
  data: [],
  css: [1, 0, 0],
  showData: false,
});

// Dashboard statistics
const todayStats = reactive({
  passed: 156,
  pending: 23,
  total: 179
});

// Quick stats for cards
const quickStats = computed(() => [
  {
    type: 'success',
    icon: CircleCheckFilled,
    title: t('qc.passRate'),
    value: '94.2%',
    change: '+2.1%',
    changeType: 'positive',
    changeIcon: ArrowUp
  },
  {
    type: 'warning',
    icon: WarningFilled,
    title: t('qc.defectRate'),
    value: '5.8%',
    change: '-1.3%',
    changeType: 'positive',
    changeIcon: ArrowDown
  },
  {
    type: 'primary',
    icon: DocumentCopy,
    title: t('qc.totalReports'),
    value: '1,247',
    change: '+15',
    changeType: 'positive',
    changeIcon: ArrowUp
  },
  {
    type: 'info',
    icon: Timer,
    title: t('qc.avgProcessTime'),
    value: '2.3h',
    change: '0%',
    changeType: 'neutral',
    changeIcon: Minus
  }
]);

// Main action cards
const mainActions = computed(() => [
  {
    id: 'input',
    title: t('qc.inputData'),
    description: t('qc.inputDataDescription'),
    icon: Edit,
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    badge: t('qc.active'),
    badgeType: 'success',
    shortcut: 'Ctrl+I',
    stats: [
      { label: t('qc.todayEntries'), value: '45' },
      { label: t('qc.pendingReview'), value: '12' }
    ],
    action: () => handleInput()
  },
  {
    id: 'inspection',
    title: t('qc.todaysInspection'),
    description: t('qc.inspectionDescription'),
    icon: TrendCharts,
    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    badge: t('qc.updated'),
    badgeType: 'primary',
    shortcut: 'Ctrl+T',
    stats: [
      { label: t('qc.completed'), value: '156' },
      { label: t('qc.inProgress'), value: '23' }
    ],
    action: () => handleTodaysReport()
  },
  {
    id: 'export',
    title: t('qc.exportOvertime'),
    description: t('qc.exportDescription'),
    icon: Timer,
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    shortcut: 'Ctrl+E',
    stats: [
      { label: t('qc.thisWeek'), value: '89h' },
      { label: t('qc.thisMonth'), value: '356h' }
    ],
    action: () => handleExportOvertime()
  }
]);

// Recent activities
const recentActivities = ref([
  {
    timestamp: '2 minutes ago',
    text: t('qc.activityInspectionCompleted'),
    status: t('qc.passed'),
    type: 'success',
    tagType: 'success',
    color: '#67c23a',
    user: 'John Doe',
    department: 'Assembly Line A'
  },
  {
    timestamp: '15 minutes ago',
    text: t('qc.activityDefectReported'),
    status: t('qc.needsReview'),
    type: 'warning',
    tagType: 'warning',
    color: '#e6a23c',
    user: 'Jane Smith',
    department: 'Cutting Department'
  },
  {
    timestamp: '1 hour ago',
    text: t('qc.activityReportGenerated'),
    status: t('qc.completed'),
    type: 'primary',
    tagType: 'primary',
    color: '#409eff',
    user: 'System',
    department: 'Quality Control'
  },
  {
    timestamp: '2 hours ago',
    text: t('qc.activityBatchProcessed'),
    status: t('qc.approved'),
    type: 'success',
    tagType: 'success',
    color: '#67c23a',
    user: 'Mike Johnson',
    department: 'Final Inspection'
  }
]);

// Quick links
const quickLinks = computed(() => [
  {
    title: t('qc.reports'),
    type: 'primary',
    icon: DocumentCopy,
    action: () => handleReports()
  },
  {
    title: t('qc.settings'),
    type: 'default',
    icon: Setting,
    action: () => handleSettings()
  },
  {
    title: t('qc.analytics'),
    type: 'success',
    icon: DataAnalysis,
    action: () => handleAnalytics()
  }
]);

// API functions
async function readFile() {
  try {
    const url = urlIp + 'api/v1/qc/titleindex4?sno=0';
    const response = await axios.get(url);
    state.data = response.data.data;

    // Update stats based on real data if available
    if (response.data.data && response.data.data.length > 0) {
      // Process real data for statistics
      updateStatsFromData(response.data.data);
    }
  } catch (error) {
    console.error('Error fetching data:', error);
    ElMessage.error(t('qc.dataLoadError'));
  }
}

function updateStatsFromData(data) {
  // Update statistics based on real data
  // This is a placeholder - implement based on your data structure
  console.log('Updating stats from data:', data);
}

// Action handlers
function handleActionClick(action) {
  if (action.action) {
    action.action();
  }
}

function handleInput() {
  Checked.value = '2';
  state.css = [false, true, false];
  tocontenx('5', '5');
}

function handleTodaysReport() {
  Checked.value = '2';
  state.css = [false, true, false];
  tocontenx('5', '3');
}

function handleExportOvertime() {
  tocontenx('8', '8');
}

function handleReports() {
  tocontenx('1', '4');
}

function handleSettings() {
  ElMessage.info(t('qc.settingsComingSoon'));
}

function handleAnalytics() {
  ElMessage.info(t('qc.analyticsComingSoon'));
}

function handleLinkClick(link) {
  if (link.action) {
    link.action();
  }
}

function viewAllActivity() {
  ElNotification({
    title: t('qc.activityLog'),
    message: t('qc.viewingAllActivities'),
    type: 'info',
    duration: 3000
  });
}

// Navigation function
function tocontenx(sno, val) {
  if (val === '5') {
    emit('setshow', sno, false, '5');
    return;
  }
  if (val === '7') {
    emit('setshow', sno, false, '7');
    return;
  }
  if (val === '4') {
    emit('setshow', sno, false, '4');
  } else if (state.css[0]) {
    emit('setshow', sno, false, '2');
  } else if (state.css[1]) {
    emit('setshow', sno, false, '3');
  }
  if (val === '8') {
    emit('setshow', sno, false, '8');
  }
}

// Lifecycle
onMounted(() => {
  readFile();
});
</script>

<style scoped>
/* Dashboard Home Styles */
.dashboard-home {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px;
  overflow-x: hidden;
}

/* Welcome Section */
.welcome-section {
  margin-bottom: 32px;
}

.welcome-card {
  border-radius: 20px;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  overflow: hidden;
  position: relative;
}

.welcome-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px;
  position: relative;
  z-index: 1;
}

.welcome-text {
  flex: 1;
}

.welcome-title {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 0 0 12px 0;
  font-size: 2rem;
  font-weight: 700;
  color: white;
}

.welcome-icon {
  font-size: 2.5rem;
  color: #ffd700;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.welcome-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.6;
}

.welcome-stats {
  display: flex;
  align-items: center;
  gap: 24px;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: rgba(255, 255, 255, 0.3);
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.stat-card {
  border-radius: 16px;
  border: none;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
}

.stat-card-success {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-left: 4px solid var(--el-color-success);
}

.stat-card-warning {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
  border-left: 4px solid var(--el-color-warning);
}

.stat-card-primary {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-left: 4px solid var(--el-color-primary);
}

.stat-card-info {
  background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
  border-left: 4px solid var(--el-color-info);
}

.stat-card-content {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px;
}

.stat-icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.icon-success {
  background: linear-gradient(135deg, var(--el-color-success) 0%, var(--el-color-success-light-3) 100%);
}

.icon-warning {
  background: linear-gradient(135deg, var(--el-color-warning) 0%, var(--el-color-warning-light-3) 100%);
}

.icon-primary {
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
}

.icon-info {
  background: linear-gradient(135deg, var(--el-color-info) 0%, var(--el-color-info-light-3) 100%);
}

.stat-icon {
  font-size: 1.5rem;
  color: white;
}

.stat-details {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--el-text-color-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-title {
  font-size: 1rem;
  color: var(--el-text-color-regular);
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.9rem;
  font-weight: 600;
}

.stat-change.positive {
  color: var(--el-color-success);
}

.stat-change.negative {
  color: var(--el-color-danger);
}

.stat-change.neutral {
  color: var(--el-text-color-regular);
}

/* Action Section */
.action-section {
  margin-bottom: 40px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 24px;
}

.section-icon {
  font-size: 1.3rem;
  color: var(--el-color-primary);
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.action-card {
  border-radius: 20px;
  border: none;
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
  background: white;
  position: relative;
}

.action-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.action-content {
  padding: 32px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.action-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.action-icon-wrapper {
  width: 64px;
  height: 64px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.action-icon {
  font-size: 1.8rem;
  color: white;
  z-index: 1;
  position: relative;
}

.action-badge {
  position: absolute;
  top: -8px;
  right: -8px;
}

.action-body {
  flex: 1;
}

.action-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.action-description {
  color: var(--el-text-color-regular);
  line-height: 1.6;
  margin: 0 0 16px 0;
}

.action-stats {
  display: flex;
  gap: 20px;
  margin-top: 16px;
}

.action-stat {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.action-stat .stat-label {
  font-size: 0.85rem;
  color: var(--el-text-color-regular);
}

.action-stat .stat-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.action-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.action-button {
  border-radius: 12px;
  font-weight: 500;
  padding: 12px 24px;
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.action-shortcut {
  font-size: 0.85rem;
  color: var(--el-text-color-placeholder);
}

/* Activity Section */
.activity-section {
  margin-bottom: 40px;
}

.activity-card {
  border-radius: 16px;
  border: none;
  background: white;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.activity-icon {
  font-size: 1.1rem;
  color: var(--el-color-primary);
}

.activity-content {
  padding: 0 24px 24px 24px;
}

.activity-timeline {
  margin-top: 16px;
}

.activity-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.activity-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.activity-text {
  font-weight: 500;
  color: var(--el-text-color-primary);
  flex: 1;
}

.activity-tag {
  flex-shrink: 0;
}

.activity-meta {
  display: flex;
  gap: 16px;
  font-size: 0.85rem;
  color: var(--el-text-color-regular);
}

.activity-user {
  font-weight: 500;
}

.activity-department {
  color: var(--el-text-color-placeholder);
}

/* Quick Links Section */
.quick-links-section {
  margin-bottom: 40px;
}

.quick-links-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.quick-link-btn {
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.quick-link-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-home {
    padding: 20px;
  }

  .action-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }

  .welcome-content {
    padding: 24px;
  }

  .welcome-title {
    font-size: 1.8rem;
  }

  .stat-value {
    font-size: 1.8rem;
  }
}

@media (max-width: 992px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .action-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .welcome-content {
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }

  .welcome-stats {
    justify-content: center;
  }

  .section-title {
    font-size: 1.3rem;
  }
}

@media (max-width: 768px) {
  .dashboard-home {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .stat-card-content {
    padding: 20px;
    gap: 16px;
  }

  .stat-icon-wrapper {
    width: 50px;
    height: 50px;
  }

  .stat-icon {
    font-size: 1.3rem;
  }

  .stat-number {
    font-size: 1.6rem;
  }

  .welcome-title {
    font-size: 1.5rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }

  .welcome-stats {
    gap: 16px;
    padding: 16px;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .action-content {
    padding: 24px;
    gap: 16px;
  }

  .action-title {
    font-size: 1.2rem;
  }

  .action-stats {
    flex-direction: column;
    gap: 12px;
  }

  .activity-content {
    padding: 0 16px 16px 16px;
  }

  .quick-links-grid {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .dashboard-home {
    padding: 12px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-card-content {
    padding: 16px;
    gap: 12px;
  }

  .welcome-content {
    padding: 20px;
  }

  .welcome-title {
    font-size: 1.3rem;
    flex-direction: column;
    gap: 8px;
  }

  .welcome-icon {
    font-size: 2rem;
  }

  .welcome-stats {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }

  .stat-divider {
    width: 100%;
    height: 1px;
  }

  .action-content {
    padding: 20px;
  }

  .action-icon-wrapper {
    width: 56px;
    height: 56px;
  }

  .action-icon {
    font-size: 1.6rem;
  }

  .action-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .action-button {
    width: 100%;
    justify-content: center;
  }

  .activity-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .activity-meta {
    flex-direction: column;
    gap: 4px;
  }

  .quick-links-grid {
    flex-direction: column;
  }

  .quick-link-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Animation Classes */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(20px);
  opacity: 0;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-color-primary-light-5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-color-primary-light-3);
}

/* Focus States */
.action-card:focus-visible {
  outline: 2px solid var(--el-color-primary);
  outline-offset: 2px;
}

.quick-link-btn:focus-visible {
  outline: 2px solid var(--el-color-primary);
  outline-offset: 2px;
}

/* Loading Animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .dashboard-home {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  }

  .stat-card,
  .action-card,
  .activity-card {
    background: var(--el-bg-color-page);
    border: 1px solid var(--el-border-color-dark);
  }

  .welcome-card {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  }
}
</style>
