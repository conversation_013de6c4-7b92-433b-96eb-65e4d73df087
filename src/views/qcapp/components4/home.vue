<template>
  <div style="margin: 30px" class="container1">
    <div class="hexagon-container">
      <div class="hexagon" id="hex" @click="Input()">{{ t('qc.Input') }}</div>
      <!-- <div class="hexagon" id="hex" @click="Input1()">{{ t('qc.Input1') }}</div> -->
      <div class="hexagon" id="hex" @click="TodaysReport1">{{ t('qc.Todays') }}</div>
      <!-- <div class="hexagon" id="hex" @click="tocontenx('1', '4')">{{ t('qc.Report') }}</div> -->
      <div class="hexagon" id="hex" @click="tocontenx('8', '8')">{{ t('qc.exportovertime') }}</div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'home',
  };
</script>
<script setup>
  import axios from 'axios';
  import { ref, reactive, onMounted } from 'vue';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();
  const urlIp = `${import.meta.env.VITE_BACKEND_URL}`;
  const emit = defineEmits(['setshow']); //定义一个变量来接收父组件传来的方法
  const Checked = ref('1');
  const state = reactive({
    titles: [],
    data: [],
    css: [1, 0, 0],
    showData: false,
  });

  async function readFile() {
    const url = urlIp + 'api/v1/qc/titleindex4?sno=0';
    axios.get(url).then((data) => {
      state.data = data.data.data;
      // console.log('Dữ liệu:', state.data);
    });
  }

  // Hàm chuyển đổi tên từ tiếng Việt sang tiếng Anh
  function itemTranslatedName(name) {
    const vietnameseToEnglish = {
      'In thân/ logo thân': t('qc.1'),
      'In logo đế trung': t('qc.2'),
      'Dán mouse đệm đế trung': t('qc.3'),
      'Ép nhiệt/ ép cao tầng': t('qc.4'),
      Thêu: t('qc.5'),
      // Thêm các cặp key-value khác nếu cần
    };
    return vietnameseToEnglish[name] || name;
  }

  onMounted(() => {
    readFile();
  });
  function TodaysReport1() {
    Checked.value = '2';
    state.css = [false, true, false];
    tocontenx('5', '3');
  }

  function Input() {
    Checked.value = '2';
    state.css = [false, true, false];
    tocontenx('5', '5');
  }

  function Input1() {
    Checked.value = '2';
    state.css = [false, true, false];
    tocontenx('7', '7');
  }
  function tocontenx(sno, val) {
    if (val === '5') {
      emit('setshow', sno, false, '5');
      return;
    }
    if (val === '7') {
      emit('setshow', sno, false, '7');
      return;
    }
    if (val === '4') {
      emit('setshow', sno, false, '4');
    } else if (state.css[0]) {
      emit('setshow', sno, false, '2');
    } else if (state.css[1]) {
      emit('setshow', sno, false, '3');
    }
    if (val === '8') {
      emit('setshow', sno, false, '8');
    }
  }
</script>
<style>
</style>
