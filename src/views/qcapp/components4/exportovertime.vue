<template>
  <div class="exportovertime-page">
    <el-card class="box-card">
      <el-form label-position="top">
        <el-row :gutter="16" align="bottom">
          <el-col :span="4">
            <el-form-item :label="t('qc.start')">
              <el-input v-model="st1" type="date" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item :label="t('qc.end')">
              <el-input v-model="ed1" type="date" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="RY">
              <el-input v-model="ry1" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item :label="t('qc.process')">
              <el-select v-model="upid2" @change="Lean" style="width: 100%;" clearable>
                <el-option v-for="item in datacsa" :key="item.SNO" :label="item.NAME" :value="item.NAME" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item :label="t('qc.lean')">
              <el-select v-model="upid1" style="width: 100%;">
                <el-option v-for="item in data" :key="item.Val" :label="item.Val" :value="item.Val" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label=" ">
              <el-button type="primary" @click="getdata" style="margin-right: 8px;">{{ t('qc.search') }}</el-button>
              <el-button type="success" @click="toexecl">{{ t('qc.excel') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

    </el-card>
    <div>
      <el-card class="box-card">

        <el-table stripe border :data="state.data_details" style="width: 100%">
          <el-table-column prop="RY" :label="'RY'" width="150"></el-table-column>
          <el-table-column prop="SKU" :label="'SKU'" width="150"></el-table-column>
          <el-table-column prop="DEPARTMENT" :label="t('qc.lean')" width="130"></el-table-column>
          <el-table-column prop="CREATEDATE" width="120" :label="t('qc.date')"></el-table-column>
          <el-table-column prop="TIMES" width="100" :label="t('qc.time')"></el-table-column>
          <el-table-column prop="Pairs" :label="t('qc.numberoforders')" width="100"></el-table-column>
          <el-table-column prop="Country" :label="t('qc.countryoforigin')"> </el-table-column>

          <el-table-column prop="XieMing" width="100" :label="t('qc.shoetypename')"></el-table-column>
          <el-table-column prop="DEFECT_NAME" width="200" :label="t('qc.defect')"></el-table-column>
          <el-table-column prop="DEFECT_QTY" width="90" :label="t('qc.defectqty')"></el-table-column>

          <el-table-column prop="INSQTY" width="100" :label="t('qc.inspestionqty')"></el-table-column>
          <el-table-column v-if="!hideOutQty()" :label="t('qc.output')">
            <template v-slot="scope">
              <!-- Kiểm tra nếu VI_CATEGORY là 'Lỗi may' -->
              {{
                scope.row.VI_CATEGORY === 'Lỗi may'
                  ? Array.isArray(scope.row.OUTQTY)
                    ? scope.row.OUTQTY.map((qty) => (qty === 0 ? 'N/A' : qty)).join(' | ')
                    : scope.row.OUTQTY === 0
                      ? 'N/A'
                      : scope.row.OUTQTY
                  : scope.row.OUTQTY
              }}
            </template>
          </el-table-column>

          <el-table-column prop="INSPECTION_RATE" :label="t('qc.inspectionpassrate')">
          </el-table-column>
          <el-table-column prop="Status" :label="t('details.results')">
            <template #default="{ row }">
              <div>
                {{
                  locale === 'en'
                    ? row.Status == 0
                      ? 'Not Passed'
                      : 'Passed'
                    : row.Status == 0
                      ? 'Không đạt'
                      : 'Đạt'
                }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>
<script>
export default {
  name: 'report2',
};
</script>
<script setup>
import axios from 'axios';
import { ref, reactive } from 'vue';
import { useI18n } from 'vue-i18n';
import { writeFile, utils } from 'xlsx-js-style';
import { ElLoading } from 'element-plus';
const { t, locale } = useI18n();
const urlIp = `${import.meta.env.VITE_BACKEND_URL}`;
const emit = defineEmits(['setshow']);
const sno = defineProps(['sno']);
const sku1 = ref('');
const ry1 = ref([]);
const upid1 = ref('');
const upid2 = ref('');
const st1 = ref();
const ed1 = ref();
const searchString = ref('');
var totalOutput = ref(0);
var totalInspection = ref(0);
var totalDefects = ref(0);
var totalPassRate = ref(0);
const data = ref([]);
const datacsa = ref([]);

const Lean = async (sno) => {
  let GXLB;
  if (upid2.value === 'Lỗi chặt') {
    GXLB = 'C';
  } else if (upid2.value === 'Lỗi may' || upid2.value === 'Bao bì may') {
    GXLB = 'S';
  } else if (upid2.value === 'Lỗi gò' || upid2.value === 'Trước khi hấp') {
    GXLB = 'A';
  } else {
    console.error('Invalid sno value. It must be 1 or 2.');
    return;
  }

  try {
    const response = await axios.post(urlIp + 'api/v1/qc/leans', { GXLB });
    data.value = response.data.data;
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};

async function readFile() {
  const url = urlIp + 'api/v1/qc/process';

  try {
    const response = await axios.get(url);
    datacsa.value = response.data.data;
  } catch (error) {
    console.error('Lỗi khi đọc dữ liệu:', error);
  }
}
const hideOutQty = () => {
  return upid2.value === 'Lỗi chặt' || upid2.value === 'Lỗi gò';
};
const execldata = ref([]);
const state = reactive({
  data: [],
  data_details: [],
  excelValue: [],
});
const subData = ref('');

/* getdatadetails */
const timeMapping = {
  1: '07:30-09:30',
  2: '09:30-11:30',
  3: '12:30-14:30',
  4: '14:30-16:30',
  5: '16:30-18:30',
};

watch(locale, () => { });

const formatDate = (date) => new Date(date).toLocaleDateString('en-GB');

const formatItem = (item) => {
  const formattedOutQty = (outQty) => {
    if (typeof outQty === 'string') {
      return outQty
        .split('|')
        .map((qty) => (qty === '0' ? 'N/A' : qty))
        .join(' | ');
    }
    return outQty === 0 ? 'N/A' : outQty;
  };

  return {
    ...item,
    CREATEDATE: formatDate(item.CREATEDATE),
    TIMES: timeMapping[item.TIMES] || 'Unknown',
    INSPECTION_RATE: `${item.INSPECTION_RATE}%`,
    OUTQTY:
      upid2.value === 'Lỗi chặt' ||
        upid2.value === 'Lỗi gò' ||
        upid2.value === 'Bao bì may' ||
        upid2.value === 'Trước khi hấp'
        ? undefined
        : formattedOutQty(item.OUTQTY),
    DEFECT_NAME: locale.value == 'vi' ? item.VI_DEFECT_NAMES : item.EN_DEFECT_NAMES,
  };
};

function toexecl() {
  const filteredData = state.data_details.map((item) => {
    const baseData = {
      RY: item.RY,
      SKU: item.SKU,
      DEPARTMENT: item.DEPARTMENT,
      CREATEDATE: item.CREATEDATE,
      TIMES: item.TIMES,
      Pairs: item.Pairs,
      Country: item.Country,
      XieMing: item.XieMing,
      DEFECT_NAME: locale.value === 'en' ? item.EN_DEFECT_NAMES : item.VI_DEFECT_NAMES,
      DEFECT_QTY: item.DEFECT_QTY,
      INSQTY: item.INSQTY,
      OUTQTY: item.VI_CATEGORY === 'Lỗi may' ? item.OUTQTY : 'N/A',
      INSPECTION_RATE: item.INSPECTION_RATE,
      Status:
        locale.value === 'en'
          ? item.Status == 0
            ? 'Not Passed'
            : 'Passed'
          : item.Status == 0
            ? 'Không đạt'
            : 'Đạt',
    };

    if (
      upid2.value === 'Lỗi chặt' ||
      upid2.value === 'Lỗi gò' ||
      upid2.value === 'Bao bì may' ||
      upid2.value === 'Trước khi hấp'
    ) {
      delete baseData.OUTQTY;
    }
    return baseData;
  });

  const ws = utils.json_to_sheet(filteredData, { cellStyles: true, origin: 'A2' });

  const wb = utils.book_new();
  utils.book_append_sheet(wb, ws, 'Sheet1');

  // Đặt tên cho các cột
  ws['A1'] = { v: '', t: 's' };
  ws['A2'].v = 'RY';
  ws['B2'].v = 'SKU';
  ws['C2'].v = `${t('qc.lean')}`;
  ws['D2'].v = `${t('qc.date')}`;
  ws['E2'].v = `${t('qc.time')}`;
  ws['F2'].v = `${t('qc.numberoforders')}`;
  ws['G2'].v = `${t('qc.countryoforigin')}`;
  ws['H2'].v = `${t('qc.shoetypename')}`;
  ws['I2'].v = `${t('qc.defect')}`;
  ws['J2'].v = `${t('qc.defectqty')}`;
  ws['K2'].v = `${t('qc.inspestionqty')}`;

  if (
    upid2.value === 'Lỗi chặt' ||
    upid2.value === 'Lỗi gò' ||
    upid2.value === 'Ba bì may' ||
    upid2.value === 'Trước khi hấp'
  ) {
    ws['L2'] = { t: 's', v: `${t('qc.inspectionpassrate')}` };
    ws['M2'] = { t: 's', v: `${t('details.results')}` };
  } else {
    ws['L2'] = { t: 's', v: `${t('qc.output')}` };
    ws['M2'] = { t: 's', v: `${t('qc.inspectionpassrate')}` };
    ws['N2'] = { t: 's', v: `${t('details.results')}` };
  }

  ws['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: 13 } }];

  if (upid2.value === 'Lỗi chặt') {
    ws['A1'].v = `${t('qc.excelTitleCut')}`;
  } else if (upid2.value === 'Lỗi may') {
    ws['A1'].v = `${t('qc.excelTitleStitch')}`;
  } else if (upid2.value === 'Lỗi gò') {
    ws['A1'].v = `${t('qc.excelTitleAsseml')}`;
  } else {
    ws['A1'].v = `${t('qc.excelTitle')}`;
  }

  ws['A1'].s = {
    alignment: { vertical: 'center', horizontal: 'center', wrapText: true },
    font: { sz: 18, bold: true },
  };

  var wsrows = [{ hpt: 40 }];

  ws['!rows'] = wsrows;
  ws['!cols'] = Array(ws['!ref'].split(':')[1].substring(0, 1).charCodeAt(0) - 64).fill({
    wpx: 110,
  });

  // Set wrapText for all cells
  const range = utils.decode_range(ws['!ref']);
  for (let row = range.s.r + 1; row <= range.e.r; row++) {
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cell = utils.encode_cell({ r: row, c: col });
      if (!ws[cell]) {
        ws[cell] = {};
      }
      ws[cell].s = {
        ...ws[cell].s,
        alignment: { vertical: 'center', horizontal: 'center', wrapText: true },
      };
    }
  }

  if (upid2.value === 'Lỗi chặt') {
    writeFile(wb, `${t('qc.excelTitleCut')}.xlsx`);
  } else if (upid2.value === 'Lỗi may') {
    writeFile(wb, `${t('qc.excelTitleStitch')}.xlsx`);
  } else if (upid2.value === 'Lỗi gò') {
    writeFile(wb, `${t('qc.excelTitleAsseml')}.xlsx`);
  } else {
    writeFile(wb, `${t('qc.excelTitle')}.xlsx`);
  }
}
function mergeDataByDate(data) {
  function mergeDefectNames(existingValue, newValue) {
    if (!existingValue) return newValue;
    if (!newValue) return existingValue;

    const defectCounts = {};

    function processDefectString(defectString) {
      const groups = defectString.split('|');
      groups.forEach((group) => {
        const defects = group.split(' - ');
        defects.forEach((defect) => {
          defect = defect.trim();
          const matches = defect.match(/(.*?)\s*\((\d+)\)\s*$/);
          if (matches) {
            const [, defectName, count] = matches;
            const cleanDefectName = defectName.trim();
            const defectCount = parseInt(count, 10);

            defectCounts[cleanDefectName] = (defectCounts[cleanDefectName] || 0) + defectCount;
          }
        });
      });
    }

    processDefectString(existingValue);
    processDefectString(newValue);

    return Object.entries(defectCounts)
      .map(([name, count]) => `${name} (${count})`)
      .join(' - ');
  }

  function uniqueJoin(existingValue, newValue) {
    const combined = existingValue ? existingValue.split('|') : [];
    const newValues = newValue ? newValue.split('|') : [];
    const allValues = [...new Set(combined.concat(newValues))];
    return allValues.join('|');
  }

  return data.reduce((acc, item) => {
    const parts = item.CREATEDATE.split('/');
    const createdDate = new Date(parts[2], parts[1] - 1, parts[0]);
    const dateKey = createdDate.toLocaleDateString('en-GB');
    const timeKey = item.TIMES;
    const key = `${dateKey}-${timeKey}-${item.DEPARTMENT}`;

    if (!acc[key]) {
      acc[key] = {
        CREATEDATE: dateKey,
        TIMES: timeKey,
        Country: item.Country,
        DEFECT_NAME: '',
        DEFECT_QTY: 0,
        DEPARTMENT: item.DEPARTMENT,
        EN_DEFECT_NAMES: '',
        INSPECTION_RATE: item.INSPECTION_RATE,
        INSQTY: 0,
        OUTQTY: 0,
        Pairs: item.Pairs,
        RY: item.RY,
        SKU: item.SKU,
        Status: item.Status,
        VI_CATEGORY: item.VI_CATEGORY,
        VI_DEFECT_NAMES: '',
        XieMing: item.XieMing,
      };
    }

    acc[key].DEFECT_QTY += parseInt(item.DEFECT_QTY, 10) || 0;
    acc[key].INSQTY += parseInt(item.INSQTY, 10) || 0;
    acc[key].INSPECTION_RATE = parseFloat(
      ((acc[key].INSQTY - acc[key].DEFECT_QTY) / acc[key].INSQTY) * 100
    ).toFixed(1);

    acc[key].DEFECT_NAME = mergeDefectNames(acc[key].DEFECT_NAME, item.DEFECT_NAME);
    acc[key].VI_DEFECT_NAMES = mergeDefectNames(acc[key].VI_DEFECT_NAMES, item.VI_DEFECT_NAMES);

    acc[key].OUTQTY = uniqueJoin(acc[key].OUTQTY, item.OUTQTY);
    const outQtyArray = acc[key].OUTQTY.split('|');
    const lastElement = outQtyArray.pop();
    acc[key].OUTQTY = lastElement;

    acc[key].Country = uniqueJoin(acc[key].Country, item.Country);
    acc[key].DEPARTMENT = uniqueJoin(acc[key].DEPARTMENT, item.DEPARTMENT);
    acc[key].EN_DEFECT_NAMES = mergeDefectNames(acc[key].EN_DEFECT_NAMES, item.EN_DEFECT_NAMES);
    acc[key].Pairs = uniqueJoin(acc[key].Pairs, item.Pairs);
    acc[key].RY = uniqueJoin(acc[key].RY, item.RY);
    acc[key].SKU = uniqueJoin(acc[key].SKU, item.SKU);
    acc[key].VI_CATEGORY = uniqueJoin(acc[key].VI_CATEGORY, item.VI_CATEGORY);
    acc[key].XieMing = uniqueJoin(acc[key].XieMing, item.XieMing);

    const departmentParts = item.DEPARTMENT.split('_');
    const lastPart = departmentParts[departmentParts.length - 1];

    // function ngCountCheck(DEFECT_QTY, threshold = 1) {
    //   if (DEFECT_QTY >= threshold) {
    //     acc[key].Status = 0;
    //   } else {
    //     acc[key].Status = 1;
    //   }
    // }
    if ((lastPart === 'C' || lastPart === 'G') && acc[key].VI_DEFECT_NAMES) {
      const defects = acc[key].VI_DEFECT_NAMES.split(' - ');

      const hasCriticalDefect = defects.some((defect) => {
        const match = defect.match(/(.*?)(?: \((\d+)\))?$/);
        if (match) {
          const defectName = match[1]?.trim(); // Tên lỗi
          const count = parseInt(match[2], 10); // Số lượng lỗi

          return defectName !== 'Lỗi khác:' && count > 1;
        }
        return false;
      });

      console.log('hasCriticalDefect', hasCriticalDefect);

      if (hasCriticalDefect) {
        acc[key].Status = 0;
      }
    } else if (lastPart === 'M' && acc[key].VI_DEFECT_NAMES) {
      const defects = acc[key].VI_DEFECT_NAMES.split(' - ');

      const targetDefect = 'Kiểm tra các chi tiết trên mũ giày có đúng tiêu chuẩn chất lượng:';
      let foundTargetDefect = false;
      let targetDefectQty = 0; // Lưu số lượng lỗi của targetDefect

      for (const defect of defects) {
        const match = defect.trim().match(/^(.*?)(?:\s*\((\d+)\))?$/);

        if (match) {
          const [, defectName, count] = match;
          const defectQty = parseInt(count, 10) || 0; // Chuyển số lượng lỗi thành số

          if (defectName.trim() === targetDefect) {
            foundTargetDefect = true;
            targetDefectQty = defectQty; // Lưu số lượng lỗi của targetDefect
          }
        }
      }

      // Nếu targetDefect có số lượng >= 2, đặt Status = 0
      if (foundTargetDefect && targetDefectQty >= 2) {
        acc[key].Status = 0;
      }

      // Chỉ tính tổng lỗi nếu Status chưa bị đặt thành 0
      if (acc[key].Status !== 0) {
        let totalDefectQty = 0;

        for (const defect of defects) {
          const match = defect.trim().match(/^(.*?)(?:\s*\((\d+)\))?$/);

          if (match) {
            const [, defectName, count] = match;
            const defectQty = parseInt(count, 10) || 0;

            // Chuẩn hóa tên lỗi để so sánh chính xác
            const cleanedDefectName = defectName.trim().replace(/[:：]/g, '');
            const cleanedTargetDefect = targetDefect.trim().replace(/[:：]/g, '');

            // Loại bỏ targetDefect
            if (cleanedDefectName !== cleanedTargetDefect) {
              totalDefectQty += defectQty;
            }
          }
        }

        if (acc[key].INSQTY >= 2 && acc[key].INSQTY <= 12) {
          if (totalDefectQty > 0) {
            acc[key].Status = 0;
          } else {
            acc[key].Status = 1;
          }
        } else if (acc[key].INSQTY >= 13 && acc[key].INSQTY <= 31) {
          if (totalDefectQty > 1) {
            acc[key].Status = 0;
          } else {
            acc[key].Status = 1;
          }
        } else if (acc[key].INSQTY >= 32 && acc[key].INSQTY <= 49) {
          if (totalDefectQty > 2) {
            acc[key].Status = 0;
          } else {
            acc[key].Status = 1;
          }
        } else if (acc[key].INSQTY >= 50 && acc[key].INSQTY <= 79) {
          if (totalDefectQty > 3) {
            acc[key].Status = 0;
          } else {
            acc[key].Status = 1;
          }
        } else if (acc[key].INSQTY >= 80 && acc[key].INSQTY <= 124) {
          if (totalDefectQty > 5) {
            acc[key].Status = 0;
          } else {
            acc[key].Status = 1;
          }
        } else if (acc[key].INSQTY >= 125 && acc[key].INSQTY <= 199) {
          if (totalDefectQty > 7) {
            acc[key].Status = 0;
          } else {
            acc[key].Status = 1;
          }
        } else if (acc[key].INSQTY >= 200 && acc[key].INSQTY <= 314) {
          if (totalDefectQty > 10) {
            acc[key].Status = 0;
          } else {
            acc[key].Status = 1;
          }
        } else if (acc[key].INSQTY >= 315) {
          if (totalDefectQty > 14) {
            acc[key].Status = 0;
          } else {
            acc[key].Status = 1;
          }
        } else {
          acc[key].Status = 'N/A';
        }
      }
    }

    return acc;
  }, {});
}
// Hàm để nối lại Status thành chuỗi
function formatStatus(statusSet) {
  return Array.from(statusSet).join(', ');
}

async function getdata() {
  // await Lean(sno.value);
  if (isNaN(new Date(st1.value)) || isNaN(new Date(ed1.value))) {
    return;
  }

  let upid = upid1.value || 'A';
  let namecsa = upid2.value || 'A';
  let ry = ry1.value.length > 1 ? ry1.value : 'A';
  let sub = 'A';
  let formattedName = namecsa !== 'A' ? `N'${namecsa}'` : namecsa;

  const url = urlIp + 'api/v1/qc/rp2detailcsa';

  const loadingInstance = ElLoading.service({
    lock: true,
    text: 'Đang tải...',
    background: 'rgba(0, 0, 0, 0.7)',
  });

  try {
    const res = await axios.get(url, {
      params: { ry: ry, st: st1.value, ed: ed1.value, sub: upid, name: formattedName },
    });

    if (res.data && Array.isArray(res.data.data) && res.data.data.length > 0) {
      const formattedData = res.data.data.map(formatItem);
      const mergedData = mergeDataByDate(formattedData);
      state.data_details = Object.values(mergedData);

      console.log('res.data.data', state.data_details);
    } else {
      state.data_details = [];
      console.log('Không có dữ liệu tìm thấy.');
    }
  } catch (error) {
    console.error('Lỗi khi tải dữ liệu:', error);
  } finally {
    loadingInstance.close();
  }
}

onMounted(() => {
  readFile();
});
watch(locale, () => {
  getdata();
});
function gohome() {
  emit('setshow', 0, true, '1');
}

const loading = ref(false);
let loadingInstance = null;
const isLoading = ref(false);
</script>

<style scoped>
.exportovertime-page {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.box-card {
  margin-top: 20px;
  flex-shrink: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.box-card:last-child {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
