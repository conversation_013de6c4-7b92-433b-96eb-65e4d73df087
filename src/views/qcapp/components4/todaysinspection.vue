<template>
  <div class="container">
    <div style="width: 80%" v-if="title">
      <div>
        <!-- <PERSON><PERSON>t Menu chỉ xuất hiện trên màn hình nhỏ -->
        <button v-if="isMobile" class="menu-button" @click="toggleMenu">
          {{ itemTranslatedName(selectedTab) || '☰ Menu' }}
        </button>
        <!-- Danh sách các nút -->
        <div class="tab-buttons" :class="{ 'show-menu': showMenu }">
          <button :class="css[index] ? 'active' : 'button'" v-for="(item, index) in title" :key="index" @click="
            Lean(item.SNO, index);
          closeMenu();
          ">
            {{ itemTranslatedName(item.NAME) }}
          </button>
        </div>
      </div>
    </div>
    <el-card class="box-card">
      <el-form label-position="top">
        <el-row :gutter="6" align="bottom" justify="end">
          <el-col :span="6">
            <el-form-item label="Chọn Lean">
              <el-select v-model="upid1" :placeholder="t('qc.lean')" size="medium" clearable @change="readFile3(upid1)"
                style="width: 200px">
                <el-option v-for="item in data" :key="item.Val" :label="item.Val" :value="item.Val" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <!-- Các phần khác của giao diện -->

    <el-card class="box-card">
      <div class="card-box" v-if="state.data_details" v-loading="loading">
        <el-row v-for="data in state.data_details" :key="data.TIMES">
          <el-col :span="8">
            <el-card class="times-box" shadow="always" style="height: 100%">
              <p>{{ timeMapping[data.TIMES] || 'N/A' }}</p>
            </el-card>
          </el-col>
          <el-col :span="16">
            <el-card shadow="always" style="min-height: 16rem">
              <p> {{ t('qc.inspectionpassrate') }}：{{ data.INSPECTION_RATE }}%</p>
              <p> {{ t('qc.inspestionqty2') }}：{{ data.INSQTY }}</p>
              <p> {{ t('qc.ngqty') }}：{{ data.DEFECT_QTY }}</p>
              <p>
                {{ t('qc.output') }}：
                <span v-if="
                  state.sno == 1 ||
                  state.sno == 3 ||
                  state.sno == 31 ||
                  state.sno == 32 ||
                  data.OUTQTY == '0' ||
                  data.OUTQTY === 0
                ">
                  N/A
                </span>
                <span v-else>{{ data.OUTQTY }}</span>
              </p>

              <div>
                {{ t('qc.status') }}：
                {{
                  locale === 'en'
                    ? data.Status == 0
                      ? 'Not Passed'
                      : 'Passed'
                    : data.Status == 0
                      ? 'Không đạt'
                      : 'Đạt'
                }}
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <el-empty v-if="!state.data_details || state.data_details.length === 0" description="No data available"
        :image-size="120" />
    </el-card>
  </div>
</template>

<script setup>
import axios from 'axios';
import { ref, reactive, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
const { t, locale } = useI18n();

const urlIp = `${import.meta.env.VITE_BACKEND_URL}`;
const emit = defineEmits(['setshow']);
const state = reactive({
  data: [],
  data_details: [],
});
const item = ref('');
const loading = ref(false);
const css = ref([]);
const title = ref([]);
const isMobile = ref(window.innerWidth <= 1023);
const showMenu = ref(false);
const selectedTab = ref('');
const data = ref('');
const upid1 = ref('');
const sno = ref('');
const toggleMenu = () => {
  showMenu.value = !showMenu.value;
};

const closeMenu = () => {
  showMenu.value = false;
};

window.addEventListener('resize', () => {
  isMobile.value = window.innerWidth <= 1023;
  if (!isMobile.value) {
    showMenu.value = false;
  }
});
async function Lean(sno, index) {
  let GXLB;

  css.value = css.value.map((_, i) => i === index);
  if (title.value[index] && title.value[index].NAME) {
    selectedTab.value = title.value[index].NAME;
  } else {
    selectedTab.value = '';
  }
  if (sno == 1) {
    GXLB = 'C';
  } else if (sno == 2 || sno == 31) {
    GXLB = 'S';
  } else if (sno == 3 || sno == 32) {
    GXLB = 'A';
  } else {
    console.error('Invalid sno value. It must be 1 or 2.');
    return;
  }

  try {
    const response = await axios.post(urlIp + 'api/v1/qc/leans', { GXLB });
    data.value = response.data.data;
  } catch (error) {
    console.error('Error fetching data:', error);
  }
  state.sno = selectedTab.value;
}

const timeMapping = {
  1: '07:30-09:30',
  2: '09:30-11:30',
  3: '12:30-14:30',
  4: '14:30-16:30',
  5: '16:30-18:30',
};

async function readFile3(upid1) {
  state.data = [];
  loading.value = true;

  // Kiểm tra và format tên
  let formattedName = state.sno !== 'A' ? `N'${state.sno}'` : state.sno;

  const url = urlIp + 'api/v1/qc/rp2detailcsa';
  try {
    const today = new Date();
    const st = today.toISOString().split('T')[0];
    const ed = today.toISOString().split('T')[0];
    const result = await axios.get(url, {
      params: {
        ry: 'A',
        st: st,
        ed: ed,
        sub: upid1,
        name: formattedName,
      },
    });

    // Kiểm tra dữ liệu trả về
    if (result.data && Array.isArray(result.data.data) && result.data.data.length > 0) {
      const mergedData = mergeDataByDate(result.data.data);
      state.data_details = Object.values(mergedData);
    } else {
      state.data_details = [];
    }
    console.log('state.data_details', state.data_details);
  } catch (error) {
    console.error('Error fetching data:', error);
  } finally {
    // Set trạng thái loading là false sau khi kết thúc
    loading.value = false;
  }
}
function ngCountCheck(DEFECT_QTY, threshold = 1) {
  if (DEFECT_QTY >= threshold) {
    acc[key].Status = 0;
  } else {
    acc[key].Status = 1;
  }
}
function mergeDataByDate(data) {
  function mergeDefectNames(existingValue, newValue) {
    if (!existingValue) return newValue;
    if (!newValue) return existingValue;

    const defectCounts = {};

    function processDefectString(defectString) {
      const groups = defectString.split('|');
      groups.forEach((group) => {
        const defects = group.split(' - ');
        defects.forEach((defect) => {
          defect = defect.trim();
          const matches = defect.match(/(.*?)\s*\((\d+)\)\s*$/);
          if (matches) {
            const [, defectName, count] = matches;
            const cleanDefectName = defectName.trim();
            const defectCount = parseInt(count, 10);

            defectCounts[cleanDefectName] = (defectCounts[cleanDefectName] || 0) + defectCount;
          }
        });
      });
    }

    processDefectString(existingValue);
    processDefectString(newValue);

    return Object.entries(defectCounts)
      .map(([name, count]) => `${name} (${count})`)
      .join(' - ');
  }

  function uniqueJoin(existingValue, newValue) {
    const combined = existingValue ? existingValue.split('|') : [];
    const newValues = newValue ? newValue.split('|') : [];
    const allValues = [...new Set(combined.concat(newValues))];
    return allValues.join('|');
  }

  return data.reduce((acc, item) => {
    const parts = item.CREATEDATE.split('/');
    const createdDate = new Date(parts[2], parts[1] - 1, parts[0]);
    const dateKey = createdDate.toLocaleDateString('en-GB');
    const timeKey = item.TIMES;
    const key = `${dateKey}-${timeKey}-${item.DEPARTMENT}`;

    if (!acc[key]) {
      acc[key] = {
        CREATEDATE: dateKey,
        TIMES: timeKey,
        Country: item.Country,
        DEFECT_NAME: '',
        DEFECT_QTY: 0,
        DEPARTMENT: item.DEPARTMENT,
        EN_DEFECT_NAMES: item.EN_DEFECT_NAMES,
        INSPECTION_RATE: item.INSPECTION_RATE,
        INSQTY: 0,
        OUTQTY: 0,
        Pairs: item.Pairs,
        RY: item.RY,
        SKU: item.SKU,
        Status: item.Status,
        VI_CATEGORY: item.VI_CATEGORY,
        VI_DEFECT_NAMES: '',
        XieMing: item.XieMing,
      };
    }

    acc[key].DEFECT_QTY += parseInt(item.DEFECT_QTY, 10) || 0;
    acc[key].INSQTY += parseInt(item.INSQTY, 10) || 0;
    acc[key].INSPECTION_RATE = parseFloat(
      ((acc[key].INSQTY - acc[key].DEFECT_QTY) / acc[key].INSQTY) * 100
    ).toFixed(1);

    acc[key].DEFECT_NAME = mergeDefectNames(acc[key].DEFECT_NAME, item.DEFECT_NAME);
    acc[key].VI_DEFECT_NAMES = mergeDefectNames(acc[key].VI_DEFECT_NAMES, item.VI_DEFECT_NAMES);

    acc[key].OUTQTY = uniqueJoin(acc[key].OUTQTY, item.OUTQTY);
    const outQtyArray = acc[key].OUTQTY.split('|');
    const lastElement = outQtyArray.pop();
    acc[key].OUTQTY = lastElement;

    acc[key].Country = uniqueJoin(acc[key].Country, item.Country);
    acc[key].DEPARTMENT = uniqueJoin(acc[key].DEPARTMENT, item.DEPARTMENT);
    acc[key].EN_DEFECT_NAMES = mergeDefectNames(acc[key].EN_DEFECT_NAMES, item.EN_DEFECT_NAMES);
    acc[key].Pairs = uniqueJoin(acc[key].Pairs, item.Pairs);
    acc[key].RY = uniqueJoin(acc[key].RY, item.RY);
    acc[key].SKU = uniqueJoin(acc[key].SKU, item.SKU);
    acc[key].VI_CATEGORY = uniqueJoin(acc[key].VI_CATEGORY, item.VI_CATEGORY);
    acc[key].XieMing = uniqueJoin(acc[key].XieMing, item.XieMing);

    const departmentParts = item.DEPARTMENT.split('_');
    const lastPart = departmentParts[departmentParts.length - 1];

    function ngCountCheck(DEFECT_QTY, threshold = 1) {
      if (DEFECT_QTY >= threshold) {
        acc[key].Status = 0;
      } else {
        acc[key].Status = 1;
      }
    }
    if ((lastPart === 'C' || lastPart === 'G') && acc[key].VI_DEFECT_NAMES) {
      const defects = acc[key].VI_DEFECT_NAMES.split(' - ');

      const hasCriticalDefect = defects.some((defect) => {
        const match = defect.match(/(.*?)(?: \((\d+)\))?$/);
        if (match) {
          const [, defectName, count] = match;
          return defectName.trim() !== 'Lỗi khác' && parseInt(count, 10) > 1;
        }
        return false;
      });
      if (hasCriticalDefect) {
        acc[key].Status = 0;
      }
    } else if (lastPart === 'M' && acc[key].VI_DEFECT_NAMES) {
      const defects = acc[key].VI_DEFECT_NAMES.split(' - ');

      const targetDefect = 'Kiểm tra các chi tiết trên mũ giày có đúng tiêu chuẩn chất lượng';
      let foundTargetDefect = false;

      for (const defect of defects) {
        const match = defect.trim().match(/^(.*?)(?:\s*\((\d+)\))?$/);

        if (match) {
          const [, defectName, count] = match;
          if (
            defectName.trim() ===
            'Kiểm tra các chi tiết trên mũ  giày có đúng tiêu chuẩn chất lượng:' &&
            !foundTargetDefect
          ) {
            foundTargetDefect = true;

            const countMatch = defect.match(/\((\d+)\)$/);
            const count = countMatch ? parseInt(countMatch[1], 10) : 0;
            if (count >= 2) {
              acc[key].Status = 0;
            }
          }
        }
      }
      if (acc[key].Status !== 0) {
        let totalDefectQty = 0;

        for (const defect of defects) {
          const match = defect.trim().match(/^(.*?)(?:\s*\((\d+)\))?$/);

          if (match) {
            const [, defectName, count] = match;
            const defectQty = parseInt(count, 10) || 0;

            // Chuẩn hóa tên lỗi để so sánh chính xác
            const cleanedDefectName = defectName.trim().replace(/[:：]/g, '');
            const cleanedTargetDefect = targetDefect.trim().replace(/[:：]/g, '');

            console.log(`Defect found: "${cleanedDefectName}" (count: ${defectQty})`);
            console.log(`Comparing with target: "${cleanedTargetDefect}"`);

            // Loại bỏ targetDefect
            if (cleanedDefectName !== cleanedTargetDefect) {
              totalDefectQty += defectQty;
            }
          }
        }
        if (acc[key].INSQTY >= 2 && acc[key].INSQTY <= 12) {
          if (totalDefectQty > 0) {
            acc[key].Status = 0;
          } else {
            acc[key].Status = 1;
          }
        } else if (acc[key].INSQTY >= 13 && acc[key].INSQTY <= 31) {
          if (totalDefectQty > 1) {
            acc[key].Status = 0;
          } else {
            acc[key].Status = 1;
          }
        } else if (acc[key].INSQTY >= 32 && acc[key].INSQTY <= 49) {
          if (totalDefectQty > 2) {
            acc[key].Status = 0;
          } else {
            acc[key].Status = 1;
          }
        } else if (acc[key].INSQTY >= 50 && acc[key].INSQTY <= 79) {
          if (totalDefectQty > 3) {
            acc[key].Status = 0;
          } else {
            acc[key].Status = 1;
          }
        } else if (acc[key].INSQTY >= 80 && acc[key].INSQTY <= 124) {
          if (totalDefectQty > 5) {
            acc[key].Status = 0;
          } else {
            acc[key].Status = 1;
          }
        } else if (acc[key].INSQTY >= 125 && acc[key].INSQTY <= 199) {
          if (totalDefectQty > 7) {
            acc[key].Status = 0;
          } else {
            acc[key].Status = 1;
          }
        } else if (acc[key].INSQTY >= 200 && acc[key].INSQTY <= 314) {
          if (totalDefectQty > 10) {
            acc[key].Status = 0;
          } else {
            acc[key].Status = 1;
          }
        } else if (acc[key].INSQTY >= 315) {
          if (totalDefectQty > 14) {
            acc[key].Status = 0;
          } else {
            acc[key].Status = 1;
          }
        } else {
          acc[key].Status = 'N/A';
        }
      }
    }

    return acc;
  }, {});
}

async function readFile() {
  const url = urlIp + 'api/v1/qc/process';
  try {
    const response = await axios.get(url);
    title.value = response.data.data;
    css.value = Array(title.value.length).fill(false);
  } catch (error) {
    console.error('Lỗi khi đọc dữ liệu:', error);
  }
}

function itemTranslatedName(name) {
  const vietnameseToEnglish = {
    'Lỗi chặt': t('qc.cut'),
    'Lỗi may': t('qc.stitch'),
    'Lỗi gò': t('qc.asseml'),
    'Bao bì may': t('qc.Stitchingpackaging'),
    'Trước khi hấp': t('qc.Beforesteaming'),
  };
  return vietnameseToEnglish[name] || name;
}

onMounted(() => {
  readFile();
});
</script>

<style>
.container {
  padding: 20px
}

.box-card {
  margin-top: 20px;
  flex-shrink: 0;
}


.box-card:last-child {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.card-box {
  font-size: 1.5rem;
}

.times-box {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
