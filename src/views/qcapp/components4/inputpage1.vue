<template>
  <div class="container">
    <div v-if="state.titles">
      <inputpage_head ref="inputpage" :sno="sno.sno" @change-ok="change_ok"></inputpage_head>
      <hr />
      <el-row justify="center">
        <el-radio-group v-model="Checked">
          <el-radio label="1">07:30-09:30</el-radio>
          <el-radio label="2">09:31-11:00</el-radio>
          <el-radio label="3">12:00-14:00</el-radio>
          <el-radio label="4">14:01-16:30</el-radio>
          <el-radio label="5">{{ t('qc.tc') }}</el-radio>
        </el-radio-group>
      </el-row>
      <p>
        <el-row justify="center">
          <el-space :size="20" :wrap="true">
            <el-card
              shadow="always"
              class="input-card"
              v-for="(title, index) in state.titles"
              :key="title.SNO"
              :header="locale === 'en' ? title.ENAME : title.NAME"
              style="
                width: 190px;
                min-height: 150px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
              "
            >
              <!-- Phần input number trước -->
              <el-input-number
                :min="0"
                :max="getMaxValue"
                v-model="state.num[index]"
                class="input-number"
                size="small"
                style="width: 110px"
                placeholder="Nhập số lượng"
                @change="OnChangeData"
                @click="OnChangeData"
              ></el-input-number>
              <span class="suffix"
                ><b>{{ getSuffix(title.SNO) }}</b></span
              >
            </el-card>
          </el-space>
        </el-row>
      </p>
      <hr />
      <el-row justify="center">
        <el-space :size="10" :wrap="true">
          <el-card>
            <div style="width: 190px; min-height: 100px">
              {{ t('qc.passrate') }} : {{ Sum }} %
              <br />
              {{
                status == 'ACCEPT'
                  ? t('qc.status1')
                  : status == 'REJECT'
                  ? t('qc.status0')
                  : t('qc.status')
              }}
            </div>
          </el-card>
        </el-space>
      </el-row>
      <hr />
      <el-row justify="center">
        <el-button type="primary" @click="insertDialogVisible = true">INSERT</el-button>
        <el-dialog v-model="insertDialogVisible" title="Cảnh báo" width="500" center>
          <div style="text-align: center"> {{ t('notification.shutdownAlert') }} </div>
          <template #footer>
            <div class="dialog-footer">
              <el-button @click="insertDialogVisible = false">{{ t('btn.no') }}</el-button>
              <el-button type="primary" @click="InsertClick">{{ t('btn.yes') }}</el-button>
            </div>
          </template>
        </el-dialog>
      </el-row>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'inputpage',
  };
</script>

<script setup>
  import axios from 'axios';
  import { ref, reactive, onMounted, watch, computed } from 'vue';
  import { useI18n } from 'vue-i18n';
  import inputpage_head from './inputpage_head.vue';
  const inputpage = ref(null);

  // Define a reactive variable
  const count = ref(0);
  const { t, locale } = useI18n();

  const urlIp = `${import.meta.env.VITE_BACKEND_URL}`;
  const emit = defineEmits(['setshow']);
  const sno = defineProps(['sno']);
  const RY = ref('');
  const SKU = ref('');
  var times1 = ref(1);
  const Checked = ref('');
  const TQuty = ref(0); //生產量
  // const InQuty = ref(0); //品檢量
  const Sum = ref(0);
  const Ispass = ref('0');
  const status = ref('N/A');
  const isok = ref('');
  const isSub = ref(1);
  const insertDialogVisible = ref(false);
  const state = reactive({
    titles: [],
    data: [],
    num: [],
  });
  const getSuffix = (sno) => {
    if (sno == 33) return '/2';
    if (sno == 34) return '/10';
    return '';
  };
  const ngCount = ref(0);

  const selectedData = ref('');

  const rys = ref(['']); // initial RY input field
  async function readFile() {
    const url = urlIp + 'api/v1/qc/titleindex4';
    axios.get(url, { params: { sno: sno.sno } }).then((result) => {
      state.titles = result.data.data;
      let tmp = [];
      for (var i = 0; i < result.data.data.length; i++) {
        tmp.push(0);
      }
      state.data = tmp;
    });
  }

  const change_ok = (ok, ry, sku_CP, select) => {
    isok.value = ok.value;
    rys.value = ry.value;
    SKU.value = sku_CP.value;
    selectedData.value = select.value;
  };

  onMounted(() => {
    readFile();
    OnChangeData();
    status.value = 'N/A';
  });

  function OnChangeData() {
    updateSum();

    for (let i = 0; i < state.num.length; i++) {
      if (sno.sno == 31 && state.num[i] > 0) {
        status.value = 'REJECT';
        Ispass.value = '0';
      } else if (sno.sno == 32 && state.num[i] >= 2) {
        status.value = 'REJECT';
        Ispass.value = '0';
      } else {
        status.value = 'ACCEPT';
        Ispass.value = '1';
      }
    }
  }
  const getMaxValue = computed(() => {
    if (sno.sno === 31) {
      return 2;
    } else if (sno.sno === 32) {
      return 10;
    }
    return Infinity;
  });

  watch(
    () => sno.sno,
    (newSno) => {
      if (newSno) {
        Sum.value = 0.0;
        state.num = [];
        selectedData.value = [];
      }
      updateSum();
    }
  );
  watch(sno, () => {
    readFile();
    selectedData.value = '';
    TQuty.value = 0;
    status.value = 'N/A';
  });

  watch(
    () => state.num,
    () => {
      updateSum();
    },
    { deep: true }
  );
  // watch(InQuty, () => {
  //   updateSum();
  // });

  function updateSum() {
    ngCount.value = 0;
    for (let i = 0; i < state.num.length; i++) {
      ngCount.value += state.num[i] || 0;
      const sno = state.titles[i]?.SNO;
      if (sno == 33) {
        Sum.value = parseFloat((((2 - ngCount.value) / 2) * 100).toFixed(1));
      } else if (sno == 34) {
        Sum.value = parseFloat((((10 - ngCount.value) / 10) * 100).toFixed(1));
      } else {
        Sum.value = 0;
      }
    }
  }
  async function InsertClick() {
    insertDialogVisible.value = false;

    // Kiểm tra dữ liệu có tồn tại không
    // const dataisexist = urlIp + 'api/v1/qc/dataisexistcsa';
    // await axios.get(dataisexist).then((res) => {
    //   if (res.data.data == 0) {
    //     times1.value = 1;
    //   } else {
    //     times1.value = res.data.data + 1;
    //   }
    // });
    times1.value = Math.floor(100 + Math.random() * 900);

    if (isok.value.length > 5) {
      if (selectedData.value == '') {
        alert(t('notification.selectline'));
        return;
      } else if (Checked.value == '') {
        alert(t('notification.selecttime'));
        return;
      } else {
        let firstRyInQuty = 0;
        let firstRyTQuty = TQuty.value;
        let firstRyIsPass = Ispass.value;
        let error = 0;
        let isErrorShown = false;
        SKU.value.forEach((sku, skuIndex) => {
          if (sku.rys && sku.rys.length > 0) {
            sku.rys.forEach((ry, ryIndex) => {
              const formdata = new FormData();
              let id = [];
              for (let i = 0; i < state.titles.length; i++) {
                id.push(state.titles[i].SNO);
                const sno = state.titles[i]?.SNO;
                if (sno == 33) {
                  firstRyInQuty = 2;
                } else if (sno == 34) {
                  firstRyInQuty = 10;
                }
              }

              // Thêm dữ liệu vào formdata
              formdata.append('upid', sno.sno);
              formdata.append('ry', ry);
              formdata.append('sku', sku.sku);
              formdata.append('times', Checked.value);
              formdata.append('id', id.toString());
              formdata.append('times1', times1.value);
              formdata.append('sub', selectedData.value);
              formdata.append('others', state.additionalInput || '');
              formdata.append('status', Ispass.value);

              // Chỉ thêm dữ liệu đầu tiên một lần
              if (ryIndex === 0 && skuIndex === 0) {
                formdata.append('data', state.num.toString());
              } else {
                formdata.append('data', 0);
              }

              // Sử dụng các giá trị đặc biệt cho SKU và ry đầu tiên
              if (ryIndex === 0 && skuIndex === 0) {
                formdata.append('inquty', firstRyInQuty);
                formdata.append('tquty', firstRyTQuty);
                formdata.append('ispass', firstRyIsPass);
                formdata.append('passrate', Sum.value);
              } else {
                formdata.append('inquty', 0);
                formdata.append('tquty', 0);
                formdata.append('ispass', 0);
                formdata.append('ngtimes', 0);
                formdata.append('passrate', 0);
              }

              try {
                axios({
                  method: 'post',
                  url: urlIp + 'api/v1/qc/titlecsa',
                  data: formdata,
                  headers: { 'Content-Type': 'multipart/form-data' },
                });
                if (skuIndex === SKU.value.length - 1 && ryIndex === sku.rys.length - 1) {
                  // Reset trạng thái sau khi xử lý xong
                  rys.value = [''];
                  SKU.value = [''];
                  isok.value = '';
                  readFile();
                  // InQuty.value = 0;
                  TQuty.value = 0;
                  selectedData.value = ref('');
                  inputpage.value.Reset_Data();

                  Sum.value = 0.0;
                  state.num = [];
                }
              } catch (error) {
                console.log(state.data, 'ERR');
                alert('Thêm dữ liệu thất bại !!!!');
                error++;
              }
            });
          }
        });
        if (error < 1) {
          alert(t('notification.successfully'));
        } else {
          alert(t('notification.datafailed'));
        }
      }
    } else {
      alert('Not find RY-SKU:' + RY.value + '-' + SKU.value);
    }
  }
</script>
<style scropt>
</style>
