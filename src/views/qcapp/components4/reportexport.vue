<template>
  <div>
    <el-row justify="center" :size="20" style="margin: 2%">
      <el-space wrap :size="30">
        <el-form-item :label="t('qc.start')">
          <el-input v-model="st1" type="date" style="width: 150px" class="input-date"></el-input>
        </el-form-item>
        <el-form-item :label="t('qc.end')">
          <el-input v-model="ed1" type="date" style="width: 150px" class="input-date"></el-input>
        </el-form-item>
        <el-form-item label="RY">
          <el-input v-model="ry1" style="width: 120px"></el-input>
        </el-form-item>

        <el-form-item :label="t('qc.process')">
          <el-select
            v-model="upid2"
            class="m-2"
            placeholder="Select"
            size="medium"
            clearable
            style="width: 240px"
            @change="getdata"
          >
            <el-option
              v-for="item in datacsa"
              :key="item.SNO"
              :label="item.NAME"
              :value="item.NAME"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('qc.lean')">
          <el-select
            v-model="upid1"
            class="m-2"
            placeholder="Select"
            size="medium"
            clearable
            style="width: 240px"
            @change="getdata"
          >
            <el-option v-for="item in data" :key="item.Val" :label="item.Val" :value="item.Val" />
          </el-select>
        </el-form-item>

        <el-button type="primary" @click="getdata">{{ t('qc.search') }}</el-button>

        <el-button type="success" @click="toexecl">{{ t('qc.excel') }}</el-button>
      </el-space>
    </el-row>
    <el-row>
      <el-table stripe border :data="state.data_details" style="margin: 0 2%; font-size: 17px">
        <el-table-column prop="RY" :label="'RY'" width="150"></el-table-column>
        <el-table-column prop="SKU" :label="'SKU'" width="150"></el-table-column>
        <el-table-column prop="DEPARTMENT" :label="t('qc.lean')" width="130"></el-table-column>
        <el-table-column prop="CREATEDATE" width="120" :label="t('qc.date')"></el-table-column>
        <el-table-column prop="TIMES" width="100" :label="t('qc.time')"></el-table-column>
        <el-table-column prop="Pairs" :label="t('qc.numberoforders')" width="100"></el-table-column>
        <el-table-column prop="Country" :label="t('qc.countryoforigin')"> </el-table-column>

        <el-table-column prop="XieMing" width="100" :label="t('qc.shoetypename')"></el-table-column>
        <el-table-column prop="DEFECT_NAME" width="200" :label="t('qc.defect')"></el-table-column>
        <el-table-column prop="DEFECT_QTY" width="90" :label="t('qc.defectqty')"></el-table-column>

        <el-table-column prop="INSQTY" width="100" :label="t('qc.inspestionqty')"></el-table-column>

        <el-table-column v-if="!hideOutQty()" :label="t('qc.output')">
          <template v-slot="scope">
            {{ scope.row.VI_CATEGORY === 'Lỗi may' ? scope.row.OUTQTY : 'N/A' }}
          </template>
        </el-table-column>

        <el-table-column prop="INSPECTION_RATE" :label="t('qc.inspectionpassrate')">
        </el-table-column>
        <el-table-column prop="Status" :label="t('details.results')">
          <template #default="{ row }">
            <div>
              {{
                locale === 'en'
                  ? row.Status == 0
                    ? 'Not Passed'
                    : 'Passed'
                  : row.Status == 0
                  ? 'Không đạt'
                  : 'Đạt'
              }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
  </div>
</template>
<script>
  export default {
    name: 'report2',
  };
</script>
<script setup>
  import axios from 'axios';
  import { ref, reactive } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { writeFile, utils } from 'xlsx-js-style';
  import { ElLoading } from 'element-plus';
  const { t, locale } = useI18n();
  const urlIp = `${import.meta.env.VITE_BACKEND_URL}`;
  const emit = defineEmits(['setshow']);
  const sno = defineProps(['sno']);
  const sku1 = ref('');
  const ry1 = ref([]);
  const upid1 = ref('');
  const upid2 = ref('');
  const st1 = ref();
  const ed1 = ref();
  const searchString = ref('');
  var totalOutput = ref(0);
  var totalInspection = ref(0);
  var totalDefects = ref(0);
  var totalPassRate = ref(0);
  const data = ref([]);
  const datacsa = ref([]);

  async function readFile() {
    const url = urlIp + 'api/v1/qc/process';

    try {
      const response = await axios.get(url);
      datacsa.value = response.data.data;
    } catch (error) {
      console.error('Lỗi khi đọc dữ liệu:', error);
    }
  }
  const hideOutQty = () => {
    // Kiểm tra xem upid2 có bằng 1 hoặc 3 không
    return upid2.value === 'Lỗi chặt' || upid2.value === 'Lỗi gò';
  };
  const execldata = ref([]);
  const state = reactive({
    data: [],
    data_details: [],
    excelValue: [],
  });
  const subData = ref('');

  /* getdatadetails */
  const timeMapping = {
    1: '07:30-09:30',
    2: '09:31-11:00',
    3: '12:00-14:00',
    4: '14:01-16:30',
    5: 'TC',
  };

  watch(locale, () => {});

  const formatDate = (date) => new Date(date).toLocaleDateString('en-GB');

  const formatItem = (item) => {
    return {
      ...item,
      CREATEDATE: formatDate(item.CREATEDATE),
      TIMES: timeMapping[item.TIMES] || 'Unknown',
      INSPECTION_RATE: `${item.INSPECTION_RATE}%`, // Chuyển thành định dạng phần trăm
      OUTQTY:
        upid2.value === 'Lỗi chặt' ||
        upid2.value === 'Lỗi gò' ||
        upid2.value === 'Bao bì may' ||
        upid2.value === 'Trước khi hấp'
          ? undefined
          : item.OUTQTY, // Bỏ gán OUTQTY
      DEFECT_NAME: locale.value == 'vi' ? item.VI_DEFECT_NAMES : item.EN_DEFECT_NAMES,
    };
  };

  function toexecl() {
    const filteredData = state.data_details.map((item) => {
      // Kiểm tra điều kiện và trả về đối tượng tương ứng
      const baseData = {
        RY: item.RY,
        SKU: item.SKU,
        DEPARTMENT: item.DEPARTMENT,
        CREATEDATE: item.CREATEDATE,
        TIMES: item.TIMES,
        Pairs: item.Pairs,
        Country: item.Country,
        XieMing: item.XieMing,
        DEFECT_NAME: locale.value === 'en' ? item.EN_DEFECT_NAMES : item.VI_DEFECT_NAMES,
        DEFECT_QTY: item.DEFECT_QTY,
        INSQTY: item.INSQTY,
        OUTQTY: item.VI_CATEGORY === 'Lỗi may' ? item.OUTQTY : 'N/A',
        INSPECTION_RATE: item.INSPECTION_RATE,
        Status:
          locale.value === 'en'
            ? item.Status == 0
              ? 'Not Passed'
              : 'Passed'
            : item.Status == 0
            ? 'Không đạt'
            : 'Đạt',
      };

      // Nếu upid2 là 'Lỗi chặt' hoặc 'Lỗi gò', không thêm OUTQTY vào dữ liệu
      if (
        upid2.value === 'Lỗi chặt' ||
        upid2.value === 'Lỗi gò' ||
        upid2.value === 'Bao bì may' ||
        upid2.value === 'Trước khi hấp'
      ) {
        delete baseData.OUTQTY; // Xóa OUTQTY khỏi baseData
      }
      return baseData; // Trả về baseData
    });

    const ws = utils.json_to_sheet(filteredData, { cellStyles: true, origin: 'A2' });
    console.log('filteredData', filteredData);

    const wb = utils.book_new();
    utils.book_append_sheet(wb, ws, 'Sheet1');

    // Đặt tên cho các cột
    ws['A1'] = { v: '', t: 's' };
    ws['A2'].v = 'RY';
    ws['B2'].v = 'SKU';
    ws['C2'].v = `${t('qc.lean')}`;
    ws['D2'].v = `${t('qc.date')}`;
    ws['E2'].v = `${t('qc.time')}`;
    ws['F2'].v = `${t('qc.numberoforders')}`;
    ws['G2'].v = `${t('qc.countryoforigin')}`;
    ws['H2'].v = `${t('qc.shoetypename')}`;
    ws['I2'].v = `${t('qc.defect')}`;
    ws['J2'].v = `${t('qc.defectqty')}`;
    ws['K2'].v = `${t('qc.inspestionqty')}`;

    // Nếu upid2 là 'Lỗi chặt' hoặc 'Lỗi gò', không tạo ô L2
    if (
      upid2.value === 'Lỗi chặt' ||
      upid2.value === 'Lỗi gò' ||
      upid2.value === 'Ba bì may' ||
      upid2.value === 'Trước khi hấp'
    ) {
      ws['L2'] = { t: 's', v: `${t('qc.inspectionpassrate')}` };
      ws['M2'] = { t: 's', v: `${t('details.results')}` };
    } else {
      ws['L2'] = { t: 's', v: `${t('qc.output')}` };
      ws['M2'] = { t: 's', v: `${t('qc.inspectionpassrate')}` };
      ws['N2'] = { t: 's', v: `${t('details.results')}` };
    }

    // merge cells hàng 1 từ A1 đến P1
    ws['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: 13 } }];

    // nội dung dòng A1 là 'Title'
    ws['A1'].v = `${t('qc.excelTitle')}`;
    ws['A1'].s = {
      alignment: { vertical: 'center', horizontal: 'center', wrapText: true },
      font: { sz: 18, bold: true },
    };

    var wsrows = [
      { hpt: 40 }, // row 1 sets to the height of 40 in points
    ];

    ws['!rows'] = wsrows;

    // Căng độ rộng cột vừa với dữ liệu bên trong
    ws['!cols'] = Array(ws['!ref'].split(':')[1].substring(0, 1).charCodeAt(0) - 64).fill({
      wpx: 110,
    });

    // Set wrapText for all cells
    const range = utils.decode_range(ws['!ref']);
    for (let row = range.s.r + 1; row <= range.e.r; row++) {
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cell = utils.encode_cell({ r: row, c: col });
        if (!ws[cell]) {
          ws[cell] = {}; // Tạo ô nếu nó không tồn tại
        }
        ws[cell].s = {
          ...ws[cell].s,
          alignment: { vertical: 'center', horizontal: 'center', wrapText: true },
        };
      }
    }

    writeFile(wb, 'QC_Report.xlsx');
  }

  async function getdata() {
    await Lean(sno.value);
    subData.value = '';

    if (isNaN(new Date(st1.value)) || isNaN(new Date(ed1.value))) {
      console.log('Date is error !!!');
      return;
    }

    let upid = upid1.value || 'A'; // upid từ el-select khác
    let namecsa = upid2.value || 'A'; // name từ el-select bạn đã chỉ định
    let ry = ry1.value.length > 1 ? ry1.value : 'A';
    let sub = 'A'; // Có thể cần cập nhật dựa trên yêu cầu của bạn
    let formattedName = namecsa !== 'A' ? `N'${namecsa}'` : namecsa;

    const url = urlIp + 'api/v1/qc/rp2detailcsa';

    loadingInstance = ElLoading.service({
      lock: true,
      text: 'Loading...',
      background: 'rgba(0, 0, 0, 0.7)',
    });

    try {
      const res = await axios.get(url, {
        params: { ry: ry, st: st1.value, ed: ed1.value, sub: upid, name: formattedName },
      });

      // Kiểm tra dữ liệu trả về
      if (res.data && Array.isArray(res.data.data) && res.data.data.length > 0) {
        state.data_details = res.data.data.map(formatItem);
        console.log('state.data_details', state.data_details);
      } else {
        state.data_details = [];
        console.log('Không có dữ liệu tìm thấy.');
      }
    } catch (error) {
      console.error('Lỗi khi tải dữ liệu:', error);
    } finally {
      setTimeout(() => {
        loadingInstance.close();
      }, 500);
    }
  }

  const Lean = async (sno) => {
    let GXLB;

    const cleanedUpid2 = upid2.value.trim();

    if (cleanedUpid2 === 'Lỗi chặt') {
      GXLB = 'C';
    } else if (cleanedUpid2 === 'Lỗi may' || cleanedUpid2 === 'Bao bì may') {
      GXLB = 'S';
    } else if (cleanedUpid2 === 'Lỗi gò' || cleanedUpid2 === 'Trước khi hấp') {
      GXLB = 'A';
    } else {
      console.error('Invalid upid2 value:', cleanedUpid2);
      return;
    }

    try {
      const response = await axios.post(`${urlIp}api/v1/qc/leans`, { GXLB });
      data.value = response.data.data;
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  onMounted(() => {
    readFile();
  });
  watch(locale, () => {
    getdata(); // Gọi lại hàm getdata khi đổi ngôn ngữ
  });
  function gohome() {
    emit('setshow', 0, true, '1');
  }

  const loading = ref(false);
  let loadingInstance = null; // Biến để lưu trữ trạng thái loading
  const isLoading = ref(false); // Add this line
</script>

<style scoped>

</style>
