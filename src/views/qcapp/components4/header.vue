<template>
  <el-header class="erp-header">
    <el-space direction="horizontal" alignment="center" class="erp-header-space">
      <el-button v-if="title[1]" class="erp-back-btn" type="text" circle @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <span class="erp-header-title">{{ getTitle(title[0]) }}</span>
    </el-space>
    <div class="erp-header-lang">
      <LangChange color="#303133" />
    </div>
  </el-header>
</template>

<script setup>
  import { ArrowLeft } from '@element-plus/icons-vue';
  import LangChange from '@/components/LangChange/index.vue';
  import { useI18n } from 'vue-i18n';
  const { t } = useI18n();
  const emit = defineEmits(['setshow']);
  const props = defineProps(['title']);
  const { title } = toRefs(props);
  const goBack = () => {
    emit('setshow', 0, true, '1');
  };

  const getTitle = () => {
    switch (title.value[0]) {
      case '5':
        return t('qc.Input');
      case '7':
        return t('qc.Input1');
      case '4':
        return t('qc.Report');
      case '3':
        return t('qc.Todays');
      case '8':
        return t('qc.exportovertime');

      default:
        return 'HOME';
    }
  };
</script>

<style scoped>
</style>
