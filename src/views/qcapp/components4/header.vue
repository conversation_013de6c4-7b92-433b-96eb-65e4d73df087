<template>
  <el-page-header :icon="ArrowLeft" v-bind="pageHeaderProps">
    <template #content>
      <span class="text-large font-600 mr-3"> {{ getTitle() }} </span>
    </template>
    <template #extra>
      <LangChange class="lang" />
    </template>
  </el-page-header>
</template>

<script setup>
import { computed, toRefs } from 'vue';
import { ArrowLeft } from '@element-plus/icons-vue';
import LangChange from '@/components/LangChange/index.vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const emit = defineEmits(['setshow']);
const props = defineProps(['title']);
const { title } = toRefs(props);

const goBack = () => {
  emit('setshow', 0, true, '1');
};

const pageHeaderProps = computed(() => {
  return title.value[1] ? { onBack: goBack } : {};
});

const getTitle = () => {
  switch (title.value[0]) {
    case '5':
      return t('qc.Input');
    case '7':
      return t('qc.Input1');
    case '4':
      return t('qc.Report');
    case '3':
      return t('qc.Todays');
    case '8':
      return t('qc.exportovertime');
    default:
      return 'HOME';
  }
};
</script>

<style scoped>
.el-page-header {
  padding: 16px 24px;
  background-color: #fff;
  border-bottom: 1px solid var(--el-border-color-lighter);
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

:deep(.el-page-header__content) {
  color: var(--el-text-color-primary);
}

.text-large {
  font-size: 1.2rem;
}

.font-600 {
  font-weight: 600;
}

.lang {
  margin-left: auto;
}
</style>
