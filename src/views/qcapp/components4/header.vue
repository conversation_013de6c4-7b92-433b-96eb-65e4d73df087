<template>
  <el-header class="erp-header">
    <el-space direction="horizontal" alignment="center" class="erp-header-space">
      <el-button v-if="title[1]" class="erp-back-btn" type="text" circle @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <span class="erp-header-title">{{ getTitle(title[0]) }}</span>
    </el-space>
    <div class="erp-header-lang">
      <LangChange color="#303133" />
    </div>
  </el-header>
</template>

<script setup>
  import { ArrowLeft } from '@element-plus/icons-vue';
  import LangChange from '@/components/LangChange/index.vue';
  import { useI18n } from 'vue-i18n';
  const { t } = useI18n();
  const emit = defineEmits(['setshow']);
  const props = defineProps(['title']);
  const { title } = toRefs(props);
  const goBack = () => {
    emit('setshow', 0, true, '1');
  };

  const getTitle = () => {
    switch (title.value[0]) {
      case '5':
        return t('qc.Input');
      case '7':
        return t('qc.Input1');
      case '4':
        return t('qc.Report');
      case '3':
        return t('qc.Todays');
      case '8':
        return t('qc.exportovertime');

      default:
        return 'HOME';
    }
  };
</script>

<style scoped>
.erp-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  background: linear-gradient(90deg, #be83f1 0%, #7f53ac 100%);
  color: #fff;
  box-shadow: 0 2px 8px rgba(126, 87, 194, 0.08);
  padding: 0 32px;
}
.erp-header-space {
  flex: 1;
  align-items: center;
}
.erp-back-btn {
  color: #fff;
  background: rgba(255,255,255,0.08);
  margin-right: 16px;
  transition: background 0.2s;
}
.erp-back-btn:hover {
  background: rgba(255,255,255,0.18);
}
.erp-header-title {
  font-size: 1.6rem;
  font-weight: 700;
  letter-spacing: 1px;
  color: #fff;
  text-shadow: 0 2px 8px rgba(126, 87, 194, 0.12);
}
.erp-header-lang {
  display: flex;
  align-items: center;
  margin-left: 24px;
}
@media (max-width: 768px) {
  .erp-header {
    height: 48px;
    padding: 0 8px;
  }
  .erp-header-title {
    font-size: 1.1rem;
  }
  .erp-header-lang {
    margin-left: 8px;
  }
}
</style>
