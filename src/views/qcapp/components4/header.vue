<template>
  <el-header class="modern-header">
    <div class="header-container">
      <!-- Left Section: Navigation -->
      <div class="header-left">
        <!-- Back Button -->
        <el-button
          v-if="title[1]"
          class="back-button"
          type="primary"
          :icon="ArrowLeft"
          circle
          @click="goBack"
          size="large"
        />

        <!-- Breadcrumb Navigation -->
        <div class="breadcrumb-section">
          <el-breadcrumb separator="/" class="header-breadcrumb">
            <el-breadcrumb-item>
              <el-icon class="breadcrumb-icon">
                <House />
              </el-icon>
              {{ t('qc.home') }}
            </el-breadcrumb-item>
            <el-breadcrumb-item v-if="title[0] !== '1'">
              <el-icon class="breadcrumb-icon">
                <component :is="getPageIcon(title[0])" />
              </el-icon>
              {{ getTitle(title[0]) }}
            </el-breadcrumb-item>
          </el-breadcrumb>

          <!-- Page Title -->
          <h1 class="page-title">
            <el-icon class="title-icon">
              <component :is="getPageIcon(title[0])" />
            </el-icon>
            {{ getTitle(title[0]) }}
          </h1>
        </div>
      </div>

      <!-- Center Section: Status Indicators -->
      <div class="header-center">
        <div class="status-indicators">
          <!-- Connection Status -->
          <el-tooltip :content="t('qc.connectionStatus')" placement="bottom">
            <div class="status-item">
              <el-icon class="status-icon online">
                <Connection />
              </el-icon>
              <span class="status-text">{{ t('qc.online') }}</span>
            </div>
          </el-tooltip>

          <!-- Current Time -->
          <div class="status-item time-display">
            <el-icon class="status-icon">
              <Clock />
            </el-icon>
            <span class="status-text">{{ currentTime }}</span>
          </div>
        </div>
      </div>

      <!-- Right Section: Actions -->
      <div class="header-right">
        <!-- Notifications -->
        <el-badge :value="notificationCount" :hidden="notificationCount === 0" class="notification-badge">
          <el-button
            class="action-button"
            :icon="Bell"
            circle
            @click="showNotifications"
            size="large"
          />
        </el-badge>

        <!-- Help Button -->
        <el-tooltip :content="t('qc.help')" placement="bottom">
          <el-button
            class="action-button"
            :icon="QuestionFilled"
            circle
            @click="showHelp"
            size="large"
          />
        </el-tooltip>

        <!-- Language Switcher -->
        <div class="language-switcher">
          <LangChange color="var(--el-text-color-primary)" />
        </div>

        <!-- User Menu -->
        <el-dropdown trigger="click" class="user-dropdown">
          <el-button class="user-button" size="large">
            <el-avatar
              :size="32"
              :src="userAvatar"
              class="user-avatar"
            >
              <el-icon><User /></el-icon>
            </el-avatar>
            <span class="user-name">{{ userName }}</span>
            <el-icon class="dropdown-arrow">
              <ArrowDown />
            </el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>
                <el-icon><User /></el-icon>
                {{ t('qc.profile') }}
              </el-dropdown-item>
              <el-dropdown-item>
                <el-icon><Setting /></el-icon>
                {{ t('qc.settings') }}
              </el-dropdown-item>
              <el-dropdown-item divided>
                <el-icon><SwitchButton /></el-icon>
                {{ t('qc.logout') }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- Mobile Header -->
    <div class="mobile-header" v-if="isMobile">
      <div class="mobile-container">
        <el-button
          v-if="title[1]"
          class="mobile-back-btn"
          :icon="ArrowLeft"
          circle
          @click="goBack"
        />

        <div class="mobile-title">
          <el-icon class="mobile-title-icon">
            <component :is="getPageIcon(title[0])" />
          </el-icon>
          <span>{{ getTitle(title[0]) }}</span>
        </div>

        <div class="mobile-actions">
          <el-badge :value="notificationCount" :hidden="notificationCount === 0">
            <el-button
              :icon="Bell"
              circle
              size="small"
              @click="showNotifications"
            />
          </el-badge>

          <el-dropdown trigger="click">
            <el-button :icon="More" circle size="small" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="showHelp">
                  <el-icon><QuestionFilled /></el-icon>
                  {{ t('qc.help') }}
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-icon><Setting /></el-icon>
                  {{ t('qc.settings') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
  </el-header>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, toRefs } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage, ElNotification } from 'element-plus';
import {
  ArrowLeft,
  House,
  Connection,
  Clock,
  Bell,
  QuestionFilled,
  User,
  ArrowDown,
  Setting,
  SwitchButton,
  More,
  DataAnalysis,
  Edit,
  Document,
  TrendCharts,
  Timer
} from '@element-plus/icons-vue';
import LangChange from '@/components/LangChange/index.vue';

const { t } = useI18n();

// Props and Emits
const emit = defineEmits(['setshow']);
const props = defineProps(['title']);
const { title } = toRefs(props);

// Reactive state
const currentTime = ref('');
const notificationCount = ref(3);
const isMobile = ref(window.innerWidth <= 768);
const userName = ref('Admin User');
const userAvatar = ref('');

// Time update
let timeInterval = null;

const updateTime = () => {
  const now = new Date();
  currentTime.value = now.toLocaleTimeString('vi-VN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// Navigation methods
const goBack = () => {
  emit('setshow', 0, true, '1');
};

// Page title and icon mapping
const getTitle = (pageId) => {
  const titleMap = {
    '1': t('qc.home'),
    '3': t('qc.todaysInspection'),
    '4': t('qc.reports'),
    '5': t('qc.inputData'),
    '7': t('qc.inputData1'),
    '8': t('qc.exportOvertime')
  };
  return titleMap[pageId] || t('qc.home');
};

const getPageIcon = (pageId) => {
  const iconMap = {
    '1': House,
    '3': TrendCharts,
    '4': Document,
    '5': Edit,
    '7': Edit,
    '8': Timer
  };
  return iconMap[pageId] || DataAnalysis;
};

// Action handlers
const showNotifications = () => {
  ElNotification({
    title: t('qc.notifications'),
    message: t('qc.noNewNotifications'),
    type: 'info',
    duration: 3000
  });
  notificationCount.value = 0;
};

const showHelp = () => {
  ElMessage({
    message: t('qc.helpComingSoon'),
    type: 'info',
    duration: 2000
  });
};

// Responsive handling
const handleResize = () => {
  isMobile.value = window.innerWidth <= 768;
};

// Lifecycle
onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
/* Modern Header Styles */
.modern-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: 1000;
  height: auto;
  padding: 0;
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  max-width: 1400px;
  margin: 0 auto;
  gap: 24px;
}

/* Left Section */
.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
  min-width: 0;
}

.back-button {
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.back-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
}

.breadcrumb-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 0;
}

.header-breadcrumb {
  font-size: 0.9rem;
  color: var(--el-text-color-regular);
}

.breadcrumb-icon {
  font-size: 0.9rem;
  margin-right: 4px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.title-icon {
  font-size: 1.3rem;
  color: var(--el-color-primary);
  flex-shrink: 0;
}

/* Center Section */
.header-center {
  display: flex;
  justify-content: center;
  flex: 0 0 auto;
}

.status-indicators {
  display: flex;
  align-items: center;
  gap: 24px;
  background: var(--el-fill-color-extra-light);
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid var(--el-border-color-lighter);
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: var(--el-text-color-regular);
}

.status-icon {
  font-size: 1rem;
}

.status-icon.online {
  color: var(--el-color-success);
  animation: pulse 2s infinite;
}

.time-display {
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

/* Right Section */
.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 0 0 auto;
}

.action-button {
  background: var(--el-fill-color-light);
  border: 1px solid var(--el-border-color-lighter);
  color: var(--el-text-color-regular);
  transition: all 0.3s ease;
}

.action-button:hover {
  background: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary-light-5);
  color: var(--el-color-primary);
  transform: translateY(-1px);
}

.notification-badge {
  position: relative;
}

.language-switcher {
  display: flex;
  align-items: center;
}

.user-dropdown {
  margin-left: 8px;
}

.user-button {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: var(--el-fill-color-extra-light);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 20px;
  transition: all 0.3s ease;
  height: auto;
}

.user-button:hover {
  background: var(--el-fill-color-light);
  border-color: var(--el-color-primary-light-5);
  transform: translateY(-1px);
}

.user-avatar {
  flex-shrink: 0;
}

.user-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  white-space: nowrap;
}

.dropdown-arrow {
  font-size: 0.8rem;
  color: var(--el-text-color-regular);
  transition: transform 0.3s ease;
}

.user-dropdown.is-opened .dropdown-arrow {
  transform: rotate(180deg);
}

/* Mobile Header */
.mobile-header {
  display: none;
  background: white;
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.mobile-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  gap: 16px;
}

.mobile-back-btn {
  flex-shrink: 0;
}

.mobile-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.mobile-title-icon {
  font-size: 1.1rem;
  color: var(--el-color-primary);
  flex-shrink: 0;
}

.mobile-title span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mobile-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .header-container {
    padding: 16px 20px;
    gap: 20px;
  }

  .status-indicators {
    gap: 16px;
  }

  .user-name {
    display: none;
  }

  .page-title {
    font-size: 1.3rem;
  }
}

@media (max-width: 992px) {
  .header-center {
    display: none;
  }

  .header-container {
    gap: 16px;
  }

  .breadcrumb-section {
    gap: 4px;
  }

  .header-breadcrumb {
    display: none;
  }
}

@media (max-width: 768px) {
  .modern-header {
    display: none;
  }

  .mobile-header {
    display: block;
  }
}

@media (max-width: 480px) {
  .mobile-container {
    padding: 10px 12px;
    gap: 12px;
  }

  .mobile-title {
    font-size: 0.95rem;
  }
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Focus States */
.back-button:focus-visible,
.action-button:focus-visible,
.user-button:focus-visible {
  outline: 2px solid var(--el-color-primary);
  outline-offset: 2px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .modern-header {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-bottom-color: var(--el-border-color-dark);
  }

  .status-indicators {
    background: var(--el-fill-color-dark);
    border-color: var(--el-border-color-dark);
  }

  .user-button {
    background: var(--el-fill-color-dark);
    border-color: var(--el-border-color-dark);
  }

  .action-button {
    background: var(--el-fill-color-dark);
    border-color: var(--el-border-color-dark);
  }
}

/* Custom Scrollbar for Dropdown */
:deep(.el-dropdown-menu) {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border: 1px solid var(--el-border-color-light);
}

:deep(.el-dropdown-menu .el-dropdown-menu__item) {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

:deep(.el-dropdown-menu .el-dropdown-menu__item:hover) {
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

/* Breadcrumb Styling */
:deep(.header-breadcrumb .el-breadcrumb__item) {
  display: flex;
  align-items: center;
}

:deep(.header-breadcrumb .el-breadcrumb__inner) {
  display: flex;
  align-items: center;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

:deep(.header-breadcrumb .el-breadcrumb__inner:hover) {
  color: var(--el-color-primary);
}
</style>
