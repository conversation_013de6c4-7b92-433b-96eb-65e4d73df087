<template>
  <header1 :title="pageNO" @setshow="setshow"></header1>

  <div v-if="index === '1'">
    <home @setshow="setshow"></home>
  </div>
  <div v-else-if="index === '2'">
    <inputpage :sno="sno" @setshow="setshow"></inputpage>
  </div>
  <div v-else-if="index === '3'">
    <TodaysInspection :sno="sno" @setshow="setshow"></TodaysInspection>
  </div>
  <div v-else-if="index === '4'">
    <ReportExport :sno="sno" @setshow="setshow"></ReportExport>
  </div>
  <div v-else-if="index === '5'">
    <ReportInput :sno="sno" @setshow="setshow"></ReportInput>
  </div>
  <div v-else-if="index === '6'">
    <inputpage1 :sno="sno" @setshow="setshow"></inputpage1>
  </div>
  <div v-else-if="index === '7'">
    <reportinput1 :sno="sno" @setshow="setshow"></reportinput1>
  </div>
  <div v-else-if="index === '8'">
    <Exportovertime :sno="sno" @setshow="setshow"></Exportovertime>
  </div>
</template>

<script>
  export default {
    name: 'App',
  };
</script>

<script setup>
  import { ref } from 'vue';
  import header1 from './components4/header.vue';
  import home from './components4/home.vue';
  import inputpage from './components4/inputpage.vue';
  import TodaysInspection from './components4/todaysinspection .vue';
  import ReportExport from './components4/reportexport.vue';
  import Exportovertime from './components4/exportovertime.vue';
  import ReportInput from './components4/reportinput.vue';
  import inputpage1 from './components4/inputpage1.vue';
  import reportinput1 from './components4/reportinput1.vue';
  
  import { useI18n } from 'vue-i18n';
  const { t } = useI18n();
  const pageNO = ref(['1', false]);
  const ishow = ref(true);
  const sno = ref();
  const index = ref('1');
  const settitle = (i) => {
    if (i === '1') {
      pageNO.value = [i, false];
    } else if (i === '2') {
      pageNO.value = [i, true];
    } else if (i === '3') {
      pageNO.value = [i, true];
    } else if (i === '4') {
      pageNO.value = [i, true];
    } else if (i === '5') {
      pageNO.value = ['5', true];
    } else if (i === '6') {
      pageNO.value = ['6', true];
    } else if (i === '7') {
      pageNO.value = ['7', true];
    } else if (i === '8') {
      pageNO.value = ['8', true];
    }
  };

  const setshow = (v1, v2, v3) => {
    ishow.value = v2;
    sno.value = v1;
    index.value = v3;
    settitle(v3);
    // console.log(v3);
  };
</script>
