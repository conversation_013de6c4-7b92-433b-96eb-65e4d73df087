<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 23.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 800 800" style="enable-background:new 0 0 800 800;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#F3F6FF;}
	.st1{fill:#DFE7FF;}
	.st2{fill:url(#SVGID_1_);}
	.st3{fill:#FFFFFF;}
	.st4{fill:url(#SVGID_2_);}
	.st5{fill:#F7F8F8;}
	.st6{fill:url(#SVGID_3_);}
	.st7{fill:url(#SVGID_4_);}
	.st8{fill:url(#SVGID_5_);}
	.st9{fill:url(#SVGID_6_);}
	.st10{fill:url(#SVGID_7_);}
	.st11{fill:url(#SVGID_8_);}
	.st12{fill:url(#SVGID_9_);}
	.st13{fill:#B2C6FF;}
	.st14{fill:url(#SVGID_10_);}
	.st15{fill:url(#SVGID_11_);}
	.st16{fill:url(#SVGID_12_);}
	.st17{fill:url(#SVGID_13_);}
	.st18{fill:url(#SVGID_14_);}
	.st19{fill:url(#SVGID_15_);}
	.st20{fill:url(#SVGID_16_);}
	.st21{fill:#D0DEFF;}
	.st22{fill:url(#SVGID_17_);}
	.st23{fill:url(#SVGID_18_);}
	.st24{fill:#7899FF;}
	.st25{fill:#ECF1FF;}
	.st26{fill:url(#SVGID_19_);}
	.st27{fill:url(#SVGID_20_);}
	.st28{fill:url(#SVGID_21_);}
	.st29{fill:url(#SVGID_22_);}
	.st30{fill:url(#SVGID_23_);}
	.st31{fill:url(#SVGID_24_);}
	.st32{fill:url(#SVGID_25_);}
	.st33{fill:url(#SVGID_26_);}
	.st34{fill:url(#SVGID_27_);}
	.st35{fill:url(#SVGID_28_);}
	.st36{fill:url(#SVGID_29_);}
	.st37{fill:url(#SVGID_30_);}
	.st38{fill:url(#SVGID_31_);}
	.st39{fill:url(#SVGID_32_);}
	.st40{fill:url(#SVGID_33_);}
	.st41{fill:url(#SVGID_34_);}
	.st42{fill:url(#SVGID_35_);}
	.st43{fill:url(#SVGID_36_);}
	.st44{fill:url(#SVGID_37_);}
	.st45{fill:url(#SVGID_38_);}
	.st46{fill:url(#SVGID_39_);}
	.st47{fill:url(#SVGID_40_);}
	.st48{fill:url(#SVGID_41_);}
	.st49{fill:url(#SVGID_42_);}
	.st50{fill:url(#SVGID_43_);}
	.st51{fill:url(#SVGID_44_);}
	.st52{fill:url(#SVGID_45_);}
	.st53{fill:url(#SVGID_46_);}
	.st54{fill:url(#SVGID_47_);}
	.st55{fill:url(#SVGID_48_);}
	.st56{fill:url(#SVGID_49_);}
	.st57{fill:url(#SVGID_50_);}
	.st58{fill:url(#SVGID_51_);}
	.st59{fill:url(#SVGID_52_);}
	.st60{fill:url(#SVGID_53_);}
	.st61{fill:url(#SVGID_54_);}
	.st62{fill:url(#SVGID_55_);}
	.st63{fill:url(#SVGID_56_);}
	.st64{fill:url(#SVGID_57_);}
	.st65{fill:url(#SVGID_58_);}
	.st66{fill:url(#SVGID_59_);}
	.st67{fill:url(#SVGID_60_);}
	.st68{fill:url(#SVGID_61_);}
	.st69{fill:url(#SVGID_62_);}
	.st70{fill:url(#SVGID_63_);}
	.st71{fill:url(#SVGID_64_);}
	.st72{fill:url(#SVGID_65_);}
	.st73{fill:url(#SVGID_66_);}
	.st74{fill:url(#SVGID_67_);}
	.st75{fill:url(#SVGID_68_);}
	.st76{fill:url(#SVGID_69_);}
	.st77{fill:url(#SVGID_70_);}
	.st78{fill:url(#SVGID_71_);}
	.st79{fill:#EBF0FF;}
	.st80{fill:url(#SVGID_72_);}
	.st81{fill:url(#SVGID_73_);}
	.st82{fill:url(#SVGID_74_);}
	.st83{fill:url(#SVGID_75_);}
	.st84{fill:url(#SVGID_76_);}
	.st85{fill:url(#SVGID_77_);}
	.st86{fill:url(#SVGID_78_);}
	.st87{fill:url(#SVGID_79_);}
	.st88{fill:url(#SVGID_80_);}
	.st89{fill:url(#SVGID_81_);}
	.st90{fill:url(#SVGID_82_);}
	.st91{fill:url(#SVGID_83_);}
	.st92{fill:url(#SVGID_84_);}
	.st93{fill:url(#SVGID_85_);}
	.st94{fill:url(#SVGID_86_);}
	.st95{fill:url(#SVGID_87_);}
	.st96{fill:url(#SVGID_88_);}
	.st97{fill:url(#SVGID_89_);}
	.st98{fill:url(#SVGID_90_);}
	.st99{fill:url(#SVGID_91_);}
	.st100{fill:url(#SVGID_92_);}
	.st101{fill:url(#SVGID_93_);}
	.st102{fill:url(#SVGID_94_);}
	.st103{fill:url(#SVGID_95_);}
	.st104{fill:url(#SVGID_96_);}
	.st105{fill:url(#SVGID_97_);}
	.st106{fill:url(#SVGID_98_);}
	.st107{fill:url(#SVGID_99_);}
	.st108{fill:url(#SVGID_100_);}
	.st109{fill:url(#SVGID_101_);}
	.st110{fill:url(#SVGID_102_);}
	.st111{fill:url(#SVGID_103_);}
	.st112{fill:url(#SVGID_104_);}
	.st113{fill:url(#SVGID_105_);}
	.st114{fill:url(#SVGID_106_);}
	.st115{fill:url(#SVGID_107_);}
	.st116{fill:url(#SVGID_108_);}
	.st117{fill:url(#SVGID_109_);}
	.st118{fill:url(#SVGID_110_);}
	.st119{fill:url(#SVGID_111_);}
	.st120{fill:url(#SVGID_112_);}
	.st121{fill:url(#SVGID_113_);}
	.st122{fill:url(#SVGID_114_);}
	.st123{fill:url(#SVGID_115_);}
	.st124{fill:url(#SVGID_116_);}
	.st125{fill:url(#SVGID_117_);}
	.st126{fill:url(#SVGID_118_);}
	.st127{fill:url(#SVGID_119_);}
	.st128{fill:url(#SVGID_120_);}
	.st129{fill:url(#SVGID_121_);}
	.st130{fill:url(#SVGID_122_);}
	.st131{fill:url(#SVGID_123_);}
	.st132{fill:url(#SVGID_124_);}
	.st133{fill:url(#SVGID_125_);}
	.st134{fill:url(#SVGID_126_);}
	.st135{fill:url(#SVGID_127_);}
	.st136{fill:url(#SVGID_128_);}
	.st137{fill:url(#SVGID_129_);}
	.st138{fill:url(#SVGID_130_);}
	.st139{fill:url(#SVGID_131_);}
	.st140{fill:url(#SVGID_132_);}
	.st141{fill:url(#SVGID_133_);}
	.st142{fill:url(#SVGID_134_);}
	.st143{fill:url(#SVGID_135_);}
	.st144{fill:url(#SVGID_136_);}
	.st145{fill:url(#SVGID_137_);}
	.st146{fill:url(#SVGID_138_);}
	.st147{fill:url(#SVGID_139_);}
	.st148{fill:url(#SVGID_140_);}
	.st149{fill:url(#SVGID_141_);}
	.st150{fill:url(#SVGID_142_);}
	.st151{fill:url(#SVGID_143_);}
	.st152{fill:url(#SVGID_144_);}
	.st153{fill:url(#SVGID_145_);}
	.st154{fill:url(#SVGID_146_);}
	.st155{fill:url(#SVGID_147_);}
	.st156{fill:url(#SVGID_148_);}
	.st157{fill:url(#SVGID_149_);}
	.st158{fill:url(#SVGID_150_);}
	.st159{fill:url(#SVGID_151_);}
	.st160{fill:url(#SVGID_152_);}
	.st161{fill:url(#SVGID_153_);}
	.st162{fill:url(#SVGID_154_);}
	.st163{fill:url(#SVGID_155_);}
	.st164{fill:url(#SVGID_156_);}
	.st165{fill:url(#SVGID_157_);}
	.st166{fill:url(#SVGID_158_);}
	.st167{fill:url(#SVGID_159_);}
	.st168{fill:url(#SVGID_160_);}
	.st169{fill:url(#SVGID_161_);}
	.st170{fill:url(#SVGID_162_);}
	.st171{fill:url(#SVGID_163_);}
	.st172{fill:url(#SVGID_164_);}
	.st173{fill:url(#SVGID_165_);}
	.st174{fill:url(#SVGID_166_);}
	.st175{fill:url(#SVGID_167_);}
	.st176{fill:url(#SVGID_168_);}
	.st177{fill:url(#SVGID_169_);}
	.st178{fill:url(#SVGID_170_);}
	.st179{fill:url(#SVGID_171_);}
	.st180{fill:url(#SVGID_172_);}
	.st181{fill:url(#SVGID_173_);}
	.st182{fill:url(#SVGID_174_);}
	.st183{fill:url(#SVGID_175_);}
	.st184{fill:url(#SVGID_176_);}
	.st185{fill:url(#SVGID_177_);}
	.st186{fill:url(#SVGID_178_);}
	.st187{fill:url(#SVGID_179_);}
	.st188{fill:url(#SVGID_180_);}
	.st189{fill:url(#SVGID_181_);}
	.st190{fill:url(#SVGID_182_);}
	.st191{fill:url(#SVGID_183_);}
	.st192{fill:#9DB6FF;}
	.st193{fill:url(#SVGID_184_);}
	.st194{fill:url(#SVGID_185_);}
	.st195{fill:url(#SVGID_186_);}
	.st196{fill:url(#SVGID_187_);}
	.st197{fill:url(#SVGID_188_);}
	.st198{fill:url(#SVGID_189_);}
	.st199{fill:url(#SVGID_190_);}
	.st200{fill:url(#SVGID_191_);}
	.st201{fill:url(#SVGID_192_);}
	.st202{fill:url(#SVGID_193_);}
	.st203{fill:#9AB3FF;}
	.st204{fill:url(#SVGID_194_);}
	.st205{fill:url(#SVGID_195_);}
	.st206{fill:url(#SVGID_196_);}
	.st207{fill:url(#SVGID_197_);}
	.st208{fill:url(#SVGID_198_);}
	.st209{fill:url(#SVGID_199_);}
	.st210{fill:url(#SVGID_200_);}
	.st211{fill:url(#SVGID_201_);}
	.st212{fill:url(#SVGID_202_);}
	.st213{fill:url(#SVGID_203_);}
	.st214{fill:url(#SVGID_204_);}
	.st215{fill:url(#SVGID_205_);}
	.st216{fill:url(#SVGID_206_);}
	.st217{fill:url(#SVGID_207_);}
	.st218{fill:url(#SVGID_208_);}
	.st219{fill:url(#SVGID_209_);}
	.st220{fill:url(#SVGID_210_);}
	.st221{fill:url(#SVGID_211_);}
	.st222{fill:url(#SVGID_212_);}
	.st223{fill:url(#SVGID_213_);}
	.st224{fill:url(#SVGID_214_);}
	.st225{fill:url(#SVGID_215_);}
	.st226{fill:url(#SVGID_216_);}
	.st227{fill:url(#SVGID_217_);}
	.st228{fill:url(#SVGID_218_);}
	.st229{fill:url(#SVGID_219_);}
	.st230{fill:url(#SVGID_220_);}
	.st231{fill:url(#SVGID_221_);}
	.st232{fill:url(#SVGID_222_);}
	.st233{fill:url(#SVGID_223_);}
	.st234{fill:url(#SVGID_224_);}
	.st235{fill:url(#SVGID_225_);}
	.st236{fill:url(#SVGID_226_);}
	.st237{fill:url(#SVGID_227_);}
	.st238{fill:url(#SVGID_228_);}
	.st239{fill:url(#SVGID_229_);}
	.st240{fill:url(#SVGID_230_);}
	.st241{fill:url(#SVGID_231_);}
	.st242{fill:url(#SVGID_232_);}
	.st243{fill:url(#SVGID_233_);}
	.st244{fill:url(#SVGID_234_);}
	.st245{fill:url(#SVGID_235_);}
	.st246{fill:url(#SVGID_236_);}
	.st247{fill:url(#SVGID_237_);}
	.st248{fill:url(#SVGID_238_);}
	.st249{fill:url(#SVGID_239_);}
	.st250{fill:url(#SVGID_240_);}
	.st251{fill:url(#SVGID_241_);}
	.st252{fill:url(#SVGID_242_);}
	.st253{fill:url(#SVGID_243_);}
	.st254{fill:url(#SVGID_244_);}
	.st255{fill:url(#SVGID_245_);}
	.st256{fill:url(#SVGID_246_);}
	.st257{fill:url(#SVGID_247_);}
	.st258{fill:url(#SVGID_248_);}
	.st259{fill:url(#SVGID_249_);}
	.st260{fill:url(#SVGID_250_);}
	.st261{fill:url(#SVGID_251_);}
	.st262{fill:url(#SVGID_252_);}
	.st263{fill:url(#SVGID_253_);}
	.st264{fill:url(#SVGID_254_);}
	.st265{fill:url(#SVGID_255_);}
	.st266{fill:url(#SVGID_256_);}
	.st267{fill:url(#SVGID_257_);}
	.st268{fill:url(#SVGID_258_);}
	.st269{fill:url(#SVGID_259_);}
	.st270{fill:url(#SVGID_260_);}
	.st271{fill:url(#SVGID_261_);}
	.st272{fill:url(#SVGID_262_);}
	.st273{fill:url(#SVGID_263_);}
	.st274{fill:url(#SVGID_264_);}
	.st275{fill:url(#SVGID_265_);}
	.st276{fill:url(#SVGID_266_);}
	.st277{fill:url(#SVGID_267_);}
	.st278{fill:url(#SVGID_268_);}
	.st279{fill:url(#SVGID_269_);}
	.st280{fill:url(#SVGID_270_);}
	.st281{fill:url(#SVGID_271_);}
	.st282{fill:url(#SVGID_272_);}
	.st283{fill:url(#SVGID_273_);}
	.st284{fill:url(#SVGID_274_);}
	.st285{fill:url(#SVGID_275_);}
	.st286{fill:url(#SVGID_276_);}
	.st287{fill:url(#SVGID_277_);}
	.st288{fill:url(#SVGID_278_);}
	.st289{fill:url(#SVGID_279_);}
	.st290{fill:url(#SVGID_280_);}
	.st291{fill:#B6C9FF;}
	.st292{fill:url(#SVGID_281_);}
	.st293{fill:url(#SVGID_282_);}
	.st294{fill:url(#SVGID_283_);}
	.st295{fill:url(#SVGID_284_);}
	.st296{fill:url(#SVGID_285_);}
	.st297{fill:url(#SVGID_286_);}
	.st298{fill:url(#SVGID_287_);}
	.st299{fill:url(#SVGID_288_);}
	.st300{fill:url(#SVGID_289_);}
	.st301{fill:#B1C5FF;}
	.st302{fill:url(#SVGID_290_);}
	.st303{fill:url(#SVGID_291_);}
	.st304{fill:url(#SVGID_292_);}
	.st305{fill:url(#SVGID_293_);}
	.st306{fill:url(#SVGID_294_);}
	.st307{fill:url(#SVGID_295_);}
	.st308{fill:url(#SVGID_296_);}
	.st309{fill:url(#SVGID_297_);}
	.st310{fill:url(#SVGID_298_);}
	.st311{fill:url(#SVGID_299_);}
	.st312{fill:url(#SVGID_300_);}
	.st313{fill:url(#SVGID_301_);}
	.st314{fill:url(#SVGID_302_);}
	.st315{fill:url(#SVGID_303_);}
	.st316{fill:url(#SVGID_304_);}
	.st317{fill:url(#SVGID_305_);}
	.st318{fill:url(#SVGID_306_);}
	.st319{fill:url(#SVGID_307_);}
	.st320{fill:url(#SVGID_308_);}
	.st321{fill:#87A4FF;}
	.st322{fill:url(#SVGID_309_);}
	.st323{fill:url(#SVGID_310_);}
	.st324{fill:url(#SVGID_311_);}
	.st325{fill:url(#SVGID_312_);}
	.st326{fill:url(#SVGID_313_);}
	.st327{fill:url(#SVGID_314_);}
	.st328{fill:url(#SVGID_315_);}
	.st329{fill:url(#SVGID_316_);}
	.st330{fill:url(#SVGID_317_);}
	.st331{fill:url(#SVGID_318_);}
	.st332{fill:url(#SVGID_319_);}
	.st333{fill:url(#SVGID_320_);}
	.st334{fill:url(#SVGID_321_);}
	.st335{fill:url(#SVGID_322_);}
	.st336{fill:url(#SVGID_323_);}
	.st337{fill:url(#SVGID_324_);}
	.st338{fill:url(#SVGID_325_);}
	.st339{fill:url(#SVGID_326_);}
	.st340{fill:url(#SVGID_327_);}
	.st341{fill:url(#SVGID_328_);}
	.st342{fill:url(#SVGID_329_);}
	.st343{fill:url(#SVGID_330_);}
	.st344{fill:url(#SVGID_331_);}
</style>
<g id="暂无内容">
</g>
<g id="图层_2">
</g>
<g id="图层_3">
</g>
<g id="图层_4">
</g>
<g id="图层_5">
	<g>
		<g>
			<circle class="st0" cx="665.41" cy="171.55" r="19.78"/>
			<circle class="st1" cx="97.29" cy="623.35" r="19.78"/>
			<path class="st1" d="M218.42,685.35c-7.38,0-13.38-6-13.38-13.38s6-13.38,13.38-13.38c7.38,0,13.38,6,13.38,13.38
				S225.79,685.35,218.42,685.35z M218.42,665.02c-3.83,0-6.95,3.12-6.95,6.95c0,3.83,3.12,6.95,6.95,6.95
				c3.83,0,6.95-3.12,6.95-6.95C225.37,668.14,222.25,665.02,218.42,665.02z"/>
			<path class="st0" d="M596.85,112.21h-19.98V92.23c0-3.66-2.96-6.62-6.62-6.62c-3.66,0-6.62,2.96-6.62,6.62v19.98h-19.98
				c-3.66,0-6.62,2.96-6.62,6.62c0,3.66,2.96,6.62,6.62,6.62h19.98v19.98c0,3.66,2.96,6.62,6.62,6.62c3.66,0,6.62-2.96,6.62-6.62
				v-19.98h19.98c3.66,0,6.62-2.96,6.62-6.62C603.47,115.17,600.5,112.21,596.85,112.21z"/>
			<path class="st1" d="M654.43,689.24h-14.84V674.4c0-3.04-2.47-5.51-5.51-5.51c-3.04,0-5.51,2.47-5.51,5.51v14.84h-14.84
				c-3.04,0-5.51,2.47-5.51,5.51s2.47,5.51,5.51,5.51h14.84v14.84c0,3.04,2.47,5.51,5.51,5.51c3.04,0,5.51-2.47,5.51-5.51v-14.84
				h14.84c3.04,0,5.51-2.47,5.51-5.51S657.47,689.24,654.43,689.24z"/>
			<path class="st0" d="M119.36,127.65c8.7,8.7,22.82,8.7,31.52,0l0,0c8.7-8.7,8.7-22.82,0-31.52l-6.66-6.66
				c-8.7-8.7-22.82-8.7-31.52,0h0c-8.7,8.7-8.7,22.82,0,31.52L119.36,127.65z"/>
			<path class="st0" d="M234.08,119.6c3.97,3.97,10.41,3.97,14.38,0c3.97-3.97,3.97-10.41,0-14.38l-10.01-10.01
				c-3.97-3.97-10.41-3.97-14.38,0c-3.97,3.97-3.97,10.41,0,14.38L234.08,119.6z"/>
			<path class="st0" d="M83.01,320.32c3.97,3.97,10.41,3.97,14.38,0h0c3.97-3.97,3.97-10.41,0-14.38l-10.01-10.01
				c-3.97-3.97-10.41-3.97-14.38,0c-3.97,3.97-3.97,10.41,0,14.38L83.01,320.32z"/>
			<path class="st1" d="M610.97,627.78c-3.97-3.97-10.41-3.97-14.38,0c-3.97,3.97-3.97,10.41,0,14.38l10.01,10.01
				c3.97,3.97,10.41,3.97,14.38,0c3.97-3.97,3.97-10.41,0-14.38L610.97,627.78z"/>
			<circle class="st1" cx="698.59" cy="636.27" r="32.77"/>
			<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="387.3627" y1="91.5774" x2="387.3627" y2="710.569">
				<stop  offset="1.918004e-07" style="stop-color:#F6F8FF"/>
				<stop  offset="1" style="stop-color:#D9E3FF"/>
			</linearGradient>
			<path class="st2" d="M699.24,484.31l-31.51-31.51l-19.97-21.27c-5.17-5.43-5.24-13.94-0.16-19.46l0,0
				c5.55-6.03,15.04-6.14,20.72-0.23l4.04,4.41l9.32,9.32c5.41,5.41,14.29,5.82,19.8,0.51c5.61-5.41,5.68-14.35,0.19-19.84
				l-18.63-18.63c-6.88-6.88-11.36-15.82-12.66-25.46c-18.22-135.39-134.2-239.79-274.58-239.79c-4.79,0-9.55,0.13-14.28,0.37
				c-5.11,0.26-10.1-1.61-13.71-5.23l-19.14-19.14c-8.55-8.55-22.47-9.18-31.32-0.94c-9.29,8.65-9.48,23.19-0.59,32.09l3.34,3.34
				l10.41,10.94c6.54,6.92,5.57,18-2.08,23.68c-6.51,4.83-14.68,3.26-20.19-2.69l-20.69-20.46l-18.04-18.04
				c-3.83-3.83-10.06-4.28-14.13-0.71c-4.46,3.92-4.62,10.71-0.49,14.84l13.14,13.14l8.56,8.24c8.7,8.52,8.89,21.8,0.24,30.36l0,0
				c-8.59,8.49-22.45,8.38-30.89-0.26l-14.74-15.33l-35.31-35.31c-8.7-8.7-22.82-8.7-31.52,0h0c-8.7,8.7-8.7,22.82,0,31.52
				l25.61,25.69l8.98,8.38c8.49,8.43,8.69,22.09,0.45,30.76l0,0c-8.82,9.29-23.69,9.11-32.28-0.4l-4.96-5.44l-15.36-15.44
				c-12.62-12.62-33.2-13.4-46.16-1.12c-13.45,12.73-13.67,33.97-0.66,46.98l37.34,37.33l17.8,17.59c6.71,6.64,6.5,17.55-0.46,23.93
				l0,0c-6.58,6.03-16.76,5.75-22.99-0.63l-5.35-5.55l-8.11-8.11c-3.97-3.97-10.41-3.97-14.38,0h0c-5.06,4.2-3.69,10.69,0.06,14.44
				l17.79,17.79l36.99,36.09c6.38,6.22,6.41,16.47,0.08,22.73l0,0c-6.18,6.11-16.12,6.15-22.34,0.08l-17.67-17.24l-25.96-25.96
				c-5.41-5.41-14.29-5.82-19.8-0.51c-5.61,5.41-5.68,14.35-0.19,19.84l43.89,43.89c3.55,3.55,5.91,8.11,6.84,13.05
				c23.49,124.93,130.8,220.27,261.3,225.36c7.2,0.28,14.06,3.16,19.28,8.14c8.46,8.08,20.27,19.34,20.27,19.34
				c8.55,8.55,22.47,9.18,31.32,0.94c9.29-8.65,9.48-23.19,0.59-32.09l-2.26-2.26l-9.81-10.24c-5.59-6.06-5.03-15.55,1.23-20.92l0,0
				c5.93-5.08,14.81-4.58,20.13,1.13l20.22,21.22l22.02,22.02c8.9,8.89,23.44,8.7,32.09-0.58c8.24-8.84,7.6-22.77-0.94-31.32
				l-9.5-9.5c0,0-3.02-3.11-6.89-7.09c-9.03-9.28-8.71-24.16,0.7-33.05l0.48-0.45c9.02-8.52,23.15-8.43,32.07,0.2l13.82,13.38
				l5.91,5.91c3.83,3.83,10.07,4.28,14.13,0.71c4.46-3.92,4.62-10.72,0.49-14.84l-5.15-5.15c-0.94,0.44-20.83-20.04-32.63-32.32
				c-4.75-4.94-4.56-12.79,0.41-17.5h0c4.95-4.69,12.77-4.47,17.45,0.48l31.03,32.82l14.15,14.15c12.62,12.62,33.19,13.4,46.16,1.13
				c13.45-12.73,13.67-33.97,0.66-46.98l-22.57-22.57l-11.4-12.43c-5.57-6.02-5.32-15.38,0.56-21.1h0
				c6.11-5.94,15.92-5.64,21.67,0.65l2.92,3.4l14.64,14.65c8.9,8.9,23.44,8.7,32.09-0.58C708.42,506.79,707.79,492.86,699.24,484.31
				z"/>
			<path class="st3" d="M427.04,205.2H412.2v-14.84c0-3.04-2.47-5.51-5.51-5.51c-3.04,0-5.51,2.47-5.51,5.51v14.84h-14.84
				c-3.04,0-5.51,2.47-5.51,5.51s2.47,5.51,5.51,5.51h14.84v14.84c0,3.04,2.47,5.51,5.51,5.51c3.04,0,5.51-2.47,5.51-5.51v-14.84
				h14.84c3.04,0,5.51-2.47,5.51-5.51S430.08,205.2,427.04,205.2z"/>
			<path class="st3" d="M582.8,423.74c-7.38,0-13.38-6-13.38-13.38c0-7.38,6-13.38,13.38-13.38s13.38,6,13.38,13.38
				C596.17,417.74,590.17,423.74,582.8,423.74z M582.8,403.4c-3.83,0-6.95,3.12-6.95,6.95c0,3.83,3.12,6.95,6.95,6.95
				c3.83,0,6.95-3.12,6.95-6.95C589.75,406.52,586.63,403.4,582.8,403.4z"/>
			<path class="st3" d="M220.15,525.77c-7.38,0-13.38-6-13.38-13.38c0-7.38,6-13.38,13.38-13.38c7.38,0,13.38,6,13.38,13.38
				C233.52,519.77,227.52,525.77,220.15,525.77z M220.15,505.44c-3.83,0-6.95,3.12-6.95,6.95c0,3.83,3.12,6.95,6.95,6.95
				c3.83,0,6.95-3.12,6.95-6.95C227.1,508.56,223.98,505.44,220.15,505.44z"/>
		</g>
		<g>
			<linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="285.1193" y1="174.8921" x2="519.4578" y2="580.7783">
				<stop  offset="0" style="stop-color:#D4E1FF"/>
				<stop  offset="1" style="stop-color:#7798FF"/>
			</linearGradient>
			<path class="st4" d="M579.77,259.16c-39.46,0-71.45-31.99-71.45-71.45c0-0.02,0-0.04,0-0.06c0-3.61-2.38-6.79-5.89-7.64
				c-30.42-7.37-67.51-12.96-110.05-12.96c-45.16,0-81.57,4.22-110.46,10.16c-3.72,0.77-6.34,4.1-6.19,7.9
				c0.03,0.87,0.05,1.74,0.05,2.61c0,39.46-31.99,71.45-71.45,71.45c-2.93,0-5.81-0.18-8.64-0.52c-4.57-0.56-8.57,3.09-8.57,7.69
				v85.62c0,225.36,156.32,281.4,201.39,292.23c1.39,0.33,2.84,0.27,4.2-0.18c52.25-17.16,202.33-69.45,202.33-292.05v-85.41
				c0-4.61-3.99-8.12-8.58-7.69C584.26,259.05,582.03,259.16,579.77,259.16z"/>
			<path class="st3" d="M428.48,459.57h-18.22v-37.41c32.12-7.9,56.02-36.93,56.02-71.46c0-40.58-33.01-73.6-73.59-73.6
				s-73.59,33.01-73.59,73.6c0,34.22,23.47,63.06,55.17,71.26v87.63c0,9.94,8.06,18,18,18s18-8.06,18-18v-14.03h18.22
				c9.94,0,18-8.06,18-18S438.42,459.57,428.48,459.57z M355.1,350.7c0-20.73,16.87-37.6,37.59-37.6s37.59,16.87,37.59,37.6
				c0,20.73-16.87,37.59-37.59,37.59S355.1,371.43,355.1,350.7z"/>
		</g>
	</g>
</g>
<g id="图层_6">
</g>
<g id="图层_7">
</g>
</svg>
