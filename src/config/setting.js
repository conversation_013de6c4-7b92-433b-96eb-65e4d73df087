export const setting = {
  base: '/',
  publicDir: 'public',
  cacheDir: 'node_modules/.vite',
  outDir: 'dist',
  assetsDir: 'static/',
  sourcemap: false,
  chunkSizeWarningLimit: 2000,
  cssCodeSplit: true,
  brotliSize: false,
  host: '0.0.0.0',
  port: '8089',
  strictPort: false,
  open: true,
  progressBar: true,
  defaultOpeneds: [],
  uniqueOpened: false,
  tokenName: 'accessToken',
  loginInterception: true,
  tokenTableName: 'access-token',
  langKey: 'i18nLang',
  lang: 'en',
  storage: 'localStorage',
  title: 'Tyxuan',
  copyright: '© Tyxuan',
  footerCopyright: true,
  keepAliveMaxNum: 99,
  recordRoute: true,
  routesWhiteList: ['/404', '/401'],
  debounce: [],
  extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json'],
  logLevel: 'info',
  clearScreen: false,
  drop_console: true,
  drop_debugger: true,
};
