import { createI18n } from 'vue-i18n';

import { setting } from '@/config/setting'; // 配置文件，包含默认语言
import { getLanguage, setLanguage } from '@/utils/cookies'; // 从 cookies 获取和设置语言

// 引入 Element Plus 的语言包
// import elementEnLocale from 'element-plus/lib/locale/lang/en';
// import elementViLocale from 'element-plus/lib/locale/lang/vi';

// 引入自定义的语言包
import enLocale from './lang/en';
import viLocale from './lang/vi';

// 合并自定义和 Element Plus 的语言包
const messages = {
  en: {
    ...enLocale,
    // ...elementEnLocale,
  },
  vi: {
    ...viLocale,
    // ...elementViLocale,
  },
};

// 获取当前语言
export const getLocale = () => {
  // 从 cookie 获取语言
  const cookieLanguage = getLanguage();
  if (cookieLanguage) {
    return cookieLanguage;
  }

  // 使用浏览器的语言
  const language = navigator.language.toLowerCase();
  const locales = Object.keys(messages);
  for (const locale of locales) {
    if (language.indexOf(locale) > -1) {
      return locale;
    }
  }

  // 如果没有找到，使用默认语言
  const { lang } = setting;
  return lang;
};

const defaultLanguage = getLocale();
// 如果 cookie 中没有语言，设置默认语言
if (typeof getLanguage() === 'undefined') {
  setLanguage(defaultLanguage);
}

// 创建 i18n 实例
const i18n = createI18n({
  legacy: false, // 使用 Composition API
  locale: defaultLanguage,
  messages: messages,
  fallbackLocale: 'en', // 设置回退语言
  missingWarn: true, // 缺少翻译的警告
  fallbackWarn: true, // 使用回退语言的警告
});

export default i18n;
