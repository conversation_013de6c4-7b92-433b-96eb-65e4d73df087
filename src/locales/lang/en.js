import { Input } from 'postcss';

export default {
  lagname: 'en',
  route: {
    errorPages: 'Error Pages',
    page401: '401',
    page404: '404',
  },

  notification: {
    shutdownAlert: 'Are you sure to save data?',
    selectline: 'Please select line!',
    selecttime: 'Please select time!',
    inspectionqty: 'Please key in Inspection qty!',
    successfully: 'Data saving successfully',
    datafailed: 'Save data failed',
    numbererr: 'Number of errors exceeds number of checks',
    errorinput: 'You have not entered the number of errors',
    IQProdoutput: 'Inspection Qty tests exceeds the production output',
  },

  errorPages: {
    title: 'Sorry!',
    btn: 'Back Home',
    404: {
      desc: 'Current page does not exist...',
      remark:
        'Please check whether the url you entered is correct, or click the button below to return to the home page',
    },
    401: {
      desc: "You don't have permission to go to this page...",
      remark:
        'Please contact the administrator or click the button below to return to the home page',
    },
  },

  btn: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    create: 'Create',
    edit: 'Edit',
    delete: 'Delete',
    submit: 'Submit',
    logout: 'Log out',
    no: 'No',
    yes: 'Yes',
  },
  qc: {
    Material: ' Material Category ',
    Todays: 'Todays Inspection Status',
    Report: 'Report Export',
    Input: 'Report Input ',
    Input1: 'Report Input 1',
    1: 'Upper Printing/Logo on upper',
    2: 'Sockliner logo printing',
    3: 'Heel attaching',
    4: 'High Frequency Deboss/Heat transfer',
    5: 'Embroidery',
    6: 'Peel off',
    7: 'Exposed cement',
    8: 'Off position',
    9: 'Needle holes',
    10: 'Color issue ',
    11: 'Others',
    12: 'Fraying edges of sockliner',
    13: 'Delamination',
    14: 'Off position',
    15: 'Poor effect(less emboss)',
    16: 'Uneven edges',
    17: ' Fraying edges',
    18: 'Threed ands/ loose thread',
    19: 'Run off stitches',
    20: 'Wrinkle',
    21: 'Off position/ incorrect direction',
    22: 'Incorect/ lack of graphic',
    23: 'Cut',
    24: 'Stitch',
    25: 'Lasting',
    home: 'Home',
    start: 'Start',
    end: 'End',
    category: 'Category',
    search: 'Search',
    excel: 'ToExcel',
    category2: 'CATEGORY',
    totaloutput: 'TOTAL OUTPUT',
    inspestiontimes: 'INSPECTION TIMES',
    inspestionqty: 'INSPECTION QTY',
    rewkqty: 'REWORKING QTY',
    rejecttime: 'REJECT TIMES',
    defectqty: 'TOTAL DEFECT QTY',
    inspectionpassrate: 'INSPECTION PASS RATE',
    reworkingrate: 'REWORKING RATE',
    defectrate1: 'DEFECT RATE',
    nodata: 'No data',
    top1defect: 'TOP1 DEFECT',
    qty: 'QTY',
    top2defect: 'TOP2 DEFECT',
    qty2: 'QTY',
    top3defect: 'TOP3 DEFECT',
    qty3: 'QTY',
    checkrysku: 'Check RY-SKU',
    output: 'OutPut',
    inspectionqty: 'Inspection Qty',
    passrate: ' PASS RATE ',
    status: 'STATUS',
    status0: 'REJECT',
    status1: 'ACCEPT',
    inspectiondefect: 'Inspection defect rate',
    inspestionqty2: 'INSPECTION QTY',
    ngqty: 'DEFECT QTY',
    cut: 'Cut',
    switch: 'Stitch',
    assembly: 'Assembly',
    pack: 'Pack',
    special: 'Special',
    search: 'Search',
    exporttoword: 'Export to Word',
    exporttoppt: 'Export to PowerPoint',
    next: 'Next',
    previous: 'Previous',
    excelTitle: 'CUTTING/ STITCHING/ASSEMBLY INSPECTION DATA',
    excelTitleCut: 'CUTTING INSPECTION DATA',
    excelTitleStitch: 'STITCHING INSPECTION DATA',
    excelTitleAsseml: 'ASSEMBLY INSPECTION DATA',

    cut: 'Cutting',
    stitch: 'Stitching',
    asseml: 'Assembling',
    lean: 'LEAN',
    date: 'DATE',
    time: 'TIME',
    numberoforders: 'ORDERS QTY',
    countryoforigin: 'COUNTRY OF ORIGIN',
    shoetypename: 'SHOE TYPE NAME',
    defect: 'DEFECT (DEFECT QTY)',
    output: 'OUTPUT',
    process: 'PROCESS',
    tc: 'Overtime',
    Stitchingpackaging: 'Stitching packaging',
    Beforesteaming: 'Before steaming',
    exportovertime: 'Export over time',
  },
  details: {
    title: 'DETAILS',
    details: 'Details',
    toExcel: 'Export to Excel',
    category: 'Category',
    search: 'Search by keyword...',
    date: 'Date',
    times: 'Times',
    ry: 'RY#',
    output: 'Output Qty',
    insQty: 'Inspection Qty',
    dfQty: 'Defect Qty',
    insPassRate: 'Inspection Pass Rate',
    dfName: 'Defect Names',
    failQty: 'Reworking Qty',
    results: 'Results',
    subCategory: 'SubCategory',
    excelTitle: 'INSPECTION REPORT OF SECONDARY PROCESS',
  },
};
