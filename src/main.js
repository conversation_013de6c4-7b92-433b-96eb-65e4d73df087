import { createApp } from 'vue';
import JsonExcel from 'vue-json-excel3';
import './config/permission';
import App from './App.vue';
const app = createApp(App);

/*import Cropper from "./components/cropper";
app.use(Cropper)
*/
import Cropper from 'vue3-cropper';
//import 'vue3-cropper/lib/vue3-cropper.css';

import '@arco-design/web-vue/dist/arco.css';
import 'animate.css/animate.min.css';

import ElementPlus from 'element-plus';
import 'element-plus/theme-chalk/display.css';
app.use(ElementPlus);

import { VueClipboard } from '@soerenmartius/vue3-clipboard';
app.use(VueClipboard);

import layoutComp from './layouts/components/export';
layoutComp(app);

import router from './router/index';
app.use(router);

import store from '@/store';
app.use(store);

import iconPark from './plugin/icon-park';
iconPark(app);

import loadI18n from './plugin/i18n';
loadI18n(app);

import buttonAntiShake from './directives/buttonAntiShake';
app.use(buttonAntiShake);
app.component('downloadExcel', JsonExcel);
app.mount('#app');
