<template>
  <div class="icon-lang">
    <el-dropdown>
      <icon-translate theme="filled" size="16" :fill="color" :strokeWidth="4" />
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="item in languages"
            :key="item.value"
            :disabled="language == item.value"
          >
            <span @click="handleSetLanguage(item.value)">{{ item.name }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script>
  export default {
    name: 'LangChange',
  };
</script>

<script setup>
  import { reactive, computed, onMounted } from 'vue';
  import { useStore } from 'vuex';
  import i18n from '@/locales';

  const { global } = i18n;

  defineProps({
    color: {
      type: String,
      default: '#666',
    },
  });

  const languages = reactive([
    {
      name: 'English',
      value: 'en',
    },
    {
      name: 'Tiếng Việt',
      value: 'vi',
    },
  ]);

  const store = useStore();
  const language = computed(() => {
    return store.getters['setting/lang'];
  });

  const handleSetLanguage = (lang) => {
    console.log('語言', lang);
    if (lang !== language.value) {
      console.log('切換', lang);
      store.dispatch('setting/changeLanguage', lang);
      global.locale.value = lang;
    }
  };

  // 加载时设置语言
  onMounted(() => {
    const storedLang = localStorage.getItem('language');
    if (storedLang && storedLang !== language.value) {
      handleSetLanguage(storedLang);
    }
  });
</script>

<style lang="scss" scoped>
  .icon-lang {
    padding: 30px 25px;
  }
</style>
