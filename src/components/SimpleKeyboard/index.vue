<template>
  <div v-show="keyboardVisible" class="keyboard-container">
    <div :class="props.keyboardClass"></div>
  </div>
</template>

<script>
  export default {
    name: 'SimpleKeyboard',
  };
</script>

<script setup>
  import { ref, watch, onMounted } from 'vue';
  import Keyboard from 'simple-keyboard';
  import 'simple-keyboard/build/css/index.css';

  const props = defineProps({
    keyboardVisible: {
      type: Boolean,
      default: false,
      required: true,
    },
    keyboardClass: {
      type: String,
      default: 'simple-keyboard',
    },
    input: {
      type: String,
      default: '',
    },
  });
  const emit = defineEmits(['update:input', 'keyPress']);

  const keyboardVisible = computed({
    get() {
      return props.keyboardVisible;
    },
    set(value) {
      emit('close', value);
    },
  });

  watch(
    () => props.input,
    (_newValue) => {
      keyboard.value.setInput(_newValue);
    }
  );

  const keyboard = ref(null);

  const handleChange = (input) => {
    emit('update:input', input);
  };
  const handleKeyPress = (button) => {
    if (button === '{shift}' || button === '{lock}') handleShift();
    emit('keyPress', button);
  };
  const handleShift = () => {
    let currentLayout = keyboard.value.options.layoutName;
    let shiftToggle = currentLayout === 'default' ? 'shift' : 'default';

    keyboard.value.setOptions({
      layoutName: shiftToggle,
    });
  };

  const init = () => {
    keyboard.value = new Keyboard(props.keyboardClass, {
      onChange: handleChange,
      onKeyPress: handleKeyPress,
    });
  };

  onMounted(() => {
    init();
  });
</script>

<style lang="scss" scoped>
  .keyboard-container {
    width: 80%;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }
</style>
