<template>
  <div class="layout">
    <div class="layout-header">
      <div class="lean">
        <el-dropdown>
          <h2>{{ `${currentBuildingNo} - ${currentLeanNo}` }}</h2>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="item in currentBuildingLeanList"
                :key="item"
                :disabled="item === currentLeanNo"
              >
                <span @click="handleSwitchLeanLine(item)">{{ item }}</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      {{ currentTime }}
      <LangChange class="lang" color="#fff" />
    </div>
    <div class="layout-main"><router-view /></div>
  </div>
</template>

<script>
  export default {
    name: 'MesLayout',
  };
</script>

<script setup>
  import { ref, computed, onBeforeMount } from 'vue';
  import { useRouter } from 'vue-router';
  import moment from 'moment';
  import LangChange from '@/components/LangChange/index.vue';
  import { factoryConfig } from '@/config/factory';

  const router = useRouter();

  const currentBuildingNo = computed(() => {
    return router.currentRoute.value.params.buildingNo;
  });
  const currentLeanNo = computed(() => {
    return router.currentRoute.value.params.leanNo;
  });
  const currentBuildingLeanList = computed(() => {
    const target = factoryConfig.factoryBuildingLeanLine[currentBuildingNo.value];
    const result = target.leanLineList.map((item) => {
      return item.leanLineName.split(' ')[1];
    });
    return result;
  });

  const currentTime = ref(moment().format('YYYY/MM/DD HH:mm:ss'));
  const syncCurrentTime = () => {
    setInterval(() => {
      currentTime.value = moment().format('YYYY/MM/DD HH:mm:ss');
    }, 1000);
  };

  const handleSwitchLeanLine = (leanLineName) => {
    if (currentLeanNo.value === leanLineName) return;
    router.push(`/mes/${currentBuildingNo.value}/${leanLineName}`);
  };

  onBeforeMount(() => {
    syncCurrentTime();
  });
</script>

<style lang="scss" scoped>
  .layout {
    width: 100vw;
    height: 100vh;
    background-color: #000;

    .layout-header {
      position: absolute;
      top: 0;
      z-index: 999;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100vw;
      height: 64px;
      padding-bottom: 10px;
      font-size: 30px;
      font-weight: bold;
      color: #fff;
      background: url('@/assets/images/header_bg.png');
      background-repeat: no-repeat;
      background-size: 100% 64px;

      .lean {
        position: absolute;
        top: -5px;
        left: 3px;
        h2 {
          color: #fff;
          font-size: 18px;
          font-weight: 800;
        }
      }

      .lang {
        position: absolute;
        top: -10px;
        right: 0;
      }
      .lang:hover {
        background: transparent;
      }
    }

    .layout-main {
      width: 100vw;
      height: 100vh;
      background-image: linear-gradient(90deg, #01355059 1px, transparent 1px),
        linear-gradient(0deg, #01355059 1px, transparent 1px);
      background-size: 30px 30px, 30px 30px;
    }
  }
</style>
