<template>
  <div v-if="store.getters['setting/routerView']" class="app-main-container">
    <router-view class="app-main-height" />
    <footer class="footer-copyright">{{ copyrightStr }} </footer>
  </div>
</template>

<script>
  export default {
    name: 'MesMain',
  };
</script>

<script setup>
  import { ref } from 'vue';
  import { useStore } from 'vuex';
  import { setting } from '@/config/setting';
  const { copyright } = setting;

  const copyrightStr = ref(copyright);
  const store = useStore();
</script>

<style lang="scss" scoped>
  .app-main-container {
    position: relative;
    // background-color: #1d1d1d;
    box-sizing: border-box;
    width: $base-width;
    overflow: hidden;
    text-align: left;
    .app-main-height {
      min-height: $app-main-min-height;
    }
    .footer-copyright {
      min-height: $footer-copyright-height;
      line-height: $footer-copyright-height;
      color: $base-color-white;
      text-align: center;
      border-top: 1px solid $base-border-color;
    }
  }
</style>
