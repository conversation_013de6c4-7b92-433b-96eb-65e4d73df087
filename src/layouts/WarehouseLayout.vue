<template>
  <div class="mes-container">
    <el-container>
      <el-container class="container">
        <el-header class="header fixed" height="70px">
          <img src="@/assets/LAI_logo.png" alt="LAIYIH" style="height: 60px; margin-right: 10px" />
          <h1 class="currentTime">{{ currentTime }}</h1>
          <div class="menu">
            <el-dropdown>
              <h2>Menu</h2>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>
                    <span @click="handleOrganizationalClick">Organizational</span>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <span @click="handleScheduleClick">Schedule</span>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <span @click="router.push('/trace/scan')">Trace</span>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <span @click="handleShelfClick">Shelf</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        <el-main class="main">
          <div class="app-main-container">
            <slot></slot>
            <footer class="footer-copyright">{{ copyrightStr }} </footer>
          </div>
        </el-main>
      </el-container>
    </el-container>

    <OrganizationalDialog
      v-if="state.organizationalDialogVisible"
      :organizationalDialogVisible="state.organizationalDialogVisible"
      @close="(value) => (state.organizationalDialogVisible = value)"
    />
    <ScheduleDialog
      v-if="state.scheduleDialogVisible"
      :scheduleDialogVisible="state.scheduleDialogVisible"
      @close="(value) => (state.scheduleDialogVisible = value)"
    />
    <ShelfDialog
      v-if="state.shelfDialogVisible"
      :shelfDialogVisible="state.shelfDialogVisible"
      @close="(value) => (state.shelfDialogVisible = value)"
    />
  </div>
</template>

<script>
  export default {
    name: 'WarehouseLayout',
  };
</script>

<script setup>
  import { ref, reactive, onBeforeMount } from 'vue';
  import { useRouter } from 'vue-router';
  import { setting } from '@/config/setting';
  import moment from 'moment';
  import OrganizationalDialog from '@/views/warehouse/components/OrganizationalDialog/index.vue';
  import ScheduleDialog from '@/views/warehouse/components/ScheduleDialog/index.vue';
  import ShelfDialog from '@/views/warehouse/components/ShelfDialog/index.vue';

  const router = useRouter();

  const currentTime = ref(moment().format('YYYY/MM/DD HH:mm:ss'));
  const syncCurrentTime = () => {
    setInterval(() => {
      currentTime.value = moment().format('YYYY/MM/DD HH:mm:ss');
    }, 1000);
  };

  const { copyright } = setting;
  const copyrightStr = ref(copyright);

  const state = reactive({
    organizationalDialogVisible: false,
    scheduleDialogVisible: false,
    shelfDialogVisible: false,
  });

  const handleOrganizationalClick = () => {
    state.organizationalDialogVisible = true;
  };

  const handleShelfClick = () => {
    state.shelfDialogVisible = true;
  };

  const handleScheduleClick = () => {
    state.scheduleDialogVisible = true;
  };

  onBeforeMount(() => {
    syncCurrentTime();
  });
</script>

<style lang="scss" scoped>
  .mes-container {
    user-select: none;
    .header {
      padding: 0;
      background-color: #241f20;
      .currentTime {
        position: absolute;
        left: 50%;
        top: 20%;
        transform: translate(-50%, -50%);
        color: white;
        font-weight: 900;
        text-align: center;
      }
      .menu {
        position: absolute;
        top: 5px;
        right: 20px;
        h2 {
          color: #fff;
          font-size: 18px;
          font-weight: 800;
        }
      }
      &.fixed {
        position: relative;
        width: 100%;
        z-index: $base-z-index-999;
      }
    }
    .main {
      padding: 0;
      height: calc(100vh - 70px);

      .app-main-container {
        position: relative;
        box-sizing: border-box;
        width: $base-width;
        overflow: hidden;
        text-align: left;
        .app-main-height {
          min-height: $app-main-min-height;
        }
        .footer-copyright {
          min-height: $footer-copyright-height;
          line-height: $footer-copyright-height;
          color: $base-color-3;
          text-align: center;
          border-top: 1px dashed $base-border-color;
        }
      }
    }
  }
</style>
