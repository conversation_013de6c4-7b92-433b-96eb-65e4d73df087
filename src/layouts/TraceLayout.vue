<template>
  <div class="mes-container">
    <el-container>
      <el-container class="container">
        <el-header class="header fixed" height="70px">
          <a href="/trace/scan">
            <img
              src="@/assets/LAI_logo.png"
              alt="LAIYIH"
              style="height: 60px; margin-right: 10px"
            />
          </a>
        </el-header>
        <el-main class="main">
          <div class="app-main-container">
            <router-view />
            <footer class="footer-copyright">{{ copyrightStr }} </footer>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
  export default {
    name: 'WarehouseLayout',
  };
</script>

<script setup>
  import { ref } from 'vue';
  import { setting } from '@/config/setting';

  const { copyright } = setting;
  const copyrightStr = ref(copyright);
</script>

<style lang="scss" scoped>
  .mes-container {
    user-select: none;
    .header {
      text-align: center;
      padding: 0;
      background-color: #241f20;

      &.fixed {
        position: relative;
        width: 100%;
        z-index: $base-z-index-999;
      }
    }
    .main {
      padding: 0;
      height: calc(100vh - 70px);

      .app-main-container {
        position: relative;
        box-sizing: border-box;
        width: $base-width;
        overflow: hidden;
        text-align: left;
        .app-main-height {
          min-height: $app-main-min-height;
        }
        .footer-copyright {
          min-height: $footer-copyright-height;
          line-height: $footer-copyright-height;
          color: $base-color-3;
          text-align: center;
          border-top: 1px dashed $base-border-color;
        }
      }
    }
  }
</style>
