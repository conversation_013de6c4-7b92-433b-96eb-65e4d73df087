import { createRouter, createWebHistory } from 'vue-router';
import i18n from '@/locales';

const { global } = i18n;

export const constantRoutes = [
  {
    path: '/',
    redirect: '/404',
    hidden: true,
  },

  {
    path: '/deletepage',
    name: 'deletepage',
    component: () => import('@/views/admin/deletepage.vue'),
    meta: { requiresAuth: true, public: true },
    hidden: true,
  },

  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/admin/login.vue'),
    meta: { title: 'login', public: true },
    hidden: true,
  },
  {
    path: '/qcindex4',
    name: 'qcindex4',
    component: () => import('@/views/qcapp/index4.vue'),
    meta: {
      title: global.t('QC App4'),
      public: true,
    },
    hidden: true,
  },
  {
    path: '/qcindex5',
    name: 'qcindex5',
    component: () => import('@/views/qcapp/index4.vue'),
    meta: {
      title: global.t('QC App4'),
      public: true,
    },
    hidden: true,
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/errorPage/404.vue'),
    hidden: true,
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404',
    hidden: true,
  },
];

export const asyncRoutes = [];

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
});

export function resetRouter() {
  router.getRoutes().forEach((route) => {
    const { name } = route;
    if (name) {
      router.hasRoute(name) && router.removeRoute(name);
    }
  });
}

// Middleware kiểm tra quyền truy cập trước mỗi lần điều hướng
router.beforeEach((to, from, next) => {
  if (to.matched.some((record) => record.meta.requiresAuth)) {
    if (!localStorage.getItem('token')) {
      next({ path: '/login' }); // Chuyển hướng đến trang đăng nhập nếu chưa đăng nhập
    } else {
      next();
    }
  } else {
    next();
  }
});

export default router;
