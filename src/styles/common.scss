@import './variable.scss';
@mixin scrollbar {
  max-height: 88vh;
  margin-bottom: 0.5vh;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

@mixin base-scrollbar {
  &::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #ddd;
    background-clip: padding-box;
    border: 3px solid transparent;
    border-radius: 7px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.5);
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

.icon-hover {
  cursor: pointer;
  &:hover {
    background-color: $base-hover-color;
  }
}

.i-icon:focus-visible {
  border: none !important;
  outline: none !important;
}

html {
  body {
    @include base-scrollbar;
    position: relative;
    box-sizing: border-box;
    height: 100vh;
    padding: 0;
    overflow: hidden;
  }
  div {
    @include base-scrollbar;
  }
}

.action-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;

  .el-pagination__sizes {
    margin-left: 16px;
  }
}
