$base-color-primary: var(--el-color-primary);

$base-color-primary-light1: var(--el-color-primary-light-1);
$base-color-primary-light2: var(--el-color-primary-light-2);
$base-color-primary-light3: var(--el-color-primary-light-3);
$base-color-primary-light4: var(--el-color-primary-light-4);
$base-color-primary-light5: var(--el-color-primary-light-5);
$base-color-primary-light6: var(--el-color-primary-light-6);
$base-color-primary-light7: var(--el-color-primary-light-7);
$base-color-primary-light8: var(--el-color-primary-light-8);
$base-color-primary-light9: var(--el-color-primary-light-9);

$base-z-index-999: 999;
$base-z-index-default: 99;

$base-hover-color: #f5f5f5;

$base-content-bg-color: #f1f2f5;

$base-title-color: #fff;

$dark-bg-color: #293246;

$base-width: 100%;
$base-tab-width_active: 70px;
$base-select-width-small: 120px;
$base-drawer-width: 320px;
$base-logo-width: 240px;

$base-unfold-width: 60px;

$base-menu-width: 240px;

$base-avatar-width: 40px;

$base-height: 100%;

$base-drawer-footer-height: 60px;

$sub-menu__title-height: 50px;

$base-logo-height: 55px;

$base-avatar-dropdown-height: 50px;

$base-avatar-height: 40px;

$footer-copyright-height: 55px;

$app-main-min-height: calc(100vh - 140px);

$base-main-mobile-top: 110px;
$base-main-mobile-no-tag-top: 60px;
$base-main-vertical-top: 50px;
$base-main-fixed-top: 110px;
$base-main-vertical-fixed-notag-top: 60px;
$base-main-notag-top: 0;

$base-border-width-mini: 1px;
$base-border-width-small: 3px;
$base-border-width-default: 5px;
$base-border-width-big: 10px;
$base-border-radius: 2px;
$base-border-radius-circle: 50%;
$base-border-none: none;

$base-font-size-small: 12px;
$base-font-size-default: 14px;
$base-font-size-big: 16px;
$base-font-size-bigger: 18px;
$base-font-size-max: 22px;
$base-border-color: #dcdfe6;

$base-icon-width-default: 14px;
$base-icon-width-small: 12px;
$base-icon-width-big: 16px;
$base-icon-width-bigger: 18px;
$base-icon-width-max: 22px;
$base-icon-width-super-max: 34px;
$base-icon-height-super-max: 50px;

$base-font-color: #606266;
$base-color-6: #666666;
$base-color-3: #333333;
$base-color-blue: $base-color-primary;
$base-color-green: #91cc75;
$base-color-white: #fff;
$base-color-black: #000;
$base-color-yellow: #fac858;
$base-color-orange: #ff6700;
$base-color-red: #ee6666;
$base-color-gray: rgba(0, 0, 0, 0.65);

$base-main-padding: 20px 30px;
$base-content-padding: 15px 20px;
$base-padding: 20px;
$base-cell-item-padding: 8px 0;
$base-padding-20-10: 20px 10px;
$base-padding-10-20: 10px 20px;
$base-padding-5-15: 5px 15px;
$base-padding-10: 10px;

$base-margin-5: 5px;
$base-margin-10: 10px;
$base-margin-15: 15px;
$base-margin-20: 20px;
$base-margin-20: 25px;

$base-box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
$base-tabs-bar-height: 55px;
$base-tag-item-height: 34px;
$base-nav-bar-height: 60px;

$base-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), border 0s, background 0s, color 0s,
  font-size 0s;
$base-transition-time: 0.3s;
$base-transition-time-4: 0.4s;
$base-color: #f45;
$green-color: #11d86c;

$color-red: red;
$color-green: green;
$color-blue: blue;

$dashborad-panel-bg: rgb(0 34 51 / 50%);
$dashborad-panel-item-bg: linear-gradient(40deg, rgb(11 101 140 / 45.1%), rgb(0 34 48 / 33.5%));
$popup-font-color: linear-gradient(180deg, #e6f7ff 0%, #26a6dd 100%);
