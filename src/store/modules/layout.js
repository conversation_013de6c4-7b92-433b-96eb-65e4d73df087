import { size } from 'lodash';

const state = {
  layoutModules: {
    manufacture: {
      Progress: {
        key: 'Progress',
        label: 'Progress',
        visible: true,
        updateTime: 1,
      },
      StitchingHourlyOutput: {
        key: 'StitchingHourlyOutput',
        label: 'Stitching HO',
        visible: true,
        updateTime: 2,
      },
      StitchingHourlyPPH: {
        key: 'StitchingHourlyPPH',
        label: 'Stitching PPH',
        visible: true,
        updateTime: 3,
      },
      Information: {
        key: 'Information',
        label: 'Information',
        visible: true,
        updateTime: 4,
      },
      AssemblyHourlyOutput: {
        key: 'AssemblyHourlyOutput',
        label: 'Assembly HO',
        visible: true,
        updateTime: 5,
      },
      AssemblyHourlyPPH: {
        key: 'AssemblyHourlyPPH',
        label: 'Assembly PPH',
        visible: true,
        updateTime: 6,
      },
    },
    quality: {
      Progress: {
        key: 'Progress',
        label: 'Progress',
        visible: true,
        updateTime: 1,
      },
      FTT: {
        key: 'FTT',
        label: 'FTT',
        visible: true,
        updateTime: 2,
      },
      HIPASS: {
        key: 'HIPASS',
        label: 'HI PASS',
        visible: true,
        updateTime: 3,
      },
      Information: {
        key: 'Information',
        label: 'Information',
        visible: true,
        updateTime: 4,
      },
      BC: {
        key: 'BC',
        label: 'BC',
        visible: true,
        updateTime: 5,
      },
      Defects: {
        key: 'Defects',
        label: 'TOP 3 Defects',
        visible: true,
        updateTime: 6,
      },
    },
  },
};

const getters = {
  layoutModules: (state) => state.layoutModules,
};

const mutations = {
  setLayoutModules(state, { page, module }) {
    state.layoutModules[page][module.key] = module;
    state.layoutModules = { ...state.layoutModules };
  },
};

const actions = {
  onToggleByModuleName({ commit }, { page, moduleName }) {
    const module = state.layoutModules[page][moduleName];
    const visible = !module.visible;
    if (visible && size(getters.layoutModules) >= 6) console.log('The upper limit is six modules');
    module.visible = visible;
    module.updateTime = new Date().getTime();
    commit('setLayoutModules', { page, module });
    return undefined;
  },
  onShowAllOrHideAll({ commit }, { page, visible }) {
    for (let key in state.layoutModules[page]) {
      const module = state.layoutModules[page][key];
      if (module.visible === visible) continue;

      module.visible = visible;
      module.updateTime = new Date().getTime();
      commit('setLayoutModules', { page, module });
    }
  },
};

export default {
  getters,
  state,
  mutations,
  actions,
};
